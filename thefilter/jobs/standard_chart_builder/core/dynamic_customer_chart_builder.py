import json
import os
import time
from dataclasses import dataclass
from typing import List, Dict, Union, Optional

from thefilter.aws.elastic_search import ElasticSearchModelRepository
from thefilter.aws.glue import GlueClientInterface
from thefilter.jobs.standard_chart_builder.chart_builder_config import ChartBuilderConfig
from thefilter.jobs.standard_chart_builder.core.base_customer_chart_builder import BaseCustomerChartBuilder
from thefilter.jobs.standard_chart_builder.core.chart_configuration import ChartConfiguration, PopularityVariant
from thefilter.jobs.standard_chart_builder.core.query_builders.availability_cte_builder import AvailabilityCTEBuilder
from thefilter.jobs.standard_chart_builder.core.query_builders.normalization_cte_builder import NormalizationCTEBuilder
from thefilter.jobs.standard_chart_builder.core.query_builders.overall_cte_builder import OverallCTEBuilder
from thefilter.jobs.standard_chart_builder.core.query_builders.popularity_cte_builder import PopularityCTEBuilder
from thefilter.jobs.standard_chart_builder.core.query_builders.trending_metrics_builder import TrendingMetricsBuilder
from thefilter.utils.datetime import DateTimeUtil, DateTimeProvider


@dataclass
class DynamicChartEntry:
    thing_id: str
    thing_brandid: str
    thing_name: str
    thing_typename: str
    filters: Dict[str, Union[str, float, int, bool, List]]
    metrics: Dict[str, float]

class DynamicCustomerChartBuilder(BaseCustomerChartBuilder):

    def __init__(self,
                 config: ChartBuilderConfig,
                 glue_client: GlueClientInterface,
                 chart_config: ChartConfiguration):
        super().__init__(config)
        self._glue_client = glue_client
        self._chart_config = chart_config

    def build_charts(self):
        # Use configuration values from chart configuration
        chart_size = self._chart_config.chart_size
        included_typenames = self._chart_config.included_typenames
        metadata_fields = self._chart_config.metadata_fields

        self._logger.info(f"Building dynamic charts with centralized configuration:")
        self._logger.info(f"  Chart size: {chart_size}")
        self._logger.info(f"  Included typenames: {included_typenames}")
        self._logger.info(f"  Metadata fields: {metadata_fields}")
        self._logger.info(f"  Popularity variants: {[v.name for v in self._chart_config.popularity_variants]}")
        self._logger.info(f"  Trending variants: {[v.name for v in self._chart_config.trending_variants]}")

        start_time = time.time()

        chart_data = self._get_chart_data(
            chart_size=chart_size,
            included_typenames=included_typenames,
            metadata_fields=metadata_fields
        )

        self._publish_charts(chart_data)

        end_time = time.time()
        elapsed_time = (end_time - start_time) * 1000
        self._chart_build_times["dynamic"] = elapsed_time

    def _get_chart_data(
            self,
            chart_size: int,
            included_typenames: List[str],
            metadata_fields: List[str]):
        self._logger.info("Constructing chart query...")

        # Get the latest timestamp for trending calculations
        latest_timestamp = self._get_latest_timestamp()
        self._logger.info(f"Using latest timestamp for trending: {latest_timestamp}")

        query = self._get_dynamic_chart_dataset_query(
            chart_size=chart_size,
            included_typenames=included_typenames,
            metadata_fields=metadata_fields,
            hidden_expression=self._chart_config.hidden_expression,
            latest_timestamp=latest_timestamp
        )

        self._logger.debug(query)

        self._logger.info("Retrieving chart data...")

        return self._query_athena(query, metadata_fields)

    def _get_latest_timestamp(self) -> str:
        """
        Gets the latest timestamp_initiated from events in the last 2 days.
        If no events are found, returns current time.
        """
        query = """
            SELECT
                max(timestamp_initiated) as max_timestamp
            FROM events
            WHERE CAST(datepartition AS DATE) >= DATE_ADD('day', -2, CURRENT_DATE)
                AND action = 'play'
                AND ignore is null
        """

        try:
            results = self._athena_client.query_athena(query)
            if results and results[0].get('max_timestamp'):
                return results[0]['max_timestamp']
            else:
                # If no events found, use current time
                self._logger.warning(f"No events in the last couple of days, no trending content")
                return DateTimeUtil.now_tf_str()
        except Exception as e:
            self._logger.warning(f"Error getting latest timestamp, using current time: {e}")
            return DateTimeUtil.now_tf_str()

    def _get_dynamic_chart_dataset_query(
            self,
            chart_size: int,
            included_typenames: List[str],
            metadata_fields: List[str],
            hidden_expression: str,
            latest_timestamp: Optional[str] = None) -> str:
        """
        Builds the dynamic chart dataset query with all necessary CTEs.
        
        This method ensures that the 30d popularity variant is always included in the configuration,
        even if it's not explicitly configured. This is necessary because the query relies on the
        play_30d_normalised metric for ordering results.
        
        Args:
            chart_size: The number of items to include in the chart
            included_typenames: List of content types to include
            metadata_fields: List of metadata fields to include in the output
            hidden_expression: SQL expression for filtering hidden content
            latest_timestamp: The latest timestamp to use for trending calculations
            
        Returns:
            The complete SQL query string
        """

        # Use current time as fallback if latest_timestamp is not provided
        if not latest_timestamp:
            latest_timestamp = DateTimeUtil.now_tf_str()

        # Create a temporary chart configuration for the builders
        # This allows us to override the chart size and other settings from the method parameters
        
        # Copy the popularity variants from the original configuration
        popularity_variants = list(self._chart_config.popularity_variants)
        
        # Ensure the 30d popularity variant is included
        if not self._chart_config.get_popularity_variant("30d"):
            self._logger.info("Adding missing 30d popularity variant to configuration")
            popularity_variants.append(
                PopularityVariant("30d", 30, "day", True, 1)
            )
        else:
            self._logger.debug("30d popularity variant already exists in configuration")
        
        temp_chart_config = ChartConfiguration(
            chart_size=chart_size,
            included_typenames=included_typenames,
            metadata_fields=metadata_fields,
            hidden_expression=hidden_expression,
            popularity_variants=popularity_variants,
            trending_variants=self._chart_config.trending_variants,
            enable_recency=self._chart_config.enable_recency,
            recency_metric=self._chart_config.recency_metric,
            enable_leaving_soon=self._chart_config.enable_leaving_soon,
            leaving_soon_metric=self._chart_config.leaving_soon_metric,
            leaving_soon_window_days=self._chart_config.leaving_soon_window_days
        )

        # Initialize query builders with the configuration
        popularity_builder = PopularityCTEBuilder(
            config={'latest_timestamp': latest_timestamp},
            chart_config=temp_chart_config
        )
        overall_builder = OverallCTEBuilder(config={}, chart_config=temp_chart_config)
        trending_builder = TrendingMetricsBuilder(config={'latest_timestamp': latest_timestamp})
        availability_builder = AvailabilityCTEBuilder(config={}, chart_config=temp_chart_config)
        normalization_builder = NormalizationCTEBuilder(config={}, chart_config=temp_chart_config)

        # Build query components
        popularity_ctes = popularity_builder.build()
        overall_cte = overall_builder.build()
        trending_cte = trending_builder.build()
        availability_cte = availability_builder.build()
        normalization_cte = normalization_builder.build()

        final_select_fields = self._build_final_select_fields(temp_chart_config)

        # Load up the popularity and overall ones first, so the others can reference them
        cte_parts = [popularity_ctes, overall_cte]

        if availability_cte:
            cte_parts.append(availability_cte)

        cte_parts.extend([
            trending_cte,
            normalization_cte
        ])

        cte_section = ',\n            '.join(cte_parts)
        return f"""
            WITH
            {cte_section}
            SELECT
                m.thing_id,
                nm.thing_brandid,
                nm.thing_typename,
                {final_select_fields}
            FROM normalised_metrics nm
            JOIN metadata m ON nm.thing_brandid = m.thing_id
            ORDER BY nm.play_30d_normalised DESC, m.thing_datepublished DESC
        """

    def _build_final_select_fields(self, chart_config: ChartConfiguration) -> str:
        fields = set()

        for metric in chart_config.all_metrics_for_output:
            fields.add(f"nm.{metric}")

        for field in chart_config.metadata_fields:
            fields.add(f"m.{field}")

        fields.add("m.thing_datepublished")
        fields.add("m.thing_name")

        return ',\n                '.join(fields)



    def _query_athena(self, query: str, metadata_fields: List[str]) -> List[DynamicChartEntry]:
        """
        Executes the Athena query and returns a list of DynamicChartEntry objects.

        Args:
            query: The Athena query to execute
            metadata_fields: List of metadata fields to extract from the query results
        """
        metadata_schema = self._glue_client.get_glue_schema("metadata")

        try:
            results = self._athena_client.query_athena(query)
        except Exception as e:
            self._logger.exception(f"Error executing Athena query when retrieving data for dynamic chart: {e}")
            raise

        if not results:
            self._logger.info("No results returned from Athena query for dynamic chart")
            return []

        try:
            return self._convert_athena_results_to_dynamic_chart_entries(
                results, 
                metadata_schema, 
                metadata_fields,
                self._chart_config
            )
        except Exception as e:
            self._logger.exception(f"Error converting Athena results to DynamicChartEntry objects: {e}")
            raise


    def _check_for_duplicates(self, documents: List[dict]) -> Dict[str, int]:
        """
        Checks for duplicate document IDs in the document set.

        Args:
            documents: List of document dictionaries to check

        Returns:
            Dictionary with counts of duplicates by ID
        """
        id_counts = {}
        for doc in documents:
            doc_id = doc["id"]
            id_counts[doc_id] = id_counts.get(doc_id, 0) + 1

        # Filter to only include IDs that appear more than once
        duplicates = {id: count for id, count in id_counts.items() if count > 1}
        return duplicates

    def _publish_charts(self, chart_data: List[DynamicChartEntry]):
        """
        Publishes chart data to Elasticsearch.

        Args:
            chart_data: List of DynamicChartEntry objects containing chart data
        """
        if not chart_data:
            self._logger.info(f"No chart data to publish to Elasticsearch")
            return

        # Use a timestamp format that includes both date and time to support multiple runs per day
        # Format: YYYYMMDD_HHMMSS (sorts chronologically when sorted alphabetically, aka chronobetically aka alphalogically)
        timestamp = DateTimeUtil.get_current_datetime_with_seconds()
        index_name = f"chart_{self._customer}_{timestamp}"

        es_documents = []
        for entry in chart_data:
            document = {
                "id": entry.thing_brandid,  # Use thing_brandid as the document ID
                "thing_id": entry.thing_id,
                "thing_brandid": entry.thing_brandid,
                "thing_name": entry.thing_name,
                "thing_typename": entry.thing_typename,
                "metrics": entry.metrics,
                "filters": entry.filters
            }
            es_documents.append(document)

        # Check for duplicates before posting to Elasticsearch
        duplicates = self._check_for_duplicates(es_documents)
        total_docs = len(es_documents)
        unique_docs = len(set(doc["id"] for doc in es_documents))

        if duplicates:
            self._logger.warning(f"Found {len(duplicates)} duplicate document IDs in chart data. This is probably a "
                                 f"bad thing! Only the last chart entry with each brand ID will be in the index after "
                                 f"this runs which could lead to incorrect chart results. This is likely an issue with "
                                 f"a join in the chart query that's letting episodes through, or something like that.")
            self._logger.warning(f"Total documents: {total_docs}, Unique IDs: {unique_docs}, Difference: {total_docs - unique_docs}")
            for doc_id, count in duplicates.items():
                duplicate_docs = [doc for doc in es_documents if doc["id"] == doc_id]
                duplicate_names = [doc["thing_name"] for doc in duplicate_docs]
                self._logger.warning(f"ID '{doc_id}' appears {count} times with names: {duplicate_names}")

        self._logger.info(f"Publishing {len(es_documents)} chart entries to Elasticsearch index {index_name} (unique IDs: {unique_docs})")

        template_path = os.path.join("thefilter", "jobs", "standard_chart_builder", "config", "chart_template.json")
        template_name = "chart_template"

        index_repo = ElasticSearchModelRepository(
            self._config.es_metadata_url,
            self._customer,
            "chart",
            index_name,
            self._logger,
            f"{self._job_name} Chart Builder"
        )

        # Check if the template exists and post it if needed
        existing_template = index_repo.get_template(template_name)

        if existing_template is not None:
            existing_template = existing_template.get(template_name)

        with open(template_path) as template_file:
            new_template = json.load(template_file)
            if existing_template is not None and existing_template == new_template:
                self._logger.info(f"Template is identical to current - not posting.")
            else:
                self._logger.info(f"Templates differ - posting.")
                index_repo.post_template(template_path, template_name)

        # Publish chart data to Elasticsearch
        index_repo.publish_results_to_es(
            es_documents,
            self._config.es_metadata_url,
            index_name
        )

        # Update the index (flip alias and delete old indices)
        index_repo.update_index()


    @staticmethod
    def _parse_comma_separated_string(field_value: str) -> List[str]:
        """
        Parse a comma-separated string into a list of strings, with each value stripped of whitespace.

        Args:
            field_value: A comma-separated string

        Returns:
            A list of strings with whitespace removed
        """
        try:
            return [v.strip() for v in field_value.split(',')]
        except Exception:
            return field_value


    @staticmethod
    def _convert_athena_results_to_dynamic_chart_entries(
            results: List[dict],
            metadata_schema: dict,
            metadata_fields: List[str],
            chart_config: ChartConfiguration
    ) -> List[DynamicChartEntry]:
        """
        Converts Athena query results to DynamicChartEntry objects.
        
        Args:
            results: List of dictionaries containing Athena query results
            metadata_schema: Dictionary mapping field names to their types
            metadata_fields: List of metadata fields to extract from the results
            chart_config: Optional ChartConfiguration object used to dynamically generate metrics
                          based on configured variants. If None, falls back to hardcoded metrics.
                          
        Returns:
            List of DynamicChartEntry objects containing the extracted data
        """
        chart_entries = []

        # Find the oldest and newest publication dates to calculate recency score
        oldest_date = None
        newest_date = None

        # First pass to find date range
        for row in results:
            date_published = row.get("thing_datepublished")
            if date_published:
                try:
                    # Convert to datetime object
                    if isinstance(date_published, str):
                        date_obj = DateTimeUtil.parse_date_or_datetime(date_published)
                    else:
                        date_obj = date_published

                    if oldest_date is None or date_obj < oldest_date:
                        oldest_date = date_obj
                    if newest_date is None or date_obj > newest_date:
                        newest_date = date_obj
                except (ValueError, TypeError):
                    # Skip invalid dates
                    pass

        # Default to current date range if no valid dates found
        now = DateTimeProvider().utc_now()
        if oldest_date is None:
            oldest_date = now.replace(year=now.year - 10)
        if newest_date is None:
            newest_date = now

        # Calculate date range in days
        date_range = (newest_date - oldest_date).days
        if date_range == 0:  # Avoid division by zero
            date_range = 1

        for row in results:
            entry = DynamicChartEntry(
                thing_id=row["thing_id"],
                thing_brandid=row["thing_brandid"],
                thing_name=row["thing_name"],
                thing_typename=row["thing_typename"],
                filters={},
                metrics={},
            )

            # For each popularity variant
            for variant in chart_config.popularity_variants:
                entry.metrics[variant.play_metric] = float(row.get(variant.play_metric, 0.0))
                entry.metrics[variant.user_metric] = float(row.get(variant.user_metric, 0.0))
                entry.metrics[f"{variant.play_metric}_normalised"] = float(row.get(f"{variant.play_metric}_normalised", 1.0))
                entry.metrics[f"{variant.user_metric}_normalised"] = float(row.get(f"{variant.user_metric}_normalised", 1.0))

            # For each trending variant
            for variant in chart_config.trending_variants:
                entry.metrics[variant.slope_metric] = float(row.get(variant.slope_metric, 0.0))
                entry.metrics[variant.normalised_metric] = float(row.get(variant.normalised_metric, 1.0))

            # Add leaving soon score if enabled
            if chart_config.enable_leaving_soon:
                entry.metrics[chart_config.leaving_soon_metric] = float(row.get(chart_config.leaving_soon_metric, 1.0))

            # Calculate recency score (1.0-2.0 scale, where 2.0 is newest and 1.0 is oldest)
            date_published = row.get("thing_datepublished")
            if date_published:
                # Convert to datetime object if it's a string
                if isinstance(date_published, str):
                    date_obj = DateTimeUtil.parse_date_or_datetime(date_published)
                else:
                    date_obj = date_published

                # Calculate days since oldest publication
                days_since_oldest = (date_obj - oldest_date).days

                # Normalize to 1.0-2.0 scale
                recency_score = 1.0 + (days_since_oldest / date_range)

                # Store the date and recency score
                entry.metrics["date_published"] = date_published
                entry.metrics["recency_score"] = recency_score
            else:
                # Default values if no date is available
                entry.metrics["date_published"] = None
                entry.metrics["recency_score"] = 1.0

            # Process metadata fields and add them to the filters dictionary
            for field_name in metadata_fields:
                if field_name in row:
                    field_value = row[field_name]

                    # Skip None values
                    if field_value is None:
                        continue

                    # Get the field type from the schema
                    field_type = metadata_schema.get(field_name, "string")

                    # Convert the value based on the field type from the schema
                    if field_type == "string" or field_type.startswith("varchar"):
                        # Handle comma-separated lists of strings
                        if isinstance(field_value, str) and ',' in field_value:
                            entry.filters[field_name] = DynamicCustomerChartBuilder._parse_comma_separated_string(field_value)
                        else:
                            entry.filters[field_name] = field_value
                    elif field_type == "int" or field_type == "integer" or field_type == "bigint":
                        try:
                            entry.filters[field_name] = int(field_value)
                        except (ValueError, TypeError):
                            # If conversion fails, keep as original type
                            entry.filters[field_name] = field_value
                    elif field_type == "double" or field_type == "float" or field_type == "decimal":
                        try:
                            entry.filters[field_name] = float(field_value)
                        except (ValueError, TypeError):
                            # If conversion fails, keep as original type
                            entry.filters[field_name] = field_value
                    elif field_type == "boolean":
                        if isinstance(field_value, str):
                            entry.filters[field_name] = field_value.lower() == "true"
                        else:
                            entry.filters[field_name] = bool(field_value)
                    elif field_type.startswith("array"):
                        if isinstance(field_value, str) and ',' in field_value:
                            entry.filters[field_name] = DynamicCustomerChartBuilder._parse_comma_separated_string(field_value)
                        elif isinstance(field_value, list):
                            entry.filters[field_name] = field_value
                        else:
                            entry.filters[field_name] = [field_value]
                    else:
                        # For any other types, keep the original value
                        entry.filters[field_name] = field_value

            # Add entry to list
            chart_entries.append(entry)

        return chart_entries