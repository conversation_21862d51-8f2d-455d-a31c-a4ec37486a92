import random
import time
from itertools import zip_longest
from typing import List, Optional

from thefilter.config.centralised_config import CentralisedConfig
from thefilter.jobs.standard_chart_builder.chart_builder_config import ChartBuilderConfig
from thefilter.jobs.standard_chart_builder.core.chart_backfill import ChartBackfill
from thefilter.jobs.standard_chart_builder.core.genre_chart_builder import \
    GenreChartBuilder
from thefilter.jobs.standard_chart_builder.core.leaving_soon_chart_builder import \
    LeavingSoonChartBuilder
from thefilter.jobs.standard_chart_builder.utils.daily_trending import DailyTrending
from thefilter.jobs.standard_chart_builder.utils.utils import get_athena_cursor, \
    filter_empty_charts
from thefilter.logs.logclient import PrintLogClient
from thefilter.model.chart import ChartDDB
from thefilter.utils.retry import retry


class StandardCustomerChartBuilder:
    def __init__(self, config: ChartBuilderConfig):
        self._config = config
        self._environment = config.environment.lower()
        self._region = config.region.lower()
        self._logger = config.logger
        self._job_name = config.job_name
        self._chart_size = config.chart_size
        self._now = config.now
        self._ttl_epoch = config.ttl_epoch
        self._formatter = config.formatter
        self._customer = config.customer
        self._athena_client = config.athena_client
        self._is_events = False
        self._bucket = config.bucket
        self._schema = config.schema
        self._chart_backfill = ChartBackfill(
            self._region, self._environment, self._customer, self._athena_client,
            self._logger
        )
        self._eventless_size = 20
        self._genre_chart_builder = GenreChartBuilder(config=config)
        self._chart_build_times = {}
        self._hidden_expression = self._set_hidden_expression()

    def build_charts(self) -> List[ChartDDB]:
        self._logger.info(f'{self._job_name} Generating charts.')
        charts = self.generate_charts()

        charts = self._chart_backfill.backfill_charts(charts)

        fallback_rfy = self._build_fallback_rfy_chart()
        charts.append(fallback_rfy)

        charts = self.create_fallback_rfy_audioseries_chart(charts)

        if self._customer.lower() == "rtve":
            fallback_rfy_kids = self._build_fallback_rfy_kids_chart()
            charts.append(fallback_rfy_kids)

        filtered_charts = filter_empty_charts(
            charts, self._logger, environment=self._environment, region=self._region,
            customer=self._customer
        )

        self._logger.info(f'{self._job_name} Publishing charts.')
        self._log_formatter(filtered_charts)

        return filtered_charts

    def generate_charts(self) -> List[ChartDDB]:
        charts = []

        self._logger.info("Creating Latest Standard chart")
        query = self._get_latest_chart_query()
        latest_chart = self.build_chart_with_timing("latest-standard-standard", query)
        charts.append(latest_chart)

        self._logger.info("Creating Latest PublicationDate chart")
        latest_publicationdate_query = self.get_latest_publicationdate_chart_query()
        latest_publicationdate_chart = self.build_chart_with_timing(
            "latest-publicationdate-standard", latest_publicationdate_query
        )
        charts.append(latest_publicationdate_chart)

        self._logger.info("Checking if charts can use events")
        self._is_events = self._athena_client.check_table_exists(table_name="events")
        if self._is_events:
            event_count = self._get_event_count()
            if event_count <= 300:
                self._is_events = False

        if self._is_events:
            self._logger.info("Using events")
            charts = self._event_based_charts(charts)
        else:
            self._logger.info("Not using events")
            charts = self._eventless_based_charts(
                charts, latest_chart, trending_periods=6
            )

        distinct_typenames = self._get_distinct_typenames()

        if 'AudioSeries' in distinct_typenames:
            self._logger.info("AudioSeries detected. Generating AudioSeries charts.")

            self._logger.info("Creating Latest AudioSeries chart")
            query = self._get_latest_chart_query(typename='AudioSeries')
            latest_audioseries_chart = self.build_chart_with_timing("latest-audioseries",
                                                                    query)
            charts.append(latest_audioseries_chart)

            self._logger.info("Creating Most Popular AudioSeries chart")
            if self._is_events:
                query = self._get_most_popular_chart_query_by_play(days=7,
                                                                   typename='AudioSeries')
                most_popular_audioseries_chart = self.build_chart_with_timing(
                    "most-popular-audioseries", query)
            else:
                query = self._get_latest_chart_query(typename='AudioSeries')
                most_popular_audioseries_chart = self.build_chart_with_timing(
                    "most-popular-audioseries", query)
            charts.append(most_popular_audioseries_chart)

        leaving_soon = LeavingSoonChartBuilder(self._config)
        leaving_soon_chart = \
            leaving_soon.create_leaving_soon_chart(is_events=self._is_events)
        charts.extend(leaving_soon_chart)
        self._chart_build_times.update(leaving_soon.chart_build_times)

        return charts

    def build_chart_with_timing(self, chart_name: str, query: str,
                                chart_name_override: Optional[str] = None,
                                persist: bool = True) -> ChartDDB:
        self._logger.info(f"Building chart: {chart_name}")
        start_time = time.time()
        chart = self.query_athena_and_format(chart_name, query, chart_name_override)
        end_time = time.time()
        elapsed_time = (end_time - start_time) * 1000
        if persist:
            self._chart_build_times[chart_name] = elapsed_time
        self._logger.info(f"Chart {chart_name} built in {elapsed_time:.2f} ms")

        return chart

    @retry(Exception, tries=2, delay=10, backoff=2, logger=PrintLogClient())
    def query_athena_and_format(
            self,
            chart_name: str,
            query: str,
            chart_name_override: Optional[str] = None
    ) -> ChartDDB:
        cursor = get_athena_cursor(self._bucket, self._schema, self._region)
        results = cursor.execute(query)

        if chart_name_override:
            chart_name = chart_name_override
        if "latest" in chart_name or "trending" in chart_name:
            # TODO: latest and trending charts do not yet include a count
            include_count = False
        else:
            include_count = True

        return self._formatter.format_chart_athena(
            self._job_name,
            chart_name,
            results.fetchall(),
            chart_item_index=0,
            include_count=include_count
        )

    def _set_hidden_expression(self) -> str:
        centralised_config = CentralisedConfig(
            region=self._region, environment=self._environment, logger=self._logger
        )
        is_backstage = centralised_config.is_backstage_customer(self._customer)

        if is_backstage:
            ishidden_exists = self._athena_client.check_field_exists(
                "metadata", "thing_custom_ishidden"
            )
            if ishidden_exists:
                hidden_expression = "AND thing_custom_ishidden != true"
            else:
                hidden_expression = ""
        else:
            hidden_expression = ""

        return hidden_expression

    def _get_latest_chart_query(self, genre: Optional[str] = None,
                                typename: Optional[str] = None) -> str:
        # This function generates the SQL query to get the latest chart
        if genre:
            genre_expression = f"AND CONTAINS(m.thing_genre_name, '{genre}')"
        else:
            genre_expression = ""

        if typename:
            typename_expression = f"AND m.thing_typename = '{typename}'"
        else:
            typename_expression = "AND m.thing_typename IN ('Movie', 'TVSeries')"

        return f"""
            SELECT
                m.thing_brandid,
                timestamp_added
            FROM
                 metadata m
            WHERE
                thing_custom_active_state = true
                {typename_expression}
                {self._hidden_expression}
                AND (m.thing_brandid is not null OR m.thing_brandid != '0')
                {genre_expression}
            GROUP BY
                m.thing_brandid,
                timestamp_added
            ORDER BY
                timestamp_added DESC
            LIMIT {self._chart_size}
        """

    def get_latest_publicationdate_chart_query(self, genre: Optional[str] = None) -> str:
        # This function generates the SQL query to get the
        # latest chart ordered by thing_publication_startdate
        # Takes the fist publication, will not work with more than 1 publication date!

        if genre:
            genre_expression = f"AND CONTAINS(m.thing_genre_name, '{genre}')"
        else:
            genre_expression = ""

        if self._customer == "rtve":
            return f"""
                SELECT
                    m.thing_brandid,
                    CASE 
                        WHEN m.thing_typename = 'TVSeries' 
                        THEN 				
                            date_parse(m.thing_datepublished, '%Y-%m-%dT%H:%i:%s+00:00')
                        WHEN m.thing_typename = 'Movie' 
                        THEN
                            date_parse(m.thing_custom_dateofemission, '%d-%m-%Y %H:%i:%s')	
                    END AS date_field
                FROM
                    metadata m
                WHERE
                    m.thing_custom_active_state = true
                    AND m.thing_typename IN ('TVSeries', 'Movie')
                    AND (m.thing_brandid IS NOT NULL OR m.thing_brandid != '0')
                    AND (
                        (m.thing_typename = 'TVSeries' 
                            AND date_parse(m.thing_datepublished, '%Y-%m-%dT%H:%i:%s+00:00') < DATE_ADD('day', 1, current_timestamp))
                        OR
                        (m.thing_typename = 'Movie' 
                            AND date_parse(m.thing_custom_dateofemission, '%d-%m-%Y %H:%i:%s') < DATE_ADD('day', 1, current_timestamp))
                    )
                GROUP BY
                    m.thing_brandid, 2
                ORDER BY
                    date_field DESC
               LIMIT {self._chart_size}
                """

        else:
            return f"""
                SELECT
                    m.thing_brandid,
                    element_at(thing_publication_startdate, 1)
                FROM
                     metadata m
                WHERE
                    thing_custom_active_state = true
                    AND m.thing_typename IN ('Movie', 'TVSeries')
                    AND (m.thing_brandid is not null OR m.thing_brandid != '0')
                    AND cast(substring(element_at(thing_publication_startdate, 1), 1, 10)  as DATE) < DATE_ADD('day', 1, CURRENT_DATE)
                    {genre_expression}
                GROUP BY
                    1, 2
                ORDER BY
                    2 DESC
                LIMIT {self._chart_size}
            """

    def get_latest_popular_query(self) -> str:
        # uses timestamp_added for the ordering of "latest" to the platform
        # We can also pass in the
        #   - genres to get "latest by genre"! :pog:
        """
        Generates the SQL query to retrieve the most popular charts, ordered by the latest.
        """
        return f"""
        WITH popular_events AS (
            SELECT
                thing_id,
                COUNT(*) AS recent_popularity
            FROM
                events
            WHERE
                CAST(datepartition AS DATE) > DATE_ADD('day', -7, CURRENT_DATE)
            AND
                ignore is null
            GROUP BY
                distinct(thing_id)
            ORDER BY recent_popularity DESC
            )
        SELECT
            m.thing_brandid,
            m.thing_typename,
            sum(pb.recent_popularity) as count,
            m.timestamp_added
        FROM popular_events as pb
        LEFT JOIN metadata AS m
        ON pb.thing_id = m.thing_id
        WHERE  
            m.thing_brandid is not null
            AND thing_custom_active_state = TRUE
            {self._hidden_expression}
            AND m.thing_typename in ('TVSeries', 'Movie')
        GROUP BY 
            m.thing_brandid,
            m.thing_typename,
            m.timestamp_added 
        ORDER BY
            m.timestamp_added DESC
        LIMIT {self._chart_size}
        """

    def _event_based_charts(self, charts: List[ChartDDB]) -> List[ChartDDB]:
        self._logger.info("Creating charts using events table")
        # distinct_typenames = self._get_distinct_typenames()
        if self._customer == "rtve":
            days = [30, 7, 2, 1]
        else:
            days = [1, 7, 14, 30]

        for day in days:
            ########## These are most popular general charts ############
            self._logger.info(f"Creating Most Popular {day} day by play")
            query = self._get_most_popular_chart_query_by_play(days=day)
            chart_name = f"most-popular-{day}d-play"
            most_popular_chart = self.build_chart_with_timing(chart_name, query)
            charts.append(most_popular_chart)

            self._logger.info(f"Creating Most Popular {day} day by user")
            query = self._get_most_popular_chart_query_by_user(days=day)
            chart_name = f"most-popular-{day}d-user"
            most_popular_chart = self.build_chart_with_timing(chart_name, query)
            charts.append(most_popular_chart)

            ########## These are most popular tvseries charts ############
            self._logger.info(f"Creating Most Popular TVSeries {day} day by play")
            query = self._get_most_popular_chart_query_by_play(days=day,
                                                               typename='TVSeries')
            chart_name = f"most-popular-tvseries-{day}d-play"
            most_popular_tvseries_play_chart = self.build_chart_with_timing(chart_name,
                                                                            query)
            charts.append(most_popular_tvseries_play_chart)

            self._logger.info(f"Creating Most Popular TVSeries {day} day by user")
            query = self._get_most_popular_chart_query_by_user(days=day,
                                                               typename='TVSeries')
            chart_name = f"most-popular-tvseries-{day}d-user"
            most_popular_tvseries_user_chart = self.build_chart_with_timing(chart_name,
                                                                            query)
            charts.append(most_popular_tvseries_user_chart)

            ########## These are most popular movie charts ############
            self._logger.info(f"Creating Most Popular Movie {day} day by play")
            query = self._get_most_popular_chart_query_by_play(days=day,
                                                               typename='Movie')
            chart_name = f"most-popular-movie-{day}d-play"
            most_popular_movie_play_chart = self.build_chart_with_timing(chart_name,
                                                                         query)
            charts.append(most_popular_movie_play_chart)

            self._logger.info(f"Creating Most Popular Movie {day} day by user")
            query = self._get_most_popular_chart_query_by_user(days=day,
                                                               typename='Movie')
            chart_name = f"most-popular-movie-{day}d-user"
            most_popular_movie_user_chart = self.build_chart_with_timing(chart_name,
                                                                         query)
            charts.append(most_popular_movie_user_chart)

        self._logger.info(f"Creating Most Popular 90 day by play")
        # Used for RFY backfill on Page Response
        query = self._get_most_popular_chart_query_by_play(days=90)
        chart_name = f"most-popular-90d-play"
        most_popular_chart = self.build_chart_with_timing(chart_name, query)
        charts.append(most_popular_chart)

        self._logger.info("Creating Latest by Popularity chart")
        query = self.get_latest_popular_query()
        latest_popular_chart = self.build_chart_with_timing("latest-standard-popular",
                                                            query)
        charts.append(latest_popular_chart)

        if self._customer == "rtve":
            # RTVE use the recent trending, so no need to build daily trending!
            pass
        else:
            self._logger.info(
                "Creating Trending charts "
                "(includes TVSeries, AudioSeries and Movie Trending)"
            )
            trending_charts = self.trending_daily_builder(days=7, periods=6)
            charts += trending_charts

        self._logger.info("Creating Genre charts")
        genre_charts = self._genre_chart_builder.create_genre_charts()
        charts += genre_charts
        self._chart_build_times.update(self._genre_chart_builder.chart_build_times)

        return charts

    def _eventless_based_charts(
            self, charts: List[ChartDDB], latest_chart: ChartDDB, trending_periods: int
    ) -> List[ChartDDB]:
        self._logger.info("Creating charts without using events table")
        """Creates random charts based on the latest chart"""

        superset_chart = latest_chart.chart
        chart_names = self._create_eventless_chart_names()

        basic_eventless_charts = self._generate_sublist_charts(
            superset_chart, chart_names
        )
        charts += basic_eventless_charts

        trending_eventless_charts = self._trending_eventless_charts(trending_periods)
        charts += trending_eventless_charts

        genre_charts = self._create_genre_charts_eventless()
        charts += genre_charts

        return charts

    def _generate_sublist_charts(
            self, superset_chart: List[str], chart_names: List[str]
    ) -> List[ChartDDB]:
        number_of_charts = len(chart_names)
        if len(superset_chart) == 0:
            return []
        elif len(superset_chart) < self._eventless_size:
            size = len(superset_chart)
            flip_index = 1
        else:
            size = self._eventless_size
            flip_index = 6

        chart_sublists = self._create_sublists(superset_chart, number_of_charts, size)
        fliperood = self._basic_flipper(chart_sublists, 1, flip_index)
        eventless_charts = []
        for chart_name, items in zip(chart_names, fliperood):
            start_time = time.time()
            chart_ddb = ChartDDB(
                ddbId=chart_name.lower(),
                created=self._now.isoformat(timespec='seconds'),
                timeToLiveEpoch=self._ttl_epoch,
                chart=items
            )
            end_time = time.time()
            elapsed_time = (end_time - start_time) * 1000
            self._chart_build_times[chart_name] = elapsed_time
            eventless_charts.append(chart_ddb)
        return eventless_charts

    @staticmethod
    def _create_sublists(superset: list, n: int, size: int) -> list:
        # Replace 1 item and shuffle 1 item
        base_sublist = superset[:size]
        sublists = [base_sublist.copy() for _ in range(n)]

        for sublist in sublists:
            # Replace 1 item
            if len(superset) > size:
                index_to_replace = random.randint(0, size - 1)
                new_element = random.choice(
                    [item for item in superset if item not in sublist]
                )
                sublist[index_to_replace] = new_element

            # Optionally shuffle 1 item
            if size > 1:
                idx1, idx2 = random.sample(range(size), 2)
                sublist[idx1], sublist[idx2] = sublist[idx2], sublist[idx1]

        return sublists

    @staticmethod
    def _basic_flipper(charts: List[list], important_index: int, items_to_check: int) -> \
            List[list]:
        for pos in range(items_to_check):
            pivot_item = charts[important_index][pos]
            for i in range(len(charts)):
                flip_value = abs(important_index - i) * 2
                if i == important_index:
                    continue
                if charts[i][pos] == pivot_item:
                    charts[i][pos:pos + flip_value] = charts[i][pos:pos + flip_value][
                                                      ::-1]

        return charts

    def _create_genre_charts_eventless(self) -> List[ChartDDB]:
        result = []

        genres = self._genre_chart_builder.get_distinct_genres()
        for genre in genres:
            chart_name = f"genre-{genre.lower()}"
            self._logger.info(f"Building genre chart: {chart_name}")

            query = self._get_latest_chart_query(genre=genre)
            genre_chart = self.build_chart_with_timing(chart_name, query)
            result.append(genre_chart)

        return result

    def get_latest_by_type(self) -> dict:
        result = {}

        type_names = ["Movie", "TVSeries"]
        for type_name in type_names:
            eventless_query = f"""
                SELECT
                    m.thing_brandid
                FROM
                     metadata m
                WHERE
                    thing_custom_active_state = true
                    {self._hidden_expression}
                    AND m.thing_typename = '{type_name}'
                    AND (m.thing_brandid is not null
                        OR m.thing_brandid != '0')
                GROUP BY
                    m.thing_brandid,
                    timestamp_added
                ORDER BY
                    timestamp_added desc
                limit {self._chart_size}
            """

            query_result = self._athena_client.query_athena(eventless_query,
                                                            simplify=True)
            result[type_name] = query_result

        # Renaming here as they play nicer with the chart name variants
        result["movies"] = result.pop("Movie")
        result["series"] = result.pop("TVSeries")

        movies = result.get("movies", [])
        series = result.get("series", [])

        standard = [
            item for pair in zip_longest(movies, series)
            for item in pair if item is not None
        ]
        result["standard"] = standard

        return result

    def _generate_recent_trending_eventless(
            self, type_name_items: dict
    ) -> List[ChartDDB]:
        recent_trending_charts = []
        trending_chart_names = [
            "trending-recent-standard",
            "trending-recent-movies",
            "trending-recent-series"
        ]
        for chart_name in trending_chart_names:
            variant = chart_name.replace("trending-recent-", "")
            items = type_name_items[variant][:self._eventless_size]

            start_time = time.time()
            chart_ddb = ChartDDB(
                ddbId=chart_name.lower(),
                created=self._now.isoformat(timespec='seconds'),
                timeToLiveEpoch=self._ttl_epoch,
                chart=items
            )
            end_time = time.time()
            elapsed_time = (end_time - start_time) * 1000
            self._chart_build_times[chart_name] = elapsed_time
            recent_trending_charts.append(chart_ddb)
        return recent_trending_charts

    def trending_daily_builder(self, days: int, periods: int) -> List[ChartDDB]:
        sql_trending = DailyTrending(
            self._environment, self._region, self._bucket, self._schema,
            self._logger, days, periods
        )

        trending_all_charts = sql_trending.daily_trending_builder(
            self.trending_all_query, trending_prefix='trending-daily-standard'
        )

        trending_movies_charts = sql_trending.daily_trending_builder(
            self.trending_movie_query, trending_prefix='trending-daily-movies'
        )

        trending_series_charts = sql_trending.daily_trending_builder(
            self.trending_series_query, trending_prefix='trending-daily-series'
        )

        combined_trending_charts = \
            trending_all_charts + trending_movies_charts + trending_series_charts

        if self._customer.lower() == "rtve":
            trending_audio_series_charts = sql_trending.daily_trending_builder(
                self.trending_audio_series_query,
                trending_prefix='trending-daily-audioseries'
            )
            combined_trending_charts += trending_audio_series_charts

        self._chart_build_times.update(sql_trending.chart_build_times)

        return combined_trending_charts

    def trending_all_query(self, start: str, end: str):
        return self.trending_aggregate_object(start, end, 'all')

    def trending_movie_query(self, start: str, end: str):
        return self.trending_aggregate_object(start, end, 'Movie')

    def trending_series_query(self, start: str, end: str):
        return self.trending_aggregate_object(start, end, 'TVSeries')

    def trending_audio_series_query(self, start: str, end: str):
        return self.trending_aggregate_object(start, end, 'AudioSeries')

    def trending_aggregate_object(self, start: str, end: str, trending_type: str) -> str:
        """ Using a wrapper function just so we don't have to write three queries"""
        trending_query_specifics_map = {
            "all": "AND m.thing_typename in ('TVSeries', 'Movie')",
            "Movie": "AND m.thing_typename = 'Movie'",
            "TVSeries": "AND m.thing_typename = 'TVSeries'",
            "AudioSeries": "AND m.thing_typename = 'AudioSeries'"
        }
        sql_params = trending_query_specifics_map.get(trending_type)

        def trending_query(start: str, end: str) -> str:
            start_partition = str(start)[0:10]
            end_partition = str(end)[0:10]

            trending_chart_depth = 100
            # This inner function gets passed as a callable,
            # and is used multiple times over the numerous segments of time.
            return f"""
                WITH popular_events AS (
                    SELECT
                        brand_id,
                        COUNT(*) AS recent_popularity
                    FROM
                        events
                    WHERE
                        datepartition BETWEEN '{start_partition}' AND '{end_partition}'
                        AND timestamp_initiated BETWEEN '{start}' AND '{end}'
                        AND ignore is null
                    GROUP BY
                        brand_id
                    ORDER BY recent_popularity DESC
                )

                SELECT
                    m.thing_brandid,
                    m.thing_typename,
                    sum(pb.recent_popularity) as count
                FROM popular_events as pb
                LEFT JOIN metadata AS m
                ON pb.brand_id = m.thing_id
                WHERE  m.thing_brandid is not null
                AND thing_custom_active_state = TRUE
                {self._hidden_expression}
                {sql_params}
                GROUP BY m.thing_brandid, m.thing_typename
                ORDER BY count DESC
                LIMIT {trending_chart_depth}
            """

        return trending_query(start, end)

    def _get_most_popular_chart_query_by_play(self, days: int,
                                              typename: Optional[str] = None) -> str:
        if typename:
            typename_expression = f"AND m.thing_typename = '{typename}'"
        else:
            typename_expression = "AND m.thing_typename IN ('TVSeries', 'Movie')"

        return f"""
            WITH popular_events AS (
                SELECT
                    brand_id,
                    COUNT(*) AS recent_popularity
                FROM
                    events
                WHERE
                    CAST(datepartition AS DATE) > DATE_ADD('day', -{days}, CURRENT_DATE)
                AND ignore is null
                GROUP BY
                    brand_id
                ORDER BY recent_popularity DESC
            )

            SELECT
                m.thing_brandid,
                m.thing_typename,
                sum(pb.recent_popularity) as count

            FROM popular_events as pb
            LEFT JOIN metadata AS m
            ON pb.brand_id = m.thing_id

            WHERE  
                m.thing_brandid is not null
                AND thing_custom_active_state = TRUE
                {self._hidden_expression}
                {typename_expression}

            GROUP BY m.thing_brandid, m.thing_typename
            ORDER BY count DESC

            LIMIT {self._chart_size}
        """

    def _get_most_popular_chart_query_by_user(self, days: int,
                                              typename: Optional[str] = None) -> str:
        if typename:
            typename_expression = f"AND m.thing_typename = '{typename}'"
        else:
            typename_expression = "AND m.thing_typename IN ('TVSeries', 'Movie')"

        return f"""
            WITH popular_events AS (
                SELECT
                    brand_id,
                    COUNT(distinct(user_primary_id)) AS recent_popularity
                FROM
                    events
                WHERE
                    CAST(datepartition AS DATE) > DATE_ADD('day', -{days}, CURRENT_DATE)
                AND ignore is null
                GROUP BY brand_id
                ORDER BY recent_popularity DESC
            )

            SELECT
                m.thing_brandid, 
                m.thing_typename,
                sum(pb.recent_popularity) as count

            FROM popular_events as pb
            LEFT JOIN metadata AS m
            ON pb.brand_id = m.thing_id

            WHERE  
                m.thing_brandid is not null
                AND thing_custom_active_state = TRUE
                {self._hidden_expression}
                {typename_expression}

            GROUP BY m.thing_brandid, m.thing_typename
            ORDER BY count DESC

            LIMIT {self._chart_size}
        """

    @staticmethod
    def _create_eventless_chart_names() -> List[str]:
        static_eventless_chart_names = [
            "latest-standard-popular",
            "most-popular-fallback"
        ]

        # Dynamic Names: Most Popular (Variant & Overlay)
        variants = [1, 7, 14, 30]
        overlays = ['user', 'play']
        most_popular_chart_names = [
            f'most-popular-{variant}d-{overlay}'
            for overlay in overlays
            for variant in variants
        ]

        result = static_eventless_chart_names + most_popular_chart_names

        return result

    def _trending_eventless_charts(self, trending_periods: int) -> List[ChartDDB]:
        type_name_items = self.get_latest_by_type()

        trending_recent_charts = \
            self._generate_recent_trending_eventless(type_name_items)

        trending_daily_charts = []
        trending_chart_names = {}
        trending_daily_names = [
            "trending-daily-standard",
            "trending-daily-movies",
            "trending-daily-series",
        ]
        for trending_chart_type in trending_daily_names:
            for i in range(trending_periods):
                trending_chart_name = f'{trending_chart_type}-{i}'
                if not trending_chart_names.get(trending_chart_type):
                    trending_chart_names[trending_chart_type] = [trending_chart_name]
                else:
                    trending_chart_names[trending_chart_type].append(trending_chart_name)

        for variant, chart_names in trending_chart_names.items():
            variant_slug = variant.replace("trending-daily-", "")
            items = type_name_items.get(variant_slug)[:30]

            sublist_charts = self._generate_sublist_charts(items, chart_names)
            trending_daily_charts += sublist_charts

        result = trending_recent_charts + trending_daily_charts
        return result

    def _get_event_count(self) -> int:
        query = """
        SELECT
            COUNT(*)
        FROM
            events
        WHERE
            CAST(datepartition AS DATE) > DATE_ADD('day', -14, CURRENT_DATE)
        AND ignore is null
        """
        try:
            event_count = int(self._athena_client.query_athena(query, simplify=True)[0])
        except Exception as e:
            self._logger.error("Issue with getting event count", e)
            event_count = 0

        return event_count

    def _log_formatter(self, charts: List[ChartDDB]):
        self._logger.info(f"{len(charts)} charts in total: (first 3 items shown)")
        for chart in charts:
            ddb_id = chart.ddbId
            num_items = len(chart.chart)
            first_three_items = chart.chart[:3]

            self._logger.info(f"- {ddb_id}: {num_items} items: {first_three_items}")

    def _get_distinct_typenames(self) -> List[str]:
        query = """
            SELECT DISTINCT thing_typename FROM metadata
            WHERE thing_custom_active_state = true
        """
        try:
            typenames = self._athena_client.query_athena(query, simplify=True)
        except Exception as e:
            self._logger.error("Issue with getting distinct typenames", e)
            typenames = []
        return typenames

    def _build_fallback_rfy_chart(self, typename: Optional[str] = None) -> ChartDDB:
        chart_name = f"fallback-rfy-{typename.lower()}" if typename else "fallback-rfy"
        self._logger.info(f"Creating {chart_name} chart")
        fallback_start = time.time()

        # extra charts are built to ensure a 180 day result set in the case of low results
        query_30d = self._get_most_popular_chart_query_by_play(days=30,
                                                               typename=typename)
        chart_name_30d = (
            f"extra-most-popular-{typename.lower()}-30d-play" if typename else "extra-most-popular-30d-play"
        )
        query_180d = self._get_most_popular_chart_query_by_play(days=180,
                                                                typename=typename)
        chart_name_180d = (
            f"extra-most-popular-{typename.lower()}-180d-play" if typename else "extra-most-popular-180d-play"
        )
        chart_30d = self.build_chart_with_timing(chart_name_30d, query_30d,
                                                 persist=False)
        chart_180d = self.build_chart_with_timing(chart_name_180d, query_180d,
                                                  persist=False)

        top_30d = chart_30d.chart[:10]
        top_180d = chart_180d.chart[:10]
        top_30d_ids = [row[0] for row in top_30d]
        top10_180d_unique = [r for r in top_180d if r[0] not in top_30d_ids]

        remainder_30d = chart_30d.chart[10:]
        remainder_180d = chart_180d.chart[10:]

        interleaved = []
        seen_ids = set(x[0] for x in (top10_180d_unique + top_30d))
        for a, b in zip_longest(remainder_30d, remainder_180d):
            if a and a[0] not in seen_ids:
                interleaved.append(a)
                seen_ids.add(a[0])
            if b and b[0] not in seen_ids:
                interleaved.append(b)
                seen_ids.add(b[0])

        final_chart = top10_180d_unique + interleaved
        final_chart = final_chart[:self._chart_size]

        fallback_chart_ddb = ChartDDB(
            ddbId=chart_name,
            created=self._now.isoformat(timespec='seconds'),
            timeToLiveEpoch=self._ttl_epoch,
            chart=final_chart
        )

        fallback_elapsed = (time.time() - fallback_start) * 1000
        self._chart_build_times[fallback_chart_ddb.ddbId] = fallback_elapsed
        self._logger.info(f"Built {chart_name} chart with {len(final_chart)} items.")

        return fallback_chart_ddb

    def _build_fallback_rfy_kids_chart(self) -> ChartDDB:
        self._logger.info("Creating fallback-rfy-kids chart for RTVE")
        fallback_start = time.time()
        # extra charts are built to ensure a 180 day result set in the case of low results
        query_30d = self._get_most_popular_chart_query_by_play(days=30)
        query_30d_kids = query_30d.replace(
            "AND thing_custom_active_state = TRUE",
            "AND thing_custom_active_state = TRUE AND contains(m.thing_genre_name, 'Infantil')",
            1
        )
        chart_30d = self.build_chart_with_timing("extra-most-popular-30d-play-kids",
                                                 query_30d_kids, persist=False)
        query_180d = self._get_most_popular_chart_query_by_play(days=180)
        query_180d_kids = query_180d.replace(
            "AND thing_custom_active_state = TRUE",
            "AND thing_custom_active_state = TRUE AND contains(m.thing_genre_name, 'Infantil')",
            1
        )
        chart_180d = self.build_chart_with_timing("extra-most-popular-180d-play-kids",
                                                  query_180d_kids, persist=False)
        top_30d = chart_30d.chart[:10]
        top_180d = chart_180d.chart[:10]
        top_30d_ids = [row[0] for row in top_30d]
        top10_180d_unique = [r for r in top_180d if r[0] not in top_30d_ids]
        remainder_30d = chart_30d.chart[10:]
        remainder_180d = chart_180d.chart[10:]
        interleaved = []
        seen_ids = set(x[0] for x in (top10_180d_unique + top_30d))
        for a, b in zip_longest(remainder_30d, remainder_180d):
            if a and a[0] not in seen_ids:
                interleaved.append(a)
                seen_ids.add(a[0])
            if b and b[0] not in seen_ids:
                interleaved.append(b)
                seen_ids.add(b[0])
        final_chart = top10_180d_unique + interleaved
        final_chart = final_chart[:self._chart_size]
        fallback_chart_ddb = ChartDDB(
            ddbId="fallback-rfy-kids",
            created=self._now.isoformat(timespec='seconds'),
            timeToLiveEpoch=self._ttl_epoch,
            chart=final_chart
        )
        fallback_elapsed = (time.time() - fallback_start) * 1000
        self._chart_build_times[fallback_chart_ddb.ddbId] = fallback_elapsed

        self._logger.info(
            f"Built fallback-rfy-kids chart with {len(final_chart)} items.")
        return fallback_chart_ddb

    def create_fallback_rfy_audioseries_chart(self, charts: List[ChartDDB]) -> List[
        ChartDDB]:
        """
        Conditionally generate the 'fallback-rfy-audioseries' chart if 'AudioSeries' assets exist.
        """
        if self.should_generate_audioseries_chart():
            self._logger.info(f"Generating 'fallback-rfy-audioseries' chart")
            fallback_rfy_audioseries = self._build_fallback_rfy_chart("AudioSeries")
            charts.append(fallback_rfy_audioseries)
        else:
            self._logger.info(f"Skipping 'fallback-rfy-audioseries' chart "
                              f"as no 'AudioSeries' assets exist.")

        return charts

    def should_generate_audioseries_chart(self) -> bool:
        """
        Check if the customer has assets with typename 'AudioSeries'.
        :return: True if 'AudioSeries' assets exist, False otherwise.
        """
        query = f"""
            SELECT COUNT(*) as count
            FROM metadata
            WHERE thing_typename = 'AudioSeries'
            AND thing_custom_active_state = true 
            {self._hidden_expression}
        """
        result = self._athena_client.query_athena(query, simplify=True)[0]
        return result > 0
