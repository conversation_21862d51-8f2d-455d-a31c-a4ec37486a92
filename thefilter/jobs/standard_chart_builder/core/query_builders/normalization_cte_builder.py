from typing import Dict, Any
from .base_query_component import BaseQueryComponent
from ..chart_configuration import ChartConfiguration


class NormalizationCTEBuilder(BaseQueryComponent):
    """Builds the normalization CTE that applies percentile ranking (1.0-2.0 scale)."""

    def __init__(self, config: Dict[str, Any] = None, chart_config: ChartConfiguration = None):
        super().__init__(config)
        self.chart_config = chart_config

    def build(self) -> str:
        """Build the normalization CTE."""
        if not self.chart_config:
            raise ValueError("ChartConfiguration is required")

        trending_fields = self._build_trending_fields()
        availability_fields = self._build_availability_fields()
        normalization_calculations = self._build_normalization_calculations()
        availability_join = self._build_availability_join()

        return f"""
            -- Add normalisation CTE using percentile ranks so that we represent the chart positions on a scale between
            -- 0 and 2, where 0 is the lowest and 2 is the highest. In reality, we will only ever see values in here
            -- between 1 and 2. This is good as we use the normalised score as a multiplier for the chart position,
            -- and in that context it's easier to reason about the resultant scores if it's purely an additive factor.
            normalised_metrics AS (
                SELECT
                    o.*,
                    {trending_fields}
                    {availability_fields}
                    {normalization_calculations}
                FROM overall o
                JOIN trending_metrics tm ON o.thing_brandid = tm.thing_brandid
                {availability_join}
            )"""
    
    def _build_trending_fields(self) -> str:
        """Build trending fields from configuration."""
        fields = []

        for variant in self.chart_config.trending_variants:
            fields.append(f"tm.{variant.slope_metric}")

        if fields:
            return ',\n                    '.join(fields) + ',\n                    '
        return ''

    def _build_normalization_calculations(self) -> str:
        """Build all normalization calculations from configuration."""
        calculations = []

        # Trending slope normalizations
        for variant in self.chart_config.trending_variants:
            calc = self._build_trending_normalization(variant.slope_metric)
            calculations.append(calc)

        # Play count normalizations
        for variant in self.chart_config.popularity_variants:
            calc = self._build_percentile_normalization(variant.play_metric)
            calculations.append(calc)

        # User count normalizations
        for variant in self.chart_config.popularity_variants:
            calc = self._build_percentile_normalization(variant.user_metric)
            calculations.append(calc)

        return ',\n                    '.join(calculations)

    def _build_availability_fields(self) -> str:
        """Build availability fields from configuration."""
        fields = []

        if self.chart_config.enable_recency:
            fields.append(f"ar.{self.chart_config.recency_metric}")

        if self.chart_config.enable_leaving_soon:
            fields.append(f"als.{self.chart_config.leaving_soon_metric}")
            fields.append(f"als.{self.chart_config.leaving_on_date_metric}")

        if fields:
            return ',\n                    '.join(fields) + ',\n                    '
        return ''

    def _build_availability_join(self) -> str:
        """Build availability JOIN clause if needed."""
        joins = []

        if self.chart_config.enable_recency:
            joins.append("""LEFT JOIN availability_recency AS ar
                    ON ar.thing_brandid = o.thing_brandid""")

        if self.chart_config.enable_leaving_soon:
            joins.append("""LEFT JOIN availability_leaving_soon AS als
                    ON als.thing_brandid = o.thing_brandid""")

        return '\n                '.join(joins)
    
    def _build_trending_normalization(self, metric: str) -> str:
        """Build normalization for trending slope metrics."""
        return f"""CASE
                        WHEN tm.{metric} < 0
                             OR max(tm.{metric}) OVER () = 0 THEN 1.0
                        ELSE 1.0
                             + CAST(tm.{metric} AS DOUBLE)
                             / CAST(max(tm.{metric}) OVER () AS DOUBLE)
                    END AS {metric}_normalised"""
    
    def _build_percentile_normalization(self, metric: str) -> str:
        """Build percentile-based normalization for count metrics."""
        return f"""CASE
                        WHEN o.{metric} = 0 THEN 1.0
                        ELSE 1.0 + CAST(PERCENT_RANK() OVER (ORDER BY o.{metric}) AS DOUBLE)
                    END AS {metric}_normalised"""