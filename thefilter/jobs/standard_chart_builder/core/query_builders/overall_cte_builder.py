from typing import List, Dict, Any
from .base_query_component import Base<PERSON>ueryComponent
from ..chart_configuration import ChartConfiguration


class OverallCTEBuilder(BaseQueryComponent):
    """Builds the overall CTE that combines metadata with popularity metrics."""

    def __init__(self, config: Dict[str, Any] = None, chart_config: ChartConfiguration = None):
        super().__init__(config)
        self.chart_config = chart_config

    def build(self) -> str:
        """Build the overall CTE."""
        if not self.chart_config:
            raise ValueError("ChartConfiguration is required")

        typename_expression = self._build_typename_expression()
        hidden_expression = self.chart_config.hidden_expression
        
        # Build SELECT fields and JOINs from configuration
        select_fields = self._build_select_fields()
        join_clauses = self._build_join_clauses()
        primary_sort_field = self._get_primary_sort_field()

        return f"""
            -- Combine all metrics for the top content
            overall AS (
                SELECT
                    m.thing_brandid,
                    MAX(m.thing_typename) AS thing_typename,
                    MAX(m.thing_datepublished) AS thing_datepublished,
                    {select_fields}
                FROM
                    metadata AS m
                {join_clauses}
                WHERE
                    m.thing_brandid is not null
                    AND thing_custom_active_state = TRUE
                    {hidden_expression}
                    {typename_expression}
                GROUP BY
                    m.thing_brandid
                ORDER BY
                    CASE
                        WHEN {primary_sort_field} > 0 THEN 0
                        ELSE 1
                    END,
                    {primary_sort_field} DESC,
                    MAX(m.thing_datepublished) DESC
                LIMIT {self.chart_config.chart_size}
            )"""
    
    def _build_select_fields(self) -> str:
        """Build SELECT fields from configuration."""
        fields = []

        for variant in self.chart_config.popularity_variants:
            fields.append(f"COALESCE(SUM({variant.alias}.{variant.play_field}), 0) AS {variant.play_metric}")
            fields.append(f"COALESCE(SUM({variant.alias}.{variant.user_field}), 0) AS {variant.user_metric}")

        return ',\n                    '.join(fields)

    def _build_join_clauses(self) -> str:
        """Build LEFT JOIN clauses from configuration."""
        joins = []

        for variant in self.chart_config.popularity_variants:
            join = f"""LEFT JOIN {variant.cte_name} AS {variant.alias}
                    ON {variant.alias}.brand_id = m.thing_id"""
            joins.append(join)

        return '\n                '.join(joins)

    def _get_primary_sort_field(self) -> str:
        """Get the primary field for sorting (typically the longest time period)."""
        if self.chart_config.popularity_variants:
            # Use the first variant's play metric as primary sort
            return self.chart_config.popularity_variants[0].play_metric
        return "play_30d"  # fallback

    def _build_typename_expression(self) -> str:
        """Build the typename filter expression."""
        included_typenames = self.chart_config.included_typenames

        if not included_typenames:
            return "AND m.thing_typename IN ('TVSeries', 'Movie')"
        elif len(included_typenames) == 1:
            return f"AND m.thing_typename = '{included_typenames[0]}'"
        else:
            parts = ', '.join([f"'{typename}'" for typename in included_typenames])
            return f"AND m.thing_typename IN ({parts})"