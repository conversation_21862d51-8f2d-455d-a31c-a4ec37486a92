"""
Availability CTE Builder for dynamic chart queries.

This module builds CTEs related to content availability and datetime-based scoring,
including recency scores and future availability-related metrics.
"""

from typing import Dict, Any
from .base_query_component import BaseQueryComponent
from ..chart_configuration import ChartConfiguration


class AvailabilityCTEBuilder(BaseQueryComponent):
    """Builds availability and datetime-related CTEs for dynamic charts."""
    
    def __init__(self, config: Dict[str, Any] = None, chart_config: ChartConfiguration = None):
        super().__init__(config)
        self.chart_config = chart_config
    
    def build(self) -> str:
        """Build the availability CTE with recency and other datetime-based metrics."""
        if not self.chart_config:
            raise ValueError("ChartConfiguration is required")
        
        cte_components = []

        if self.chart_config.enable_recency:
            recency_cte = self._build_recency_cte()
            cte_components.append(recency_cte)

        if self.chart_config.enable_leaving_soon:
            leaving_soon_cte = self._build_leaving_soon_cte()
            cte_components.append(leaving_soon_cte)

        if not cte_components:
            return ""

        return ',\n'.join(cte_components)
    
    def _build_recency_cte(self) -> str:
        return f"""
            -- Recency scoring based on content publication date
            availability_recency AS (
                SELECT
                    m.thing_brandid,
                    -- Recency score: newer content gets higher scores
                    -- Uses exponential decay based on days since publication
                    CASE
                        WHEN m.thing_datepublished IS NULL THEN 1.0
                        WHEN DATE_DIFF('day', CAST(FROM_ISO8601_TIMESTAMP(m.thing_datepublished) AS DATE), CURRENT_DATE) <= 0 THEN 2.0
                        WHEN DATE_DIFF('day', CAST(FROM_ISO8601_TIMESTAMP(m.thing_datepublished) AS DATE), CURRENT_DATE) <= 30 THEN
                            1.0 + EXP(-0.1 * DATE_DIFF('day', CAST(FROM_ISO8601_TIMESTAMP(m.thing_datepublished) AS DATE), CURRENT_DATE))
                        WHEN DATE_DIFF('day', CAST(FROM_ISO8601_TIMESTAMP(m.thing_datepublished) AS DATE), CURRENT_DATE) <= 365 THEN
                            1.0 + 0.5 * EXP(-0.01 * DATE_DIFF('day', CAST(FROM_ISO8601_TIMESTAMP(m.thing_datepublished) AS DATE), CURRENT_DATE))
                        ELSE 1.0
                    END AS {self.chart_config.recency_metric}
                FROM metadata AS m
                WHERE m.thing_brandid IS NOT NULL
                    -- VERY necessary for deduplication when we have a tv series situation
                    AND m.thing_id = m.thing_brandid
                    AND thing_custom_active_state = TRUE
            )"""

    def _build_leaving_soon_cte(self) -> str:
        """Build leaving soon CTE that scores content based on proximity to end date."""
        window_days = self.chart_config.leaving_soon_window_days

        return f"""
            -- Leaving soon scoring based on content end date
            availability_leaving_soon AS (
                SELECT
                    m.thing_brandid,
                    -- Leaving soon score: content expiring sooner gets higher scores
                    -- Uses exponential scoring within the configured window
                    CASE
                        WHEN m.thing_publication_enddate IS NULL THEN 1.0
                        WHEN CAST(SUBSTRING(ARRAY_MIN(m.thing_publication_enddate), 1, 10) AS DATE) <= CURRENT_DATE THEN 1.0
                        WHEN DATE_DIFF('day', CURRENT_DATE, CAST(SUBSTRING(ARRAY_MIN(m.thing_publication_enddate), 1, 10) AS DATE)) > {window_days} THEN 1.0
                        ELSE
                            -- Exponential scoring: content expiring today gets 2.0, content at window edge gets ~1.0
                            1.0 + EXP(-0.5 * DATE_DIFF('day', CURRENT_DATE, CAST(SUBSTRING(ARRAY_MIN(m.thing_publication_enddate), 1, 10) AS DATE)) / {window_days})
                    END AS {self.chart_config.leaving_soon_metric},
                    -- Include the actual leaving date for reference
                    ARRAY_MIN(m.thing_publication_enddate) AS {self.chart_config.leaving_on_date_metric}
                FROM metadata AS m
                WHERE m.thing_brandid IS NOT NULL
                    -- VERY necessary for deduplication when we have a tv series situation
                    AND m.thing_id = m.thing_brandid
                    AND thing_custom_active_state = TRUE
            )"""
    
    def get_join_clause(self) -> str:
        joins = []

        if self.chart_config and self.chart_config.enable_recency:
            joins.append("""LEFT JOIN availability_recency AS ar
                    ON ar.thing_brandid = o.thing_brandid""")

        if self.chart_config and self.chart_config.enable_leaving_soon:
            joins.append("""LEFT JOIN availability_leaving_soon AS als
                    ON als.thing_brandid = o.thing_brandid""")

        return '\n                '.join(joins)
    
    def get_select_fields(self) -> list:
        """Get the fields that should be included in the final SELECT."""
        fields = []

        if self.chart_config and self.chart_config.enable_recency:
            fields.append(f"ar.{self.chart_config.recency_metric}")

        if self.chart_config and self.chart_config.enable_leaving_soon:
            fields.append(f"als.{self.chart_config.leaving_soon_metric}")
            fields.append(f"als.{self.chart_config.leaving_on_date_metric}")

        return fields
    
    def has_metrics(self) -> bool:
        """Check if this builder will generate any metrics."""
        if not self.chart_config:
            return False

        return self.chart_config.enable_recency or self.chart_config.enable_leaving_soon
