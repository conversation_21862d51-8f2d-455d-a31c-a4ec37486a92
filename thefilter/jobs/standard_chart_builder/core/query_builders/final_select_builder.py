from typing import Dict, Any
from .base_query_component import BaseQueryComponent
from ..chart_configuration import ChartConfiguration


class FinalSelectBuilder(BaseQueryComponent):
    """Builds the final SELECT statement for the dynamic chart query."""
    
    def __init__(self, config: Dict[str, Any] = None, chart_config: ChartConfiguration = None):
        super().__init__(config)
        self.chart_config = chart_config
    
    def build(self) -> str:
        """Build the final SELECT statement."""
        if not self.chart_config:
            raise ValueError("ChartConfiguration is required")
            
        # Build the dynamic fields based on configuration
        metrics_fields = self._build_metrics_fields()
        metadata_fields_select = self._build_metadata_fields_select()
        
        return f"""
            SELECT 
                metadata.thing_id,
                metadata.thing_brandid,
                metadata.thing_name,
                metadata.thing_typename,
                metadata.thing_datepublished,
                {metrics_fields}
                {metadata_fields_select}
            FROM normalised_metrics AS nm
            JOIN metadata ON nm.thing_brandid = metadata.thing_id"""
    
    def _build_metrics_fields(self) -> str:
        """Build the metrics fields selection based on configuration."""
        fields = []
        
        # Add all metrics from the configuration
        for metric in self.chart_config.all_metrics_for_output:
            fields.append(f"nm.{metric}")
        
        return ",\n                ".join(fields)
    
    def _build_metadata_fields_select(self) -> str:
        """Build the metadata fields selection."""
        # Use metadata fields from chart_config if available, otherwise fall back to config
        metadata_fields = self.chart_config.metadata_fields if self.chart_config else self.config.get('metadata_fields', [])
        if not metadata_fields:
            return ""
        
        fields = []
        for field in metadata_fields:
            fields.append(f"metadata.{field}")
        
        return ",\n                " + ",\n                ".join(fields)