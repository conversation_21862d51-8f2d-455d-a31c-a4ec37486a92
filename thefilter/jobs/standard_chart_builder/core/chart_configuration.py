"""
Centralised configuration for dynamic chart builders.

This module provides a single source of truth for all chart builder components,
ensuring consistency across popularity CTEs, trending CTEs, normalisation, and output.
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional


@dataclass
class PopularityVariant:
    """Configuration for a popularity metric variant."""
    name: str                    # e.g., "30d", "6h"
    time_value: int             # e.g., 30, 6
    time_unit: str              # "day" or "hour"
    use_datepartition: bool     # True for day-based, False for hour-based
    partition_safety_days: int  # Extra days to include in partition filter
    
    @property
    def cte_name(self) -> str:
        """Generate CTE name: popular_events_30d"""
        return f"popular_events_{self.name}"
    
    @property
    def alias(self) -> str:
        """Generate table alias: p30"""
        return f"p{self.name}"
    
    @property
    def play_field(self) -> str:
        """Generate play count field: plays_30d"""
        return f"plays_{self.name}"
    
    @property
    def user_field(self) -> str:
        """Generate user count field: distinct_users_30d"""
        return f"distinct_users_{self.name}"
    
    @property
    def play_metric(self) -> str:
        """Generate play metric name: play_30d"""
        return f"play_{self.name}"
    
    @property
    def user_metric(self) -> str:
        """Generate user metric name: user_30d"""
        return f"user_{self.name}"


@dataclass
class TrendingVariant:
    """Configuration for a trending metric variant."""
    name: str                    # e.g., "recent_trending", "daily_trending"
    description: str            # Human-readable description
    recent_hours: int           # Hours for recent window
    previous_hours: int         # Hours for previous window
    partition_safety_days: int  # Extra days to include in partition filter
    
    @property
    def slope_metric(self) -> str:
        """Generate slope metric name: recent_trending_play_slope"""
        return f"{self.name}_play_slope"
    
    @property
    def normalised_metric(self) -> str:
        """Generate normalised metric name: recent_trending_play_slope_normalised"""
        return f"{self.name}_play_slope_normalised"


@dataclass
class ChartConfiguration:
    """Centralised configuration for all chart builder components."""

    # Chart settings
    chart_size: int
    included_typenames: List[str]
    metadata_fields: List[str]
    hidden_expression: str

    # Popularity variants - order matters for SQL generation
    popularity_variants: List[PopularityVariant]

    # Trending variants - order matters for SQL generation
    trending_variants: List[TrendingVariant]

    # Recency settings
    enable_recency: bool
    recency_metric: str

    # Leaving soon settings
    enable_leaving_soon: bool
    leaving_soon_metric: str
    leaving_soon_window_days: int
    
    def get_popularity_variant(self, name: str) -> Optional[PopularityVariant]:
        """Get a popularity variant by name."""
        return next((v for v in self.popularity_variants if v.name == name), None)
    
    def get_trending_variant(self, name: str) -> Optional[TrendingVariant]:
        """Get a trending variant by name."""
        return next((v for v in self.trending_variants if v.name == name), None)
    
    @property
    def all_play_metrics(self) -> List[str]:
        """Get all play metric names for normalisation."""
        return [variant.play_metric for variant in self.popularity_variants]
    
    @property
    def all_user_metrics(self) -> List[str]:
        """Get all user metric names for normalisation."""
        return [variant.user_metric for variant in self.popularity_variants]
    
    @property
    def all_trending_slope_metrics(self) -> List[str]:
        """Get all trending slope metric names for normalisation."""
        return [variant.slope_metric for variant in self.trending_variants]
    
    @property
    def all_metrics_for_output(self) -> List[str]:
        """Get all metric names that should be included in final output."""
        metrics = []
        
        # Add popularity metrics (raw and normalized)
        for variant in self.popularity_variants:
            metrics.extend([
                variant.play_metric,
                variant.user_metric,
                f"{variant.play_metric}_normalised",
                f"{variant.user_metric}_normalised"
            ])
        
        # Add trending metrics (raw and normalized)
        for variant in self.trending_variants:
            metrics.extend([
                variant.slope_metric,
                variant.normalised_metric
            ])
        
        # Add recency if enabled
        if self.enable_recency:
            metrics.append(self.recency_metric)

        # Add leaving soon if enabled
        if self.enable_leaving_soon:
            metrics.append(self.leaving_soon_metric)

        return metrics
    
    def validate(self) -> List[str]:
        errors = []
        
        # Check for duplicate variant names
        popularity_names = [v.name for v in self.popularity_variants]
        if len(popularity_names) != len(set(popularity_names)):
            errors.append("Duplicate popularity variant names found")
        
        trending_names = [v.name for v in self.trending_variants]
        if len(trending_names) != len(set(trending_names)):
            errors.append("Duplicate trending variant names found")
        
        # Check chart size
        if self.chart_size <= 0:
            errors.append("Chart size must be positive")
        
        # Check time values
        for variant in self.popularity_variants:
            if variant.time_value <= 0:
                errors.append(f"Invalid time value for {variant.name}: {variant.time_value}")
            if variant.time_unit not in ["day", "hour"]:
                errors.append(f"Invalid time unit for {variant.name}: {variant.time_unit}")
        
        for variant in self.trending_variants:
            if variant.recent_hours <= 0 or variant.previous_hours <= 0:
                errors.append(f"Invalid hours for {variant.name}")

        # Check leaving soon settings
        if self.enable_leaving_soon:
            if self.leaving_soon_window_days <= 0:
                errors.append("Leaving soon window days must be positive")
            if not self.leaving_soon_metric:
                errors.append("Leaving soon metric name cannot be empty")

        return errors


def create_default_config() -> ChartConfiguration:
    return ChartConfiguration(
        chart_size=100,
        included_typenames=['TVSeries', 'Movie'],
        metadata_fields=[],
        hidden_expression="",
        popularity_variants=[
            PopularityVariant("30d", 30, "day", True, 1),
            PopularityVariant("14d", 14, "day", True, 1),
            PopularityVariant("7d", 7, "day", True, 1),
            PopularityVariant("1d", 1, "day", True, 1),
            PopularityVariant("6h", 6, "hour", False, 1),
        ],
        trending_variants=[
            TrendingVariant("recent_trending", "Recent 3-hour windows", 3, 6, 1),
            TrendingVariant("daily_trending", "Daily 24-hour windows", 24, 48, 3),
            TrendingVariant("weekly_trending", "Weekly 7-day windows", 168, 336, 15),
            TrendingVariant("weekly_comparison_trending", "Week-over-week comparison", 24, 192, 9),
        ],
        enable_recency=True,
        recency_metric="recency_score",
        enable_leaving_soon=False,
        leaving_soon_metric="leaving_soon_score",
        leaving_soon_window_days=7
    )


def create_custom_config(**overrides) -> ChartConfiguration:
    config = create_default_config()

    for key, value in overrides.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown configuration key: {key}")

    errors = config.validate()
    if errors:
        raise ValueError(f"Configuration validation failed: {errors}")

    return config


def create_customer_config(
    chart_size: int = None,
    included_typenames: List[str] = None,
    additional_popularity_variants: List[PopularityVariant] = None,
    additional_trending_variants: List[TrendingVariant] = None,
    **other_overrides
) -> ChartConfiguration:

    # Start with default
    config = create_default_config()

    # Apply overrides
    if chart_size is not None:
        config.chart_size = chart_size
    if included_typenames is not None:
        config.included_typenames = included_typenames

    for key, value in other_overrides.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            raise ValueError(f"Unknown configuration key: {key}")

    # Add additional variants if provided
    if additional_popularity_variants:
        config.popularity_variants.extend(additional_popularity_variants)

    if additional_trending_variants:
        config.trending_variants.extend(additional_trending_variants)

    errors = config.validate()
    if errors:
        raise ValueError(f"Customer configuration validation failed: {errors}")

    return config


def create_config_from_json(json_config: Dict[str, Any]) -> ChartConfiguration:
    if not json_config.get('enabled', False):
        raise ValueError("Dynamic charts are not enabled in configuration")

    # Start with default configuration
    config = create_default_config()

    # Apply JSON overrides with proper defaults
    config.chart_size = json_config.get('chart_size', config.chart_size)
    config.included_typenames = json_config.get('included_typenames', config.included_typenames)
    config.metadata_fields = json_config.get('metadata_fields', config.metadata_fields)
    config.hidden_expression = json_config.get('hidden_expression', config.hidden_expression)
    config.enable_recency = json_config.get('enable_recency', config.enable_recency)
    config.recency_metric = json_config.get('recency_metric', config.recency_metric)
    config.enable_leaving_soon = json_config.get('enable_leaving_soon', config.enable_leaving_soon)
    config.leaving_soon_metric = json_config.get('leaving_soon_metric', config.leaving_soon_metric)
    config.leaving_soon_window_days = json_config.get('leaving_soon_window_days', config.leaving_soon_window_days)

    # Handle custom popularity variants if provided
    if 'popularity_variants' in json_config:
        custom_variants = []
        for variant_data in json_config['popularity_variants']:
            variant = PopularityVariant(
                name=variant_data['name'],
                time_value=variant_data['time_value'],
                time_unit=variant_data['time_unit'],
                use_datepartition=variant_data.get('use_datepartition', True),
                partition_safety_days=variant_data.get('partition_safety_days', 1)
            )
            custom_variants.append(variant)

        # Replace or extend default variants based on configuration
        if json_config.get('replace_default_popularity_variants', False):
            config.popularity_variants = custom_variants
        else:
            config.popularity_variants.extend(custom_variants)

    # Handle custom trending variants if provided
    if 'trending_variants' in json_config:
        custom_variants = []
        for variant_data in json_config['trending_variants']:
            variant = TrendingVariant(
                name=variant_data['name'],
                description=variant_data['description'],
                recent_hours=variant_data['recent_hours'],
                previous_hours=variant_data['previous_hours'],
                partition_safety_days=variant_data.get('partition_safety_days', 1)
            )
            custom_variants.append(variant)

        # Replace or extend default variants based on configuration
        if json_config.get('replace_default_trending_variants', False):
            config.trending_variants = custom_variants
        else:
            config.trending_variants.extend(custom_variants)

    # Validate the final configuration
    errors = config.validate()
    if errors:
        raise ValueError(f"JSON configuration validation failed: {errors}")

    return config


def load_chart_config_from_centralised_config(centralised_config: Dict[str, Any]) -> ChartConfiguration:
    dynamic_chart_config = centralised_config.get('dynamic_chart_config', {})

    if not dynamic_chart_config:
        raise ValueError("dynamic_chart_config section not found in centralised configuration")

    return create_config_from_json(dynamic_chart_config)
