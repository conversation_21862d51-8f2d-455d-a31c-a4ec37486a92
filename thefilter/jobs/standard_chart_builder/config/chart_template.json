{"aliases": {}, "index_patterns": ["chart_*"], "order": 0, "settings": {"index": {"number_of_shards": "3", "refresh_interval": "1m", "number_of_replicas": "1", "analysis": {"normalizer": {"lowercase_normalizer": {"type": "custom", "char_filter": [], "filter": "lowercase"}}}}}, "mappings": {"_doc": {"dynamic_templates": [{"strings_as_lowercase_keywords": {"match_mapping_type": "string", "mapping": {"type": "keyword", "normalizer": "lowercase_normalizer"}}}], "properties": {"thing_id": {"type": "keyword"}, "thing_brandid": {"type": "keyword"}, "thing_name": {"type": "text", "index": false, "store": true}, "thing_typename": {"type": "keyword"}, "metrics": {"properties": {"play_30d": {"type": "double"}, "play_14d": {"type": "double"}, "play_7d": {"type": "double"}, "play_1d": {"type": "double"}, "user_30d": {"type": "double"}, "user_14d": {"type": "double"}, "user_7d": {"type": "double"}, "user_1d": {"type": "double"}, "play_30d_normalised": {"type": "double"}, "play_14d_normalised": {"type": "double"}, "play_7d_normalised": {"type": "double"}, "play_1d_normalised": {"type": "double"}, "user_30d_normalised": {"type": "double"}, "user_14d_normalised": {"type": "double"}, "user_7d_normalised": {"type": "double"}, "user_1d_normalised": {"type": "double"}, "date_published": {"type": "date", "format": "yyyy-MM-dd||yyyy-MM-dd'T'HH:mm:ss.SSSZ||strict_date_optional_time"}, "recency_score": {"type": "double"}}}, "filters": {"type": "object", "dynamic": true, "properties": {"leaving_on": {"type": "date", "format": "yyyy-MM-dd||yyyy-MM-dd'T'HH:mm:ss.SSSZ||strict_date_optional_time"}}}}}}}