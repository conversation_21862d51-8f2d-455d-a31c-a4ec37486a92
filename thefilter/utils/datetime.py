import abc
import math
import re
from copy import deepcopy
from datetime import datetime, timezone, timedelta
from typing import Op<PERSON>, Tu<PERSON>, List

from dateutil.relativedelta import relativedelta


class ISODatetime:
    @classmethod
    def convert_to_python_iso(cls, iso_datetime: str) -> str:
        """Formats an iso compliant datetime string for consumption by the fromisoformat
        method of datetime.
        """
        iso_datetime = iso_datetime.replace('Z', '+00:00')
        iso_datetime = re.sub(r'\+(\d\d)(\d\d)', r'+\g<1>:<2>', iso_datetime)
        iso_datetime = re.sub(r'\.\d*', '', iso_datetime)
        return iso_datetime

    @classmethod
    def convert_to_datetime(cls, iso_datetime: str) -> datetime:
        iso_datetime = ISODatetime.convert_to_python_iso(iso_datetime)
        return datetime.fromisoformat(iso_datetime)


class BaseDateTimeProvider(abc.ABC):
    @abc.abstractmethod
    def utc_now(self) -> datetime:
        raise NotImplementedError()


class DateTimeProvider(BaseDateTimeProvider):
    def utc_now(self) -> datetime:
        return datetime.now(timezone.utc)


class StubDateTimeProvider(BaseDateTimeProvider):
    def __init__(self, to_return: datetime):
        self._to_return = to_return

    def utc_now(self) -> datetime:
        return self._to_return


class EpochUtils:
    @staticmethod
    def date_time_str_to_epoch(date_time_str: str) -> float:
        """
        Converts datetime strings into epoch time, mainly for DDB
        No truncating of ms,
        TODO: round3 to 3dp
        """
        # 2021-06-17T00:15:01+00:00
        expr = '\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\+00:00'
        date_time = re.findall(expr, date_time_str)
        if date_time:
            date_time_obj = datetime.strptime(date_time_str, '%Y-%m-%dT%H:%M:%S%z')
            epoch_ms = date_time_obj.timestamp()
            return epoch_ms

        # "2022-08-01T03:40:08.673+00:00"
        expr = '\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+00:00'
        date_time = re.findall(expr, date_time_str)
        if date_time:
            date_time_obj = datetime.strptime(date_time_str,
                                              '%Y-%m-%dT%H:%M:%S.%f%z')
            epoch_ms = date_time_obj.timestamp()
            return epoch_ms

        # 2022-07-11T11:00:00
        expr = '\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}'
        date_time = re.findall(expr, date_time_str)
        if date_time:
            date_time_obj = datetime.strptime(date_time_str, '%Y-%m-%dT%H:%M:%S')
            epoch_ms = date_time_obj.astimezone(timezone.utc).timestamp()
            return epoch_ms

    @staticmethod
    def epoch_to_formatted_datetime(epoch: int) -> str:
        """
        Converts an epoch time to a TF formatted datetime string in UTC:
        1670021700000 -> "2022-12-02T22:55:00+00:00"
        """
        if len(str(epoch)) == 13:
            epoch = int(str(epoch)[:-5] + "00")  # because we don't need microseconds
        result = f"{datetime.fromtimestamp(epoch, tz=timezone.utc).strftime('%Y-%m-%dT%H:%M:%S')}+00:00"
        return result

    @staticmethod
    def generate_ttl_epoch(
            years=0, months=0, days=0, hours=0, minutes=0, seconds=0
    ) -> int:
        if years == 0 and months == 0 and days == 0 \
                and hours == 0 and minutes == 0 and seconds == 0:
            months = 18
        """
        Used to create TTL values for DDB records
        Standard to use is `months = 18`
        """
        now = datetime.now(tz=timezone.utc)
        ttl_date = now + relativedelta(
            years=years,
            months=months,
            days=days,
            hours=hours,
            minutes=minutes,
            seconds=seconds
        )
        ttl_epoch = round(math.floor(ttl_date.timestamp()), 6)
        return ttl_epoch

    @staticmethod
    def epoch_to_date_finder(string: str, format: Optional[str] = "%Y-%m-%d"):
        """
        Tries to find an epoch time in the sting, and then returns as datetime str

        epoch_to_date_finder("source/views_007/day_utc=1681862400")
        #>>> 2023-04-19

        """
        pattern = r"\d{10}"
        match = re.search(pattern, string)
        if match:
            epoch_value = int(match.group(0))

            # Convert epoch time to datetime object
            dt_object = datetime.fromtimestamp(epoch_value)

            # Format datetime object to a string in the desired format
            formatted_date = dt_object.strftime(format)
            return formatted_date

    @staticmethod
    def seconds_between_epochs(start: int, end: int) -> int:
        dt1 = datetime.fromtimestamp(start)
        dt2 = datetime.fromtimestamp(end)
        time_difference = dt2 - dt1
        seconds = time_difference.total_seconds()
        if round:
            seconds = round(seconds)
        return seconds

    @classmethod
    def now_epoch(cls) -> int:
        """
        `now` as an epoch
        """
        now = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S+00:00")
        now_epoch = int(cls.date_time_str_to_epoch(now))
        return now_epoch


class DateTimeUtil:
    @classmethod
    def now_tf_str(cls) -> str:
        """
        `now` as a TF formatted string, to the second granularity in UTC
        Example: 2023-05-18T01:02:03+00:00
        """
        now = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S+00:00")
        return now

    @classmethod
    def today_tf_str(cls) -> str:
        """
        `today` as a TF formatted string
        Example: 2023-05-18
        """
        today = datetime.now().strftime("%Y-%m-%d")
        return today

    @classmethod
    def get_current_date_formatted(cls) -> str:
        """
        current date in YYYYMMDDHHMM format, like 20250102000000
        """
        return datetime.now().strftime("%Y%m%d%H%M")
        
    @classmethod
    def get_current_datetime_with_seconds(cls) -> str:
        """
        current date and time in YYYYMMDD_HHMMSS format, like 20250102_000000
        Used for generating chronologically sortable timestamps for filenames and indexes.
        """
        return datetime.now().strftime("%Y%m%d_%H%M%S")

    @classmethod
    def add_duration_to_date(
            cls, date_string: str,
            years=0, months=0, days=0, hours=0, minutes=0, seconds=0, subtract=False,
            round: Optional[str] = None) -> str:
        """Can also subtract time with `subtract=True`"""

        date_format = "%Y-%m-%dT%H:%M:%S+00:00"
        date_object = datetime.strptime(date_string, date_format)
        updated_date = cls._add_duration_to_date_object(
            date_object, years, months, days, hours, minutes, seconds, subtract, round
        )

        updated_date_string = updated_date.strftime(date_format)
        return updated_date_string

    @classmethod
    def add_duration_to_short_date(
            cls, short_date_string: str, years=0, months=0, days=0, subtract=False
    ) -> str:
        """Can also subtract time with `subtract=True`"""

        date_format = "%Y-%m-%d"
        date_object = datetime.strptime(short_date_string, date_format)

        updated_date = cls._add_duration_to_date_object(
            date_object, years, months, days, 0, 0, 0, subtract
        )

        updated_date_string = updated_date.strftime(date_format)
        return updated_date_string

    @classmethod
    def add_duration_to_datetime(cls, date_obj: datetime,
                                 years=0, months=0, days=0, hours=0, minutes=0,
                                 seconds=0, subtract=False,
                                 round: Optional[str] = None) -> datetime:
        """Can also subtract time with `subtract=True`"""

        updated_date = cls._add_duration_to_date_object(
            date_obj, years, months, days, hours, minutes, seconds, subtract, round
        )

        return updated_date

    @staticmethod
    def _add_duration_to_date_object(date_object: datetime, years=0, months=0, days=0,
                                     hours=0, minutes=0, seconds=0, subtract=False,
                                     round: Optional[str] = None) -> datetime:
        duration = relativedelta(years=years, months=months, days=days, hours=hours,
                                 minutes=minutes, seconds=seconds)
        if subtract:
            duration *= -1

        updated_date = date_object + duration

        if round == "down":
            updated_date = updated_date.replace(microsecond=0, second=0, minute=0)
        elif round == "up":
            updated_date = updated_date.replace(microsecond=0, second=0, minute=0,
                                                hour=updated_date.hour + 1)

        return updated_date

    @staticmethod
    def duration_between_times(
            start: str,
            end: str,
            timestamp_format: Optional[str] = None,
            absolute: Optional[bool] = False,
            pretty_print: Optional[bool] = False
    ) -> float:
        """
        Calculates the difference in seconds between two timestamp strings.
        Defaults to timestamp_format = "%Y-%m-%dT%H:%M:%S%z"

        Example:
        start = '2022-07-11T15:20:19+00:00'
        end = '2022-07-11T15:21:29+00:00'

        diff_between_times(start, end)
        >>> 70.0

        The result will be negative if the end time occurs before the start time.

        if the `absolute` flag is True, then it can work both ways
        (it returns a positive difference between two times).
        start = '2023-07-11T15:20:19+00:00'
        end = '2021-07-11T15:21:29+00:00'
        diff_between_times(start, end, absolute=True)
        >>> 63071930
        """

        if not timestamp_format:
            timestamp_format = "%Y-%m-%dT%H:%M:%S%z"

        timestamp_start = datetime.strptime(start, timestamp_format)
        timestamp_end = datetime.strptime(end, timestamp_format)

        if absolute:
            duration = abs(timestamp_end - timestamp_start).total_seconds()
        else:
            duration = (timestamp_end - timestamp_start).total_seconds()

        if pretty_print:
            # Calculate hours, minutes, and seconds
            hours = int(duration // 3600)
            minutes = int((duration % 3600) // 60)
            seconds = int(duration % 60)

            # Print the result
            duration = f"{hours} hours, {minutes} minutes, {seconds} seconds"
        return duration

    @staticmethod
    def convert_seconds_to_hms(seconds: int) -> str:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds_remainder = seconds % 60

        return f"{hours} hours, {minutes} minutes, {seconds_remainder} seconds"

    @staticmethod
    def days_between_dates(min_date_str: str, max_date_str: str) -> int:
        timestamp_format = "%Y-%m-%dT%H:%M:%S%z"
        min_date = datetime.strptime(min_date_str, timestamp_format)
        max_date = datetime.strptime(max_date_str, timestamp_format)

        date_difference = max_date - min_date

        number_of_days = date_difference.days
        return number_of_days

    @staticmethod
    def is_timestamp_correct_format(timestamp: str) -> bool:
        """
        Returns a boolean if the provided timestamp is in the TF format of
        "YYYY-mm-DDTHH:MM:SS+hh:mm"
        strftime('%Y-%m-%dT%H:%M:%S%z')
        """
        result = False
        pattern = r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[\+\-]\d{2}:\d{2}"
        match = re.match(pattern, timestamp)

        if match:
            result = True

        return result

    @staticmethod
    def split_iso_8601_timestamp(timestamp: str) -> Tuple[str, str]:
        """
        Can be used to split an iso6801 timestamp into the UTC time and the timezone
        offset value.

        is_timestamp_correct_format should be used before calling this method
        """

        # Get the current UTC time
        timestamp_format = "%Y-%m-%dT%H:%M:%S%z"
        datetime_obj = datetime.strptime(timestamp, timestamp_format)

        return (datetime_obj.astimezone(timezone.utc).isoformat(), f"{timestamp[19:25]}")

    @staticmethod
    def str_time_to_date_time(timestamp: str) -> datetime:
        timestamp_format = "%Y-%m-%dT%H:%M:%S%z"
        date_time = datetime.strptime(timestamp, timestamp_format)
        return date_time

    @staticmethod
    def date_time_to_str(timestamp: datetime):
        return timestamp.strftime("%Y-%m-%dT%H:%M:%S+00:00")

    @staticmethod
    def short_date_to_date_time(timestamp: str) -> datetime:
        timestamp_format = "%Y-%m-%d"
        date_time = datetime.strptime(timestamp, timestamp_format)
        return date_time

    @staticmethod
    def date_time_to_short_date(timestamp: datetime) -> str:
        timestamp_format = "%Y-%m-%d"
        short_date = timestamp.strftime(timestamp_format)
        return short_date

    @staticmethod
    def convert_dmy_to_date_time_str(dmy_timestamp: str) -> str:
        dmy_timestamp_format = "%d-%m-%Y %H:%M:%S"
        date_time = datetime.strptime(dmy_timestamp, dmy_timestamp_format)
        date_time_str = date_time.strftime("%Y-%m-%dT%H:%M:%S+00:00")
        return date_time_str

    @staticmethod
    def convert_epoch_to_date_time_str(epoch: int) -> str:
        return datetime.fromtimestamp(epoch).strftime(
            "%Y-%m-%dT%H:%M:%S+00:00")

    @staticmethod
    def convert_epoch_ms_to_date_time_str(epoch_ms: int) -> str:
        return datetime.fromtimestamp(epoch_ms / 1000).strftime(
            "%Y-%m-%dT%H:%M:%S+00:00")

    @staticmethod
    def get_dates_between(start_date: str, end_date: str) -> List[str]:
        """
        Generate all dates between two dates in 'YYYY-MM-DD' format.

        :param start_date: Start date as a string in 'YYYY-MM-DD' format.
        :param end_date: End date as a string in 'YYYY-MM-DD' format.
        :return: List of dates as strings in 'YYYY-MM-DD' format.
        """
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        date_list = []

        # Support providing dates in any order
        if start >= end:
            new_end = start
            start = end
            end = new_end

        current_date = start
        while current_date <= end:
            date_list.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)

        return date_list

    @staticmethod
    def convert_seconds_to_min_and_sec(seconds: int) -> str:
        minutes = (seconds % 3600) // 60
        seconds_remainder = seconds % 60
        return f"{minutes}m {seconds_remainder}s"
        
    @staticmethod
    def parse_date_or_datetime(date_str) -> datetime:
        """
        Parses a date string in either 'YYYY-MM-DD' format or ISO format with 'T'.
        If the input is not a string, returns it as is.
        
        Args:
            date_str: A date string in 'YYYY-MM-DD' format, ISO format, or a datetime object
            
        Returns:
            A datetime object representing the input date
            
        Raises:
            ValueError: If the date string format is not supported
        """
        if not isinstance(date_str, str):
            return date_str

        try:
            return DateTimeUtil.short_date_to_date_time(date_str)
        except ValueError:
            try:
                if 'T' in date_str:
                    date_part = date_str.split('T')[0]
                    return DateTimeUtil.short_date_to_date_time(date_part)
                else:
                    raise ValueError(f"Unsupported date format: '{date_str}'. Expected format is 'YYYY-MM-DD'.")
            except ValueError:
                # If all parsing attempts fail, re-raise the error
                raise


class DurationUtils:
    @classmethod
    def convert_duration_to_minutes(cls, duration: str) -> int:
        """
        ISO 8601 duration format -> minutes
        "PT1H20M" -> 80
        """
        result = 0
        if duration is None:
            return result

        pattern = r'PT((\d+)D)?((\d+)H)?((\d+)M)?((\d+)S)?'
        match = re.match(pattern, duration)
        if match:
            day = cls._to_minute(match.group(2), 1440)
            hour = cls._to_minute(match.group(4), 60)
            minute = cls._to_minute(match.group(6), 1)
            second = round(cls._to_minute(match.group(8), 1 / 60))

            return day + hour + minute + second

        return result

    @classmethod
    def convert_duration_to_seconds(cls, duration: str) -> int:
        """
        ISO 8601 duration format -> seconds
        "PT1H20M" -> 4800
        """
        result = 0
        if duration is None:
            return result

        pattern = r'PT((\d+)D)?((\d+)H)?((\d+)M)?((\d+)S)?'
        match = re.match(pattern, duration)
        if match:
            day = cls._to_second(match.group(2), 86400)
            hour = cls._to_second(match.group(4), 3600)
            minute = cls._to_second(match.group(6), 60)
            second = cls._to_second(match.group(8), 1)

            return day + hour + minute + second

        return result

    @staticmethod
    def _to_second(value, multiplier):
        return int(value) * multiplier if value else 0

    @classmethod
    def convert_seconds_to_duration(cls, seconds: int) -> str:
        """
        Seconds -> ISO 8601 duration format
        5000 -> "PT1H23M20S"
        """
        if seconds is None or seconds <= 0:
            return "PT0S"

        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds_remainder = seconds % 60

        duration_str = "PT"
        if hours > 0:
            duration_str += f"{hours}H"
        if minutes > 0:
            duration_str += f"{minutes}M"
        if seconds_remainder > 0:
            duration_str += f"{seconds_remainder}S"

        return duration_str

    @classmethod
    def _to_minute(cls, value: str, multiplier: float):
        if value is None:
            result = 0
        else:
            result = int(value) * multiplier
        return result
