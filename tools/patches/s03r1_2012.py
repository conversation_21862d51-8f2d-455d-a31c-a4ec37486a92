import os

import boto3

ENVIRONMENT = os.environ["ENVIRONMENT"]

if ENVIRONMENT in boto3.session.Session().available_profiles:
    print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
    boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
else:
    print(f"Matching profile NOT found in config, using default profile")

# Patch script for deployment steps for:
# https://aferian.atlassian.net/browse/PRJ032DATA-2012
#
# Steps required:
# 1. Create s3 bucket for operational info in eu-west-2
# 2. Create glue database
# 3. Create glue table for "customeroverview"
#
s3_client = boto3.client("s3", region_name="eu-west-2")
glue_client = boto3.client("glue", region_name="eu-west-2")

bucket_name = f"thefilter-{ENVIRONMENT}-operational-info"
database_name = f"{ENVIRONMENT}operationaldatabase".lower()

bucket_exists = False
try:
    # This call will throw an exception if the bucket doesn't exist
    s3_client.head_bucket(Bucket=bucket_name)
    bucket_exists = True
except Exception as e:
    print("Bucket doesn't exist, creating")

if not bucket_exists:
    s3_client.create_bucket(
        Bucket=bucket_name,
        CreateBucketConfiguration={"LocationConstraint": "eu-west-2"}
    )

    s3_client.put_bucket_encryption(
        Bucket=bucket_name,
        ServerSideEncryptionConfiguration={
            "Rules": [
                {
                    "ApplyServerSideEncryptionByDefault": {
                        "SSEAlgorithm": "AES256"
                    }
                },
            ]
        }
    )

    s3_client.put_public_access_block(
        Bucket=bucket_name,
        PublicAccessBlockConfiguration={
            "BlockPublicAcls": True,
            "IgnorePublicAcls": True,
            "BlockPublicPolicy": True,
            "RestrictPublicBuckets": True
        }
    )

database_exists = False
try:
    database_exists = glue_client.get_database(Name=database_name)
    table_exists = True
except Exception as e:
    print("Database doesn't exist, creating")

if not database_exists:
    glue_client.create_database(
        DatabaseInput={
            "Name": database_name,
            "Description": f"Database for Operational and Centralised Analytics",
        }
    )

table_exists = False
try:
    # This call will throw an exception if the bucket doesn't exist
    glue_client.get_table(DatabaseName=database_name, Name="customer_overview")
    table_exists = True
except Exception as e:
    print("Table doesn't exist, creating")

if not table_exists:
    glue_client.create_table(
        DatabaseName=database_name,
        TableInput={
            "Name": "customer_overview",
            "PartitionKeys": [
                {
                    "Name": "date_generated",
                    "Type": "string",
                },
            ],
            "Parameters": {
                "classification": "json",
                'partition_filtering.enabled': 'true',
                "typeOfData": "file"
            },
            "StorageDescriptor": {
                "Columns": [
                    {
                        "Name": "customer",
                        "Type": "string"
                    },
                    {
                        "Name": "plays_last_7days",
                        "Type": "int"
                    },
                    {
                        "Name": "users_last_7days",
                        "Type": "int"
                    },
                    {
                        "Name": "plays_last_30days",
                        "Type": "int"
                    },
                    {
                        "Name": "users_last_30days",
                        "Type": "int"
                    },
                    {
                        "Name": "plays_last_60days",
                        "Type": "int"
                    },
                    {
                        "Name": "views_last_60days",
                        "Type": "int"
                    },
                    {
                        "Name": "plays_bounce_rate_last_60days",
                        "Type": "double"
                    },
                    {
                        "Name": "viewers_last_60days",
                        "Type": "int"
                    },
                    {
                        "Name": "users_last_60days",
                        "Type": "int"
                    },
                    {
                        "Name": "brands_played_last_60days",
                        "Type": "int"
                    },
                    {
                        "Name": "avg_progress_last_60days",
                        "Type": "double"
                    },
                ],
                "Location": f"s3://{bucket_name}/data/customer_overview",
                "InputFormat": "org.apache.hadoop.mapred.TextInputFormat",
                "OutputFormat": "org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat",
                "Compressed": False,
                "SerdeInfo": {
                    "SerializationLibrary": "org.openx.data.jsonserde.JsonSerDe",
                    "Parameters": {
                        "paths": "avg_progress,brands_played,customer,plays,plays_bounce_rate,plays_last_30days,plays_last_7days,users,users_last_30days,users_last_7days,viewers,views"
                    }
                }
            },
            "TableType": "EXTERNAL_TABLE"
        }
    )

print("S04R1 2012 patch applied - Operational database & customeroverview table created")
