import boto3
import requests
from requests_aws4auth import AWS4Auth

host = 'https://search-cwc---staging-production-i2zkl2y7nwit2ywmwlgt3duymq.eu-central-1.es.amazonaws.com/'  # domain endpoint with trailing /
region = 'eu-central-1'  # e.g. us-west-1
service = 'es'
credentials = boto3.Session().get_credentials()
awsauth = AWS4Auth(credentials.access_key, credentials.secret_key, region, service,
                   session_token=credentials.token)

# Register repository

path = '_cat/indices?v'  # the OpenSearch API endpoint
url = host + path

headers = {"Content-Type": "application/json"}

r = requests.get(url, auth=awsauth, headers=headers)
data = r.text


def extract_index_names(text):
    # Split the text into lines
    lines = text.strip().split('\n')

    # Extract index names from the second column of each line
    index_names = [line.split()[2] for line in lines]
    index_size = [line.split()[-1] for line in lines]

    result = [{i:j} for i, j in zip(index_names, index_size)][1:]
    return result


size_data = extract_index_names(data)


def convert_to_gigabytes(size_str):
    # Define multipliers for different units
    multipliers = {'kb': 1024, 'mb': 1024 ** 2, 'gb': 1024 ** 3}

    # Check if the size string ends with a valid suffix
    for suffix, multiplier in multipliers.items():
        if size_str.endswith(suffix):
            return float(size_str[:-len(suffix)]) * multiplier

    # If no valid suffix found, assume the size is in bytes and convert to gigabytes
    try:
        size_in_bytes = float(size_str)
        return size_in_bytes / (1024 ** 3)  # Convert bytes to gigabytes
    except ValueError:
        return 0  # Return 0 if conversion fails


def sum_values(dicts_list, ignore_key):
    total_gb = 0
    for dictionary in dicts_list:
        for key, value in dictionary.items():
            if ignore_key not in key:
                total_gb += convert_to_gigabytes(value)
    return total_gb


total_size = sum_values(size_data, ignore_key='events') / 1000000000

print("Total size:", round(total_size, 3), "GB")
