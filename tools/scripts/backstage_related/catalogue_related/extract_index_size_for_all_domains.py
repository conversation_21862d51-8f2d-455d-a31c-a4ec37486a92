import boto3
import requests
from requests_aws4auth import A<PERSON><PERSON><PERSON><PERSON>

from tools.scripts.backstage_related.catalogue_related.list_es_domains import \
    ListOpernsearchDomains


def convert_to_gigabytes(size_str):
    # Define multipliers for different units
    multipliers = {'kb': 1024, 'mb': 1024 ** 2, 'gb': 1024 ** 3}

    # Check if the size string ends with a valid suffix
    for suffix, multiplier in multipliers.items():
        if size_str.endswith(suffix):
            return float(size_str[:-len(suffix)]) * multiplier

    # If no valid suffix found, assume the size is in bytes and convert to gigabytes
    try:
        size_in_bytes = float(size_str)
        return size_in_bytes / (1024 ** 3)  # Convert bytes to gigabytes
    except ValueError:
        return 0  # Return 0 if conversion fails


class ExtractIndexSize:
    def __init__(self):
        self._region = 'eu-central-1'
        self._service = 'es'
        self._credentials = boto3.Session().get_credentials()
        self._awsauth = AWS4Auth(
            self._credentials.access_key,
            self._credentials.secret_key,
            self._region,
            self._service,
            session_token=self._credentials.token
        )

        self._path = '_cat/indices?v'

        self._headers = {"Content-Type": "application/json"}

    def run(self, customer: str, host: str):
        # self._host = 'https://search-cwc---staging-production-i2zkl2y7nwit2ywmwlgt3duymq.eu-central-1.es.amazonaws.com/'  # domain endpoint with trailing /

        url = f"https://{host}/{self._path}"

        r = requests.get(url, auth=self._awsauth, headers=self._headers)
        data = r.text

        size_data = self.extract_index_names(data)
        total_size = self.sum_values(size_data, ignore_key='events') / 1000000000

        print(f"Total size for {customer}:", round(total_size, 3), "GB")

    @staticmethod
    def extract_index_names(text):
        # Split the text into lines
        lines = text.strip().split('\n')

        # Extract index names from the second column of each line
        index_names = [line.split()[2] for line in lines]
        index_size = [line.split()[-1] for line in lines]

        result = [{i: j} for i, j in zip(index_names, index_size)][1:]
        return result

    @staticmethod
    def sum_values(dicts_list, ignore_key):
        total_gb = 0
        for dictionary in dicts_list:
            for key, value in dictionary.items():
                if ignore_key not in key:
                    total_gb += convert_to_gigabytes(value)
        return total_gb


extract_index_size_api = ExtractIndexSize()
domains = ListOpernsearchDomains().main()
for domain in domains:

    extract_index_size_api.run(domain[0], domain[1])


