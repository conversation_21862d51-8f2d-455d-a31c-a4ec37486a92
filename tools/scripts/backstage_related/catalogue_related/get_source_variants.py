import boto3
import requests
from requests_aws4auth import AWS4Auth


class GetSourceVariants:
    def __init__(self):
        self._region = 'eu-central-1'
        self._service = 'es'
        self._credentials = boto3.Session().get_credentials()
        self._awsauth = AWS4Auth(
            self._credentials.access_key,
            self._credentials.secret_key,
            self._region,
            self._service,
            session_token=self._credentials.token
        )

        self._path = 'sources/_search'

        self._headers = {"Content-Type": "application/json"}
        self._host = 'search-cwc---staging-production-i2zkl2y7nwit2ywmwlgt3duymq.eu-central-1.es.amazonaws.com'  # domain endpoint with trailing /

    def get_data(self):
        url = f"https://{self._host}/{self._path}"

        r = requests.get(url, auth=self._awsauth, headers=self._headers)
        data = r.text
        print(data)

api = GetSourceVariants()
api.get_data()