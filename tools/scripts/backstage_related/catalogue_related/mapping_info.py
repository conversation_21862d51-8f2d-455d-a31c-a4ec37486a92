from tools.scripts.backstage_related.catalogue_related.movies_example import \
    movies_example


def flatten_mapping(mapping, parent_key='', sep='.'):
    flattened_mapping = {}
    for key, value in mapping.items():
        new_key = f"{parent_key}{sep}{key}" if parent_key else key
        if isinstance(value, dict) and 'properties' in value:
            flattened_mapping.update(flatten_mapping(value['properties'], new_key, sep=sep))
        else:
            flattened_mapping[new_key] = value.get('type', 'unknown')
    return flattened_mapping


import boto3
import requests
from requests_aws4auth import AWS4Auth
#
# host = 'https://search-cwc---staging-production-i2zkl2y7nwit2ywmwlgt3duymq.eu-central-1.es.amazonaws.com/'  # domain endpoint with trailing /
# region = 'eu-central-1'  # e.g. us-west-1
# service = 'es'
# credentials = boto3.Session().get_credentials()
# awsauth = AWS4Auth(credentials.access_key, credentials.secret_key, region, service,
#                    session_token=credentials.token)
#
# # Register repository
# index = "movies"
# path = f'{index}/_mapping'  # the OpenSearch API endpoint
# url = host + path
#
# headers = {"Content-Type": "application/json"}
#
# r = requests.get(url, auth=awsauth, headers=headers)
# data = r.text
data = movies_example["movies"]["mappings"]["movie"]["properties"]
flattened_mapping = flatten_mapping(data)

# Print flattened mapping
for field, field_type in flattened_mapping.items():
    print(f"{field}: {field_type}")