import boto3

# backstage domains and domain endpoints, like this:
# Domain Name: v6-7newsmaxstag-production, Endpoint: search-v6-7newsmaxstag-production-**************************.eu-central-1.es.amazonaws.com


#backstage AWS keys
region = "eu-central-1"


class ListOpernsearchDomains:
    @staticmethod
    def list_opensearch_domains():
        # Initialize the OpenSearch client
        client = boto3.client('opensearch')

        # Retrieve all domains
        response = client.list_domain_names()

        # Extract domain names
        domains = response['DomainNames']
        domain_info = []

        # Iterate over each domain to retrieve endpoint
        for domain in domains:
            domain_name = domain['DomainName']
            # Use describe_domain to get the endpoint
            domain_description = client.describe_domain(DomainName=domain_name)
            domain_endpoint = domain_description['DomainStatus']['Endpoint']
            volume_size = domain_description["DomainStatus"]['EBSOptions']['VolumeSize']
            instance_type = domain_description["DomainStatus"]['ClusterConfig']['InstanceType']
            instance_count = domain_description["DomainStatus"]['ClusterConfig']['InstanceCount']
            domain_info.append((domain_name, domain_endpoint, volume_size, instance_type, instance_count))

        return domain_info

    # Main function
    def main(self):
        # List all OpenSearch domains with endpoints
        opensearch_domains = self.list_opensearch_domains()

        # Print the domain names and endpoints
        print("OpenSearch Domains:")
        for domain_name, domain_endpoint, vol_size, instance_type, instance_count in opensearch_domains:
            print(f"Domain Name: {domain_name}, Endpoint: {domain_endpoint}, Size: {vol_size} instance_type: {instance_type}, instance_count: {instance_count}")
        return opensearch_domains


if __name__ == "__main__":
    ListOpernsearchDomains().main()