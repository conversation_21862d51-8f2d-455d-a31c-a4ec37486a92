movies_example= {
  "movies" : {
    "mappings" : {
      "movie" : {
        "properties" : {
          "age_classification" : {
            "properties" : {
              "types" : {
                "properties" : {
                  "indicatorIds" : {
                    "type" : "text",
                    "fields" : {
                      "keyword" : {
                        "type" : "keyword",
                        "ignore_above" : 256
                      }
                    }
                  },
                  "rating" : {
                    "type" : "long"
                  },
                  "type" : {
                    "type" : "text",
                    "fields" : {
                      "keyword" : {
                        "type" : "keyword",
                        "ignore_above" : 256
                      }
                    }
                  }
                }
              }
            }
          },
          "averageUserRating" : {
            "type" : "long"
          },
          "checksum" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "createdAt" : {
            "type" : "long"
          },
          "crew" : {
            "properties" : {
              "id" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              },
              "role" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              }
            }
          },
          "description" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "duration" : {
            "type" : "long"
          },
          "externalAuthDetails" : {
            "properties" : {
              "primetime" : {
                "properties" : {
                  "isProtected" : {
                    "type" : "boolean"
                  }
                }
              }
            }
          },
          "externalRefs" : {
            "properties" : {
              "DAI" : {
                "type" : "object"
              },
              "IMDB" : {
                "type" : "object"
              }
            }
          },
          "external_id" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "genres" : {
            "properties" : {
              "id" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              }
            }
          },
          "geoblock" : {
            "properties" : {
              "allow" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              },
              "deny" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              }
            }
          },
          "id" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "images" : {
            "properties" : {
              "background" : {
                "properties" : {
                  "size" : {
                    "properties" : {
                      "height" : {
                        "type" : "long"
                      },
                      "width" : {
                        "type" : "long"
                      }
                    }
                  },
                  "url" : {
                    "type" : "text",
                    "fields" : {
                      "keyword" : {
                        "type" : "keyword",
                        "ignore_above" : 256
                      }
                    }
                  }
                }
              },
              "poster" : {
                "properties" : {
                  "size" : {
                    "properties" : {
                      "height" : {
                        "type" : "long"
                      },
                      "width" : {
                        "type" : "long"
                      }
                    }
                  },
                  "url" : {
                    "type" : "text",
                    "fields" : {
                      "keyword" : {
                        "type" : "keyword",
                        "ignore_above" : 256
                      }
                    }
                  }
                }
              },
              "still" : {
                "properties" : {
                  "size" : {
                    "properties" : {
                      "height" : {
                        "type" : "long"
                      },
                      "width" : {
                        "type" : "long"
                      }
                    }
                  },
                  "url" : {
                    "type" : "text",
                    "fields" : {
                      "keyword" : {
                        "type" : "keyword",
                        "ignore_above" : 256
                      }
                    }
                  }
                }
              }
            }
          },
          "isAdult" : {
            "type" : "boolean"
          },
          "isHidden" : {
            "type" : "boolean"
          },
          "label" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "productIds" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "rating" : {
            "type" : "long"
          },
          "release_date" : {
            "type" : "date"
          },
          "short_description" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "sourceId" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "subscriptionTags" : {
            "properties" : {
              "id" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              }
            }
          },
          "translations" : {
            "properties" : {
              "description" : {
                "type" : "text"
              },
              "label" : {
                "type" : "text"
              },
              "lang" : {
                "type" : "text"
              },
              "shortDescription" : {
                "type" : "text"
              }
            }
          },
          "type" : {
            "type" : "text",
            "fields" : {
              "keyword" : {
                "type" : "keyword",
                "ignore_above" : 256
              }
            }
          },
          "updatedAt" : {
            "type" : "long"
          },
          "updatedByUserAt" : {
            "type" : "long"
          },
          "userRatingCount" : {
            "type" : "long"
          },
          "windows" : {
            "type" : "nested",
            "properties" : {
              "blocked" : {
                "type" : "boolean"
              },
              "endsAt" : {
                "type" : "integer"
              },
              "startsAt" : {
                "type" : "integer"
              },
              "title" : {
                "type" : "text"
              }
            }
          },
          "workflowTags" : {
            "properties" : {
              "id" : {
                "type" : "text",
                "fields" : {
                  "keyword" : {
                    "type" : "keyword",
                    "ignore_above" : 256
                  }
                }
              }
            }
          },
          "year" : {
            "type" : "long"
          }
        }
      }
    }
  }
}
