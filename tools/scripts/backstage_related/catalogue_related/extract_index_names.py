import boto3
import requests
from requests_aws4auth import AWS4Auth

host = 'https://search-cwc---staging-production-i2zkl2y7nwit2ywmwlgt3duymq.eu-central-1.es.amazonaws.com/'  # domain endpoint with trailing /
host = 'https://search-bs-cluster-production-001-e3byho4xueygkr67k2j2hwvoka.eu-central-1.es.amazonaws.com/'  # domain endpoint with trailing /
region = 'eu-central-1'  # e.g. us-west-1
service = 'es'
credentials = boto3.Session().get_credentials()
awsauth = AWS4Auth(credentials.access_key, credentials.secret_key, region, service,
                   session_token=credentials.token)

# Register repository

path = '_cat/indices?v'  # the OpenSearch API endpoint
url = host + path

headers = {"Content-Type": "application/json"}

r = requests.get(url, auth=awsauth, headers=headers)
data = r.text


def extract_index_names(text):
    # Split the text into lines
    lines = text.strip().split('\n')

    # Extract index names from the second column of each line
    index_names = [line.split()[2] for line in lines]

    return index_names


print(extract_index_names(data))
# print(r.status_code)
# print(r.text)

