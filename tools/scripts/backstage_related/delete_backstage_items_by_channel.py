import json
from typing import List

import requests

from thefilter.logs.logclient import <PERSON><PERSON>og<PERSON><PERSON>, LogClient

"""
This was set up for the IBC Demo and was used to remove all items on a channel between
a given start and end epoch.

Fill in the details at the bottom. It might need some adjustment to suit your needs.
The Get Media By Channel request can be useful here:
```
https://backstage-api.com/media/channels/<CHANNEL_ID>/epg?from=<START_EPOCH>&to=<END_EPOCH>
```

"""


class BackstageIdDeleter:
    def __init__(
            self,
            customer: str,
            host: str,
            consumer_key: str,
            x_service_id: str,
            logger: LogClient,
            start_epoch: int,
            end_epoch: int
    ):
        self._customer = customer
        self._consumer_key = consumer_key
        self._x_service_id = x_service_id
        self._logger = logger
        self._host = host
        self._start_epoch = start_epoch  # 1694736000
        self._end_epoch = end_epoch  # 1695081600

    def _delete_items_by_id(self, list_of_ids: List[str]):
        bulk_body = []
        for item_id in list_of_ids:
            bulk_body.append({
                'id': item_id
            })
        delete_body = {
            'delete': bulk_body
        }
        url = 'https://dapi.backstage-api.com/broadcasts/bulk'
        headers = {
            'Consumer-Key': self._consumer_key,
            'X-Service-ID': self._x_service_id
        }
        content = json.dumps(delete_body)
        response = requests.post(
            url=url,
            headers=headers,
            data=content
        )

    def _get_backstage_items_by_channel(self, type_name: str, channel_id: str) -> dict:
        result = {}
        offset = 0
        size = 50

        # https://backstage-api.com/media/channels/24169850-4e47-11ee-a683-1d4f65fcd334/epg?from=1694736000&to=1695081600
        url = f'{self._host}/{type_name}/channels/{channel_id}/epg?from={self._start_epoch}&to={self._end_epoch}'
        headers = {
            'Consumer-Key': self._consumer_key,
            'X-Service-ID': self._x_service_id
        }

        parameters = {'offset': offset}

        # This first request is just to check there are items.
        # Building up the result data is done in the while loop below.
        response = requests.get(
            url=url,
            headers=headers,
            params=parameters
        )
        if not response.content:
            return result

        response_content = json.loads(response.content)

        total = response_content.get('total', 0)

        while offset < total:
            self._logger.info(f'getting {type_name}, with offset {offset}')
            parameters = {
                'offset': offset,
                'size': size
            }

            response = requests.get(
                url=url,
                headers=headers,
                params=parameters
            )

            response_content = json.loads(response.content)
            items = response_content.get('items')

            offset += size
            if items:
                res = {i['id']: i for i in items}
                result = {**result, **res}
            else:
                self._logger.info("No more items in the response_content, exiting loop.")
                break

            if offset >= 10000:
                # Hack alert! Backstage only allows us to retrieve up to 10k items!
                self._logger.info("More than 10k items not possible, exiting loop.")
                break

        return result


if __name__ == '__main__':
    customer = "BroadcastStudio"
    host = 'https://backstage-api.com'
    consumer_key = '<FILL_THIS_IN>'
    x_service_id = '<FILL_THIS_IN>'
    start_epoch = 1694736000
    end_epoch = 1695081600

    backstage_api = BackstageIdDeleter(
        customer=customer, host=host,
        consumer_key=consumer_key,
        x_service_id=x_service_id,
        logger=PrintLogClient(),
        start_epoch=start_epoch,
        end_epoch=end_epoch
    )
    # 'cd58f0f0-5249-11ee-b5cd-350e77566358', DONE
    channel_ids = [
        # '136562e0-5249-11ee-8dc3-3d67e4ed8df7'
        # '136a2460-5249-11ee-baaf-25cc81440381',
        # '2e31d990-5218-11ee-ac15-f186622293eb',
        # '241235b0-4e47-11ee-9ebc-5f446b65e4e4',
        # '2e37c800-5218-11ee-ab78-d7352fc5c36d',
        # '2415d0e0-4e47-11ee-9959-9d549e4538aa'
        # '13695980-5249-11ee-8485-e5e59fa1d03a',
        # '2e36db40-5218-11ee-a9ba-a97dccce9e83',
        # 'd919e2a0-5160-11ee-a7f2-bfb73e0d65be',
        # 'd8fb4770-5160-11ee-bdb3-85f692423651',
        # 'AWiwPIUBeFR1ygXQXag4',
        # 'A2iwPIUBeFR1ygXQXag4'
        # 'AmiwPIUBeFR1ygXQXag4'
        # '2LQIyocBrtgi2DDbZrn8',amagi2
        # '3d7eUYcBc1PXi-rWKxC9', amagi2
        # 'SbTXUYcBrtgi2DDbKbKi', #amagi3
        # 'obQKTIcBrtgi2DDb865M', #Amagi 5
        #  U7TfUYcBrtgi2DDb3LIY Amagi
        # nbQJTIcBrtgi2DDbRa6a Amagi
    ]
    channel_map = {
        'cd58f0f0-5249-11ee-b5cd-350e77566358': 'Eurosport 1 International',
        'cd3b5800-5249-11ee-9858-63181ec4ce38': 'National Geographic',
        'cd2deba0-5249-11ee-96b4-ed928128e02e': 'SBS Food',
        'cd5b74a0-5249-11ee-9a79-b5819b536852': 'CNN International',
        '23f809f0-4e47-11ee-b266-17c1e0c01b7c': 'Sky Cinema Family',
        '2404f740-4e47-11ee-b477-2d66b8bfa389': 'Nick jr.',
        '23fd1ba0-4e47-11ee-ae6b-6b97eee91963': 'Sky Cinema action',
        '23f655f0-4e47-11ee-8f4c-dd1a877780aa': 'Travel channel',
        '24123c20-4e47-11ee-9f91-e13cd059b513': 'Food Network',
        '23ee8c20-4e47-11ee-8493-bb93d4130b12': 'Nickelodeon',
        '24169850-4e47-11ee-a683-1d4f65fcd334': 'BBC One'
    }
    for channel_id in channel_ids:
        print(f'{channel_map.get(channel_id)}========')
        response = backstage_api._get_backstage_items_by_channel('media', channel_id)
        broadcast_ids = list(response.keys())
        print(broadcast_ids)

        ### THIS WILL DELETE DATA!
        # backstage_api._delete_items_by_id(broadcast_ids)
