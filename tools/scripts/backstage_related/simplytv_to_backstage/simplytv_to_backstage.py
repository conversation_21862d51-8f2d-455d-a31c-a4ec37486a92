import json
import os
from copy import deepcopy
from datetime import datetime
from io import BytesIO
from typing import List, Tuple

import requests
from PIL import Image  # pip install PILLOW

from thefilter.utils.file_handlers import read_csv_file
from thefilter.utils.iterable_utils import flatten_list

SIMPLY_TV_API_KEY = os.environ.get('SIMPLY_TV_API_KEY')


class SimplytvToBackstage:
    """
    Calls SimplyTV to get program and channel info in the backstage format.
    """

    def __init__(self):

        self._host = 'https://delivery.simply.tv/'
        self._api_key = SIMPLY_TV_API_KEY
        self._catalogue = []
        self._no_details = []
        self._windows = {}
        self._broadcasts = []
        self._channels = {}
        self._number_of_broadcast_items = 200
        self._image_url_cache = {}

    def create_vod_catalogue(self):
        combined_tv, combined_movies = self._load_csv_files()

        self._create_vod_asset(combined_tv, 'TVSeries')
        self._create_vod_asset(combined_movies, "Movie")

        print(len(self._no_details), 'skipped vod count')
        print(len(self._catalogue), 'total vod assets')

        file_name = 'simplytv_vod_catalogue'
        self._create_json_file(file_name, self._catalogue)

    def create_broadcasts(self, channels: List[str]):
        self._create_broadcasts(channels)

        print(len(self._broadcasts), 'total broadcasts')
        file_name = 'simplytv_broadcasts'
        self._create_json_file(file_name, self._broadcasts)

    @staticmethod
    def _load_csv_files() -> tuple[List[str], List[str]]:
        combined_tv_filepath = './data/combined-tv.csv'
        combined_tv = read_csv_file(combined_tv_filepath, has_headers=False)
        combined_tv = flatten_list(combined_tv)
        combined_tv.reverse()

        combined_movies_filepath = './data/combined-movies.csv'
        combined_movies = read_csv_file(combined_movies_filepath, has_headers=False)
        combined_movies = flatten_list(combined_movies)
        combined_movies.reverse()

        return combined_tv, combined_movies

    def _call_simply_tv_api(self, item_id: str, asset_type: str):
        """ type can be 'programs' or 'schedules' """
        url = f'{self._host}{asset_type}/v1/24i/{item_id}.json?api_key={self._api_key}'
        response = requests.get(url)

        if not response.content:
            print('error!')
        try:
            response_content = json.loads(response.content)
        except:
            response_content = None
        return response_content

    @staticmethod
    def _create_json_file(file_name: str, data: json):
        with open(f"{file_name}.json", "w", encoding='utf-8') as f:
            f.write(json.dumps(data))

    def _create_vod_asset(self, imdb_id_list: List[str], type_name: str):
        for imdb_id in imdb_id_list:
            self.call_and_format_vod_asset(imdb_id, type_name, 'catalogue', "no_channel")

    def call_and_format_vod_asset(self, imdb_id, type_name, asset_type, channel):
        if imdb_id == 0:
            response_content = {}
        else:
            response_content = api._call_simply_tv_api(imdb_id, 'programs')
        if not response_content or response_content == {} or not response_content.get(
                'duration') \
                or not response_content.get('descriptions') \
                or not response_content.get('titles'):
            self._no_details.append(imdb_id)
        else:
            formatted_asset = self._format_asset(
                response_content,
                type_name,
                asset_type,
                channel
            )
            self._catalogue.append(formatted_asset)

    def _format_asset(
            self,
            response_content: dict,
            type_name: str,
            asset_type: str,
            channel
    ):
        external_id = response_content.get('externalRefs')
        if external_id:
            response_content['external_id'] = external_id.get('IMDB', {}).get('id')
        else:
            response_content['external_id'] = response_content.get('id')
        self._handle_type(response_content, type_name)

        # if asset_copy.get('series'):
        #     self._handle_episodes(asset_copy, (n + 1))

        if asset_type == 'broadcast':
            self._handle_channel_label(response_content, channel)
            # self._add_in_broadcast_channel_info(response_content, channel)

            if response_content.get('series'):
                response_content.pop('series')
            if response_content.get('episode_number'):
                response_content.pop('episode_number')
            if response_content.get('season_number'):
                response_content.pop('season_number')

        self._handle_crew(response_content)
        self._handle_description(response_content)
        self._handle_label(response_content)
        self._handle_release_year(response_content)
        self._handle_images(response_content)

        if asset_type == 'catalogue':
            self._handle_vod_windows(response_content)
            response_content.pop('id')
        else:
            self._handle_linear_windows(response_content)

        return response_content

    def _handle_images(self, response_content):
        # add image dimensions
        orig_images = response_content.get('images')
        result = {
            "still": [],
            "background": [],
            "poster": [],
            "highlight": []
        }

        if orig_images:
            for image_type, images in orig_images.items():
                images_seen = []
                for image in images:
                    image_url = image.get('url')
                    if image_url in images_seen:
                        continue
                    else:
                        images_seen.append(image_url)
                    if image_url in self._image_url_cache.keys():
                        width, height = self._image_url_cache[image_url]
                    else:
                        width, height = self._get_image_dimensions(image_url)
                        self._image_url_cache[image_url] = (width, height)
                    image_size = {
                        'width': width,
                        'height': height
                    }
                    result[image_type] = [
                        {
                            'url': image_url,
                            'size': image_size
                        }
                    ]
            # We need to populate all image fields with best efforts.
            result['still'] = \
                result['still'] or result['background'] or result['highlight']
            result['background'] = \
                result['background'] or result['still'] or result['highlight']
            result['highlight'] = \
                result['highlight'] or result['background'] or result['still']

        response_content['images'] = result

    @staticmethod
    def _get_image_dimensions(image_url: str) -> Tuple[int, int]:
        # Call to get image width and height - adds a bit of time to everything :(
        try:
            response = requests.get(image_url)
            if response.status_code == 200:
                image_data = BytesIO(response.content)
                img = Image.open(image_data)
                width, height = img.size
                return width, height
            else:
                print(f"Failed to fetch the image. Status code: {response.status_code}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    @staticmethod
    def _handle_vod_windows(response_content):
        # store windows:
        response_content['windows'] = [
            {
                "blocked": False,
                "blockedMessage": None,
                "countries": [],
                "applications": [],
                "endsAt": 2147476800,
                "startsAt": 1599476550,
                "title": None
            }
        ]

    @staticmethod
    def _handle_crew(response_content):
        # remove crew[n].id
        orig_crew = response_content.get('crew')
        new_crew = [{k: v for k, v in d.items() if k != 'id'} for d in orig_crew]
        response_content['crew'] = new_crew

    @staticmethod
    def _handle_description(response_content):
        # descriptions[n].text -> description
        orig_description = response_content.get('descriptions')
        new_description = orig_description[0]['text']
        response_content['description'] = new_description
        response_content.pop('descriptions')

    @staticmethod
    def _handle_label(response_content: dict):
        # titles[n].text -> label
        orig_titles = response_content.get('titles')
        text = orig_titles[0]['text']
        response_content['label'] = text
        response_content.pop('titles')

    @staticmethod
    def _handle_type(response_content, type_name):
        type_name_map = {
            'TVSeries': 'episode',  # VOD
            'Movie': 'movie',  # VOD
            'broadcast': 'broadcast'
        }
        type = type_name_map[type_name]
        response_content['type'] = type

    def _create_broadcasts(self, channels):
        for channel in channels:
            print(f"channel {channel}")
            content = api._call_simply_tv_api(channel, "schedules")
            self._gather_windows(content['listings'])
            # self._create_channel(content['channels'])
            api._create_broadcast_asset(content['programs'], channel)

    def _create_broadcast_asset(self, programs_data: List[dict], channel):
        for item in programs_data:
            imdb_id = self._get_imdb_id(item)
            type_name = 'broadcast'
            self.call_and_format_item(item, imdb_id, type_name, 'broadcast', channel)

    def _gather_windows(self, listings_data: List[dict]):
        for listing in listings_data:
            item_id = listing['asset_id']
            if self._windows.get(item_id):
                self._windows[item_id] += listing['windows']
            else:
                self._windows[item_id] = listing['windows']

    @staticmethod
    def _get_imdb_id(item):
        try:
            imdb_id = item["externalRefs"]["IMDB"]["id"]
        except Exception as e:
            imdb_id = 0

        return imdb_id

    def _handle_linear_windows(self, linear_item: dict):
        # we gather the windows, and will make
        # individual objects from each viewing later on
        linear_item_id = linear_item.get('id')
        if self._windows.get(linear_item_id):
            windows = self._windows[linear_item_id]
            linear_item['simplytv_windows'] = windows

    @staticmethod
    def _convert_datetime_str_to_epoch(date_time_str: str) -> int:
        date_time_obj = datetime.strptime(date_time_str, '%Y-%m-%dT%H:%M:%S.000Z')
        epoch_ms = int(date_time_obj.timestamp())
        return epoch_ms

    @staticmethod
    def _handle_release_year(item):
        # release_year -> release_date format YYYY-MM-DD / year int
        release_year = item.get('release_year')
        if release_year:
            item['year'] = release_year
            item['release_date'] = f'{release_year}-01-01'
            item.pop('release_year')

    @staticmethod
    def _handle_channel_label(response_content, channel):
        response_content['channel_label'] = f'Channel {channel}'

    def _handle_episodes(self, formatted_asset, episode_number: int):
        # BS doesn't support Series - so we made the series into an episode,
        # and now we make this item look more like an "episode"
        # episode_number: int not used
        series_object = deepcopy(formatted_asset)
        series_object['type'] = 'series'
        series_object.pop('startsAt')
        series_object.pop('endsAt')
        series_object.pop('id')
        series_object.pop('duration')
        series_object.pop('channels')
        series_object.pop('series')
        series_object['crew'] = []
        series_object['genres'] = []
        formatted_asset['series'] = series_object

        # uncomment the below if you want more fields
        # formatted_asset['episode_number'] = episode_number
        # formatted_asset['season_number'] = 1
        # formatted_asset['label'] = f"{formatted_asset['label']} (S1 E{episode_number})"

    def call_and_format_item(self, item, imdb_id, type_name, asset_type, channel):
        if item == {} \
                or not item.get('descriptions') \
                or not item.get('titles'):
            self._no_details.append(item.get((channel, item.get('id'))))
        else:
            formatted_asset = self._format_asset(
                item,
                type_name,
                asset_type,
                channel
            )
            # Create new objects from windows, make each one into a new asset
            windows = formatted_asset['simplytv_windows']
            for n, window in enumerate(windows):
                asset_copy = deepcopy(formatted_asset)
                asset_copy['startsAt'] = self._convert_datetime_str_to_epoch(
                    window['startsAt'])
                asset_copy['endsAt'] = self._convert_datetime_str_to_epoch(
                    window['endsAt'])
                asset_copy.pop('simplytv_windows')
                asset_copy.pop('id')

                self._broadcasts.append(asset_copy)

    def call_and_format_broadcast(self, item, imdb_id, type_name, param, channel):
        pass

    def _create_channel(self, channel_info):
        channel_number = channel_info[0]['id'].replace('channel_', "")
        image_url = channel_info[0]['images']['logo'][0]['url']
        width, height = self._get_image_dimensions(image_url)
        image_size = {
            'width': width,
            'height': height
        }
        print(channel_number, image_url)
        self._channels[channel_number] = {
            'number': channel_number,
            'label': channel_info[0]['label'],
            'description': channel_info[0]['label'],
            'images': {
                "logo": [
                    {
                        "url": image_url,
                        "size": image_size
                    }
                ]
            }
        }

    def _add_in_broadcast_channel_info(self, response_content, channel):
        response_content['channels'] = self._channels[channel]


if __name__ == '__main__':
    api = SimplytvToBackstage()
    """
    Uncomment below to create IMDB based TVSeries and Movies
    """
    # api.create_vod_catalogue()

    """
    Uncomment below to create broadcasts.
    """
    # See the readme for details
    channels = [
        '244', '8510', '3622', '178', '2293', '4175', '3674', '714', '718',
        '198', '572', '7461', '4235', '286', '21'
    ]
    api.create_broadcasts(channels)
