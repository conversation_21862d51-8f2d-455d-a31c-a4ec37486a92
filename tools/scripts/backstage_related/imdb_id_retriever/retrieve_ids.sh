#!/bin/bash -e

mkdir -p output-movies
mkdir -p output-tv

echo "Getting IDs for Top 250 Movies"
wget https://www.imdb.com/chart/top --user-agent="Mozilla/5.0 (Macintosh Intel Mac OS X 13_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
cat top | sed -E 's/<li/\n<li/g' | grep 'title/tt[0-9]*' | sed -E 's/.*(tt[0-9]+).*/\1/g' | sort | uniq > output-movies/top250movies.csv

echo "Getting IDs for Top 100 Most Popular Movies"
wget https://www.imdb.com/chart/moviemeter --user-agent="Mozilla/5.0 (Macintosh Intel Mac OS X 13_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
cat moviemeter | sed -E 's/<li/\n<li/g'  | grep 'title/tt[0-9]*' | sed -E 's/.*(tt[0-9]+).*/\1/g' | sort | uniq > output-movies/popular100movies.csv

echo "Combining all files to get unique set"
cat output-movies/*.csv | sort | uniq > combined-movies.csv

unique_count=$(cat combined-movies.csv | wc -l)
echo -e "$unique_count Top and Popular Movie IDs retrieved"

echo "Starting download of IMDB TV Show IDs..."
genres=(action adventure animation biography comedy crime documentary drama family fantasy game-show history horror music musical mystery news reality-tv romance sci-fi short sport talk-show thriller war western)
echo "Getting IDs for Top 250 TV Shows"
wget https://www.imdb.com/chart/toptv --user-agent="Mozilla/5.0 (Macintosh Intel Mac OS X 13_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
cat topv | sed -E 's/<li/\n<li/g' | grep 'title/tt[0-9]*' | sed -E 's/.*(tt[0-9]+).*/\1/g' | sort | uniq > output-tv/top250tv.csv

echo "Getting IDs for 100 Most Popular TV Shows"
wget https://www.imdb.com/chart/tvmeter --user-agent="Mozilla/5.0 (Macintosh Intel Mac OS X 13_5_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Safari/605.1.15"
cat tvmeter | sed -E 's/<li/\n<li/g' | grep 'title/tt[0-9]*' | sed -E 's/.*(tt[0-9]+).*/\1/g' | sort | uniq > output-tv/popular100tv.csv

for genre in ${genres[@]}; do
  echo "Getting Top 50 shows in genre: $genre"
  curl "https://www.imdb.com/search/title/?genres=$genre&sort=user_rating,desc&title_type=tv_series,mini_series&num_votes=5000," -A "ReqBin Curl Client/1.0" -s | sed -E 's/<li/\n<li/g' | grep 'title/tt[0-9]*' | sed -E 's/.*(tt[0-9]+).*/\1/g' | sort | uniq > output-tv/top$genre.csv
done

echo "Combining all files to get unique set"
cat output-tv/*.csv | sort | uniq > combined-tv.csv

unique_count=$(cat combined-tv.csv | wc -l)
echo -e "$unique_count Top and Popular TV Show IDs retrieved"
