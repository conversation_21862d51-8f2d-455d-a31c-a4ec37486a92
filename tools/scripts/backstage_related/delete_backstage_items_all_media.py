import json
from typing import List

import requests

from thefilter.logs.logclient import PrintLog<PERSON><PERSON>, LogClient

"""
This was set up for the UKTV RFP demo and will delete ALL media from the supplied
Backstage instance information

Fill in the details at the bottom. It might need some adjustment to suit your needs.
"""


class BackstageIdDeleter:
    def __init__(
            self,
            customer: str,
            host: str,
            consumer_key: str,
            x_service_id: str,
            logger: LogClient
    ):
        self._customer = customer
        self._consumer_key = consumer_key
        self._x_service_id = x_service_id
        self._logger = logger
        self._host = host

    def delete_items_by_id(self, items: List[dict]):
        media_types = {}
        for item in items.values():
            type = item['type']

            if type in media_types.keys():
                media_types[type].append(item['id'])
            else:
                media_types[type] = [item['id']]

        bulk_body = []
        for key in media_types.keys():
            for item_id in media_types[key]:
                bulk_body.append({
                    'id': item_id
                })
            delete_body = {
                'delete': bulk_body
            }
            if key == "live_event":
                url = f'https://dapi.backstage-api.com/liveEvents/bulk'
            elif key == "series":
                url = f'https://dapi.backstage-api.com/series/bulk'
            else:
                url = f'https://dapi.backstage-api.com/{key}s/bulk'
            headers = {
                'Consumer-Key': self._consumer_key,
                'X-Service-ID': self._x_service_id
            }
            content = json.dumps(delete_body)
            print(f"Would hit {url} with {content}")

            response = requests.post(
                url=url,
                headers=headers,
                data=content
            )

    def get_backstage_items(self) -> dict:
        result = {}
        offset = 0
        size = 50

        url = f'{self._host}/media'
        headers = {
            'Consumer-Key': self._consumer_key,
            'X-Service-ID': self._x_service_id
        }

        parameters = {'offset': offset}

        # This first request is just to check there are items.
        # Building up the result data is done in the while loop below.
        response = requests.get(
            url=url,
            headers=headers,
            params=parameters
        )
        if not response.content:
            return result

        response_content = json.loads(response.content)

        total = response_content.get('total', 0)

        while offset < total:
            self._logger.info(f'getting media, with offset {offset}')
            parameters = {
                'offset': offset,
                'size': size
            }

            response = requests.get(
                url=url,
                headers=headers,
                params=parameters
            )

            response_content = json.loads(response.content)
            items = response_content.get('items')

            offset += size
            if items:
                res = {i['id']: i for i in items}
                result = {**result, **res}
            else:
                self._logger.info("No more items in the response_content, exiting loop.")
                break

            if offset >= 10000:
                # Hack alert! Backstage only allows us to retrieve up to 10k items!
                self._logger.info("More than 10k items not possible, exiting loop.")
                break

        return result


if __name__ == '__main__':
    customer = "UKTV Demo"
    host = 'https://dapi.backstage-api.com'
    consumer_key = '<FILL IN>'
    x_service_id = '<FILL IN>'

    backstage_api = BackstageIdDeleter(
        customer=customer, host=host,
        consumer_key=consumer_key,
        x_service_id=x_service_id,
        logger=PrintLogClient()
    )

    response = backstage_api.get_backstage_items()
    media_ids = list(response.keys())
    print(media_ids)

    # backstage_api.delete_items_by_id(response)
