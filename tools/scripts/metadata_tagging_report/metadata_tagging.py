import csv
import os
import sys
import argparse

import boto3
from pyathena import connect


class MetadataTaggingReport:
    def __init__(self, region: str, environment: str, customer: str):
        self._region = region
        self._customer = customer
        self._environment = environment
        self._schema = f"{self._environment}{self._customer}database"
        self._bucket = f"s3://thefilter-{self._environment}-{self._region}-athena-queryresults"

    def get_data(self) -> list:

        print(f' Getting  {self._customer} entity association data')

        query = f"""
                               select thing_id 
                                      ,thing_type
                                      ,thing_name
                                      ,entity_type
                                      ,count(*)  as entity_type_count 
                                 from entity_association ea
                            group by thing_id, thing_type, thing_name, entity_type
                            order by entity_type_count DESC, thing_id, thing_type, thing_name, entity_type 
                   """

        staging_dir = f"f{self._bucket}/scripts/"
        cursor = connect(s3_staging_dir=staging_dir, region_name=self._region).cursor(
            schema_name=self._schema)

        result = cursor.execute(query)

        ea_results = result.fetchall()

        return ea_results

    def csv_file_writer(self, ea_results: list) -> str:

        print(f' Creating csv file {self._customer}_entity_association_report.csv')

        report = f"{self._customer}_entity_association_report.csv"

        f = csv.writer(open(report, "w", newline=''))

        headers = ["thing_id", "thing_type", "thing_name", "entity_type", "entity_type_count"]
        f.writerow(headers)

        # 1 is added as the glue_buffer value, this allows glue to correctly use the first row
        # as a header row and set the column names correctly for the table
        for item in ea_results:
            f.writerow([item[0],
                        item[1],
                        item[2],
                        item[3],
                        item[4]
                        ])

        return report

    def add_to_S3(self, report: str):

        s3 = boto3.client('s3')
        bucket = f'thefilter-{self._environment}-{self._region}-{self._customer}'
        S3filePath = 'report/entity_association/'

        print(f'Uploading {report} to S3/{bucket}/{S3filePath}')
        try:
            key = S3filePath + report
            s3.upload_file(
                report,
                bucket,
                key,
                ExtraArgs={"ServerSideEncryption": "AES256"}
            )
        except Exception as e:
            print(f'Failed to add file to S3 bucket: {e}')
            sys.exit(1)


if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Creates report about entity association")
    parser.add_argument("--region", "-r", help="The source region for Athena query", required=True)
    parser.add_argument("--environment", "-e", help="The source environment for Athena query",
                        required=True)
    parser.add_argument("--customer", "-c",
                        help="The customer for which we want to get report about entity association", required=True)
    args = parser.parse_args()

    source_region = str(args.region).lower()
    source_environment = str(args.environment).lower()
    source_customer = str(args.customer).lower()

    eareporter = MetadataTaggingReport(source_region, source_environment, source_customer)

    results = eareporter.get_data()

    report_name = eareporter.csv_file_writer(results)

    eareporter.add_to_S3(report_name)

    os.remove(f'{report_name}')
