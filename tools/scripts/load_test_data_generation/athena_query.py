from typing import List
from pyathena import connect
from pyathena.cursor import Cursor


class UserHistoryAthenaQuery:
    def __init__(self, region: str, environment: str, customer: str, slotId: str, limit: int, days: int):
        self._region = region
        self._s3_destination_bucket = f"s3://thefilter-{environment}-{region}-athena-queryresults"
        self._schema = f"{environment}{customer}database"
        self.customer = customer
        self.slotId = slotId
        self.limit = limit
        self.days = days

    def execute(self):
        results = self.query_athena()
        return results

    def _get_athena_cursor(self) -> Cursor:
        staging_dir = f"f{self._s3_destination_bucket}/scripts/"
        return connect(
            s3_staging_dir=staging_dir,
           region_name=self._region
        ).cursor(schema_name=self._schema)

    def query_athena(self) -> List[dict]:
        query = f"""
        SELECT DISTINCT
            user_userid,
            parameters_query_string_q,
            parameters_query_string_subscriptiontype,
            parameters_subscription_type,
            parameters_seedids,
            parameters_rule_id,
            parameters_query_string_ignore
        FROM "personalisation"
        WHERE CAST(REPLACE(timestamp_initiated, 'T', ' ') AS Timestamp) >= DATE_ADD('day', -{self.days}, current_date)
        AND parameters_rule_id = '{self.slotId}'
        AND user_userid != 'site24x7'
        AND parameters_query_string_ignore is NULL
        limit {self.limit}
        """
        cursor = self._get_athena_cursor()
        results = cursor.execute(query)

        return self._format_athena_result(results.fetchall())

    def _format_athena_result(self, results: Cursor) -> List[dict]:
        result = []

        for row in results:
            user_id = self.handle_none(str(row[0]))
            parameters_query_string_q = self.handle_none(str(row[1]))
            parameters_query_string_subscriptiontype = self.handle_none(str(row[2]))
            parameters_subscription_type = self.handle_none(str(row[3]))
            parameters_seedids = self.handle_none(self.handle_array(str(row[4])))
            parameters_rule_id = self.handle_none(str(row[5]))
            result.append({"user_id": user_id,
                           "parameters_query_string_q": parameters_query_string_q,
                           "parameters_query_string_subscriptiontype": parameters_query_string_subscriptiontype,
                           "parameters_subscription_type": parameters_subscription_type,
                           "parameters_seedids": parameters_seedids,
                           "parameters_rule_id": parameters_rule_id})

        return result

    @staticmethod
    def handle_array(parameters_seedids: str) -> str:
        if parameters_seedids.startswith("["):
            return parameters_seedids[1:-1]

    @staticmethod
    def handle_none(parameter: str):
        if parameter == "None":
            return None
        else:
            return parameter
