from typing import List

import boto3
import requests


class SlotFinder:
    def __init__(self, environment: str, customer: str, api_key: str):
        self.CLIENT_SECRET = None
        self.CLIENT_ID = None
        self.environment = environment
        self.customer = customer
        self.api_key = api_key

    def execute(self):
        auth_token_source = self.get_auth("source")
        slots = self.read_slots(self.api_key, auth_token_source)
        return slots

    def read_slots(self, api_key: str, access_token: str) -> List[dict]:
        host = f'https://{self.customer}.{self.environment}.thefilter.com/v0/slots/'
        slot_list = [{}]
        try:
            request = requests.get(
                url=host,
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "x-api-key": api_key
                }
            )
            if request.ok:
                result = request.json()['result']
                for i in result:
                    slot = {"slotId": i.get("slotId"), "slotName": i.get("slotName")}
                    slot_list.append(slot)
                return slot_list
            else:
                print(request.status_code)
                print("Error reading slots, exiting...")
                exit()
        except Exception as e:
            print(e)

    def get_auth(self, sequence: str) -> str:
        try:
            cognito_user_pool_id = self.get_cognito_user_pool_id()
            self.set_cognito_app_client_id_and_secret(cognito_user_pool_id)
        except Exception as e:
            print(f"Exception: Unable to get cognito app client id and secret "
                  f"for customer {self.customer} in environment {self.environment} for sequence {sequence}", e)

        try:
            response = requests.post(
                url=f'https://users.{self.environment}.thefilter.com/oauth2/token',
                data={'grant_type': 'client_credentials'},
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                auth=(self.CLIENT_ID, self.CLIENT_SECRET)
            )
            if response.status_code != 200:
                print(f"Exception: Unable to get authentication with "
                      f"code {response.status_code} for customer {self.customer} "
                      f"in environment {self.environment}", response.json())
                raise Exception()

            access_token = response.json().get('access_token')
            return access_token

        except requests.exceptions.HTTPError as e:
            print('Error with getting client_credentials', e)
            raise

    def get_cognito_user_pool_id(self) -> str:
        cognito_client = self.get_cognito_client()
        user_pools_response = cognito_client.list_user_pools(MaxResults=2)

        for pool in user_pools_response['UserPools']:
            if pool['Name'] == f"{self.environment}-userpool":
                return pool['Id']

        print("Unable to get User Pool id, exiting")
        exit(1)

    def get_cognito_client(self) -> boto3.session.Session.client:
        session = boto3.session.Session(profile_name=self.environment)
        if self.environment == "production":
            cognito_client = session.client('cognito-idp', region_name="us-east-1")
        else:
            cognito_client = session.client('cognito-idp', region_name="eu-west-2")
        return cognito_client

    def set_cognito_app_client_id_and_secret(self, user_pool_id: str):
        customer = self.customer
        cognito_client = self.get_cognito_client()
        response = cognito_client.list_user_pool_clients(
            UserPoolId=user_pool_id,
            MaxResults=10
        )

        customer_client_id = ""
        customer_secret = ""
        if self.customer == 'uktv':
            customer = 'UKTV'
        for client in response['UserPoolClients']:
            if client['ClientName'] == f"{self.environment}-{customer}-AppClient":
                customer_client_id = client['ClientId']
                client_response = cognito_client.describe_user_pool_client(
                    UserPoolId=user_pool_id,
                    ClientId=customer_client_id
                )
                customer_secret = client_response['UserPoolClient']['ClientSecret']

        self.CLIENT_ID = customer_client_id
        self.CLIENT_SECRET = customer_secret
