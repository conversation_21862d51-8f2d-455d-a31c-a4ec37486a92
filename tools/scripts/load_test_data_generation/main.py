import argparse

from thefilter.logs.logclient import PrintLogClient
from tools.scripts.load_test_data_generation.athena_query import UserHistoryAthenaQuery
from tools.scripts.load_test_data_generation.slot_finder import SlotFinder
from tools.scripts.load_test_data_generation.url_generator import UrlGenerator

parser = argparse.ArgumentParser(
    description="Gets data from athena and uses them "
                "to create load tests for use in Locust, "
                "the api_key is the only required arg"
)

parser.add_argument(
    "--region", "-r",
    help="The source region the table we wish to query resides in"
         "defaults to eu-west-2",
    default="eu-west-2",
    required=False
)

parser.add_argument(
    "--environment", "-e",
    help="The source environment we wish to query"
         "defaults to pre",
    default='pre',
    required=False
)

parser.add_argument(
    "--api_key", "-k",
    help="The API Key required to access the Source API",
    default="",
    required=True
)

parser.add_argument(
    "--limit", "-l",
    help="The limit for the Athena query.",
    default="100",
    required=False
)

parser.add_argument(
    "--customers", "-c",
    help='The list of customers you want to iterate through'
         'must be in format "example1,example2,..."'
         'defaults to "epix,uktv"',
    default="epix,uktv",
    required=False
)

parser.add_argument(
    "--destination", "-d",
    help="Destination - either local or s3"
         "defaults to s3",
    default="s3",
    required=False
)

parser.add_argument(
    "--days", "-dy",
    help="Time past from now to gather athena results from"
         "Must be a positive number of days",
    default="7",
    required=False
)

if __name__ == "__main__":
    logger = PrintLogClient()
    args = parser.parse_args()

    api_key = str(args.api_key)
    environment = str(args.environment).lower()
    region = str(args.region).lower()
    limit = int(args.limit)
    customers = str(args.customers).lower().split(",")
    destination = str(args.destination).lower()
    days = int(args.days)

    for customer in customers:
        customer = customer.strip()
        logger.info(f"--- {customer} ---")
        logger.info("Finding slots...")
        slot_finder = SlotFinder(environment, customer, api_key)
        url_generator = UrlGenerator()
        slots = slot_finder.execute()
        url_list = []
        for slot in slots:
            slotId = slot.get("slotId")
            slotName = slot.get("slotName")
            logger.info(f"Slot ID: {slotId}")
            logger.info(f"Slot Name: {slotName}")

            logger.info("Querying Athena...")
            athena_query = UserHistoryAthenaQuery(region, environment, customer, slotId, limit, days)
            query_results = athena_query.execute()

            logger.info("Generating URLs...")
            urls = url_generator.format_urls(query_results, slotName)
            for url in urls:
                url_list.append(url)

        logger.info("Writing URLs...")
        if destination == "s3":
            url_generator.write_to_s3(url_list, customer, environment)
        elif destination == "local":
            url_generator.write_to_local(url_list, customer)
        else:
            logger.info("Error - destination not properly defined.")
