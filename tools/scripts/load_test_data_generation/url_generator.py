import boto3


class UrlGenerator:
    @staticmethod
    def format_urls(results: [dict], slotName: str) -> []:
        urls = []

        for row in results:
            parameters_rule_id = row["parameters_rule_id"]  # Slot ID is always present
            user_id = row.get("user_id", None)
            parameters_query_string_q = row.get("parameters_query_string_q", None)
            parameters_query_string_subscriptiontype = row.get("parameters_query_string_subscriptiontype", None)
            parameters_subscription_type = row.get("parameters_subscription_type", None)
            parameters_seedids = row.get("parameters_seedids", None)

            if slotName == "Search" and parameters_query_string_q is None:
                print("Intercepted attempt to use search slot with no search parameter.")
            else:
                url_string = f"/slots/{parameters_rule_id}/items?"

                if user_id:
                    url_string += f"userId={user_id}&"
                if parameters_query_string_q:
                    url_string += f"q={parameters_query_string_q}&"
                if parameters_query_string_subscriptiontype:
                    url_string += f"subscriptionType={parameters_query_string_subscriptiontype}&"
                if parameters_subscription_type:
                    url_string += f"subscriptionType={parameters_subscription_type}&"
                if parameters_seedids:
                    url_string += f"seedIds={parameters_seedids}&"

                url_string += "ignore=load_test"
                urls.append(url_string)

        return urls

    @staticmethod
    def write_to_s3(url_list: [], customer: str, environment: str):
        s3 = boto3.resource("s3")
        s3_bucket = f"{environment}-thefilter-dataops"
        s3_path = f"load-tests/{customer}/urls.txt"
        path = s3.Object(s3_bucket, s3_path)
        url_block = ""
        for url in url_list:
            url_block += f"{url}\n"
        path.put(Body=url_block)

    @staticmethod
    def write_to_local(url_list: [], customer: str):
        url_block = ""
        with open(f"{customer}/urls.txt", "w") as urlfile:
            for url in url_list:
                url_block += f"{url}\n"
            urlfile.write(url_block)
