import os

from tools.scripts.ddb_write_test.ddb_maximise_throughput import DDBMaximiseThroughput

SQS_QUEUE = os.environ["SQS_QUEUE"]
CUSTOMER = os.environ["CUSTOMER"]


def lambda_handler(event, context):
    """
    Picks up messages from an SQS queue and writes them to DDB
    Concurrent lambdas will speed this process up.

    :param event: Not used, but required for lambda handler
    :param context: Not used, but required for lambda handler
    """
    sqs_reader = DDBMaximiseThroughput(SQS_QUEUE, CUSTOMER)
    messages = sqs_reader.format_messages(event)
    if messages:
        sqs_reader.publish_to_ddb(messages)
