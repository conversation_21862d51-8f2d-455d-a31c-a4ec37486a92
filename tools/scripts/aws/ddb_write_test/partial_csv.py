import csv


class PartialCSV:
    """
    Takes a csv and generates a copy with {line_count} number of records.
    """

    def __init__(self):
        self.input_file_path = '/Users/<USER>/Desktop/model/recommendations.csv'
        self.output_file_path = '/Users/<USER>/Desktop/model/recommendations_10.csv'
        self._line_count = 10

    def read_csv(self):
        with open(self.input_file_path) as f, \
                open(self.output_file_path, 'w') as o:
            csv_data = csv.reader(f, delimiter=',')
            writer = csv.writer(o, delimiter=',')
            # headers = next(csv_data)
            # headers = ['userId', 'size', 'modelKey', 'recommendations']

            for n, row in enumerate(csv_data):
                if n >= self._line_count:
                    break
                writer.writerow(row)


if __name__ == "__main__":
    partial_csv = PartialCSV()
    partial_csv.read_csv()

# unique items in 1st place: {'d_p25gk9tmyyc', 'd_pdsnpfhbpjf', 'd_puo5l4z1o4y', 'd_p92gdoq65m3', 'd_po9uu6j0bd5', 'd_pjm940egtv1', 'd_pjf512tz7zq', 'd_pt53uny5bkc', 'd_pjm940hs0wq', 'd_pzuekr6p4f7', 'd_py26g7ki2p6', 'd_p85maqqalb1', 'd_p13tbpxg57q', 'd_pjm940i7h44', 'd_pw19kstovdf', 'b_pyexdszkxz1', 'd_po9y9840zl0', 'd_pov7iwlccr3', 'd_pmmtcja1k11', 'd_pkw6hk0knle', 'd_p1pv5lhfreh'}
