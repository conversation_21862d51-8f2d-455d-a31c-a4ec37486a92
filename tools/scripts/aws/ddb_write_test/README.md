To test:
1. partial_csv.py will generate a 
2. The write_csv_to_queue.py file will write the messages to a sqs queue, where the 
messages are in the format:
```json
[
    "+7jnH3QuaNdEtVyx3EHgQp+0//U=",
    "23",
    "rfy_20211104",
    "[{\"id\": \"d_po9y9840zl0\", \"alg\": \"p\"}, {\"id\": \"d_pkw6hk0knle\", \"alg\": \"p\"}, {\"id\": \"d_ptzm2qh0gts\", \"alg\": \"p\"}, {\"id\": \"d_p7nv4v9mbyb\", \"alg\": \"p\"}, {\"id\": \"d_pkw6hjoi56o\", \"alg\": \"p\"}, {\"id\": \"d_ptvp1o1vpe5\", \"alg\": \"p\"}, {\"id\": \"d_pn437vt9a9v\", \"alg\": \"p\"}, {\"id\": \"d_pwptap5001q\", \"alg\": \"p\"}, {\"id\": \"d_pq38ltodqp0\", \"alg\": \"p\"}, {\"id\": \"d_pmhvgm3ls7v\", \"alg\": \"p\"}, {\"id\": \"d_ppprm4mwnv1\", \"alg\": \"p\"}, {\"id\": \"d_pcq2yxyb8cl\", \"alg\": \"p\"}, {\"id\": \"d_pz910any5qe\", \"alg\": \"p\"}, {\"id\": \"d_pkzjrc8kx36\", \"alg\": \"p\"}, {\"id\": \"d_pmlyes78rdg\", \"alg\": \"p\"}, {\"id\": \"d_pw6s85w1izw\", \"alg\": \"p\"}, {\"id\": \"d_pnab6516gaq\", \"alg\": \"p\"}, {\"id\": \"d_prijs9p6m0r\", \"alg\": \"p\"}, {\"id\": \"d_prmg046bvl0\", \"alg\": \"p\"}, {\"id\": \"d_p1alk8ft8iy\", \"alg\": \"p\"}]"
]
```
and the fields are ['userId', 'size', 'modelKey', 'recommendations']

3. The messages in the queue are picked up by the lambda_handler.py, which makes use of
the ddb_maximise_throughput.py's DDBMaximiseThroughput class.
