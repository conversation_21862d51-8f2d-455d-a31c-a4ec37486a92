import os

import boto3

ENVIRONMENT = os.environ["ENVIRONMENT"]
REGION = os.environ["REGION"]

if ENVIRONMENT in boto3.session.Session().available_profiles:
    print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
    boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
else:
    print(f"Matching profile NOT found in config, using default profile")

sns_client = boto3.client("sns", region_name=REGION)

topic_list = []
topics_response = sns_client.list_topics()
for topic in topics_response["Topics"]:
    topic_list.append(topic["TopicArn"])

while topics_response.get("NextToken") is not None:
    next_token = topics_response.get("NextToken")
    topics_response = sns_client.list_topics(
        NextToken=next_token
    )
    for topic in topics_response["Topics"]:
        topic_list.append(topic["TopicArn"])

subscription_list = []
subscriptions_response = sns_client.list_subscriptions()
for subscription in subscriptions_response["Subscriptions"]:
    subscription_list.append(subscription)

while subscriptions_response.get("NextToken") is not None:
    next_token = subscriptions_response["NextToken"]
    subscriptions_response = sns_client.list_subscriptions(
        NextToken=next_token
    )
    for subscription in subscriptions_response["Subscriptions"]:
        subscription_list.append(subscription)
    next_token = subscriptions_response.get("NextToken")

subscription_count = len(subscription_list)
delete_count = 0

for subscription in subscription_list:
    if subscription["TopicArn"] not in topic_list:
        print(f"Deleting subscription {subscription['SubscriptionArn']} with {subscription['TopicArn']}")
        sns_client.unsubscribe(
            SubscriptionArn=subscription['SubscriptionArn']
        )
        delete_count += 1

print(f"Deleted {delete_count} unassociated subscriptions out of {subscription_count} subscriptions")

for topic in topic_list:
    topic_attributes = sns_client.get_topic_attributes(
        TopicArn=topic
    )

    if topic_attributes['Attributes'].get('SubscriptionsConfirmed') == '0':
        print(f"Deleting {topic} as no associated subscriptions")
        sns_client.delete_topic(
            TopicArn=topic
        )
