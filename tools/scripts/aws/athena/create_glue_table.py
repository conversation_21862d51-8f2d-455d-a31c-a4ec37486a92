import json
import argparse
import boto3

parser = argparse.ArgumentParser(
    description="Makes a metadata Glue table for a provided ndjson file"
)

parser.add_argument(
    "-f",
    help="A path to an ndjson file which we want to represent with a glue table in S3.",
    default="",
    required=True,
)

parser.add_argument(
    "-b",
    help="The bucket path the table should point to",
    default="",
    required=True,
)

parser.add_argument(
    "-d",
    help="The glue database we create the metadata table in",
    default="",
    required=True,
)

parser.add_argument(
    "--tableName", "-n",
    help="The name of the table to create",
    default="",
    required=True,
)

args = parser.parse_args()

key_types = {}

with open(args.f, encoding="utf-8") as f:
    for line in f.readlines():
        item = json.loads(line)

        for key, value in item.items():
            if value:
                t = str(type(value).__name__)

                if not key_types.get(key):
                    key_types[key] = []

                if t not in key_types[key]:
                    key_types[key].append(t)

columns = []
for key, value in key_types.items():
    t = {
        "str": "string",
        "bool": "boolean",
        "list": "array<string>",
        "int": "int"
    }[value[0]]

    column = {
        'Name': key.lower(),
        'Type': t,
        'Comment': ""
    }

    columns.append(column)

client = boto3.client('glue')

table_input = {
    'Name': args.tableName,
    'StorageDescriptor': {
        'Columns': columns,
        "Location": args.b,
        'InputFormat': 'org.apache.hadoop.mapred.TextInputFormat',
        'OutputFormat': 'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat',
        'SerdeInfo': {
            'SerializationLibrary': 'org.openx.data.jsonserde.JsonSerDe',
            "Parameters": {
                "paths": ",".join([c["Name"] for c in columns])
            }
        }
    }
}

print(json.dumps(table_input))

response = client.create_table(
    DatabaseName=args.d,
    TableInput=table_input
)

print(response)
