import argparse

import boto3.session

from thefilter.aws.s3 import S3AthenaQueryResultsPolicy
from thefilter.config.aws_accounts import environments

parser = argparse.ArgumentParser(
    description="Applies S3 bucket deletion policy to all relevant buckets"
)

parser.add_argument(
    "--environment", "-e",
    help="The environment you wish to apply the policy to, e.g. feature, pre, production",
    required=True,
)

args = parser.parse_args()

ENVIRONMENT = str(args.environment).lower()

if __name__ == '__main__':
    if ENVIRONMENT in boto3.session.Session().available_profiles:
        print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
        boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
    else:
        print(f"Matching profile NOT found in config, using default profile")
        exit()

    for region in environments[ENVIRONMENT]:
        s3_deletion_policy_client = S3AthenaQueryResultsPolicy(ENVIRONMENT, region)
        s3_deletion_policy_client.apply_deletion_policy()
