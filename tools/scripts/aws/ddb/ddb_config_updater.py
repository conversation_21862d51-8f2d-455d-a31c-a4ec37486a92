import json
import os

from thefilter.aws.dynamoDB import ConfigRepositoryDDBClient
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import PrintLogClient

# CONFIG_NAME could be customer_config

CONFIG_NAME = os.environ["CONFIG_NAME"]
REGION = os.environ["REGION"]
ENVIRONMENT = os.environ["ENVIRONMENT"]

logger = PrintLogClient()

config_api = CentralisedConfig(
    REGION, ENVIRONMENT, logger, repo_type="ddb"
)

ddb_config_repository = ConfigRepositoryDDBClient(
    region=REGION,
    environment=ENVIRONMENT,
    logger=logger
)

dir_path = os.path.dirname(os.path.abspath(__file__))
config_file_path = os.path.join(dir_path, "../../../../thefilter/config/centralised_config.json")
with open(config_file_path, 'r') as json_file:
    config_json = json.load(json_file)

print(config_json)

ddb_config_repository.write_config(key=CONFIG_NAME, data=json.dumps(config_json))

print("complete!")
