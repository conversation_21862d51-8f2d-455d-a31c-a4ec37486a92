import os
from datetime import datetime

import boto3

from thefilter.aws.dynamoDB import DDBSlotsRepository
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import Print<PERSON>ogClient
from thefilter.repositories import SlotsRepository, Slot


def has_slots(slot_repo: SlotsRepository) -> bool:
    return len(slot_repo.scan_slots()) > 0


def refactor_slot_structure(slot_repo: SlotsRepository):
    slots = slot_repo.scan_slots()
    updated_slots = []
    for slot in slots:
        updated_slot = slot_repo.refactor_slot(Slot(**slot))
        updated_slot.lastUpdatedBy = "slot_refactor_script"
        updated_slot.dateUpdated = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S+00:00")

        updated_slots.append(updated_slot)
        # Comment this in to refactor existing slots
        # slot_repo.write_slot(updated_slot)
    # print(updated_slots)


REGION = os.environ["REGION"].lower()
ENVIRONMENT = os.environ["ENVIRONMENT"].lower()

if __name__ == '__main__':
    customer_config = CentralisedConfig(REGION, ENVIRONMENT, PrintLogClient())
    customers = customer_config.get_customer_names()
    if ENVIRONMENT in boto3.session.Session().available_profiles:
        print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
        boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
    else:
        print(f"Matching profile NOT found in config, exiting...")
        exit(1)

    for customer in customers:
        ddb_slot_repo = DDBSlotsRepository(
            region=REGION,
            environment=ENVIRONMENT,
            customer=customer
        )

        if has_slots(ddb_slot_repo):
            print(f"Refactoring all slots for {customer}")
            refactor_slot_structure(ddb_slot_repo)
