import os

import boto3

from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import PrintLogClient

REGION = os.environ["REGION"].lower()
ENVIRONMENT = os.environ["ENVIRONMENT"].lower()

if __name__ == '__main__':
    customer_config = CentralisedConfig(REGION, ENVIRONMENT, PrintLogClient())
    customers = customer_config.get_customer_names()
    if ENVIRONMENT in boto3.session.Session().available_profiles:
        print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
        boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
    else:
        print(f"Matching profile NOT found in config, exiting...")
        exit(1)

    ddb_client = boto3.client("dynamodb", region_name=REGION)
    for customer in customers:
        list_response = ddb_client.list_tables(
            ExclusiveStartTableName=f"{ENVIRONMENT}-{customer}",
            Limit=50
        )

        table_name = f"{ENVIRONMENT}-{customer}-info"

        if table_name in list_response["TableNames"]:
            response = ddb_client.update_table(
                TableName=table_name,
                DeletionProtectionEnabled=False
            )

            print(f"Disable deletion protection for {table_name}")
        else:
            print(f"Table not found for {table_name}")