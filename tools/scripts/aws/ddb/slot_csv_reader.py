import argparse
import csv
import json

parser = argparse.ArgumentParser(
    description="Transforms a CSV dump from DynamoDB into a list of models and parameters"
)

parser.add_argument(
    "--source_file", "-f",
    help="The CSV dump file to transform",
    required=True,
)

args = parser.parse_args()

source_file = args.source_file

slot_definitions = {}

with open(source_file) as slots_csv:
    csv_data = csv.reader(slots_csv, delimiter=',')
    headers = next(csv_data)

    for row in csv_data:
        slot_def = json.loads(row[10].replace("\\n", "").replace('""', '"')[1:-1])
        name = slot_def["name"]
        type = slot_def.get("type", "").lower()
        models = []
        parameters = {}
        for experiment in slot_def["experiments"]:
            for model in experiment["modelDefinitions"]:
                models.append(model["source"])
                if type not in ["browse", "search"] and name.lower() not in ["browse",
                                                                             "search"]:
                    parameters[model["source"]] = model.get("parameters", {})

        slot_definitions[f"{row[0]}_{row[1]}"] = {
            "name": name,
            "models": models,
            "parameters": parameters
        }

print(json.dumps(slot_definitions, indent=2, sort_keys=True))
