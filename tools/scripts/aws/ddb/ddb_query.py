import argparse
import boto3
from boto3.dynamodb.conditions import Key
from datetime import datetime


def get_active_promotions(dynamodb: boto3.dynamodb) -> None:
    current_datetime = datetime.utcnow().strftime("%Y-%m-%dT%H:00:00+00:00")
    chart_table = dynamodb.Table(f"{ENVIRONMENT}-{CUSTOMER}-promotion")
    response = chart_table.scan(
        FilterExpression=Key('startDate').lt(current_datetime) and Key('endDate').gt(current_datetime),
        Select='SPECIFIC_ATTRIBUTES',
        ProjectionExpression='promotionId, promotionName'
    )

    data = response['Items']
    print(data)


def get_latest_chart(dynamodb: boto3.dynamodb) -> None:
    chart_creation_time = datetime.utcnow().strftime("%Y-%m-%dT%H")
    chart_table = dynamodb.Table(f"{ENVIRONMENT}-{CUSTOMER}-chart")
    response = chart_table.scan(
        FilterExpression=Key('created').gt(chart_creation_time),
        Select='SPECIFIC_ATTRIBUTES',
        ProjectionExpression='ddbId, created, chart'
    )

    data = response['Items']

    while 'LastEvaluatedKey' in response:
        response = chart_table.scan(
            ExclusiveStartKey=response['LastEvaluatedKey'],
            FilterExpression=Key('created').gt(chart_creation_time),
            Select='SPECIFIC_ATTRIBUTES',
            ProjectionExpression='ddbId, created, chart'
        )
        data.extend(response['Items'])

    print(data)
    print(f"Chart length: {len(data)}")


def get_latest_chart_item(dynamodb: boto3.dynamodb, chart_ddbId: str) -> None:
    chart_creation_time = datetime.utcnow().strftime("%Y-%m-%dT%H")
    chart_table = dynamodb.Table(f"{ENVIRONMENT}-{CUSTOMER}-chart")
    response = chart_table.query(
        KeyConditionExpression = Key('ddbId').eq(chart_ddbId) & Key('created').gt(chart_creation_time),
        Select='SPECIFIC_ATTRIBUTES',
        ProjectionExpression='ddbId, created, chart'
    )

    data = response['Items']

    print(data)


def get_user_history(dynamodb: boto3.dynamodb, user_id: str) -> None:
    user_history_table = dynamodb.Table(f"{ENVIRONMENT}-{CUSTOMER}-user_history")
    response = user_history_table.query(
        KeyConditionExpression=Key('userId').eq(user_id),
        ScanIndexForward=False,
        Limit=25
    )

    data = response['Items']

    print(data)


parser = argparse.ArgumentParser(
    description="Performs a selected dynamodb query"
)

parser.add_argument(
    "--region", "-r",
    help="The region the dynamoDB table we wish to query resides in",
    required=True
)

parser.add_argument(
    "--customer", "-c",
    help="The customer's dynamoDB table we wish to query",
    required=True
)

parser.add_argument(
    "--environment", "--env", "-e",
    help="The environment we wish to query",
    required=True
)

args = parser.parse_args()

REGION = str(args.region).lower()
CUSTOMER = str(args.customer).lower()
ENVIRONMENT = str(args.environment).lower()

if __name__ == '__main__':
    dynamodb = boto3.resource('dynamodb', region_name=REGION)

    query_type = input(
        "Please select a query type to run:\n"
        "  1: Get all latest chart\n"
        "  2: Get latest specific chart\n"
        "  3: Get user history for specific user\n"
        "  4: Get list of active promotions for this customer\n"
   )

    query_type = int(query_type)
    if query_type == 1:
        get_latest_chart(dynamodb)
    elif query_type == 2:
        chart_ddbId = input("Which chart_ddbId would you like to retrieve?\n")
        get_latest_chart_item(dynamodb, str(chart_ddbId))
    elif query_type == 3:
        user_id = input("Which user_id would you like to retrieve history for?\n")
        get_user_history(dynamodb, str(user_id))
    elif query_type == 4:
        get_active_promotions(dynamodb)
    else:
        print("Invalid choice, exiting")
        exit(1)
