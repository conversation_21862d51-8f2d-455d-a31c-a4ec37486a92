#!/usr/bin/env python3
import os

import boto3
from boto3.dynamodb.conditions import Attr
from botocore.exceptions import ClientError

KEY_FIELDS = ["customer", "slotId"]

def scan_items():
    """Yield items whose 'type' attribute is 'cw'."""
    kwargs = {
        "FilterExpression": Attr("type").eq("cw"),
        "ProjectionExpression": ", ".join(set(KEY_FIELDS + ["#type"])),  # keep it lean
        "ExpressionAttributeNames": {"#type": "type"}
    }
    while True:
        resp = table.scan(**kwargs)
        for item in resp.get("Items", []):
            yield item
        if "LastEvaluatedKey" not in resp:
            break
        kwargs["ExclusiveStartKey"] = resp["LastEvaluatedKey"]

def update_item(keys: dict):
    """Set zeroResultsOK = true on a single item."""
    print("Updating zeroResultsOK on CW for item:", keys)
    try:
        table.update_item(
            Key=keys,
            UpdateExpression="SET zeroResultsOk = :z",
            ExpressionAttributeValues={":z": True},
            ConditionExpression=Attr("type").eq("cw")  # protects against race conditions
        )
    except ClientError as e:
        # Ignore ConditionalCheckFailed (item changed between scan & update), re-raise others
        if e.response["Error"]["Code"] != "ConditionalCheckFailedException":
            raise

def update_cw_slots():
    count = 0
    for item in scan_items():
        # Build the key dict (only the key attributes!)
        key = {k: item[k] for k in KEY_FIELDS}
        update_item(key)
        count += 1
        if count % 50 == 0:
            print(f"Updated {count} items…")
    print(f"Done. Updated {count} items.")

REGION = os.environ.get("REGION")
ENVIRONMENT = os.environ.get("ENVIRONMENT")

if not REGION:
    print("Error: The 'REGION' environment variable is not set.")
    exit(1)
if not ENVIRONMENT:
    print("Error: The 'ENVIRONMENT' environment variable is not set.")
    exit(1)

REGION = REGION.lower()
ENVIRONMENT = ENVIRONMENT.lower()

TABLE_NAME = f"{ENVIRONMENT}-slots"

if __name__ == '__main__':
    if ENVIRONMENT in boto3.session.Session().available_profiles:
        print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
        boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
    else:
        print(f"Matching profile NOT found in config, exiting...")
        exit(1)

    dynamodb = boto3.resource("dynamodb", region_name=REGION)
    table = dynamodb.Table(TABLE_NAME)

    print(f"Updating cw slots")
    update_cw_slots()
