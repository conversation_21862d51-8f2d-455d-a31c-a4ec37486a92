import json
import os
import uuid
from copy import deepcopy
from datetime import datetime

import boto3

from thefilter.aws.dynamoDB import DDBSlotsRepository
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import PrintLogClient
from thefilter.repositories import SlotsRepository, Slot


def has_slots(slot_repo: SlotsRepository) -> bool:
    return len(slot_repo.scan_slots()) > 0


def create_most_popular_slots(slot_repo: SlotsRepository):
    slots = slot_repo.scan_slots()
    for slot in slots:
        # Move initial most popular to 7 days, and create 14 day and 30 day versions
        # using the initial one as a base
        if slot["type"] == "most_popular" or "most popular" in slot["slotName"].lower():
            initial_slot = Slot(**slot)
            initial_slot.slotName = "Most Popular (7 days)"
            initial_slot.description = "Most Popular Chart - 7 days"
            initial_slot.lastUpdatedBy = "create_bs_mp_slots.py"
            initial_slot.dateUpdated = datetime.utcnow().strftime(
                "%Y-%m-%dT%H:%M:%S+00:00")

            # Overwrite experiments
            initial_slot.experiments = [
                {
                    'id': str(uuid.uuid4()),
                    'userPercentage': 100,
                    'isBaseRecipe': True,
                    'size': 12,
                    'title': 'Most Popular (7 days)',
                    'notes': 'Autogenerated Most Popular 7 day slot',
                    'modelDefinitions': [
                        {
                            'key': 'most-popular-week',
                            'version': 1,
                            'source': 'NamedChart',
                            'fulfilment': {
                                'ranges': [{'start': 0, 'end': 100}]
                            },
                            "parameters": {
                                "chartName": "most-popular-week",
                                "use_recommendation_id": True
                            }
                        }
                    ]
                }
            ]

            fourteen_day_slot = copy_most_popular_slot(initial_slot,
                                                       "Most Popular (14 days)")
            thirty_day_slot = copy_most_popular_slot(initial_slot,
                                                     "Most Popular (30 days)")

            # Comment this in to refactor existing slots
            # slot_repo.write_slot(initial_slot)
            # slot_repo.write_slot(fourteen_day_slot)
            # slot_repo.write_slot(thirty_day_slot)
            break


def copy_most_popular_slot(slot: Slot, slot_name: str) -> Slot:
    copied_slot = deepcopy(slot)
    copied_slot.slotId = str(uuid.uuid4())
    copied_slot.slotName = slot_name
    copied_slot.description = slot_name

    if "14" in slot_name:
        copied_slot.experiments = [
            {
                'id': str(uuid.uuid4()),
                'userPercentage': 100,
                'isBaseRecipe': True,
                'size': 12,
                'title': 'Most Popular (14 days)',
                'notes': 'Autogenerated Most Popular 14 day slot',
                'modelDefinitions': [
                    {
                        'key': 'most-popular-fortnight',
                        'version': 1,
                        'source': 'NamedChart',
                        'fulfilment': {
                            'ranges': [{'start': 0, 'end': 100}]
                        },
                        "parameters": {
                            "chartName": "most-popular-fortnight"
                        }
                    }
                ]
            }
        ]
    else:
        copied_slot.experiments = [
            {
                'id': str(uuid.uuid4()),
                'userPercentage': 100,
                'isBaseRecipe': True,
                'size': 12,
                'title': 'Most Popular (30 days)',
                'notes': 'Autogenerated Most Popular 30 day slot.',
                'modelDefinitions': [
                    {
                        'key': 'most-popular-month',
                        'version': 1,
                        'source': 'NamedChart',
                        'fulfilment': {
                            'ranges': [{'start': 0, 'end': 100}]
                        },
                        "parameters": {
                            "chartName": "most-popular-month"
                        }
                    }
                ]
            }
        ]

    return copied_slot


REGION = os.environ["REGION"].lower()
ENVIRONMENT = os.environ["ENVIRONMENT"].lower()

if __name__ == '__main__':
    customer_config = CentralisedConfig(REGION, ENVIRONMENT, PrintLogClient())
    backstage_customers = customer_config.get_backstage_customer_names()
    if ENVIRONMENT in boto3.session.Session().available_profiles:
        print(f"Found matching profile in config, using profile named {ENVIRONMENT}")
        boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
    else:
        print(f"Matching profile NOT found in config, exiting...")
        exit(1)

    for customer in backstage_customers:
        ddb_slot_repo = DDBSlotsRepository(
            region=REGION,
            environment=ENVIRONMENT,
            customer=customer
        )

        if has_slots(ddb_slot_repo):
            print(f"Creating multiple most popular slots for {customer}")
            create_most_popular_slots(ddb_slot_repo)
