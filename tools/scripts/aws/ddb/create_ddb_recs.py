from copy import deepcopy

import boto3

user_ids = [
    'vV6Fa0XSCfhih0pa18KIFuxr3lo=',
    'yKJhjlhtFJugSu+JgMfSnvLSs9c=',
    'Ec50znWSSPbSRBC+AO51DX4Rtug=',
    '8CCwgquB19fXMVhe+y9iql9rz5s='
]

recs = [
    {
        "userId": {
            "S": "[USER_ID]_06:00:00-16:00:00"
        },
        "model": {
            "S": "rfy_20220322_hero"
        },
        "recommended": {
            "S": "[{\"id\": \"b_pvsbhvzx4wl\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_pd7vfii8pho\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p28myra1eo4\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_pmn1vuugcby\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p66qs6xt9eu\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p2iazavclqx\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p28n7xpshj9\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"d_p1al9svet19\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p1bb983fqwn\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p8mibe3lg36\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}]"
        }
    },
    {
        "userId": {
            "S": "[USER_ID]_16:00:00-06:00:00"
        },
        "model": {
            "S": "rfy_20220322_hero"
        },
        "recommended": {
            "S": "[{\"id\": \"b_p2ekbhtfnwb\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"d_pw19kstovdf\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_py64sha7pbc\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"b_pvsbhvzx4wl\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_pd7vfii8pho\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p28myra1eo4\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_pmn1vuugcby\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p66qs6xt9eu\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p2iazavclqx\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p28n7xpshj9\", \"typeName\": \"Movie\", \"alg\": \"p\"}]"
        }
    },
    {
        "userId": {
            "S": "[USER_ID]_free_16:00:00-06:00:00"
        },
        "model": {
            "S": "rfy_20220314_hero"
        },
        "recommended": {
            "S": "[{\"id\": \"d_p13tbpxg57q\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pz910any5qe\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pkvar2whyrd\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_paqgugx3tgm\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_po9y9840zl0\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p28n2c8ecm6\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pl2tpcl1t65\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_poe2qo59awc\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p4r7icvtp18\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p4cfgho3taa\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}]"
        }
    },
    {
        "userId": {
            "S": "[USER_ID]_free_06:00:00-16:00:00"
        },
        "model": {
            "S": "rfy_20220314_hero"
        },
        "recommended": {
            "S": "[{\"id\": \"d_pn437vt9a9v\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pql9nlisfvq\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p28ndtm587k\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p13tbpxg57q\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pz910any5qe\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pkvar2whyrd\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_paqgugx3tgm\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_po9y9840zl0\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p28n2c8ecm6\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pl2tpcl1t65\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}]"
        }
    },
    {
        "userId": {
            "S": "[USER_ID]"
        },
        "model": {
            "S": "rfy_20220322"
        },
        "recommended": {
            "S": "[{\"id\": \"b_pmn1vuugcby\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p66qs6xt9eu\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p2iazavclqx\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p28n7xpshj9\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"d_p1al9svet19\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p1bb983fqwn\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p8mibe3lg36\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pwl1lsfqong\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"b_p6uhbrpz2ha\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"d_p25hbg0wart\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"b_p5fdkcpkg2i\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_ppeq57pjj1i\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p1je32ol0bd\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"b_p9m6sbcpanf\", \"typeName\": \"Movie\", \"alg\": \"p\"}, {\"id\": \"d_p1akpifn7yg\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p20ag559w0e\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}]"
        }
    },
    {
        "userId": {
            "S": "[USER_ID]_free"
        },
        "model": {
            "S": "rfy_20220314"
        },
        "recommended": {
            "S": "[{\"id\": \"d_paqgugx3tgm\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_po9y9840zl0\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p28n2c8ecm6\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pl2tpcl1t65\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_poe2qo59awc\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p4r7icvtp18\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p4cfgho3taa\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_ppprm4mwnv1\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pwptap5001q\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pga67b8sr1g\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pm01i56ufbs\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_prijs9p6m0r\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pkqqvfpiri1\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_p7987ub2rsb\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pl2tze8am3o\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pl2twtxzrhw\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}, {\"id\": \"d_pme8a9gjty1\", \"typeName\": \"TVSeries\", \"alg\": \"p\"}]"
        }
    }
]

region = boto3.session.Session().region_name or "eu-west-2"

ddb_client = boto3.client(
    service_name="dynamodb",
    region_name="eu-west-1"
)

# Was used for a previous customer, though the script is sound
table_name = "{env}-{customer}-recommendation"

print(ddb_client.describe_table(TableName=table_name))

for user_id in user_ids:
    for messageDDB in recs:
        copied_message = deepcopy(messageDDB)
        copied_message["userId"]["S"] = copied_message["userId"]["S"].replace("[USER_ID]", user_id)

        ddb_client.put_item(TableName=table_name, Item=copied_message)
