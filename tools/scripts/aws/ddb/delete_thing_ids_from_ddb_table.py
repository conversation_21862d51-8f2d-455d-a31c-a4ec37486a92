import csv
from typing import List

import boto3


def get_thing_ids(csv_file_path: str):
    thing_ids = []

    with open(csv_file_path, mode='r', newline='') as file:
        reader = csv.reader(file)
        next(reader)  # Skip the header

        for row in reader:
            thing_ids.append(row[0])
    return thing_ids


def delete_all_items(thing_ids: List[str], table_name: str):
    dynamodb = boto3.resource('dynamodb')
    table = dynamodb.Table(table_name)

    # Delete each item
    count = 0

    with table.batch_writer() as batch:
        for item in thing_ids:
            if count % 5000 == 0:
                print(f"Deleted {count} items")

            batch.delete_item(
                Key={
                    'thingId': item
                }
            )
            count += 1


# Replace the following two parameters:
csv_file_path = 'filepath.csv' # csv of thing ids to delete
table_name = "tablename" # replace tablename with the name of your table

items = get_thing_ids(csv_file_path)
delete_all_items(items, table_name)
