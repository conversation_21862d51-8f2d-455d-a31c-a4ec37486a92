from urllib.parse import unquote
import json
import boto3
from boto3.dynamodb.conditions import Key
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import NoOpLogger

environment = "feature"
region = "eu-west-2"
dynamodb = boto3.resource('dynamodb', region_name=region)

logger = NoOpLogger()
config = CentralisedConfig(
    region=region,
    environment=environment,
    logger=logger,
    repo_type="local"
)

customers_in_env = config.get_customer_names_by_environment(environment)

customers_in_region = config.get_customer_names_by_region(region)

all_customers = [c for c in customers_in_env if c in customers_in_region]

for customer in all_customers:
    table_name = f"{environment}-{customer}-information"
    table = dynamodb.Table(table_name)

    response = table.query(
        KeyConditionExpression=Key('domain').eq('model')
    )
    items = response.get('Items', [])

    while 'LastEvaluatedKey' in response:
        response = table.query(
            KeyConditionExpression=Key('domain').eq('model'),
            ExclusiveStartKey=response['LastEvaluatedKey']
        )
        items.extend(response.get('Items', []))

    with table.batch_writer() as batch:
        for item in items:
            topic  = item.get("topic")
            unquoted_topic = unquote(unquote(topic))
            if topic != unquoted_topic:
                item["topic"] = unquoted_topic
                table.delete_item(Key={"domain":"model","topic":topic})
            if "deletable" not in item["value"].keys():
                item["value"]["deletable"] = False
            else:
                pass
            item["value"]["owner"] = "24iQ"

            batch.put_item(Item=item)

    print(f"Updated 'deletable' field for {customer}")
