import argparse
import json
from decimal import Decimal
from typing import Optional, List

import boto3
from boto3.dynamodb.conditions import Key


class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return json.JSONEncoder.default(self, obj)


class DDBDumpNUpper:
    def __init__(self,
                 destination_environment: str,
                 destination_region: str,
                 source_environment: str,
                 source_region: str,
                 customer: str,
                 table: str,
                 centralised: bool,
                 override: bool,
                 domain: Optional[str] = None
                 ):

        self._destination_region = destination_region
        self._source_region = source_region
        self._customer = customer
        self._centralised = centralised
        self._override = override
        self._domain = domain
        self._source_environment_name = source_environment
        self._destination_environment_name = destination_environment

        if centralised:
            self._source_table_name = f"{source_environment}-{table}"
            self._destination_table_name = f"{destination_environment}-{table}"
        else:
            self._source_table_name = f"{source_environment}-{customer}-{table}"
            self._destination_table_name = f"{destination_environment}-{customer}-{table}"

        self._source_env = boto3.session.Session(profile_name=source_environment)
        self._destination_environment_env = boto3.session.Session(
            profile_name=destination_environment)

    @staticmethod
    def _clean_null_terms(d: dict):
        return {k: v for k, v in d.items() if v is not None}

    def dump_source_items(self):
        print(
            f"Dumping source items from {self._source_table_name} in {self._source_region}")
        dynamodb = self._source_env.resource('dynamodb', region_name=self._source_region)
        table = dynamodb.Table(self._source_table_name)

        if self._centralised and not self._override:
            response = table.query(
                KeyConditionExpression=Key('customer').eq(self._customer)
            )
        elif TABLE == "information" and DOMAIN:
            response = table.query(
                KeyConditionExpression=Key('domain').eq(DOMAIN)
            )
        else:
            response = table.scan()

        data = response['Items']

        while 'LastEvaluatedKey' in response:
            response = table.scan(ExclusiveStartKey=response['LastEvaluatedKey'])
            data.extend(response['Items'])

        print(f"Dumped {len(data)} items")

        with open(f"{self._source_table_name}.json", 'w') as writer:
            writer.write(json.dumps(data, cls=DecimalEncoder))

    def upsert_items_to_destination(self):
        print(
            f"Upserting items to {self._destination_table_name} in {self._destination_region}")

        dynamodb = self._destination_environment_env.resource('dynamodb',
                                                              region_name=self._destination_region)
        table = dynamodb.Table(self._destination_table_name)

        with open(f"{self._source_table_name}.json", 'r') as f:
            data = json.load(f, parse_float=Decimal)

            table_name_check = f"{self._source_environment_name}-{self._customer}-information"
            if self._source_table_name == table_name_check and self._domain == "model":
                data = self._update_model_environments(data,
                                                       self._source_environment_name,
                                                       self._destination_environment_name)

            with table.batch_writer() as batch:
                for record in data:
                    batch.put_item(Item=self._clean_null_terms(record))

    @staticmethod
    def _update_model_environments(
            data: List[dict], source_environment: str, destination_environment: str
    ) -> List[dict]:
        for i in data:
            value = i["value"]
            slot_url = value["slot_url"]
            updated_url = slot_url.replace(source_environment, destination_environment)
            value["slot_url"] = updated_url

        return data


parser = argparse.ArgumentParser(
    description="Performs a DDB table data migration "
                "from one environment to another "
)

parser.add_argument(
    "--destination_environment", "-de",
    help="The destination environment we wish to post to",
    required=True
)

parser.add_argument(
    "--destination_region", "-dr",
    help="The destination region the dynamoDB table we wish to post to",
    default="eu-west-2",
    required=False
)

parser.add_argument(
    "--source_environment", "-se",
    help="The source environment we wish to query",
    default="pre",
    required=False
)

parser.add_argument(
    "--source_region", "-sr",
    help="The source region the dynamoDB table we wish to query resides in"
         "defaults to eu-west-2",
    default="eu-west-2",
    required=False
)

parser.add_argument(
    "--customer", "-c",
    help="The customer's whose data we wish to migrate",
    default="",
    required=True
)

parser.add_argument(
    "--table", "-t",
    help="The dynamoDB table we wish to migrate",
    default="",
    required=True
)

parser.add_argument(
    "--domain", "-d",
    help="For the information table, a domain must be supplied",
    default="",
    required=False
)

parser.add_argument(
    "--override",
    help="Override centralised table behaviour, and copy it all",
    const=True,
    default=False,
    nargs="?",
    required=False
)

if __name__ == "__main__":
    args = parser.parse_args()

    centralised_tables = [
        "model_config",
        "slots",
        "models",
        "page",
        "rec_filters"
    ]

    SOURCE_ENVIRONMENT = str(args.source_environment).lower()
    SOURCE_REGION = str(args.source_region).lower()

    DESTINATION_ENVIRONMENT = str(args.destination_environment).lower()
    DESTINATION_REGION = str(args.destination_region).lower()
    CUSTOMER = str(args.customer).lower()
    TABLE = str(args.table).lower()
    DOMAIN = str(args.domain).lower()  # Only used for info table

    CENTRALISED = TABLE in centralised_tables

    if TABLE == "information" and not DOMAIN:
        print("Info table needs a domain specified for copying, exiting...")
        exit(1)

    ddb_dumpnupper = DDBDumpNUpper(
        destination_environment=DESTINATION_ENVIRONMENT,
        destination_region=DESTINATION_REGION,
        source_region=SOURCE_REGION,
        source_environment=SOURCE_ENVIRONMENT,
        customer=CUSTOMER,
        table=TABLE,
        centralised=CENTRALISED,
        override=bool(args.override),
        domain=DOMAIN
    )

    ddb_dumpnupper.dump_source_items()

    ddb_dumpnupper.upsert_items_to_destination()
