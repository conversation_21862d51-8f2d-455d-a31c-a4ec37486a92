import json
import boto3

from thefilter.config.aws_accounts import account_map


def create_eventbridge_mlt_rule(
        customer_name: str, environment: str, region: str, model_date: str
):
    account_number = account_map[environment]

    client = boto3.client('events', region_name=region)

    rule_name = f"{customer_name}-MLT"
    description = f"Customer:{customer_name}|Job:more_like_this|Model:mlt_{model_date}"

    # Mon, Wed and Fri at 12:45:00 UTC
    schedule_expression = "cron(45 12 ? * 2,4,6 *)"
    target_name = f"{environment}-{customer_name}-RecsToDDB"
    target_arn = f"arn:aws:lambda:{region}:{account_number}:function:{target_name}"

    input_transformer = {
        "InputPathsMap": {
            "run_id": "$.id"
        },
        "InputTemplate": json.dumps({
            "run_id": "<run_id>",
            "customer": customer_name,
            "algorithm": "MLT-ddb-v0",
            "hyperParameters": {
                "environmentName": environment,
                "regionName": region,
                "model_version": f"mlt_{model_date}",
                "size": "50",
                "customer": customer_name,
                "model_type": "more_like_this",
                "MODEL_SERVER_WORKERS": "4",
                "MODEL_SERVER_TIMEOUT": "120"
            },
            "trainingImage": f"{account_number}.dkr.ecr.{region}.amazonaws.com/awssagemaker/generic_training:latest",
            "trainingInstanceType": "ml.m5.4xlarge",
            "notificationTopic": f"arn:aws:sns:{region}:{account_number}:{environment}-{customer_name}-RecsToDDBSageMaker-Topic"
        })
    }

    client.put_rule(
        Name=rule_name,
        ScheduleExpression=schedule_expression,
        State='DISABLED',
        Description=description
    )

    client.put_targets(
        Rule=rule_name,
        Targets=[
            {
                'Id': target_name,
                'Arn': target_arn,
                'InputTransformer': input_transformer
            }
        ]
    )


create_eventbridge_mlt_rule('flowsports', 'pre', 'eu-west-2', '********')
