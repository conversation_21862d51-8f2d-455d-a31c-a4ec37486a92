import json

import boto3

from thefilter.config.aws_accounts import account_map


def create_eventbridge_rule(
        customer_name: str, environment: str, region: str, model_date: str
):
    account_number = account_map[environment]

    client = boto3.client('events', region_name=region)

    rule_name = f"{environment}-{customer_name}-CatalogueProcessor"
    description = f"Customer:{customer_name}|Job:catalogue_processor|Model:cat_{model_date}"

    # Every day at 10:00:00 UTC
    schedule_expression = "cron(00 10 ? * * *)"
    target_name = f"{customer_name}CatalogueProcessorJob"
    target_arn = f"arn:aws:batch:{region}:{account_number}:job-queue/DataScienceJobQueue"
    job_definition_arn = f"arn:aws:batch:{region}:{account_number}:job-definition/CatalogueProcessorJobDefinition"

    input_constant = {
        "ContainerOverrides": {
            "Command": ["train"],
            "Environment": [
                {"Name": "environment_name", "Value": environment},
                {"Name": "customer", "Value": customer_name},
                {"Name": "model_type", "Value": "catalogue_processor"},
                {"Name": "model_version", "Value": f"cat_{model_date}"}
            ]
        }
    }

    client.put_rule(
        Name=rule_name,
        ScheduleExpression=schedule_expression,
        State='DISABLED',
        Description=description
    )

    client.put_targets(
        Rule=rule_name,
        Targets=[
            {
                'Id': target_name,
                'Arn': target_arn,
                'RoleArn': f"arn:aws:iam::{account_number}:role/BatchEventRole",
                'BatchParameters': {
                    'JobDefinition': job_definition_arn,
                    'JobName': target_name
                },
                'Input': json.dumps(input_constant)
            }
        ]
    )


create_eventbridge_rule('flowsports', 'pre', 'eu-west-2', '********')
