These scripts create the basic cat_processor, mlt and rfy triggers

It sets the triggers up in a DISABLED state, with the following crons:
- cat_processor: # Every day at 10:00:00 UTC
- MLT: # Mon, Wed and Fri at 12:45:00 UTC
- RFY: Once a week on Mon at 12:15:00 UTC

Rerunning the scripts will override the triggers to the definition in the script

The model_date is in the format YYYYMMDD and should relate to 
those set in the model config.

Example commands:
```commandline
create_eventbridge_rule('flowsports', 'pre', 'eu-west-2', '20230808')

create_eventbridge_mlt_rule('flowsports', 'pre', 'eu-west-2', '20230808')

create_eventbridge_rfy_rule('flowsports', 'pre', 'eu-west-2', '20230808')
```