import json
import boto3

from thefilter.config.aws_accounts import account_map


def create_eventbridge_rfy_rule(
        customer_name: str, environment: str, region: str, model_date: str
):
    account_number = account_map[environment]

    client = boto3.client('events', region_name=region)

    rule_name = f"{customer_name}-RFY"
    description = f"Customer:{customer_name}|Job:recommended_for_you|Model:rfy_{model_date}"

    # Once a week on Mon at 12:15:00 UTC
    schedule_expression = "cron(15 12 ? * 2 *)"
    target_name = f"{environment}-{customer_name}-RecsToDDB"
    target_arn = f"arn:aws:lambda:{region}:{account_number}:function:{target_name}"

    input_transformer = {
        "InputPathsMap": {
            "run_id": "$.id"
        },
        "InputTemplate": json.dumps({
            "run_id": "<run_id>",
            "customer": customer_name,
            "algorithm": "RFY-ddb-v0",
            "hyperParameters": {
                "environmentName": environment,
                "regionName": region,
                "model_version": f"rfy_{model_date}",
                "size": "50",
                "customer": customer_name,
                "model_type": "for_you",
                "MODEL_SERVER_WORKERS": "4",
                "MODEL_SERVER_TIMEOUT": "120"
            },
            "trainingImage": f"{account_number}.dkr.ecr.{region}.amazonaws.com/awssagemaker/generic_training:latest",
            "trainingInstanceType": "ml.m5.4xlarge",
            "notificationTopic": f"arn:aws:sns:{region}:{account_number}:{environment}-{customer_name}-RecsToDDBSageMaker-Topic"
        })
    }

    client.put_rule(
        Name=rule_name,
        ScheduleExpression=schedule_expression,
        State='DISABLED',
        Description=description
    )

    client.put_targets(
        Rule=rule_name,
        Targets=[
            {
                'Id': target_name,
                'Arn': target_arn,
                'InputTransformer': input_transformer
            }
        ]
    )


create_eventbridge_rfy_rule('flowsports', 'pre', 'eu-west-2', '********')
