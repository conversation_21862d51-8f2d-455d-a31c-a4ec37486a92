import json

from thefilter.aws.glue import GlueTableDetails

"""Some glue scripts to check differences in schemas"""


def get_glue_details():
    region = 'eu-west-2'
    environment = 'pre'
    glue_api = GlueTableDetails(region, environment)
    table_name = "events"
    # view can be "field_name", "customer"
    view = ["field_name", "customer"][1]
    table_details = glue_api.get_table_diff(table_name, view)
    print(json.dumps(table_details, indent=2))

    print(
        f"Customers missing the {table_name} table: {glue_api.customers_missing_table}")
    # pprint(table_details)


def get_glue_table_schema():
    region = 'eu-west-2'
    environment = 'pre'
    customer = "broadwayhd"
    table_name = "events"

    glue_api = GlueTableDetails(region, environment)
    tables = glue_api._get_customer_tables(customer)
    table_schema = glue_api._get_table_schema(tables, table_name)
    print(json.dumps(table_schema, indent=2))

# get_glue_details()
