import json

from thefilter.aws.glue import UpdateGlueCrawler

"""Update the region, environment and the exclusion_paths to update the 
all crawlers that target the /data folder. 
"""
region = "eu-west-2"
environment = "feature"
exclusion_paths = ["user/**", "raw-events-processed/**"]

crawler_api = UpdateGlueCrawler(region, environment)
crawler_info = crawler_api.get_crawler_info()
print(json.dumps(crawler_info, indent=4))
updatable_crawlers = []
for crawler, details in crawler_info.items():
    if details["Path"].endswith("/data"):
        updatable_crawlers.append(crawler)

# These `updatable_crawlers` may already have the desired exclusion paths on them,
# luckily, the update_crawler_exclusion_path will take care of this!
print(updatable_crawlers)

# Uncomment the below to make changes
# for crawler in updatable_crawlers:
#     crawler_api.update_crawler_exclusion_path(crawler, exclusion_paths)
