import argparse
import time
import uuid

import boto3

parser = argparse.ArgumentParser(
    description='Arguments for the QS theme copy script'
)

parser.add_argument(
    '--theme_id', '-t',
    help='The theme_id you wish to copy , e.g. 69cffbf5-d03d-4cc9-9c4a-a6213e0b4ddb-us-east-1',
    required=True
)

parser.add_argument(
    '--environment_from', '-ef', '-e',
    help='The environment you wish to copy from, e.g. feature, pre, production',
    required=False,
    default="production"
)

parser.add_argument(
    '--region_from', '-rf', '-r',
    help='The region you wish to copy from, e.g. eu-west-2, us-east-1',
    required=True
)

parser.add_argument(
    '--environment_to', '-et',
    help='The environment you wish to copy to, e.g. feature, pre, production',
    required=False,
    default=None
)

parser.add_argument(
    '--region_to', '-rt',
    help='The region you wish to copy to, e.g. eu-west-2, us-east-1',
    required=False,
    default=None
)

parser.add_argument(
    '--users', '-u',
    help='The users you wish to apply permissions to, separated by commas e.g. <EMAIL>,<EMAIL>',
    required=False,
    default="<EMAIL>,<EMAIL>"
)

args = parser.parse_args()

THEME_ID = args.theme_id
ENV_FROM = args.environment_from
REG_FROM = args.region_from
ENV_TO = args.environment_to if args.environment_to else args.environment_from
REG_TO = args.region_to if args.region_to else args.region_from
USERS = args.users.split(",")

copy_client = boto3.client('quicksight', region_name=REG_FROM)
post_client = boto3.client('quicksight', region_name=REG_TO)

account_id = boto3.client('sts', region_name=REG_FROM).get_caller_identity().get('Account')

try:
    response = copy_client.describe_theme(
        AwsAccountId=account_id,
        ThemeId=THEME_ID
    )

    theme_config = response["Theme"]["Version"]["Configuration"]
    theme_name = response["Theme"]["Name"] + " (copy)"

    THEME_ID = str(uuid.uuid4())

    actions = ["quicksight:DescribeTheme",
               "quicksight:ListThemeVersions",
               "quicksight:DescribeThemeAlias",
               "quicksight:ListThemeAliases"]

    permissions = []
    for USER in USERS:
        principal = f"arn:aws:quicksight:eu-west-2:{account_id}:user/default/" + USER
        permissions.append({"Principal": principal, "Actions": actions})

    try:
        post_client.create_theme(
            AwsAccountId=account_id,
            ThemeId=THEME_ID,
            Name=theme_name,
            BaseThemeId="SEASIDE",
            Configuration=theme_config,
            Permissions=permissions
        )

        try:
            response = post_client.describe_theme(
                AwsAccountId=account_id,
                ThemeId=THEME_ID
            )

            while response["Theme"]["Version"]["Status"] == "CREATION_IN_PROGRESS":
                print("Theme creation in progress...")
                time.sleep(1)
                response = post_client.describe_theme(
                    AwsAccountId=account_id,
                    ThemeId=THEME_ID
                )

            if response["Theme"]["Version"]["Status"] == "CREATION_SUCCESSFUL":
                print(f"Successfully created theme:\n"
                      f"ID: {THEME_ID}\n"
                      f"Name: {theme_name}\n"
                      f"Created on: {ENV_TO} {REG_TO}\n"
                      f"Copied from: {ENV_FROM} {REG_FROM}")

            elif response["Theme"]["Version"]["Status"] == "CREATION_FAILED":
                errors = response["Theme"]["Version"]["Errors"]
                print("Creation failed. See below for errors.\n")
                for error in errors:
                    print(f"Error type: {error['Type']}\n"
                          f"Error message: {error['Message']}\n")

        except Exception as e:
            print(f"Couldn't describe new theme:\n"
                  f"ID: {THEME_ID}\n"
                  f"Environment: {ENV_TO}\n"
                  f"Region: {REG_TO}\n"
                  f"Error: {e}")

    except Exception as e:
        print(f"Couldn't create new theme:\n"
              f"ID: {THEME_ID}\n"
              f"Name: {theme_name}\n"
              f"Environment: {ENV_TO}\n"
              f"Region: {REG_TO}\n"
              f"Error: {e}")

except Exception as e:
    print(f"Couldn't describe original theme:\n"
          f"ID: {THEME_ID}\n"
          f"Environment: {ENV_FROM}\n"
          f"Region: {REG_FROM}\n"
          f"Error: {e}")
