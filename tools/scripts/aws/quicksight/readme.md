There are two scripts contained within this directory, `copy_qs_theme.py` and `copy_qs_analysis.py`.
These scripts were developed for Analytics to help speed up copying configurations between regions in QuickSight.

For `copy_qs_theme.py`:
+ This script allows copying of themes between regions for QS.
+ The arguments for the script are as follows:
```commandline
--theme_id / -t: The theme id you wish to copy [REQUIRED].
--environment_from / -ef / -e: The environment you wish to copy from. Defaults to "production" if not given.
--region_from / -rf / -r: The region you wish to copy from [REQUIRED].
--environment_to / -et: The environment you wish to copy to. Defaults to the --environment_from value if not given.
--region_to / -rt: The region you wish to copy to. Defaults to the --region_from value if not given.
--users / -u: The users you wish to apply permissions to in relation to this analysis. Defaults to "<EMAIL>,<EMAIL>" if not given.
```
+ An example for running this script could be:
`python3 copy_qs_theme.py -t 69cffbf5-d03d-4cc9-9c4a-a6213e0b4ddb-us-east-1 -rf us-east-1 -rt eu-west-2`
  + This command will run the `copy_qs_theme.py` script to copy theme_id `69cffbf5-d03d-4cc9-9c4a-a6213e0b4ddb-us-east-1` from region `us-east-1` to region `eu-west-2`. As the environment is not specified, it will operate on environment `production`.

For `copy_qs_analysis.py`:
+ This script allows copying of analyses between regions for QS.
+ The arguments for the script are as follows:
```commandline
--analysis_id / -t: The analysis id you wish to copy [REQUIRED].
--dataset_ids / -d: The ids of the new, manually created datasets in the target region (NOT the old ones in the original region) [REQUIRED].
--environment_from / -ef / -e: The environment you wish to copy from. Defaults to "production" if not given.
--region_from / -rf / -r: The region you wish to copy from [REQUIRED].
--environment_to / -et: The environment you wish to copy to. Defaults to the --environment_from value if not given.
--region_to / -rt: The region you wish to copy to. Defaults to the --region_from value if not given.
--users / -u: The users you wish to apply permissions to in relation to this analysis. Defaults to "<EMAIL>,<EMAIL>" if not given.
```
+ An example for running this script could be:
  `python3 copy_qs_analysis.py -a 6cb1a23e-b6b0-488e-a688-4ef416bfef88 -d 4c6f8951-21b5-42ef-bcb4-82a6f3d821bd,1248df45-a7bc-45a2-9314-2a303afca258 -rf eu-west-2 -rt us-east-1`
    + This command will run the `copy_qs_analysis.py` script to copy analysis_id `6cb1a23e-b6b0-488e-a688-4ef416bfef88` and datasets with ids `4c6f8951-21b5-42ef-bcb4-82a6f3d821bd` and `1248df45-a7bc-45a2-9314-2a303afca258` from region `us-east-1` to region `eu-west-2`. As the environment is not specified, it will operate on environment `production`.

Note: Though copying between environments was not specified in the brief, the foundations for support for this are present in both scripts. This likely won't work right now, but will be a good starting point if we decide to add it. This is why there are environment "from" and "to" variants, and why `region_from` can be included with the single-character tag `-r`. In the meantime, simply specify the environment you wish to operate on with `-e` (or do not include an environment to default to `production`).