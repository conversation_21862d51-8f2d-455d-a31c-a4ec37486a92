import argparse
import time
import uuid

import boto3

parser = argparse.ArgumentParser(
    description='Arguments for the analysis copy script'
)

parser.add_argument(
    '--analysis_id', '-a',
    help='The original analysis_id you wish to copy, e.g. 6cb1a23e-b6b0-488e-a688-4ef416bfef88',
    required=True
)

parser.add_argument(
    '--dataset_ids', '-d',
    help='The newly created dataset_ids in the target region, separated by commas, e.g. 4c6f8951-21b5-42ef-bcb4-82a6f3d821bd,1248df45-a7bc-45a2-9314-2a303afca258',
    required=True
)

parser.add_argument(
    '--environment_from', '-ef', '-e',
    help='The environment you wish to copy from, e.g. production',
    required=False,
    default="production"
)

parser.add_argument(
    '--region_from', '-rf', '-r',
    help='The region you wish to copy from, e.g. eu-west-2',
    required=True
)

parser.add_argument(
    '--environment_to', '-et',
    help='The environment you wish to copy to, e.g. pre',
    required=False,
    default=None
)

parser.add_argument(
    '--region_to', '-rt',
    help='The region you wish to copy to, e.g. us-east-1',
    required=False,
    default=None
)

parser.add_argument(
    '--users', '-u',
    help='The users you wish to apply permissions to, separated by commas e.g. <EMAIL>,<EMAIL>',
    required=False,
    default="<EMAIL>,<EMAIL>"
)

args = parser.parse_args()

ANALYSIS_ID = args.analysis_id
DATASET_IDS = args.dataset_ids.split(",")
ENV_FROM = args.environment_from
REG_FROM = args.region_from
ENV_TO = args.environment_to if args.environment_to else args.environment_from
REG_TO = args.region_to if args.region_to else args.region_from
USERS = args.users.split(",")

copy_client = boto3.client('quicksight', region_name=REG_FROM)
post_client = boto3.client('quicksight', region_name=REG_TO)

account_id = boto3.client('sts', region_name=REG_FROM).get_caller_identity().get('Account')

try:
    response = copy_client.describe_analysis(
        AwsAccountId=account_id,
        AnalysisId=ANALYSIS_ID
    )

    template_id = str(uuid.uuid4())

    analysis_name = response["Analysis"]["Name"]
    dataset_arns = response["Analysis"]["DataSetArns"]
    arn = response["Analysis"]["Arn"]

    dataset_references = []
    i = 0
    for dataset_arn in dataset_arns:
        dataset_references.append({
            "DataSetPlaceholder": analysis_name + f" placeholder {i}",
            "DataSetArn": dataset_arn
        })
        i += 1

    source_entity = {
        "SourceAnalysis": {
            "Arn": arn,
            "DataSetReferences": dataset_references
        }
    }

    actions = ["quicksight:UpdateTemplatePermissions",
               "quicksight:DescribeTemplatePermissions",
               "quicksight:UpdateTemplateAlias",
               "quicksight:DeleteTemplateAlias",
               "quicksight:DescribeTemplateAlias",
               "quicksight:ListTemplateAliases",
               "quicksight:ListTemplates",
               "quicksight:CreateTemplateAlias",
               "quicksight:DeleteTemplate",
               "quicksight:UpdateTemplate",
               "quicksight:ListTemplateVersions",
               "quicksight:DescribeTemplate",
               "quicksight:CreateTemplate"]

    permissions = []
    for USER in USERS:
        principal = f"arn:aws:quicksight:eu-west-2:{account_id}:user/default/" + USER
        permissions.append({"Principal": principal, "Actions": actions})

    try:
        response = copy_client.create_template(
            AwsAccountId=account_id,
            TemplateId=template_id,
            SourceEntity=source_entity,
            Permissions=permissions
        )

        try:
            response = copy_client.describe_template(
                AwsAccountId=account_id,
                TemplateId=template_id
            )

            while response["Template"]["Version"]["Status"] == "CREATION_IN_PROGRESS":
                print("First template creation in progress...")
                time.sleep(1)
                response = copy_client.describe_template(
                    AwsAccountId=account_id,
                    TemplateId=template_id
                )

            if response["Template"]["Version"]["Status"] == "CREATION_SUCCESSFUL":
                print("Successfully created first template.")

            template_id = str(uuid.uuid4())

            arn = response["Template"]["Arn"]
            source_entity = {
                "SourceTemplate": {
                    "Arn": arn
                }
            }

            try:
                response = post_client.create_template(
                    AwsAccountId=account_id,
                    TemplateId=template_id,
                    SourceEntity=source_entity,
                    Permissions=permissions
                )

                try:
                    response = post_client.describe_template(
                        AwsAccountId=account_id,
                        TemplateId=template_id
                    )

                    while response["Template"]["Version"]["Status"] == "CREATION_IN_PROGRESS":
                        print("Second template creation in progress...")
                        time.sleep(1)
                        response = post_client.describe_template(
                            AwsAccountId=account_id,
                            TemplateId=template_id
                        )

                    if response["Template"]["Version"]["Status"] == "CREATION_SUCCESSFUL":
                        print("Successfully created second template.")

                    ANALYSIS_ID = str(uuid.uuid4())

                    arn = response["Template"]["Arn"]

                    dataset_arns = []
                    for DATASET_ID in DATASET_IDS:
                        dataset_arns.append(f"arn:aws:quicksight:{REG_TO}:{account_id}:dataset/{DATASET_ID}")

                    dataset_references = []
                    i = 0
                    for dataset_arn in dataset_arns:
                        dataset_references.append({
                            "DataSetPlaceholder": analysis_name + f" placeholder {i}",
                            "DataSetArn": dataset_arn
                        })
                        i += 1

                    source_entity = {
                        "SourceTemplate": {
                            "DataSetReferences": dataset_references,
                            "Arn": arn
                        }
                    }

                    actions = ["quicksight:RestoreAnalysis",
                               "quicksight:UpdateAnalysisPermissions",
                               "quicksight:DeleteAnalysis",
                               "quicksight:QueryAnalysis",
                               "quicksight:DescribeAnalysisPermissions",
                               "quicksight:DescribeAnalysis",
                               "quicksight:UpdateAnalysis"]

                    permissions = []
                    for USER in USERS:
                        principal = f"arn:aws:quicksight:eu-west-2:{account_id}:user/default/" + USER
                        permissions.append({"Principal": principal, "Actions": actions})

                    analysis_name = analysis_name + " (copy)"

                    try:
                        response = post_client.create_analysis(
                            AwsAccountId=account_id,
                            AnalysisId=ANALYSIS_ID,
                            Name=analysis_name,
                            SourceEntity=source_entity,
                            Permissions=permissions
                        )

                        try:
                            response = post_client.describe_analysis(
                                AwsAccountId=account_id,
                                AnalysisId=ANALYSIS_ID
                            )

                            while response["Analysis"]["Status"] == "CREATION_IN_PROGRESS":
                                print("Analysis creation in progress...")
                                time.sleep(1)
                                response = post_client.describe_analysis(
                                    AwsAccountId=account_id,
                                    AnalysisId=ANALYSIS_ID
                                )

                            if response["Analysis"]["Status"] == "CREATION_SUCCESSFUL":
                                print(f"Successfully created analysis:\n"
                                      f"ID: {ANALYSIS_ID}\n"
                                      f"Name: {analysis_name}\n"
                                      f"Created on: {ENV_TO} {REG_TO}\n"
                                      f"Copied from: {ENV_FROM} {REG_FROM}")

                            elif response["Analysis"]["Status"] == "CREATION_FAILED":
                                print(f"Failed to create analysis:\n"
                                      f"ID: {ANALYSIS_ID}\n"
                                      f"Name: {analysis_name}\n"
                                      f"Created on: {ENV_TO} {REG_TO}\n"
                                      f"Copied from: {ENV_FROM} {REG_FROM}\n")

                                errors = response["Analysis"]["Errors"]
                                print("See below for errors.\n")
                                for error in errors:
                                    print(f"Error type: {error['Type']}\n"
                                          f"Error message: {error['Message']}\n")

                        except Exception as e:
                            print(f"Failed during new analysis description - {e}")

                    except Exception as e:
                        print(f"Failed during new analysis creation - {e}")

                except Exception as e:
                    print(f"Failed during second template description - {e}")

            except Exception as e:
                print(f"Failed during second template creation - {e}")

        except Exception as e:
            print(f"Failed during first template description - {e}")

    except Exception as e:
        print(f"Failed during first template creation - {e}")

except Exception as e:
    print(f"Failed during original analysis description - {e}")
