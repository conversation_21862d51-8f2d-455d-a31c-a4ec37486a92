import argparse
import time

import boto3

parser = argparse.ArgumentParser(
    description='Arguments for the CloudWatch log retention script'
)

parser.add_argument(
    '--environment', '-e',
    help='The environment you wish to set log retention policies for',
    required=True
)

parser.add_argument(
    '--months', '-m',
    help='The number of months you wish for logs to persist for',
    required=True
)

args = parser.parse_args()

ENVIRONMENT = str(args.environment).lower()
MONTHS = int(args.months)
boto3.setup_default_session(profile_name=f"{ENVIRONMENT}")
REGIONS = ["eu-west-2"]
if ENVIRONMENT == "pre":
    REGIONS.extend(["eu-central-1"])
if ENVIRONMENT == "production":
    REGIONS.extend(["us-east-1", "eu-central-1"])


# 18 months retention is set to 545 (limited set supported by AWS API)
if MONTHS == 18:
    retention_days = 545
else:
    retention_days = MONTHS * 30

for REGION in REGIONS:
    cwl_client = boto3.client("logs", region_name=REGION)
    try:
        print(f"Getting all log groups in {ENVIRONMENT} {REGION}...")
        log_groups = []
        paginator = cwl_client.get_paginator('describe_log_groups')
        for page in paginator.paginate():
            for group in page['logGroups']:
                log_groups.append(group)

        print(f"Setting {len(log_groups)} log groups with retention of {MONTHS} months...")
        failed_groups = []
        for log_group in log_groups:
            log_group_name = log_group.get("logGroupName")
            try:
                response = cwl_client.put_retention_policy(
                    logGroupName=log_group_name,
                    retentionInDays=retention_days
                )

            except Exception as e:
                print(f"Failed to set log group {log_group_name} retention {MONTHS} months.")
                failed_groups.append(log_group_name)

        print(f"\nRun finished for {ENVIRONMENT} {REGION}.")

        # Retry the failed log groups (errors can be from API rate throttling)
        if failed_groups:
            print("Failed log groups found. Retrying failed log groups...")
            for failed_group in failed_groups:
                try:
                    # Give it some room to breathe
                    time.sleep(0.25)
                    response = cwl_client.put_retention_policy(
                        logGroupName=failed_group,
                        retentionInDays=MONTHS*30
                    )

                    print(f"Successfully set log group {failed_group} with retention {MONTHS} months.")

                except Exception as e:
                    print(f"Failed to set log group {failed_group} with retention {MONTHS} months again - {e}")

        else:
            print("No visible failures - check CloudWatch and ensure everything looks good.")

    except Exception as e:
        print(f"Failed to get log groups from {ENVIRONMENT} {REGION} - {e}")
