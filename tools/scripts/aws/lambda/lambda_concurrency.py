import os
from pprint import pprint

import boto3

ENVIRONMENT = os.environ.get("ENVIRONMENT", "pre")
REGION = os.environ.get("REGION", "eu-west-2")

session = boto3.session.Session(profile_name=ENVIRONMENT)
lambda_client = session.client("lambda", region_name=REGION)

lambda_response = lambda_client.list_functions()

lambda_functions = lambda_response["Functions"]

while lambda_response.get("NextMarker"):
    lambda_response = lambda_client.list_functions(Marker=lambda_response["NextMarker"])
    lambda_functions.extend(lambda_response["Functions"])

function_names = [function["FunctionName"] for function in lambda_functions]

lambda_stats = {}
total_reserved = 0

for function in function_names:
    response = lambda_client.get_function_concurrency(
        FunctionName=function
    )

    if response.get("ReservedConcurrentExecutions"):
        lambda_stats[function] = {
            "ReservedConcurrentExecutions": response["ReservedConcurrentExecutions"]
        }

        total_reserved += response["ReservedConcurrentExecutions"]

pprint(lambda_stats)

print(f"Total Reserved: {total_reserved}")
