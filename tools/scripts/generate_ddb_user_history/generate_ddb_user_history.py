import random
import uuid
from typing import List
import boto3

from thefilter.aws.athena import AthenaQueryClient
from thefilter.aws.dynamoDB import DynamoBulkPublisher
from thefilter.utils.datetime import EpochUtils


class GenerateDDBUserHistory:
    """Provide start, end and ttl as epoch times"""

    def __init__(self, region, environment, customer, start, end, ttl):
        self._environment = environment
        self._customer = customer
        self._start = start
        self._end = end
        self._ttl = ttl

        self._s3_client = boto3.client('s3')
        self._athena_client = AthenaQueryClient(
            region=region,
            environment=environment,
            customer=customer
        )
        self._ddb_publisher = DynamoBulkPublisher(
            table_name='user_history',
            environment=self._environment,
            customer=self._customer
        )

    def generate_with_userid_and_thingids(self, user_ids: List[str], thing_ids: list):
        # lookup thing_ids and populate brandId and typename
        metadata = self._query_athena(thing_ids)
        # generate timestamps between start and end
        timestamps_epoch = self._generate_epochs(len(thing_ids))
        # generate timestamps


        user_history_records = []
        for user_id in user_ids:
        # format records
            user_specfic_records = self._format_records(
                user_id,
                thing_ids,
                metadata,
                timestamps_epoch
            )

            user_history_records += user_specfic_records
        return user_history_records


    def _query_athena(self, thing_ids: list):
        formatted_things = ", ".join([f"'{i}'" for i in thing_ids])
        query = \
            f"""
            SELECT 
                thing_id, 
                thing_brandid, 
                thing_typename
            FROM 
                {self._environment}{self._customer}database.metadata
            WHERE thing_id in ({formatted_things})
        """
        query_result = self._athena_client.query_athena(query)
        return query_result

    def _generate_epochs(self, count: int):
        results = []
        for i in range(count):
            random_epoch = random.randint(self._start, self._end)
            results.append(random_epoch)
        return results

    def _publish_to_ddb_uh(self, user_history_records):
        self._ddb_publisher.publish_dict(user_history_records)

    def _format_records(self, user_id, thing_ids, metadata, timestamps_epoch):
        result = []
        metadata = {i['thing_id']: i for i in metadata}

        for n, i in enumerate(thing_ids):
            record = {
             "userId": user_id,
             "timestampKey": timestamps_epoch[n],
             "action": "play",
             "beaconId": str(uuid.uuid4()),
             "brandId": str(metadata.get(i, {}).get('thing_brandid')),
             "thingId": str(i),
             "thingTypeName": str(metadata.get(i, {}).get('thing_typename')),
             "timestampInitiated":
                 EpochUtils.epoch_to_formatted_datetime(timestamps_epoch[n]),
             "timestampReceived":
                 EpochUtils.epoch_to_formatted_datetime(timestamps_epoch[n]),
             "timeToLiveEpoch": self._ttl,
            }
            result.append(record)
        return result


if __name__ == '__main__':
    """Update the folowing variables to suit your needs"""
    region = 'eu-west-2'
    environment = 'pre'
    customer = 'backstagedemo'

    # Note the API User history_age_days is set to 60 days!

    start_epoch = 1692057600  # 00:00 15/08
    end_epoch = 1694649600  # 00:00 14/09
    ttl = 1756684800 # 00:00 01/09/25

    # Note: user_id MUST finish in "_userId"!
    user_ids = ["user1_userid", "user2_userid"]
    thing_ids = ["1", "2"]
    api = GenerateDDBUserHistory(
        region,
        environment,
        customer,
        start_epoch,
        end_epoch,
        ttl
    )

    user_history = api.generate_with_userid_and_thingids(user_ids, thing_ids)
    api._publish_to_ddb_uh(user_history)