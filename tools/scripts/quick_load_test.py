"""
Performs a quick local load test for a given customer, requires an
- customer
- API_KEY
- slot_id
- number of distinct request
- number of total requests to run
"""
import argparse
import time

import boto3
import requests

from thefilter.aws.athena import AthenaQueryClient
from thefilter.logs.logclient import PrintLogClient


class QuickLoadTest:
    def __init__(self, region, environment, customer, api_key, slot_id):
        self._region = region
        self._environment = environment
        self._customer = customer
        self._api_key = api_key
        self._slot_id = slot_id
        self._CLIENT_SECRET = None
        self._CLIENT_ID = None
        self._athena_client = AthenaQueryClient(
            region=region,
            environment=environment,
            customer=customer
        )
        self._distinct_request_number = 10
        self._repeats = 2

    def main(self):
        auth_token_source = self.get_auth("source")
        self._do_request(auth_token_source)

    def get_auth(self, sequence: str) -> str:
        try:
            cognito_user_pool_id = self.get_cognito_user_pool_id()
            self.set_cognito_app_client_id_and_secret(cognito_user_pool_id)
        except Exception as e:
            print(f"Exception: Unable to get cognito app client id and secret "
                  f"for customer {self._customer} in environment {self._environment} for sequence {sequence}", e)

        try:
            response = requests.post(
                url=f'https://users.{self._environment}.thefilter.com/oauth2/token',
                data={'grant_type': 'client_credentials'},
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                auth=(self._CLIENT_ID, self._CLIENT_SECRET)
            )
            if response.status_code != 200:
                print(f"Exception: Unable to get authentication with "
                      f"code {response.status_code} for customer {self._customer} "
                      f"in environment {self._environment}", response.json())
                raise Exception()

            access_token = response.json().get('access_token')
            return access_token
        except requests.exceptions.HTTPError as e:
            print('Error with getting client_credentials', e)
            raise

    def _do_request(self, access_token):
        print("Gathering data")
        user_ids, thing_ids, search_qs = self._get_request_parameters()
        permutations = self._get_permutations(user_ids, thing_ids, search_qs)

        print("Starting Load test")

        for round in range(self._repeats):
            print(f"Starting round {round}")

            for qsp in permutations:
                host = f'https://{self._customer}.{self._environment}.thefilter.com/v0/slots/{self._slot_id}/items?ignore=postman&{qsp}'
                try:
                    request = requests.get(
                        url=host,
                        headers={
                            "Authorization": f"Bearer {access_token}",
                            "x-api-key": self._api_key
                        }
                    )
                    print(request.json())
                except Exception as e:
                    print("issue", e)

            time.sleep(1)


    def get_cognito_user_pool_id(self) -> str:
        cognito_client = self.get_cognito_client()
        user_pools_response = cognito_client.list_user_pools(MaxResults=2)

        for pool in user_pools_response['UserPools']:
            if pool['Name'] == f"{self._environment}-userpool":
                return pool['Id']

        print("Unable to get User Pool id, exiting")
        exit(1)

    def get_cognito_client(self) -> boto3.session.Session.client:
        session = boto3.session.Session(profile_name=self._environment)
        if self._environment == "production":
            cognito_client = session.client('cognito-idp', region_name="us-east-1")
        else:
            cognito_client = session.client('cognito-idp', region_name="eu-west-2")
        return cognito_client

    def set_cognito_app_client_id_and_secret(self, user_pool_id: str):
        customer = self._customer
        cognito_client = self.get_cognito_client()
        response = cognito_client.list_user_pool_clients(
            UserPoolId=user_pool_id,
            MaxResults=10
        )

        customer_client_id = ""
        customer_secret = ""
        if self._customer == 'uktv':
            customer = 'UKTV'
        for client in response['UserPoolClients']:
            if client['ClientName'] == f"{self._environment}-{customer}-AppClient":
                customer_client_id = client['ClientId']
                client_response = cognito_client.describe_user_pool_client(
                    UserPoolId=user_pool_id,
                    ClientId=customer_client_id
                )
                customer_secret = client_response['UserPoolClient']['ClientSecret']

        self._CLIENT_ID = customer_client_id
        self._CLIENT_SECRET = customer_secret

    def _get_request_parameters(self):
        metadata_query = f"""
            SELECT DISTINCT 
                thing_brandid,
                thing_brandname
            FROM metadata
            limit 10
        """
        metadata_query_results = self._athena_client.query_athena(metadata_query, simplify=True)

        events_query = f"""
            SELECT DISTINCT 
                user_id
            FROM events
            WHERE user_id is not null 
            AND cast(datepartition as date) = current_date
        """
        user_ids = self._athena_client.query_athena(events_query, simplify=True)

        thing_ids = metadata_query_results["thing_brandid"]
        search_qs = metadata_query_results["thing_brandname"]
        return user_ids, thing_ids, search_qs

    def _get_permutations(self, user_ids, thing_ids, search_qs):
        permutations = []
        for i in range(self._distinct_request_number):
            qsp = f"userId={user_ids[i]}&seedIds={thing_ids[i]}&q={search_qs[i][:5]}"
            permutations.append(qsp)
        return permutations


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Gets data from athena and uses them "
                    "to create load tests for use in Locust, "
                    "the api_key is the only required arg"
    )

    parser.add_argument(
        "--region", "-r",
        help="The source region defaults to eu-west-2",
        required=True
    )

    parser.add_argument(
        "--environment", "-e",
        help="The source environment we wish to query",
        required=True
    )

    parser.add_argument(
        "--api_key", "-k",
        help="The API Key required to access the Source API",
        required=True
    )

    parser.add_argument(
        "--customer", "-c",
        help='The customer you want to perform a load test for',
        required=True
    )

    parser.add_argument(
        "--slot_id", "-s",
        help='The slot_id you want to perform a load test for',
        required=True
    )

    logger = PrintLogClient()
    args = parser.parse_args()

    api_key = str(args.api_key)
    environment = str(args.environment).lower()
    region = str(args.region).lower()
    customer = str(args.customer).lower()
    slot_id = str(args.slot_id).lower()

    api = QuickLoadTest(region, environment, customer, api_key, slot_id)
    api.main()