# Portal / Cognito User creation

To allow a customer or colleague to login and access the Portal system, a user has to be
created first within Cognito. Portals for each environment are available at:

* [Feature](https://portal.feature.thefilter.com)
* [Pre](https://portal.pre.thefilter.com)
* [Production](https://portal.thefilter.com)

The script resides under `tools/scripts/dataops/` and can be run like this:
```shell
create_cognito_user.sh <username> <user_email> <group> <environment>
```

**NOTE**: To run this command, you must have the AWS cli set-up and relevant access to Cognito
in the AWS account you wish to give access 

### User creation for 24i employee
To create a login for a colleague, the following command can be run from the repository root:
```shell
create_cognito_user.sh <username> <<EMAIL>> thefilter-full <environment>
```

As a complete example, for `adam.poolman`, this is the command to run to create an account
in the `preproduction` environment.
```shell
create_cognito_user.sh pre.adam.poolman <EMAIL> thefilter-full pre
```

### User creation for a customer account
Like the colleague login, you'd run the command but with slightly different arguments.
```shell
create_cognito_user.sh <username> <user_email> <customer_slug>-full <environment>
```

As a complete example, for the user `Joe Bloggs` at the company `test`, we would run the
following command to create an account in the production environment:
```shell
create_cognito_user.sh test.joe.bloggs <EMAIL> test-full production
```

### User Password Reset

Resetting a user password can be done in two ways:

1. Log into the AWS console and go to Cognito service.
2. Select the user pool, likely named `{env}-userpool`.
3. Got Users > User Management
4. Find the user in the list that you'd like to reset and click on their name.
5. From the 'Actions' dropdown, select the Reset Password option.
6. Confirm the reset. This will send an email to the user with instructions on how to reset.

**Alternatively, you can recreate the user**

1. Log into the AWS console and go to Cognito service.
2. Select the user pool, likely named `{env}-userpool`.
3. Got Users > User Management
4. Find the user in the list that you'd like to reset and select them.
5. Select 'Delete User', disable their access.
6. Select 'Delete User', actually delete them.
7. Recerate the account using the information above.

**Note:** If a user needs to change their email address, you'll need to recreate the,.

### User Deletion

To delete a user:
1. Log into the AWS console and go to Cognito service.
2. Select the user pool, likely named `{env}-userpool`.
3. Got Users > User Management
4. Find the user in the list that you'd like to reset and select them.
5. Select 'Delete User', disable their access.
6. Select 'Delete User', actually delete them.
