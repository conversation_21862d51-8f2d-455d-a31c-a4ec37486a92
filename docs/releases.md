# Release Process

This section is tied in with [deployment.md](deployment.md) and covers the sprint release cycle.

## Overview
The sprints within 24iQ take place over a two week period, running Wednesday to Wednesday.
With the first week of the sprint looking to produce `Sprint x [R1]` release and the second
week producing the `Sprint X [R2]` release.
 
```mermaid
timeline
    title Release Timeline
    1st Wednesday   : Sprint Starts
                    : Deploy latest candidate build to feature
    1st Thursday    : Make [R2] release from previous sprint
                    : Deploy [R2] master release to pre-production
    1st Tuesday     : Deploy [R2] master release to production
    2nd Wednesday   : Deploy latest candidate build to feature
    2nd Thursday    : Make [R1] master release from current sprint
                    : Deploy [R1] master release to pre-production
    2nd Tuesday     : Deploy [R1] master release to production
```

The idea of deploying to `feature` at the end of Wednesday, allows for some preliminary testing
of the code that will constitute the next master release. Similarly, deploying to `pre-production`
on Thursday, allows for the code to remain unchanged and run over the weekend for all customers
with real data to ensure that the build is good for rolling out to `production` on a
Tuesday.

At the start of every sprint (or before), releases should be made within Jira to allow for
tickets to be linked to releases. These will go to create a release page in Confluecen, that
you can find [here](https://aferian.atlassian.net/wiki/spaces/PRDDATADATAOPS/pages/50823797/Deployments).
When a new release is ready, it's easier to clone the last release and update the JQL for the tables
accordingly to the newly released version. For reference, that JQL, for `S21 [R2]` release was:
```jql
Project=PRJ032DATA&fixVersion IN ('S21 [R2]') ORDER BY Key 
```

**NOTE**: Once a ticket is completed, the next corresponding release should be added to the
fixed version field.

## Customer Communications

On the 3rd Thursday of every month, the following format (example for November 2024) is sent to the Slack channels for
UKTV & MGM+. This could potentially be expanded going forwards to other customers like RTVE via Crossmedia.

---
Hi,

Please find below the upcoming planned maintenance schedule for November 2024.
It is unlikely we’ll use the full window, and updates are usually carried out in the earlier part of the window.
The rest of the window is used for any final checks and, allows us to conduct any larger updates, if required.

If you have any questions or concerns, please let us know.

Note: All times are in GMT

05/11/2024 10:00 to 01/11/2024 16:00
* Reference: S22-R1
* Region: All
* Impact: No expected service impact

12/11/2024 10:00 to 08/11/2024 16:00
* Reference: S22-R2
* Region: All
* Impact: No expected service impact

19/11/2024 10:00 to 15/11/2024 16:00
* Reference: S23-R1
* Region: All
* Impact: No expected service impact

26/11/2024 10:00 to 22/11/2024 16:00
* Reference: S23-R2
* Region: All
* Impact: No expected service impact
---