# Quicksight

This section contains some information about QuickSight user and asset management.

## User Management

Within Quickisght, there lies a distinciation between IAM users and 'email' users. In the 
case of the former, only an IAM user can be made a true admin and have the ability to manage
all assets even if they're not shared with them. 'Email' admin users can only share assets
they have access to. 

To create an IAM based user, login to QuickSight with an admin user (either IAM or email registered)
then under `Manage QuickSight > Manage Users`, when selecting to invite a user, enter the IAM
login of that user (e.g. `prod.adam.poolman`), this will then register them as an IAM user. 
You'll need to provide an email address for login, but this will create the necessary link
between the IAM and QuickSight user.

## Asset Management

With an IAM admin user, an additional option is shown under `Manage QuickSight` called
`Manage Assets`. This allows you to share and enable access to **any** asset (Analyses, Datasets etc.)
without having to have it shared with you. This option is only available to **IAM Admin** users
at the time of writing. 