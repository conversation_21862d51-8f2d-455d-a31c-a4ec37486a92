# Recommendations serving

The core function of this repo is to serve recommendations via the [api_rec lambda](../thefilter/functions/api_rec/lambda_function.py).
This section provides a brief overview of the process, and the classes involved in serving the recommendations.
It also details the usage of the testing class [test_api_personalisation.py](../test_api_personalisation.py)
for getting recommendations on your local machine using the customer data within AWS.

## api_rec

Being the most complex of the lambda functions, this function makes use of code in several
other classes within the codebase, namely:

* [personalisation.py](../thefilter/behaviours/personalisation.py) - Sets up a factory for the retrieval of recommendations from AWS
* [invokable_models.py](../thefilter/inference/logic/invokable_models.py) - Defines the model classes. Each model class can be referenced within a slot definition, with the main models being:
  * PrebuiltModel - Retrieves recommendations (generally for MLT & RFY) from DynamoDB `customer-recommendations` table
  * NamedChart - Retrieves chart based recommendations from DynamoDB `customer-chart` table
  * StubModel - Used in Health check, provides a stubbed response for testing connectivity
  * TrendingModel - Retrieves trending chart from `customer-chart` table with logic based on time of day
  * BrowseModel - Provides access to the ElasticSearch browse index and will filter on letter and genre
  * SearchModel - Provides access to the ElasticSearch search index, searching on the value provided from the `q` query string parameter
  * PrebuiltBYWModel - Gets an item from a user's history, then returns the MLT for that item
* [models.py](../thefilter/inference/models.py) - Main access point for invoking the models via the `RichModelInvoker` class

### Overview of recommendation process

1. User makes a request to the 24iQ API `slots` endpoint supplying at least `userId` QSP, though others including `seedIds` and `q` may also be provided depending on the slot.
2. API Factory sets up access to resources like DynamoDB, ElasticSearch etc.
3. Slot ID is converted into a model type. 
4. RichModelInvoker class invokes the requested model and gathers the result(s).
5. Results may then be filtered by either QSP, a user's watch history and availability. **NOTE** Some of this depends on the slot configuration
6. Results are formatted for return

## Testing personalisation locally

To run the personalisation code locally, there is a script [test_api_personalisation.py](../test_api_personalisation.py)
at the top level of the repository that accepts the following environment variables and will
connect to the correct AWS and return the recommendations based on the supplied request. This
is very useful in debugging recommendation output.

* `customer` - the customer you want to make the request for
* `environment` - the environment you want to make the request on (e.g. feature, pre, production)
* `region` - the region you want to make the request in
* `request` - the full URL for the request you wish to make, see example below

A potential request would look like the following:
```
https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/63740562-701f-4391-87da-baf604c28a31/items?userId={{INFO_RANDOM_USER}}&ignore=local
```
The following values are placeholders and are replaced automatically by the script, this allows
the same request to be used for multiple customers (e.g. Backstage customers all share the same slot IDs)
* `{{CUSTOMER}}` - Will get removed, but allows request to be copied from Postman
* `{{ENVIRONMENT}}` - Will get removed, but allows request to be copied from Postman
* `{{INFO_RANDOM_USER}}` - Replaced with a random user taken from the info endpoint
* `{{INFO_RANDOM_SEED}}` - Replaced with a random seed taken from the info endpoint
