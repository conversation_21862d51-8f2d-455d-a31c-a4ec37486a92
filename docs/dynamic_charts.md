# Dynamic Charts: Developer Guide

This guide provides practical information for developers working with the dynamic chart system in The Filter. It focuses on what you need to know to configure and use dynamic charts effectively, with minimal implementation details.

## Table of Contents

1. [Overview](#overview)
2. [Enabling Dynamic Charts for a Customer](#enabling-dynamic-charts-for-a-customer)
   - [Prerequisites](#prerequisites)
   - [Configuration Steps](#configuration-steps)
   - [Order of Operations](#order-of-operations)
3. [Creating Custom Charts](#creating-custom-charts)
   - [Customizing Variants and Overlays](#customizing-variants-and-overlays)
   - [Adding Custom Metadata Fields](#adding-custom-metadata-fields)
4. [Querying Dynamic Charts](#querying-dynamic-charts)
   - [Filtering Options](#filtering-options)
5. [Scoring Approaches](#scoring-approaches)
   - [Popularity Scoring](#popularity-scoring)
   - [Recency Scoring](#recency-scoring)
   - [Trending Scoring](#trending-scoring)
6. [Troubleshooting](#troubleshooting)
7. [Reference](#reference)
   - [Metric Naming Convention](#metric-naming-convention)
   - [Configuration Parameters](#configuration-parameters)

## Overview

Dynamic charts are automatically generated charts based on real-time popularity metrics derived from user interactions. Unlike static charts that are manually curated, dynamic charts are built by analyzing user behavior data (such as plays and distinct user counts) over different time periods.

The system supports multiple time periods (variants) and metric types (overlays):
- **Variants**: 6h, 1d, 7d, 14d, 30d (representing the time period)
- **Overlays**: play (for play counts), user (for distinct user counts)

## Enabling Dynamic Charts for a Customer

### Prerequisites

Before enabling dynamic charts for a customer, ensure the following prerequisites are met:

1. **Data Availability**: The customer must have sufficient user interaction data in the events table in Athena.
2. **Metadata Fields**: Identify which metadata fields should be included in the chart entries for filtering.

### Configuration Steps

To enable dynamic charts for a customer:

1. **Update Centralized Configuration**: Add dynamic chart configuration to the customer's configuration in DynamoDB:

   ```json
   {
     "dynamic_chart_config": {
       "enabled": true,
       "chart_size": 500,
       "included_typenames": ["TVSeries", "Movie"],
       "metadata_fields": ["thing_genre_name", "thing_custom_agerangeuid", "thing_custom_availabilityarray"]
     }
   }
   ```

2. **Configure Required Parameters**:
   - `enabled`: Set to `true` to enable dynamic charts
   - `chart_size`: Set the maximum number of items to include in the chart (recommended: 500-1000)
   - `included_typenames`: Specify the content types to include (e.g., "TVSeries", "Movie")
   - `metadata_fields`: Specify the metadata fields to include for filtering

### Order of Operations

When setting up dynamic charts, follow this order of operations:

1. **Configure**: Update the customer's configuration in DynamoDB with the dynamic chart settings.
2. **Build**: Run the chart builder job to generate the initial dynamic chart.
3. **Verify**: Check that the chart has been created in Elasticsearch by querying the index.
4. **Update API Model**: Update the API model definition to use the dynamic chart by setting `isDynamicChart: true`.
5. **Test**: Test the API to ensure it returns the expected results.

## Creating Custom Charts

### Customizing Variants and Overlays

The dynamic chart system supports customization of variants (time periods) and overlays (metric types). By default, the system includes:

- **Variants**: 6h, 1d, 7d, 14d, 30d
- **Overlays**: play, user

To create a custom chart with different variants or overlays:

1. **Modify the Chart Builder**: Update the Athena query in `DynamicCustomerChartBuilder` to include your custom time periods or metrics.
2. **Update the Elasticsearch Mapping**: Ensure the chart template includes your custom metrics.
3. **Update the Query Construction**: Modify the `elastic_search_chart.py` file to handle your custom variants and overlays.

Example: To add a new 60-day variant:

1. Add a new CTE for 60-day metrics in the Athena query
2. Add the 60-day metrics to the overall query
3. Add normalization calculations for the 60-day metrics
4. Update the chart template to include the new metrics
5. No changes needed to `elastic_search_chart.py` as it dynamically constructs the scoring field

### Adding Custom Metadata Fields

To include additional metadata fields for filtering:

1. **Update Configuration**: Add the field names to the `metadata_fields` list in the customer's configuration.
2. **Verify Field Availability**: Ensure the fields exist in the metadata table in Athena.
3. **Test Filtering**: Test that the fields can be used for filtering in API queries.

## Querying Dynamic Charts

When querying dynamic charts through the API, you can specify various parameters to control the results returned.

### Filtering Options

You can filter the chart results using the following parameters:

| Parameter       | Description               | Example                             |
|-----------------|---------------------------|-------------------------------------|
| `genre`         | Filter by genre           | `genre=Comedy`                      |
| `typename`      | Filter by typename        | `typename=Movie`                    |
| `excludeGenre`  | Exclude a genre           | `excludeGenre=Horror`               |
| `channelId`     | Filter by channel ID      | `channelId=bbc_one`                 |
| `programTypeId` | Filter by program type ID | `programTypeId=series`              |
| `ageRating`     | Filter by age rating      | `ageRating=12`                      |
| `countryCode`   | Filter by country code    | `countryCode=GB`                    |
| `dedupeOn`      | A field to deduplicate on | `dedupeOn=filters.thing_genre_name` |

Example API request with filtering:
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&genre=Comedy&typename=Movie&size=10
```

## Scoring Approaches

The dynamic chart system supports multiple scoring approaches that can be used to rank and order the results. Each scoring approach uses different parameters and algorithms to determine the relevance of items in the chart.

### Popularity Scoring

Popularity scoring is the default scoring approach used by dynamic charts. It ranks items based on their popularity metrics (play counts or user counts) over a specified time period.

#### How Popularity Scoring Works

Popularity scoring uses two key parameters to determine how items are scored:

1. **Variant**: Defines the time period for which popularity is measured (6h, 1d, 7d, 14d, 30d)
2. **Overlay**: Defines the metric type used for scoring (play counts or user counts)

The system calculates a normalised score for each item based on these parameters, where higher values indicate more popular content.

#### Popularity Scoring Parameters

| Parameter          | Description                                               | Example        | Default                |
|--------------------|-----------------------------------------------------------|----------------|------------------------|
| `variant`          | The time period to use for scoring (6h, 1d, 7d, 14d, 30d) | `variant=14d`  | `30d`                  |
| `overlay`          | The metric type to use for scoring (play, user)           | `overlay=user` | `play`                 |
| `popularityWeight` | Weight of popularity in the scoring calculation           | `1.0`          | `popularityWeight=0.8` |

#### The 6-Hour Variant: Enhancing Trending Quality

The 6-hour variant (`6h`) is specifically designed to improve trending quality by providing a "generally popular in recent range" metric. This variant is particularly valuable for:

**Use Cases:**
- **Consistent Catalog Interactions**: When users habitually watch the same series or content types and don't incur sudden rises in content policy
- **Trending Enhancement**: Blending with trending metrics to surface content that's both trending and recently popular
- **Stable Content Discovery**: Platforms where new content isn't constantly added but existing content has consistent engagement

**Why 6 Hours?**
- **Habitual Patterns**: Captures consistent viewing patterns without being too narrow
- **Data Availability**: Works within the 3-hour data delay constraints while providing meaningful recent popularity

**Recommended Combinations:**
```
# Trending + Recent Popularity Blend
GET v0/slots/{slot_id}/items?variant=6h&overlay=play&enableTrending=true&recentTrendingWeight=1.5&popularityWeight=1.2&size=20

# Consistent Catalog Discovery
GET v0/slots/{slot_id}/items?variant=6h&overlay=user&popularityWeight=2.0&size=15
```

#### Example API Requests with Popularity Scoring

Basic popularity scoring (30-day play counts):
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&size=10
```

Popularity based on user counts over 7 days:
```
GET v0/slots/{slot_id}/items?variant=7d&overlay=user&size=10
```

Recent popularity (1-day play counts):
```
GET v0/slots/{slot_id}/items?variant=1d&overlay=play&size=10
```

Ultra-recent popularity (6-hour play counts):
```
GET v0/slots/{slot_id}/items?variant=6h&overlay=play&size=10
```

### Recency Scoring

The dynamic chart system supports recency-based scoring, which allows newer content to be surfaced more prominently. This feature uses the publication date of each item to calculate a normalized recency score, where 2.0 represents the most recent item and 1.0 represents the oldest item.

#### How Recency Scoring Works

When recency scoring is enabled:

1. Each item's recency score (1.0-2.0) is multiplied by the `recencyWeight`
2. Each item's popularity score is multiplied by the `popularityWeight`
3. The final score is the sum of these two weighted scores

This allows you to balance the importance of recency vs. popularity in the results. A higher `recencyWeight` will give more prominence to newer content, while a higher `popularityWeight` will favor more popular content.

#### Recency Scoring Parameters

| Parameter       | Description                                  | Default | Example              |
|-----------------|----------------------------------------------|---------|----------------------|
| `enableRecency` | Enable recency-based scoring                 | `false` | `enableRecency=true` |
| `recencyWeight` | Weight of recency in the scoring calculation | `1.0`   | `recencyWeight=1.5`  |


#### Example API Requests with Recency Scoring

Basic recency scoring (equal weights):
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableRecency=true&size=10
```

Emphasize recency over popularity:
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableRecency=true&recencyWeight=1.5&popularityWeight=0.8&size=10
```

Emphasize popularity over recency:
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableRecency=true&recencyWeight=0.8&popularityWeight=1.5&size=10
```

### Trending Scoring

The dynamic chart system includes comprehensive trending scoring that identifies content with increasing momentum across multiple time horizons. The system provides four different trending approaches that can be used individually or blended together for sophisticated ranking algorithms.

#### Trending Approaches

All trending approaches use the same two-point linear regression formula: `(recent_period - previous_period) / previous_period`, but with different time windows to capture trending patterns at various scales.

##### 1. Recent Trending (3-Hour Windows)
The most reactive trending approach, designed for real-time trend detection:
- **Recent Window**: From `latest_timestamp - 3 hours` to `latest_timestamp`
- **Previous Window**: From `latest_timestamp - 6 hours` to `latest_timestamp - 3 hours`
- **Use Case**: Detecting viral content and immediate spikes in popularity
- **Metrics**: `recent_trending_play_slope`, `recent_trending_play_slope_normalised`

##### 2. Daily Trending (24-Hour Windows)
Captures day-over-day trending patterns:
- **Recent Window**: From `latest_timestamp - 24 hours` to `latest_timestamp`
- **Previous Window**: From `latest_timestamp - 48 hours` to `latest_timestamp - 24 hours`
- **Use Case**: Identifying content gaining momentum over daily cycles
- **Metrics**: `daily_trending_play_slope`, `daily_trending_play_slope_normalised`

##### 3. Weekly Trending (7-Day Windows)
Identifies longer-term trending patterns:
- **Recent Window**: From `latest_timestamp - 7 days` to `latest_timestamp`
- **Previous Window**: From `latest_timestamp - 14 days` to `latest_timestamp - 7 days`
- **Use Case**: Detecting sustained growth over weekly periods
- **Metrics**: `weekly_trending_play_slope`, `weekly_trending_play_slope_normalised`

##### 4. Week-over-Week Comparison (24-Hour Windows, 7 Days Apart)
Compares the same time period across different weeks:
- **Recent Window**: From `latest_timestamp - 24 hours` to `latest_timestamp`
- **Previous Window**: From `latest_timestamp - 192 hours` to `latest_timestamp - 168 hours` (same 24h period, 7 days earlier)
- **Use Case**: Detecting weekly cyclical trends and seasonal patterns
- **Metrics**: `weekly_comparison_trending_play_slope`, `weekly_comparison_trending_play_slope_normalised`

#### How Trending Calculation Works

1. **Latest Timestamp Detection**: The system first queries for the most recent `timestamp_initiated` from events in the last 2 days. If no recent events are found, it uses the current time.

2. **Play Count Calculation**: For each content item and each trending approach, the system counts plays in the respective time windows.

3. **Trending Slope Calculation**: The trending slope is calculated using the same logic for all approaches:
   - If both windows have 0 plays: slope = 0.0
   - If previous window has 0 plays but recent window has plays: slope = 1.0
   - Otherwise: slope = (recent_plays - previous_plays) / previous_plays

4. **Normalization**: Each trending slope is independently normalized to a 1.0-2.0 scale where:
   - 1.0 represents content with negative or no trending momentum
   - 2.0 represents content with the highest trending momentum in the dataset for that specific approach

#### Why This Approach

This reactive trending approach is designed to work within the constraints of data availability:

- **3-Hour Delay**: There's typically up to 3 hours of delay before plays become available in Athena, so the recent window ends 3 hours before the latest available event.
- **Reactive Window**: Using 3-hour windows provides a balance between being reactive to trends while having enough data for meaningful calculations.

#### Trending Scoring Metrics

The trending calculations produce the following metrics that are available for use in Elasticsearch queries:

| Metric                                             | Description                                | Range   |
|----------------------------------------------------|--------------------------------------------|---------|
| `recent_trending_play_slope`                       | Raw recent trending slope (3-hour windows) | Any     |
| `recent_trending_play_slope_normalised`            | Normalized recent trending score           | 1.0-2.0 |
| `daily_trending_play_slope`                        | Raw daily trending slope (24-hour windows) | Any     |
| `daily_trending_play_slope_normalised`             | Normalized daily trending score            | 1.0-2.0 |
| `weekly_trending_play_slope`                       | Raw weekly trending slope (7-day windows)  | Any     |
| `weekly_trending_play_slope_normalised`            | Normalized weekly trending score           | 1.0-2.0 |
| `weekly_comparison_trending_play_slope`            | Raw week-over-week comparison slope        | Any     |
| `weekly_comparison_trending_play_slope_normalised` | Normalized week-over-week comparison score | 1.0-2.0 |

#### Trending Scoring Parameters

The trending system can be controlled through API parameters that allow fine-tuning of each trending approach:

| Parameter                        | Description                                 | Default | Example                              |
|----------------------------------|---------------------------------------------|---------|--------------------------------------|
| `enableTrending`                 | Enable trending-based scoring               | `false` | `enableTrending=true`                |
| `recentTrendingWeight`           | Weight for recent trending (3-hour windows) | `0.0`   | `recentTrendingWeight=1.5`           |
| `dailyTrendingWeight`            | Weight for daily trending (24-hour windows) | `0.0`   | `dailyTrendingWeight=1.2`            |
| `weeklyTrendingWeight`           | Weight for weekly trending (7-day windows)  | `0.0`   | `weeklyTrendingWeight=0.8`           |
| `weeklyComparisonTrendingWeight` | Weight for week-over-week comparison        | `0.0`   | `weeklyComparisonTrendingWeight=1.0` |

#### Parameter Precedence

Trending parameters follow the same precedence pattern as other dynamic chart parameters:

1. **Query String Parameters**: Highest priority - parameters passed in the API request URL
2. **Slot Definition Parameters**: Medium priority - default values defined in the slot configuration
3. **System Defaults**: Lowest priority - built-in default values

#### Conditional Function Score Application

- When `enableTrending=false`, all trending function scores are excluded from the Elasticsearch query
- When any trending weight parameter is set to `0`, that specific trending function score is excluded
- Only trending approaches with weight > 0 are included in the scoring calculation
- This applies to all scoring approaches (popularity, recency, and trending)

#### Example API Requests with Trending Scoring

Basic trending scoring (recent trending only):
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableTrending=true&recentTrendingWeight=1.0&size=10
```

Multi-scale trending (all approaches):
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableTrending=true&recentTrendingWeight=1.5&dailyTrendingWeight=1.2&weeklyTrendingWeight=0.8&weeklyComparisonTrendingWeight=1.0&size=10
```

Viral content detection (recent + daily trending):
```
GET v0/slots/{slot_id}/items?variant=7d&overlay=play&enableTrending=true&recentTrendingWeight=2.0&dailyTrendingWeight=1.0&size=10
```

Sustained growth detection (weekly trending):
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&enableTrending=true&weeklyTrendingWeight=1.5&size=10
```

Combined popularity, recency, and trending:
```
GET v0/slots/{slot_id}/items?variant=30d&overlay=play&popularityWeight=1.0&enableRecency=true&recencyWeight=0.8&enableTrending=true&recentTrendingWeight=1.2&dailyTrendingWeight=0.8&size=10
```

#### Blending Trending Approaches

The multiple trending approaches can be combined in sophisticated ways:

- **Viral Detection**: High `recentTrendingWeight` + moderate `dailyTrendingWeight`
- **Sustained Growth**: Focus on `weeklyTrendingWeight` for consistent upward momentum
- **Seasonal Patterns**: Use `weeklyComparisonTrendingWeight` for day-of-week performance patterns
- **Multi-Scale Trending**: Blend all approaches with different weights to capture both immediate viral content and sustained growth patterns

## Troubleshooting

Common issues and their solutions:

1. **No results returned**: 
   - Check that dynamic charts are enabled in the configuration
   - Verify that the chart has been built recently
   - Ensure the Elasticsearch index exists and contains data

2. **Incorrect results**: 
   - Check the variant and overlay parameters to ensure you're using the correct metrics for scoring
   - Verify that the filters are correctly specified
   - If using recency scoring, check that the `enableRecency` parameter is set to `true`

3. **Missing metadata fields**: 
   - Check that the metadata fields are correctly configured in the dynamic chart configuration
   - Verify that the fields exist in the metadata table

4. **Performance issues**: 
   - Check the size of the chart and consider reducing it if necessary
   - Review the number of metadata fields included and remove any that aren't needed

5. **Invalid scoring field**:
   - Ensure that the overlay and variant parameters are valid values
   - Valid overlays are `play` and `user`
   - Valid variants are `30d`, `14d`, `7d`, `1d`, and `6h`

6. **Recency scoring issues**:
   - Verify that the chart has been built with the latest version that includes recency scoring
   - Check that the items have valid publication dates in the metadata
   - Ensure the `recencyWeight` and `popularityWeight` parameters are positive numbers
   - If recency doesn't seem to have enough impact, try increasing the `recencyWeight` relative to `popularityWeight`

## Reference

### Metric Naming Convention

The dynamic chart system uses a consistent naming convention for metrics:

- **Raw metrics**: `{overlay}_{variant}`
  - Examples: `play_30d`, `user_7d`, `play_14d`, `user_1d`, `play_6h`, `user_6h`

- **Normalized metrics**: `{overlay}_{variant}_normalised`
  - Examples: `play_30d_normalised`, `user_7d_normalised`, `play_14d_normalised`, `user_1d_normalised`, `play_6h_normalised`, `user_6h_normalised`

- **Recency metrics**:
  - `date_published`: The publication date of the item
  - `recency_score`: A normalized score (1.0-2.0) representing the recency of the item, where 2.0 is the newest and 1.0 is the oldest

Where:
- `overlay` is either `play` (for play counts) or `user` (for distinct user counts)
- `variant` is one of `30d`, `14d`, `7d`, `1d`, or `6h` (representing the time period)

### Configuration Parameters

The dynamic chart configuration includes the following parameters:

| Parameter            | Type            | Description                                                           | Required |
|----------------------|-----------------|-----------------------------------------------------------------------|----------|
| `enabled`            | boolean         | Whether dynamic charts are enabled for the customer                   | Yes      |
| `chart_size`         | integer         | The maximum number of items to include in the chart                   | Yes      |
| `included_typenames` | list of strings | The content types to include in the chart (e.g., "TVSeries", "Movie") | Yes      |
| `metadata_fields`    | list of strings | The metadata fields to include in the chart entries for filtering     | Yes      |

If `included_typenames` is not specified, the system defaults to including "TVSeries" and "Movie" types.