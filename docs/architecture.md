# Architecture

This page contains a basic overview of the architecture of the 24iQ Platform. 
More information is available in Confluence: https://aferian.atlassian.net/wiki/spaces/PRDDATADATAOPS/pages/132481096/2024+Architecture

## Diagrams

Flow diagrams of the data and services used to process and store for key parts of the
platform. For more detailed diagrams, see Confluence.

### Catalogue Metadata
```mermaid
graph LR
    A[Customer Catalogue Metadata] --> B(Catalogue Loader)
    B --> C(ElasticSearch metadata index)
    B --> D(DynamoDB metadata_lite table)
    B --> E(S3 Bucket & Athena - metadata table)
```

### Events 
```mermaid
graph LR
    A[Realtime Events] --> B(API Gateway)
    B --> C(api_events Lambda)
    C --> D(Kinesis)
    D --> E(S3 Bucket - raw_events)
    E --> F(Events Persistence Job)
    F --> G(S3 Bucket & Athena - events table)
    
    H[Batch Events] --> I(Events Loader)
    I --> G
```

### Personalisation Requests
```mermaid
graph LR
    A[Personalisation Request] <--> B(API Gateway)
    B <--> C(api_rec Lambda)
    C --> F(Kinesis)
    F --> G(S3 Bucket - raw_events)
    G --> H(Events Persistence Job)
    H --> I(S3 Bucket & Athena - personalisation table)
    
    C <--> D(DynamoDB Tables)
    C <--> E(ElasticSearch - search/browse indices)
```

### Data Science Models (RFY/MLT)
```mermaid
graph LR
    EB(EventBridge trigger) --> R2DL(RecsToDDB Lambda)
    
    R2DL --> job
    subgraph job[Step Function]
        SM(SageMaker Training Job)
        AE(Athena events table) --> SM
        AM(Athena metadata table) --> SM
        SM --> R2DJ(RecsToDDB Job)
    end
    R2DJ --> DDB(DynamoDB recommendation table)
```

### Chart Builders
```mermaid
graph LR
    EB(EventBridge Trigger) --> job
    subgraph job[Chart Builder Batch Job]
        CB(Chart Builder Process)
        AE(Athena events table) --> CB
        AM(Athena metadata table) --> CB
    end
    CB --> DDB(DynamoDB chart table)
```

### Search & Browse Indexers
```mermaid
graph LR
    EB(EventBridge Trigger) --> searchjob
    subgraph searchjob[Search Indexer Batch Job]
        SI(Search Indexer Process)
        ASE(Athena events table) --> SI
        ASM(Athena metadata table) --> SI
    end
    searchjob --> ESS(ElasticSearch search index)
    
    EB2(EventBridge Trigger) --> browsejob
    subgraph browsejob[Browse Indexer Batch Job]
        BI(Browse Indexer Process)
        ABE(Athena events table) --> BI
        ABM(Athena metadata table) --> BI
    end
    browsejob --> ESB(ElasticSearch browse index)
```

## Used AWS Services

The following AWS services are used within the 24iQ Platform, this section has been copied
from the Confluence page linked above

| Service name                   | Usage                                                                                                                                                                 | Reason                                                                                                                                                                                                                                                 |
|--------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| API Gateway                    | Hosts both the Catalogue and Recommendations APIs.                                                                                                                    | Provides a fully managed, scalable, extensible, secure and cacheable gateway with simple integration with other AWS services.                                                                                                                          |
| Athena                         | Used for querying data stored within S3.                                                                                                                              | A serverless, low-cost, performant, scalable and flexible database engine that allows for querying data in a variety of file formats stored with S3 buckets.                                                                                           |
| Backup                         | To backup specific information on a regular basis.                                                                                                                    | Automatable and scalable backup solution for AWS based infrastructure                                                                                                                                                                                  |
| Batch                          | Provides support for running loader, builder and other longer running tasks.                                                                                          | A fully-managed service using on-demand EC2 instances for running specific jobs that aren’t suitable for running within a lambda. This covers the various loader jobs, index builders and fallback builders.                                           |
| CloudFormation                 | Defines the services and components used within the Platform, the permissions they require and how they link together.                                                | Allows for infrastructure as a code, including the ability to visualise links between infrastructure components. Due to its parameterised nature, one CloudFormation definition can be duplicated and replicated per customer and across environments. |
| CloudFront                     | Hosting support for the Portal and fallback services.                                                                                                                 | A low-cost, distributed CDN backed from S3 to provide resilience and redundancy.                                                                                                                                                                       |
| CloudWatch                     | Alarm and logging support.                                                                                                                                            | Easily integrated with all other services to provide monitoring and alarms based on service metrics or log messages.                                                                                                                                   |
| Cognito                        | Authentication support for Portal and API access.                                                                                                                     | Scalable, secure and compliant user management system that allows authentication for other AWS services.                                                                                                                                               |
| DynamoDB                       | Stores both ‘source’ data like entity association, editorial blocks, promotions, as well as ‘recommendation’ data like charts, trending, MLT and RFY recommendations. | High uptime SLA (99.999%), fast response times, high performance and serverless NoSQL database.                                                                                                                                                        |
| Elastic Container Repository   | Stores images used in both Batch and Sagemaker training services.                                                                                                     | AWS hosting that is seamlessly integrated with Batch and Sagemaker, allowing custom docker images and jobs to be run within AWS with minimal set-up time.                                                                                              |
| ElasticSearch (OpenSearch)     | Used as an interim store for catalogue metadata before persisting to S3.                                                                                              | Also, provides search and browse functionality.                                                                                                                                                                                                        |Secure and fully-managed ElasticSearch hosting that can be used to store a variety of document formats with minimal overheads.
| EventBridge                    | Scheduling of sagemaker training and batch jobs.                                                                                                                      | Provides an interface to trigger other AWS servicesbased on either schedule or specific events.                                                                                                                                                        |
| Glue                           | Athena database table creation and maintenance.                                                                                                                       | Provides the schema definition and discovery for Athena. It can be used to crawl and create queryable tables based on data within S3.                                                                                                                  |
| Identity and Access Management | User account, role and permissions support. Used to create roles for each service to limit access to the underlying data-sources or infrastructure.                   | Allows fine-grained access and set-up of security credentials to a access data or infrastructure within AWS.                                                                                                                                           |
| Kinesis Data Firehose          | Used for transport and queuing of customer events to S3.                                                                                                              | A fully managed, scalable, real-time and cost efficient streaming technology that provides the ability to capture, transform and load streaming data directly into AWS.                                                                                |
| Key Management Service         | Key generation and storage for encryption purposes.                                                                                                                   | Provides support for centralised managed keys that can be used across all integrated AWS services like S3, SQS and EC2.                                                                                                                                |
| Lambda                         | Multiple functions for supporting the variety of API methods provided via the API gateways.                                                                           | Provides a serverless, event-driven service that can run self-contained code that scales automatically based on usage.                                                                                                                                 |
| QuickSight                     | Our data analytics visualization solution that allows unlimited users to access analytics in real time.                                                               | Integrates with all other AWS services, whilst still providing a stand-alone dashboard and interface for analytic dashboards.                                                                                                                          |
| S3 Buckets                     | Used for long-term storage of customer data, and required for use of Athena.                                                                                          | Secure, reliable and cost-effective storage for large datasets.                                                                                                                                                                                        |
| Sagemaker                      | Runs regular training jobs for data science models.                                                                                                                   | A flexible toolkit for Data Scientists, allowing for data manipulation, model training, testing and deployment.                                                                                                                                        |
| SQS                            | Used for queuing a variety of data types before being picked up by other processes.                                                                                   | Fully-managed, scalable queue service that allows for linking of AWS services.                                                                                                                                                                         |
| Step Functions                 | Link jobs in a state machine.                                                                                                                                         | Provides workflow orchestration, allowing us to link multiple jobs together whilst keeping them separate.                                                                                                                                              |
| Web Application Firewall       | Used to protect APIs from common attacks.                                                                                                                             | Provides an easily configured, fully managed firewall for protecting and monitoring APIs and CloudFront distributions.                                                                                                                                 |
|                                |                                                                                                                                                                       |                                                                                                                                                                                                                                                        |
| StatusCake                     | External uptime monitoring and alerting.                                                                                                                              | Reliable, scalable and cost-effective monitoring of APIs and webhosts from multiple external infrastructure.                                                                                                                                           |