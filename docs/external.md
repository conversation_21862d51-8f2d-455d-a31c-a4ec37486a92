# External Services

To help support and run the platform, we use a number of external services. To access these
services, you'll either be given personal credentials or access to LastPass for shared
credentials. 
---
### AWS Account logins
* [AWS SSO](https://d-9367025e46.awsapps.com/start/#/?tab=accounts) - Individual account, request access from Cloud Ops / GAIT team
* [AWS Feature - Root Login](https://signin.aws.amazon.com/signin) - ************ - (Shared account - <EMAIL>) Minimal usage, access via LastPass
* [AWS Pre - Root Login](https://signin.aws.amazon.com/signin) - ************ - (Shared account - <EMAIL>) Minimal usage, access via LastPass
* [AWS Production - Root Login](https://signin.aws.amazon.com/signin) - ************ - (Shared account - <EMAIL>) Minimal usage, access via LastPass
---
### ElasticSearch Kibana access, requires a VPN login
* [ElasticSearch Kibana - Feature - eu-west-2](https://vpc-feature-metadata-tan6foqzl2raixrza2egt2h3di.eu-west-2.es.amazonaws.com/_plugin/kibana/app/kibana)
* [ElasticSearch Kibana - Pre - eu-west-2](https://vpc-pre-metadata-yzozwm4vjlitzij4sl5lhhr42m.eu-west-2.es.amazonaws.com/_plugin/kibana/app/kibana)
* [ElasticSearch Kibana - Pre - eu-central-1](https://vpc-pre-metadata-oigjnabkbhpfvdomkykkxdjuga.eu-central-1.es.amazonaws.com/_plugin/kibana/app/kibana)
* [ElasticSearch Kibana - Production - eu-west-2](https://vpc-production-metadata-zurssxi4hrysi2pmyi52256k7u.eu-west-2.es.amazonaws.com/_plugin/kibana/app/kibana)
* [ElasticSearch Kibana - Production - us-east-1](https://vpc-production-metadata-ahrxbhadjxiwxhfurw2umxa6me.us-east-1.es.amazonaws.com/_plugin/kibana/app/kibana)
* [ElasticSearch Kibana - Production - eu-central-1](https://vpc-production-metadata-clczkkbncm5yt5wo7n3yzqkxbe.eu-central-1.es.amazonaws.com/_plugin/kibana/app/kibana)
---
### OpenVPN Servers

These have a mix of shared (root accounts) and individual accounts for login. Access is controlled by the 24iQ team.
* [Feature eu-west-2 OpenVPN](https://vpn.feature.thefilter.com:943/)
* [Feature eu-west-2 OpenVPN - Admin](https://vpn.feature.thefilter.com:943/admin/)
* [Pre eu-west-2 OpenVPN](https://vpn.pre.thefilter.com:943/)
* [Pre eu-west-2 OpenVPN - Admin](https://vpn.pre.thefilter.com:943/admin/)
* [Pre eu-west-2 DS OpenVPN](https://vpnds.pre.thefilter.com:943)
* [Pre eu-west-2 DS OpenVPN - Admin](https://vpnds.pre.thefilter.com:943/admin/)
* [Pre eu-central-1 OpenVPN](https://vpn-eucentral.pre.thefilter.com:943)
* [Pre eu-central-1 OpenVPN - Admin](https://vpn-eucentral.pre.thefilter.com:943/admin/)
* [Production eu-west-2 OpenVPN](https://vpn.production.thefilter.com:943/)
* [Production eu-west-2 OpenVPN - Admin](https://vpn.production.thefilter.com:943/admin/)
* [Production us-east-1 OpenVPN](https://vpnus.production.thefilter.com:943/)
* [Production us-east-1 OpenVPN - Admin](https://vpnus.production.thefilter.com:943/admin/)
* [Production eu-central-1 OpenVPN](https://vpn-eucentral.production.thefilter.com:943/)
* [Production eu-central-1 OpenVPN - Admin](https://vpn-eucentral.production.thefilter.com:943/admin/)

These need to have security updates applied on a regular basis, each server is hosted on an EC2 in the relevant
account. To update them, log in, by going to the EC2 section of the AWS console, selecting the 
instance to update and clicking on `Connect`. 

**Note**: Security Groups shouldn't be altered, and all updates and connections must be done via
the Connect functionality in the AWS Console.

To update to the latest packages for a VPN server, run the following:
```shell
sudo apt update
sudo apt upgrade
# 1. Select default answer for any questions, the configuration should be maintained and not updated
# 2. On any dialogues, hit tab and select <ok> option to continue
sudo reboot now
```
Once rebooted, connect to the VPN server and ensure you can still access the ElasticSearch
cluster within that environment and region.

#### Root / `openvpn` user login

The credentials for the `openvpn` root account is available with the 24iQ Admins folder within 
LastPass. Shoudl they be lost for any reason, they can be reset manually using the guide avaialbe
here:  https://openvpn.net/as-docs/reset-admin-access.html

#### SSL Set-up

Details copied from: https://aferian.atlassian.net/browse/PRJ032DATA-2323
Based on these official guides:
* https://support.openvpn.com/hc/en-us/articles/*************-Access-Server-Install-Let-s-Encrypt-SSL-Certificates-and-Automate-it-via-CertBot
* https://openvpn.net/as-docs/tutorials/tutorial--install-ssl-certificate.html

##### Setup  certifcate, port 80 needs to be open in security group

Example set-up for feature

```commandline
root@ip-172-20-1-52:~# certbot certonly --standalone --preferred-challenges http -d vpn.feature.thefilter.com
Saving debug log to /var/log/letsencrypt/letsencrypt.log
Requesting a certificate for vpn.feature.thefilter.com

Successfully received certificate.
Certificate is saved at: /etc/letsencrypt/live/vpn.feature.thefilter.com/fullchain.pem
Key is saved at:         /etc/letsencrypt/live/vpn.feature.thefilter.com/privkey.pem
This certificate expires on 2024-10-23.
These files will be updated when the certificate renews.
Certbot has set up a scheduled task to automatically renew this certificate in the background.

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
```

```commandline
/usr/local/openvpn_as/scripts/sacli --key "cs.priv_key" --value_file "/etc/letsencrypt/live/vpn.feature.thefilter.com/privkey.pem" ConfigPut 
/usr/local/openvpn_as/scripts/sacli --key "cs.cert" --value_file "/etc/letsencrypt/live/vpn.feature.thefilter.com/cert.pem" ConfigPut
/usr/local/openvpn_as/scripts/sacli --key "cs.ca_bundle" --value_file "/etc/letsencrypt/live/vpn.feature.thefilter.com/chain.pem" ConfigPut 
/usr/local/openvpn_as/scripts/sacli start
```

Once done, reboot the server, and then go into the admin server and update the hostname so the certificate matches correctly.

#### Certifcate auto-renewal

On the Open VPN EC2, follow these steps:

1. ```vi /usr/local/sbin/certbotrenew.sh```

2. Enter this data:
```
#!/bin/bash
certbot renew --standalone
sleep 30
/usr/local/openvpn_as/scripts/sacli --key "cs.priv_key" --value_file "/etc/letsencrypt/live/vpnds.pre.thefilter.com/privkey.pem" ConfigPut 
/usr/local/openvpn_as/scripts/sacli --key "cs.cert" --value_file "/etc/letsencrypt/live/vpnds.pre.thefilter.com/cert.pem" ConfigPut
/usr/local/openvpn_as/scripts/sacli --key "cs.ca_bundle" --value_file "/etc/letsencrypt/live/vpnds.pre.thefilter.com/chain.pem" ConfigPut 
/usr/local/openvpn_as/scripts/sacli start
```

3. ```chmod +x /usr/local/sbin/certbotrenew.sh```
4. ```crontab -e```
5. Enter this data:
```
SHELL=/bin/bash
0 8 1 * * /usr/local/sbin/certbotrenew.sh
```
---
### Monitoring services

Whilst the majority of our monitoring is within CloudWatch, we use some external tools for API health checks.
* [Status Cake](https://www.statuscake.com) - (Shared account - <EMAIL>) Used for API health monitoring and alerting - access details available in LastPass