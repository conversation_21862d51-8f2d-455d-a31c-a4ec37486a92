# Fallback

This section details the fallback functionality available as part of the 24iQ Platform.
The purpose of the fallback is to provide a set of cached responses in the case that there
is a major issue within AWS and the recommendations cannot be served as normal.

## Architecture

The basic flow for the fallback is as follows:

```mermaid
graph LR
    R53(Route53 Healthcheck) 
    R53 --Healthy--> R53PRI(Route53 Primary Record) --> API(Customer API Gateway)
    API --> LAM(API Lambdas)
    R53 --Unhealthy-->  R53FAIL(Route53 Failover Record) --> CDN(CloudFront Fallback Distribution)
    CDN --> S3(S3 Cached\n Recommendations)
```

## Deployment

To deploy the fallback stack, use the `deployment/scripts/fallback_deploy.py` script. This
will then deploy the following resources to the `us-east-1` region for use with CloudFront.

* S3 Bucket - Linked to the Cloudfront distribution and used to store recommendation response
* CloudFront Distribution - CDN used to serve recommendations in case of an outage
* Fallback Alarms - Provides alerts when fallback is in use, or getting errors
* Lambda@Edge functions - Functions used to rewrite messages and provide auth
* Route53 Healthcheck - Healthcheck used to switch between API and Fallback serving of recommendations
* Route53 Records - Provide primary and failover records to allow switching to fallback

To run the script, use the following command:
```shell
PYTHONPATH=../.. python3 fallback_deploy.py -e <environment> -t <tag> -c <customer>
```

This will deploy the CloudFormation for you to the correct region, and then carry out the
manual steps to create the Route53 records and necessary certificates.

## Population via Batch Job

The recommendations served by the fallback distribution are generated by the `fallback_response_objects`
job. By default, this job is set to run in two modes:
* Daily for general recommendations
* Weekly for search fallback support

### Daily job

The daily job makes use of the various charts that are available to provide a fallback object
for RFY, Hero, Trending, Search & Browse models. For MLT, once an MLT model is available,
then the MLT results are stored to allow the return to be the same as the standard recommendation.

There is a generic set-up that should be suitable for all customers, however the job is
customisable like other jobs to allow for adaptations depending on certain customer customisations. 

### Weekly search job

Due to the time it takes to build search results, this job is run on a weekly basis. For 
search, a number of requests are made to ElasticSearch based on upto 3 character searches
and the results for these are then stored within the bucket. This allows for some search 
support in the case that the ElasticSearch has gone down for any reason.

## Lambda@Edge support

To help support the transformation of a fallback request to an S3 object, there are a
number of Lambda@Edge functions that are made use of by the CloudFront distribution, namely:

* `cloudfront_auth` - Provides a Cognito auth layer on top of the fallback
* `cloudfront_fallback_request_rewriter` - Take the requests and maps it to the correct object within S3. Has special handling for MLT and Search slots
* `cloudfront_fallback_origin_response` - Handles the response from S3, in the case of a 400, returns an empty recommendation response
