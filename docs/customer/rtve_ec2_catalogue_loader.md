# RTVE Catalogue loader update

To update the RTVE catalogue loader in the `preproduction` and `production` environments,
you'll need to follow these steps. The steps differ slightly for the `auto` and `manual` loaders.

## General Overview

In both cases, the EC2 is set-up to be running from the `master` and should **not** be changed 
from this. This allows for a simple update procedure to get the latest code changes as part of
a deployment. 

To run update the instance, you'll require a GitHub Token that has been authorised via
SSO. This can be created at: https://github.com/settings/tokens, following the steps to
create a classic token, with an expiration, then configure SSO to allow for access.

## Manual Loader

This system can be updated in the same manner as any other GitHub checkout. 

1. Start and log onto the system via EC2 Connect in the AWS Console.
2. Navigate to `/home/<USER>/Github/thefilter`
3. Run the `git pull` command and enter your GitHub username, and the access token gathered above
4. That's it, the system is updated ready for running

## Automatic Loader

Much like the manual, the steps are fairly straight forward, but require exiting before
too much of the loader can run, so be prepared before running these steps. 

1. Start and log onto the system via EC2 Connect in the AWS Console
   a. **NOTE**: once started, the catalogue loader job will start, so you'll want to run these commands as quickly as possible
2. Navigate to `/home/<USER>/Github/thefilter`
3. Run the `git pull` command and enter your GitHub username, and the access token gathered above
4. Run the shutdown now command, to stop the instance and running tasks instantly
  ```
  sudo shutdown now
  ```