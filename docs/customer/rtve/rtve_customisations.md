# RTVE Customisations

This document outlines customisations made to support RTVE's specific requirements. As RTVE is a direct customer of 
The Filter, these customisations are tailored to their needs and may not be applicable to other customers.

In any case where code or queries is referenced here, it's as a general guide and may not be the exact code used in
the current code. It is intended to illustrate the logic and approach taken to meet RTVE's requirements.

## Aliased type names

RTVE have a concept of type names that are not directly represented by the classic `thing.typeName`.

These typenames imply a combination of typename and several other filters that are likely confusing in isolation. 

### Video

![RTVE video result example](rtve_video_example.png)

Ultimately ends up being a combination of TVEpisode and Movie. The SQL below is broadly what happens:

TVEpisode should be deduplicated on TVSeries via the brand id.

```sql
WHERE
    m.thing_custom_contenttype = 'video'
    AND m.thing_custom_type_id  = 39816 -- "completo"
    AND m.thing_custom_consumption NOT IN ('live', 'Live')
    AND m.thing_custom_active_state = true
```

### Podcast

![RTVE podcast result example](rtve_podcast_example.png)

Should always be AudioEpisode, deduplicated on AudioSeries via the brand id

```sql
WHERE
    m.thing_custom_contenttype = 'audio'
    AND m.thing_custom_type_id  = 39816 -- "completo"
    AND m.thing_custom_consumption NOT IN ('live', 'Live')
    AND m.thing_custom_active_state = true
```

### Documentary

![RTVE documentary result example](rtve_documentary_example.png)

```sql
WHERE
    m.thing_custom_contenttype = 'video'
    AND m.thing_custom_subtype_id = 130735 -- documentaries
    AND m.thing_custom_type_id  = 39816 -- "completo"
    AND m.thing_custom_consumption NOT IN ('live', 'Live')
    AND m.thing_custom_active_state = true
```

## Deduplication

In some cases, RTVE has a requirement to deduplicate content based on the `thing_brandid` field. This is used to ensure 
that only one episode of a series is shown in the chart, even if there are multiple episodes available from that 
series which should be shown based on the chart's criteria.

## Chart customisations

There's a fair bit of custom chart logic to consider. 

In the standard chart builder this is mostly contained within `thefilter/jobs/standard_chart_builder/customers/rtve/rtve_additional_chart_builder.py`

### Searched-for chart metric

RTVE has a custom chart metric that is not available in the standard chart builder. This metric is based on how many 
plays came from search, and is used to determine the popularity of content that is being actively searched for by users.

The logic for this is something like:
```sql
WHERE
    action = 'play'
    AND m.thing_brandid IS NOT NULL
    AND thing_custom_active_state = TRUE
    AND headers_referer = 'https://www.rtve.es/play/buscador/'
    AND CAST(datepartition AS DATE) > DATE_ADD('day', -{days}, CURRENT_DATE)
    AND e.ignore is null
```

