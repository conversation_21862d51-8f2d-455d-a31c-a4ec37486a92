# Backups

To improve stability and support restoring potentially lost data, we have a number of backup
routines in place to cover the long-term storage used within the 24iQ platform. 

The following services are backed up in one form or another:
* DynamoDB - used to store recommendations, entity association, editorials, slots & configuration
* ElasticSearch - used to store metadata, search and browse indices
* S3 - used to store customer data for use in Athena

**NOTE**: See [Confluence](https://aferian.atlassian.net/wiki/spaces/PRDDATADATAOPS/pages/50824137/24iQ+Backup+Recovery+Testing) for more on 
backup recovery testing

## DynamoDB Backups

All tables within DynamoDB have two forms of backup associated with them:
* Point-in-Time Recovery (PiTR) - AWS managed and allows a table to be rolled back to a specific point in time
* Automated Backup - Controlled by tags, this uses the AWS Backup to backup a table on a daily or weekly basis

For PiTR, this is fully managed by AWS and provides a number of restore points going back
over the last month. This is configured by CloudFormation during installation.

For Automated Backup, there is a special `Backup` tag that can accept either the `Daily` 
or `Weekly` value. This will inform AWS Backup to run a backup either on a daily or weekly
basis.

**NOTE**: To restore a DynamoDB table, you need to create a clone table from a backup, delete 
the original then restore the clone to the name of the original.

## ElasticSearch Backups

As with DynamoDB, there are two forms of backup associated with 24iQ's ElasticSearch clusters:
* Hourly Snapshot - Managed by AWS
* Weekly S3 Backup - Weekly operational batch job set-up to back-up all indices in a cluster

The hourly snapshot, provided by AWS, produces a set of internal backups that are only available
to the domain whilst it's in service. If the domain was deleted for any reason, then these
backups are no longer accessible. However, they do provide an way to restore an index if 
it's deleted, or data within is unexpectedly removed. 

The weekly backup, is managed by an operational job. This runs in the early hours of Tuesday
and backups all the indices across all clusters in all regions. This more thorough backup, 
to S3, can then be used to fully restore a domain, if the domain itself has been deleted.

## S3 Backups

S3 buckets for customers aren't backed up, per se, however the data within them is recoverable
via a couple of methods:
* Versioned buckets
* Lower environment 'backup'

All customer buckets have versioning enabled, this allows for multiple versions of files
to be stored within the bucket. If some data is over-written or deleted, then it's simply
the case to find the file in-question in the AWS S3 Console with `Show Versions` enabled
and remove the `Delete Marker`, this will then restore the file for use in other processes.

Finally, for all production customers, there is a data replay set-up, such that 100% of 
events received on the production are replayed to the pre-production environment. This means
that should the event data in production be removed for some reason, there is a backup of it
available in a separate account that could be migrated over. 