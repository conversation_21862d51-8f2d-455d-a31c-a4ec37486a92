# 4. Add the option to read chart from dynamodb or elasticsearch based on slot configuration

Date: 2025-07-22

## Status

Accepted

## Context

Currently, charts used in the recommendation API are retrieved from DynamoDB. These charts are static lists of IDs that are filtered after retrieval based on various criteria specified in the request parameters (genre, typename, etc.). This approach has several limitations:

1. The filtering happens after the chart is retrieved, which means we need to retrieve a larger chart than necessary to ensure we have enough items after filtering.
2. We can't easily incorporate real-time metrics or scoring into the chart ordering.
3. We need to maintain separate charts for different variants (e.g., 1d, 7d, 30d) and overlays.

Recently, we've implemented a dynamic chart builder that writes charts to Elasticsearch. These dynamic charts include additional metadata and metrics that could be used for more sophisticated filtering and scoring. By reading charts from Elasticsearch instead of DynamoDB, we can:

1. Apply filters as part of the query, retrieving only the items that match the criteria.
2. Use different scoring fields based on the variant and overlay.
3. Implement deduplication at query time, ensuring we get the highest scoring item for each dedupeId.

## Decision

We will add the option to read charts from Elasticsearch instead of DynamoDB based on slot configuration. The approach will be as follows:

1. **Slot Configuration**: Add an `isDynamicChart` flag to the slot definition JSON in the `modelDefinitions.parameters` section. This flag will determine whether to use the Elasticsearch-based chart or the DynamoDB-based chart.

2. **Chart Service**: Create a new `ChartService` class that manages the logic around retrieving charts. 
   - This service will check the `isDynamicChart` flag and decide whether to query Elasticsearch or DynamoDB.
   - If `isDynamicChart` is true, it will query Elasticsearch via an Elasticsearch flavour of chart repository.
   - If `isDynamicChart` is false, it will continue to use the existing DynamoDB chart repository.

3. **Model Filters in Elasticsearch Queries**: Instead of applying model filters after retrieving the chart, incorporate them into the Elasticsearch query:
   - Convert the model filters from the request parameters to Elasticsearch query filters.
   - Support all existing filter types (genre, typename, excludeGenre, etc.).
   - Apply these filters as part of the Elasticsearch query to retrieve only matching items.
   - Add a parameter to specify whether we should post-filter the results after retrieval. If this parameter is set to true, we will apply the filters after retrieving the chart, similar to the current DynamoDB implementation.

4. **Variant and Overlay Support**: Use the variant and overlay parameters to determine the scoring field:
   - For variant "1d", use the day_1_* metrics.
   - For variant "7d", use the day_7_* metrics.
   - For variant "30d", use the day_30_* metrics.
   - Apply overlay-specific scoring adjustments if specified.

5. **DedupeId Implementation**: Support deduplication at query time:
   - Add a dedupeId field to chart documents (typically the brand ID).
   - Use Elasticsearch aggregations with top_hits to get the highest scoring item for each dedupeId.
   - Return the deduplicated results as the chart.

## Consequences

### Benefits:
1. **Improved Filtering**: Filtering at query time means we only retrieve items that match the criteria, improving efficiency.
2. **Dynamic Scoring**: We can use different scoring fields based on the variant and overlay, providing more relevant recommendations.
3. **Efficient Deduplication**: Deduplication at query time ensures we get the highest scoring item for each dedupeId.
4. **Flexibility**: The approach allows for a gradual migration from DynamoDB to Elasticsearch, as we can enable it on a per-slot basis.

### Risks and Mitigations:
1. **Performance**: Elasticsearch queries might be slower than DynamoDB lookups for simple cases. We'll need to monitor performance and optimize queries as needed.
2. **Complexity**: The implementation adds complexity to the chart retrieval process. We'll need to ensure proper documentation and testing.
3. **Backward Compatibility**: We need to ensure that existing slots continue to work with the DynamoDB implementation. The `isDynamicChart` flag will default to false for backward compatibility.
4. **Error Handling**: We'll need robust error handling to fall back to DynamoDB if Elasticsearch queries fail.

### Implementation Considerations:


The implementation should be backward compatible, with the `isDynamicChart` flag defaulting to false.

There are absolutely scenarios where querying things live from Elasticsearch is not appropriate, such as when we aren't 
building a lot of different variants, or when traffic is exceptionally high. In these cases, we will continue to use 
DynamoDB. However, we will also be able to read charts from Elasticsearch based on slot configuration, allowing 
us to leverage the benefits of dynamic charts when appropriate.
