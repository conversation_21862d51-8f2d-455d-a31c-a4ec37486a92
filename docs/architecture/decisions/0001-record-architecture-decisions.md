# 1. Record architecture decisions

Date: 2025-07-15

## Status

Accepted

## Context

We need to record the architectural decisions made on this project.

## Decision

We will use Architecture Decision Records, as [described by <PERSON>](http://thinkrelevance.com/blog/2011/11/15/documenting-architecture-decisions).

When we create new records, we use [adr-tools](https://github.com/npryce/adr-tools) to generate a new file in the 
`docs/architecture/decisions` directory. The filename will be in the format `000X-description.md`, 
where `X` is a sequential number and description is a short identifier for the decision.

With adr tools installed, we can create a new record with the command:

```bash
adr new "Store users in a user table"
```

## Consequences

See <PERSON>'s article, linked above. For a lightweight ADR toolset, see <PERSON>'s [adr-tools](https://github.com/npryce/adr-tools).
