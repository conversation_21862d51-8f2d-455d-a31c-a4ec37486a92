# 3. Start building some charts in to elasticsearch

Date: 2025-07-17

## Status

Accepted

## Context

In cases where we have a highly filter-able chart there are scenarios where some filter permutations can result in a 
small number of items actually coming back in a recommendation call. This problem is down to the fact that we are:
1. Prebuilding the top N items for a few filter permutations
2. Storing those as named charts in dynamodb
3. Applying a suite of filters on those charts in the API code before we return the recommendation

This means that if we have a chart that has the most popular 1000 items in it, but the call applies a filter that 
excludes all of those items, it's an empty result set.

In the case of RTVE, we already build a lot of permutations of these charts to try and account for a lot of the 
different scenarios, but that is already becoming prohibitively expensive to run and maintain. Adding _more_ charts 
to the system is not a viable option, especially to account for cases like we're seeing where we'd need to multiply the 
number of charts by the number of distinct country codes we're supporting.

## Decision

We will start building some charts in elasticsearch. This will start as an RTVE-only feature to deal with the current 
issues we're facing there, but will move out to a more general solution in the future if it proves to be successful.

The rationale for our decision is as follows:
- We can build fewer distinct charts, saving on the cost of building and maintaining them.
- We can build charts that are more dynamic, allowing us to filter them in a way that is more efficient.
- We already use elasticsearch for other parts of the recommendation system, so this is a natural extension of that.
- We can leverage the existing infrastructure and expertise we have in elasticsearch to build these charts.

Moving beyond the immediate needs of the RTVE case:
- We can use upcoming work around user clustering to personalise charts on the fly with elasticsearch's newer 
  - Though this will require us to migrate to a newer version of elasticsearch
  - User clustering is a future work item that will be based off [the user table work](0002-store-users-in-a-user-table.md)
- We can build charts that are more flexible and can adapt to different use cases, rather than being tied to a specific 
  set of filters or permutations.

In this initial case, the RTVE chart build will also manage the generation of a new chart index in elasticsearch. Each 
time it runs, it will:
- Do the expected chart processing
- Create a single new chart index and populate it with the results of the chart processing
- Flip the chart alias over to the new index
- Delete the previous chart index

Then, if a chart slot is configured to read from the elasticsearch chart, it will:
- Query elasticsearch for the chart data, applying any additional filters as needed as part of the query
- Return the results to the API

This will solve the immediate problem of needing to filter charts that have very few items in them. Even if the first 
1000 items in the chart are not allowed by a given filter set, the elasticsearch query can still return the next best 
items until the filters exhaust the entire catalogue.

## Consequences

**Positive**
- Chart builds will be more efficient and cost-effective.
- We can handle more complex filtering scenarios without needing to prebuild every possible permutation.
- We will be able to support more dynamic and personalised recommendations in the future.

**Negative**
- We need to support both the existing chart system and the new elasticsearch-based charts for a while, which will add complexity.
- It's more load on elasticsearch, which may require us to monitor and optimise performance as we scale.
