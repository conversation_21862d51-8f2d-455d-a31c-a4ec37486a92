# 2. Store users in a user table

Date: 2025-07-15

## Status

Accepted

## Context

Our platform requires a consistent, extensible, and privacy-conscious user model to support:

- Advanced user-based recommendation systems
- Analytics and segmentation
- Compliance with anonymisation and data protection regulations

We need to accommodate various types of identifiers (e.g., device IDs, profile IDs), track relationships between users 
and entities (e.g., bookmarks, favourites), and store derived summary data for ML and reporting use.

## Decision

We will implement a new user system using **two DynamoDB tables**. 

These tables will use a new internal user identifier (`tfid`) that will be treated as a primary key for user records. 
This is to provide an abstracted, consistent identifier that can represent a user without relying on the external 
identifiers we use elsewhere and focus us towards a more privacy-conscious approach that can support simpler data 
anonymisation in the future. Hypothetically, we can do this by using a salted hash of the external identifiers. However, 
we will not be implementing this in the first iteration of the user system since it really needs to be considered 
as part of the whole inbound event processing flow and not just the user system.

These tables can be pushed to S3 in order to be used by Athena queries, but we will not be doing this in the first iteration.

### 1. User Table

Stores derived attributes about each user, such as:

- Top genres, brands, device platforms
- Churn and activity scores
- Content affinity indicators (e.g., VOD vs. live, popularity vs. recency)
- Campaign attribution
- Clustering/model results for ML
- Timestamps and S3 partitions for event sourcing

#### Columns

|                       |               |                                                                                             |
|-----------------------| ------------- |---------------------------------------------------------------------------------------------|
| tfid                  | str           | The internal identifier for a user, starts out as primary id                                |
| userProfileType       | str           | kids, adult, mixed, mixed_profile, etc.                                                     |
| devicePlatforms       | List[str]     | the most recent 5 device_platforms                                                          |
| deviceTimezones       | List[str]     | the most recent 5 device_timezones                                                          |
| timestampAdded        | TimeStamp     | the timestamp when this user was added to the database                                      |
| timestampInitiated    | TimeStamp     | the most recent time the user job ran to collect information about this user                |
| topRecTypes           | str           | the most recent 5 rec types (MLT, RFY, BYW, Most Popular, etc.)                             |
| topGenres             | List[str]     | the most recent top 5 genres                                                                |
| topBrands             | List[str]     | the most recent top 5 brands                                                                |
| actions               | Obj[str, int] | object of the action types and count                                                        |
| avgProgressPct        | float         | average of all play progress as a percentage, null if no plays                              |
| campaign              | str           | the promotional campaign that brought the user to the platform                              |
| vodLive               | float         | a value between 0 and 1 that indicates if a user values vod or live data                    |
| brandCount            | int           | the distinct number of brands played                                                        |
| activity              | int           | a score of 1-4 of low to high activity                                                      |
| partition             | S3 location   | s3 location with year, month and day partitions                                             |
| assetTypes            | Obj[str, int] | the typeName of asset and the count                                                         |
| churnStatus           | int           | a score of 1-4 of low to high churn                                                         |
| clusterUserSimilarity | guid          | a cluster value for the user, that relates to a DS model (Investigation required)           |
| popularityRecency     | float         | a value between 0 and 1 that indicates if a user values item popularity or recency          |
| discoveryHabit        | float         | a value between 0 and 1 that indicates if a user values new brands or a small set of brands |
| timeToLiveEpoch       | float         | The timestamp (epoch) after which this row should expire (for TTL cleanup)                  |


### 2. Realtime Relationship Table

Captures dynamic interactions between users and entities or "things", including:

- Favourites, ratings, bookmarks, blocks, continue-watching
- Optional contextual value (e.g., rating amount, progress percent)

This is extremely similar to the concept of "entity associations" in the existing system. We could reuse elements of that, 
but the difficulty there is that an entity is a customer-agnostic concept. It'd be a far bigger and more difficult change 
to make users and per-customer catalogue as "entities" and piggyback on the existing entity associations than it would be 
to introduce this as a new concept given that in most cases both sides of that relationship are customer-specific.

#### Columns

|                 |                 |                                                                                     |
|-----------------|-----------------|-------------------------------------------------------------------------------------|
| id              | str             | The id of the relationship                                                          |
| tfid            | str             | The internal user id, is a reference to the new user table                          |
| type            | str             | The type of relationship e.g. favourite, continue watching, block                   |
| entityId        | Optional[str]   | Optionally link the user to an entity                                               |
| entityType      | Optional[str]   | If we're linking to an entity, provide the entity type for reference                |
| thingId         | Optional[str]   | Optionally link the user to a thing                                                 |
| thingType       | Optional[str]   | If we're linking to a thing, provide the thing type for reference                   |
| value           | Optional[float] | Provide a contextual value to the relationship e.g. rating amount or watch progress |
| timestamp       | TimeStamp       | When the record was added                                                           |
| timeToLiveEpoch | float           | The timestamp (epoch) after which this row should expire (for TTL cleanup)          |


### Users Batch Processing Job

A daily batch job will:

- Query distinct users from the past day’s activity in Athena
- Create new identification entries as needed
- Generate 30-day summaries of these users' behaviour and write to the User Table using `24iqId` as the key

Because we're updating users by their primary id, we can just update the record each time this job processes a user.

Purely anonymous users (i.e. those without any identifiable information) will not be created in the User Table as of this 
first iteration. This allows us to focus on users with identifiable information, such as those who have logged in or 
interacted with the platform in a way that generates identifiable data. This is to avoid creating unnecessary records 
for users who do not engage with the platform in a way that requires tracking or analysis. It would also help to avoid 
scenarios where individuals would generate a large number of anonymous records due to their identifiers changing 
frequently.

If we need more information about anonymous users in the future, we can always extend this system to include them, but 
we will have to take the above in to account.

### Persistence of Users summary data to S3 for consumption by Quicksight and Athena

As part of this decision, we will _not_ be persisting the user summary data to S3 for consumption by Quicksight and Athena.

Eventually it may be useful to persist this data to S3 for reporting and analytics purposes, but for now we will focus on the
User Table and the Realtime Relationship Table to support our immediate needs for user-based recommendations. This is 
to keep the implementation simple and avoid unnecessary complexity at this stage. 

## Consequences

### Positive
- Gives us a consistent, extensible user model that can evolve with the platform
- Supports real-time user-entity interactions and relationships
- An initial hook-in point that can eventually be used for user-based recommendations and analytics

### Negative
- Batch job for user creation introduces data latency for user creation (up to 1 day)
- We introduce a variation in experience for users who are not logged in or do not have identifiable information, as they will not be represented in the User Table

## Alternatives Considered

- Single unified table 
  - Flattening all identification and behaviour into one table would reduce table complexity but would make queries and updates inefficient, particularly at scale and with varied access patterns
  - This would also complicate the separation of concerns between user identity and user behaviour, making it harder to manage data lifecycle and privacy compliance.
- Standalone "identity" table which stores identifiers, using a hash and salt to create an internal id that maps to the external identifiers
  - This would be more complex to manage and query, and actually we're not storing any PII in the user table yet, so we can always add this later if needed using the new user table as an entry point