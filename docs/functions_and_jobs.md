# Functions and Jobs

The majority of the code within this repo is either used in lambda functions, under the 
[../thefilter/functions](../thefilter/functions) directory, for use within the APIs
or under the [../thefilter/jobs](../thefilter/jobs) job directory, for use within the various,
regularly running, batch jobs.

**NOTE**: The lists below will likely get out of date overtime

## Functions

There are a number of lambda functions available for use by our clients, and too many to 
list and describe here. The current (21/10/2024) set of functions are listed below:

* [api_beacon_lite](../thefilter/functions/api_beacon_lite) - Used by UKTV to provide customer events into the platform
* [api_catalogue](../thefilter/functions/api_catalogue) - Provides support for CRUD operations on catalogue data, used exclusively by the Catalogue API
* [api_customer_add_entity_associations](../thefilter/functions/api_customer_add_entity_associations) - Allows entity associations to be added/removed
* [api_customer_entity_associations](../thefilter/functions/api_customer_entity_associations) - Get information about entity associations
* [api_delete_editorial](../thefilter/functions/api_delete_editorial) - DELETE items from the editorial (promotions and blocked items) table
* [api_events](../thefilter/functions/api_events) - Event ingestion endpoint, includes some basic validation and feedback for testing purposes
* [api_get_editorial](../thefilter/functions/api_get_editorial) - GET items from the editorial (promotions and blocked items) table
* [api_info](../thefilter/functions/api_info) - Interact with the information table to get domain & topic specific information
* [api_metadata](../thefilter/functions/api_metadata) - Get metadata about one or more requested items, by thing_id
* [api_model_config](../thefilter/functions/api_model_config) - Interact with the model configuration [GitHub](https://github.com/24i/24iQ-configuration) repository 
* [api_page_config](../thefilter/functions/api_page_config) - (Deprecated) Support for setting up page configuration, for use in `api_pages`
* [api_pages](../thefilter/functions/api_pages) - (Deprecated) Support for requesting paged recommendations, based on page configuration
* [api_post_editorial](../thefilter/functions/api_post_editorial) - POST items to the editorial (promotions and blocked items) table
* [api_promotion](../thefilter/functions/api_promotion) - Interact with item promotions
* [api_rec](../thefilter/functions/api_rec) - (MAIN function) Get recommendations from underlying models
* [api_rec_lowlatency](../thefilter/functions/api_rec_lowlatency) - (MGM+ only) Fastest/simpler `api_rec` endpoint for use in RFY email models
* [api_reports](../thefilter/functions/api_reports) - Get a mapping of seedId to user, to see which users have been recommended certain content
* [api_slot_info](../thefilter/functions/api_slot_info) - Get information about slots and what parameters they support
* [api_slots](../thefilter/functions/api_slots) - GET & POST updates to slot configuration
* [api_user](../thefilter/functions/api_user) - GET user watch history information
* [api_user_history](../thefilter/functions/api_user_history) - (UKTV only) - Get user event history, as part of a GDPR request
* [cloudfront_auth](../thefilter/functions/cloudfront_auth) - See [fallback docs](./fallback.md)
* [cloudfront_fallback_origin_response](../thefilter/functions/cloudfront_fallback_origin_response) - See [fallback docs](./fallback.md)
* [cloudfront_fallback_request_rewriter](../thefilter/functions/cloudfront_fallback_request_rewriter) - See [fallback docs](./fallback.md)
* [consumer_beacon_fast_lane](../thefilter/functions/consumer_beacon_fast_lane) - (Deprecated) - No longer used
* [consumer_catalogue_to_elasticsearch](../thefilter/functions/consumer_catalogue_to_elasticsearch) - Used by the catalogue SQS queu to send catalogue data to ElasticSearch
* [consumer_events_fast_lane](../thefilter/functions/consumer_events_fast_lane) - Passes events from the `api_events` lambda to Kinesis for storage in S3
* [consumer_request_fast_lane](../thefilter/functions/consumer_request_fast_lane) - Passes personalisation request events from the `api_rec` lambda to Kinesis for storage in S3
* [consumer_user_history_to_dynamo](../thefilter/functions/consumer_user_history_to_dynamo) - Transforms and stores specific received events in DynamoDB 
* [ec2_lifecycle](../thefilter/functions/ec2_lifecycle) - Supports start-up and shut-down of an event-specified EC2 instance
* [elasticache_events](../thefilter/functions/elasticache_events) - GET & POST events to the elasticache event store
* [gateway_authorizer](../thefilter/functions/gateway_authorizer) - Used for authorising API access either via API keys or JWTs supplied via Cognito
* [inquisitor_lambda](../thefilter/functions/inquisitor_lambda) - Supports the Inquisitor functionality for diagnosing issues as they occur
* [job_fail_message_converter](../thefilter/functions/job_fail_message_converter) - Converts and sends messages about Job failures
* [mapping_to_elasticsearch](../thefilter/functions/mapping_to_elasticsearch) - (Deprecated?) - Sends mapping from S3 to ElasticSearch
* [mparticle_events](../thefilter/functions/mparticle_events) - (MGM+ only) Support for getting realtime events from mParticle
* [operational_add_entity](../thefilter/functions/operational_add_entity) - Add new entities to the platform
* [operational_canonical_id_lookup](../thefilter/functions/operational_canonical_id_lookup) - (Deprecated) Look-up catalogue data across customer catalogues
* [operational_composer](../thefilter/functions/operational_composer) - Support for operations on Portal-driven composer pages
* [operational_config](../thefilter/functions/operational_config) - Retrieve configuration items (centralised, portal etc.) from DynamoDB
* [operational_search](../thefilter/functions/operational_search) - Search for different available entities
* [proxy_options](../thefilter/functions/proxy_options) - Provides a proxy for OPTIONS requests when used with an AWS HTTP API
* [quicksight_url](../thefilter/functions/quicksight_url) - (Deprecated) Get the necessary URL to access QuickSight dashboards via the Portal 
* [recs_to_ddb](../thefilter/functions/recs_to_ddb) - Used as part of the recs-to-ddb process, creates the necessary step function definition to run a training job
* [s3_batch_trigger](../thefilter/functions/s3_batch_trigger) - Support triggering batch jobs from S3 data dumping
* [step_function_trigger](../thefilter/functions/step_function_trigger) - (Deprecated) Used by older customer to trigger a step function to load multiple data sets
* [uktv_user_table_generation](../thefilter/functions/uktv_user_table_generation) - (UKTV) Run daily to generate new user table records for UKTV

## Jobs

As with functions, there a number of batch jobs that the 24iQ platform makes use of for
loading, ingesting and transforming data. The current (21/10/2024) set of jobs are as follows:

* [analytics_job](../thefilter/jobs/analytics_job) - Analytics related jobs, either for Data Operations or Analytics usage
* [backstage_metadata_generation](../thefilter/jobs/backstage_metadata_generation) - Converts 24iQ metadata into Backstage format
* [customer_event_loader](../thefilter/jobs/customer_event_loader) - Support for loading events from customer event formats
* [entities_persistence](../thefilter/jobs/entities_persistence) - Persist entity informaton
* [events_persistence](../thefilter/jobs/events_persistence) - Transform and persist events from `api_events` and `api_beacon_lite`
* [fallback_response_objects](../thefilter/jobs/fallback_response_objects) - Create recommendation response objects to use in fallback scenarios
* [inquisitor](../thefilter/jobs/inquisitor) - Provide information about the platform
* [metadata_persistence](../thefilter/jobs/metadata_persistence) - (Deprecated) Persist metadata from ElasticSearch to S3
* [operational_jobs](../thefilter/jobs/operational_jobs) - Houses tasks relating to operational parts of the system like ElasticSearch backup
* [page_compiler](../thefilter/jobs/page_compiler) - (Deprecated) Pre-compile pages for use in `api_pages`
* [persistence_jobs](../thefilter/jobs/persistence_jobs) - Small persistence tasks from DDB to S3 for customer editorial, entity association etc.
* [recommendation_table_jobs](../thefilter/jobs/recommendation_table_jobs) - Jobs related to the recommendation table, mostly export and reporting
* [recs_to_ddb](../thefilter/jobs/recs_to_ddb) - Support for storing Data Science generated recommendations from S3 into DDB
* [simplytv_importer](../thefilter/jobs/simplytv_importer) - (BackStage only) Provides a regular job to import EPG data from SimplyTV into Backstage
* [standard_browse_indexer](../thefilter/jobs/standard_browse_indexer) - Build a browse index for customers, with optional customer specific code
* [standard_catalogue_loader](../thefilter/jobs/standard_catalogue_loader) - Convert customer catalogue data, from multiple sources and data types, to 24iQ format 
* [standard_chart_builder](../thefilter/jobs/standard_chart_builder) - Build multipls charts (most popular, latest, trending etc.) for customers, with optional customer specific code
* [standard_search_indexer](../thefilter/jobs/standard_search_indexer) - Build a search index for customers, with optional customer specific code
* [user_table_generator](../thefilter/jobs/user_table_generator) - Used by Data Analytics, to generate user related information for use in Analytics dashboards and reports
