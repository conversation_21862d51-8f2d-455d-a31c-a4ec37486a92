## `thefilter` / 24iQ Documentation

This folder contains some general documentation about the set-up, deploy and some frequently 
asked questions for `thefilter` repo. Each section is within it's own folder and this README
acts as a contents page for further documentation.

### Contents
* [Basic architecture](architecture.md)
* [Unit testing](unittests.md)
* [Deployment](deployment.md)
* [Release Process](releases.md)
* [Portal / Cognito User Creation](cognito_user.md)
* [Fallback](fallback.md)
* [Backups](backups.md)
* [Functions & Job Overview](functions_and_jobs.md)
* [Recommendations and their responses](recommendations.md)
* [New Environment Set-up](new_environment.md)
* [New Customer Set-up](../deployment/scripts/new_customer/README.md)
* [QuickSight](../deployment/scripts/quicksight.md)
* [External Services - Including ElasticSearch, OpenVPN and monitoring services](external.md)
* [Tips & Tricks](tipsandtricks.md)

**NOTE:** GitHub was chosen to keep this information alongside the code, rather than using
Confluence. This should allow for a search within this repo to return both code and associated
documentation and information.