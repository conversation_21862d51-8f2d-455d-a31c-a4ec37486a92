# Unit Testing

In an ideal world, all code changes would have an associated unit test(s) to at least cover
the change that is being made. Unfortunately, certain processes have been written in a way 
that it's not possible to construct a unit test without a lot of effort. 

This document contains information on how to run the unit tests in this repository, as well
as generating coverage statistics.

### Running the unit tests

Before running the unit tests, ensure that you have the latest set of requirements installed.
To do this, run the following command from the root of the repository.
```shell
pip3 install -r full_requirements.txt
```

Once installed, the unit tests can be configured in PyCharm using the following steps:
1. Open up `Run > Edit Configurations`
2. Select the `+` to add a new configuration and select the `Unittests` option under `Python Tests`
3. Set the following parameters:
    - Target: Script Path with value: `/absolute/path/to/repo/thefilter/tests`
    - Working Directory: `/absolute/path/to/repo/thefilter`
    - Tick `Add content roots to PYTHONPATH`
    - Tick `Add source roots to PYTHONPATH`

With these commands, followed you'll be able to run the unit tests. However, certain tests
require AWS credentials to be set-up. This will require the AWS CLI tool to be installed 
and configured. Information for how to do this is available here: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html

Once installed, it is suggested to configure your connection via SSO following the steps 
provided by AWS. In short to do this, run `aws configure sso` in a terminal and fill in 
the required information. Once completed, the rest of the unit tests should run as expected.
Further usage of AWS service, may require a `aws sso login` when the session expires.

**Note**: The `~/.aws/config` file needs to be set-up with the following profile to allow all tests to run:
```text
[default]
region=eu-west-2
output=json
```

### Unit test coverage

To see the coverage of our unit tests, you can run the following command in a terminal:
```shell
coverage run --source=thefilter -m unittest discover -s "tests" && coverage html
```
This will output an html coverage report in the `htmlcov` directory. The output is searchable,
sortable and allows for seeing the line coverage for each file within the codebase. An example
output is as follows, trimmed for readability:

```
Name                                                                          Stmts   Miss  Cover
------------------------------------------------------------------------
thefilter/aws/api_gateway.py                           149     47    68%
thefilter/aws/app_config.py                             14      0   100%
thefilter/aws/athena.py                                209    124    41%
thefilter/aws/aws_lambda.py                            199     61    69%
thefilter/aws/batch.py                                  54     25    54%
thefilter/aws/cloudfront.py                             33      5    85%
thefilter/aws/cloudwatch.py                             33     11    67%
thefilter/aws/cognito.py                                31      0   100%
thefilter/aws/dynamoDB.py                              994    514    48%
thefilter/aws/elastic_search.py                        696    390    44%
...
------------------------------------------------------------------------
TOTAL                                                32011  14607    54%
```

Further documentation available here: https://coverage.readthedocs.io/en/7.3.2/
