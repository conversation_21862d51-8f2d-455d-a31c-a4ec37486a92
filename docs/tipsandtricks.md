# Tips & Tricks

This section looks to cover some miscellaneous tips and tricks that don't really fit into
any other section but are worth documenting nonetheless.

### S3 Environment to Environment copying

For a much faster transfer of files from one environment to another, it is possible to
grant permissions to a different account to put items into an s3 bucket in another account.
To do this, you'll want to add the following policy section to the S3's Bucket Policy you want
to **copy** to.

```json
        {
            "Sid": "ProdToPreCopy",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::<aws_account_number>:root"
            },
            "Action": [
                "s3:Put*",
                "s3:List*",
                "s3:AbortMultipartUpload",
                "s3:GetObjectAcl",
                "s3:GetObjectTagging"
            ],
            "Resource": [
                "arn:aws:s3:::thefilter-<env>-<region>-<customer>",
                "arn:aws:s3:::thefilter-<env>-<region>-<customer>/*"
            ]
        }
```

The example is for customer data, but could equally apply to any other bucket. 

**Note**: In the interest of security, prod should only be able to copy to pre, and pre to
feature. 

Finally, to then copy a directory, e.g. `source/events/` from one production to pre,
the following command could be used:
```shell
aws --profile production s3 cp --recursive s3://thefilter-production-<region>-<customer>/source/events/ s3://thefilter-pre-<region>-<customer>source/events/  --acl bucket-owner-full-control
```

### S3 Bucket Emptying

Follow the guide here to automatically empty a bucket using lifecycle rules: https://repost.aws/knowledge-center/s3-empty-bucket-lifecycle-rule