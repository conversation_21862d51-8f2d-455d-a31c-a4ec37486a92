# New Environment/Region Deployment

To allow for code (cloudformation, functions & jobs) to be added to a new region, the following areas need to be updated:
- `config/ci_build-images.json` - Add new region to jobs that will be needed in that region
- `build_s3.py` - Add new region to REGIONS variable at top of file
- `build_push_images.py` - Add new region to the region_profile_map in the `push_image` method
- `operational_ddb.yaml` - Add new Replica for 'Global' DDB tables 
- `operational_gateway.yaml` - Add in new region to ApiSlotInfoRole & QuickSightURLRole
- `thefilter/config/aws_accounts.py` - Update environments variable to include the new region
- `thefilter/config/elasticache_config.py` - Update ec_map variable to include the new region
- `thefilter/config/elasticsearch_config.py` - Update es_mp variable to include the new region
- `thefilter/aws/s3.py` - Add new region to the `used_aws_regions` variable
- `thefilter/jobs/events_persistence/events_persistence.py` - Add new region to the `used_aws_regions` variable

# New region
- Create new certificate via ACM for the `{env}.thefilter.com` domain, with associated wildcard
- Update the operational_ddb stack in the core (eu-west-2) region in that environment to ensure global tables are created
- AppConfig Under the core region for each environment (eu-west-2 in both pre and production), update the deployment parameters config to include any missing stacks.
- Parameter Store: Manually create the VPC related SSM parameters and run the create_and_update_ssm_parameters script for that region
- Parameter Store: Add entries for Postman manually

## Deployment notes
- Start with VPC stack on its own
- Then deploy the ElasticSearch stack, and wait for it to complete.
- Then deploy the Key stack
- Then deploy the Messaging stack
  - Confirm subscriptions to notification topics
- Then deploy the Core Operational stack
- Then deploy the rest of the core stacks, following the information below

### ElasticSearch / database stack
This stack needs to be deployed manually due to the time it takes to deploy/create the 
resources. The initial stack can be created via a dry-run, then exit and start the actual
deployment within the CloudFormation AWS console - select the stack, change sets then 
execute change-set.

Once deployed, manually create a s3 bucket `thefilter-{env}-elasticsearhc-meta-backup-{region}` for the scheduled ES backups, then update and 
run the [tools/scripts/elasticsearch/snapshot_creator.py](../tools/scripts/elasticsearch/snapshot_creator.py) script
to create the necessary snapshot setup. Note, you'll need to be connected to the VPN for that
region and environment to create the snapshots, so will want to create the OpenVPN service first
using the instructions below.

### Core Operational Stack
Note that an EC2 key pair will need to be created manually with the name <EnvironmentName>-ec2
  #  e.g production-ec2

### Catalogue
Within APIGateway, go to settings and set the CloudWatch Logging role to:
  `arn:aws:iam::{account}:role/TF-CloudWatch`

Then once the stack is deployed, manually set-up the custom domain name in API Gateway and add an API mapping to the catalogue API itself.
Finally, in Route53 configure this link by creating an entry under the `{env}.thefilter.com` Hosted Zone

### Canonical Database
ToDo!
- Sync data from main cluster to entities index in new region
- Run persistence job
- Create glue crawler and generate tables and database manually

### OpenVPN Set-up
Previous: Follow the instructions in this video: https://www.youtube.com/watch?v=VWqRrMGHJQg

Manual instructions:
- Launch EC2 Instance
- Search for OpenVPN Access Server in AMI - likely the first match - no connected devices text
- Select the AMI and Subscribe to i
- Create a fresh key pair (OpenVPN-env-region)
- Update network settings to the the vpc created, and a public subnet (e.g. env-vpc)
- Create a new security group, but remove SSH access, this will be added later
- Launch Instance
- Set-up Elastic IP address and associate with instance
- From list of instances, select instance and press 'Connect', this will provide a subnet to add for SSH access (EC2 Connect)
- Run through steps and select defaults
- Connect to instance
- Follow info in  to set-up SSL certificates (https://aferian.atlassian.net/browse/PRJ032DATA-2323)

### DDB backup bucket
For manual use, we need to set-up a bucket to be used with DynamoDB. This should be created with
the name `thefilter-{env}-dynamodb-backup-{region}` and can have the settings copied from one
of the existing DynamoDB backup buckets.

### S3 copying
1. Set bucket policy in pre as follows:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "DelegateS3Access",
            "Effect": "Allow",
            "Principal": {
              "AWS": "arn:aws:iam::{account_name}:user/Jane"
            },
            "Action": [
                "s3:GetObject",
                "s3:GetObjectAcl",
                "s3:GetBucketLocation",
                "s3:GetObjectTagging",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::awsexamplesourcebucket/*",
                "arn:aws:s3:::awsexamplesourcebucket"
            ]
        }
    ]
}
```

2. Copy using the command:
```commandline
aws --profile production s3 cp s3://{pre_bucket} s3://{prod_bucket} --recursive --source-region eu-west-2 --region {prod_region}
```

## DATA SCIENCE

1. Copy DS Generic Images from ECR to new region, using profiles for each like:
   (ensure a docker daemon is running)

this will take a while, so you might want to do this on EC2 instance

```commandline
v2PullThenPushToEnvs.sh awssagemaker/generic_training ************ pre_london pre_frankfurt 
v2PullThenPushToEnvs.sh awssagemaker/generic_training latest pre_london pre_frankfurt 
```

2. Manually copy across the model config for the Catalogue Processor, the MLT and RFY with today's date
   (as of 2024-09-26, Conviva had the most up to date configs), then update the rule and slots to use those model values.