# Deployment

Routine deployments are done via the ['<PERSON>' deployment script](../deployment/scripts/deploy_tag_to_env.py)
and can be used to deploy CloudFormation scripts to any of the 24iQ environments.

This page will deal with using the script, the build process and some information on 
supporting scripts.

## Deployment Script

The deployment script will generally be run in the following way:
```shell
 PYTHONPATH=../.. python deploy_tag_to_env.py -t tag_name -e feature -y -s -dr       
```

The arguments available are as follows:
* `--tag / -t` - The tag to deploy, these are available on the `tm-data-dennis_deployment` Slack channel
* `--environment / -e` - The environment to deploy to, e.g. feature, pre, production. This should match your AWS credential set-up
* `--dry_run / -dr` - Conduct a dry run of parameters before running. This is recommended as it will show you which resources will be updated
* `--auto / -a / -y` - Skip confirmation steps when deploying
* `--slackbot / -s` - Send deployment status messages to the `tm-data-dennis_deployment` Slack channel
* `--debug / -d` - Enter debug mode, mainly for use within PyCharm to debug the script
* `--config_sync / -cs` - Sync the local [centralised_config.json](../thefilter/config/centralised_config.json) with DynamoDB
* `--local / -lc` - Use local config when deploying rather than DynamoDB
* `--parameters / -p` - (Deprecated) Use a local JSON to get the CloudFormation parameters from

The following are used with GitHub actions:
* `--all` - Automatically select the **ALL** options when deploying
* `--ci_dry_run / -cdr` - Run the dry-run process and exit after. Used for getting CI to do the dry run for us as part of release process

Once running, it will show a list of Core or Customer stacks, then regions to deploy to.
Select the stacks you wish to deploy by pressing space on each one, or select ALL, then hit
enter to carry out the deployment.

The majority of CloudFormation parameters are now set as part of the script. However, a number
of older customers and the core stacks have their parameters stored within AppConfig in 
`eu-west-2` for each environment.

## Stack Overview

As part of the deployment script, you'll be presented with a list of stacks that can be deployed.
The set of core stacks is outlined below, linked to the corresponding yaml file.

### Core Stacks

The core stacks are deployed once per region. These set-up the core components that are 
shared by all customers in that region. This includes things like the ElasticSearch cluster,
SQS queues and fallback handling.

The `eu-west-2` region has a slightly different set of stacks deployed as these were created
using an older version of the stack definitions and is very hard to migrate to the newer definitions
as seen in all other regions.

#### eu-west-2 core stacks

* [env-cloudformation-role](../deployment/cloudformation/iam/role_policy.yaml) - Creates IAM roles for use in the deployment process
* [env-keys](../deployment/cloudformation/keys.yaml) - Creates keys for CloudWatch alarms 
* [env-messaging](../deployment/cloudformation/messaging.yaml) - Queues relating to messaging across the platform
* [env-stack](../deployment/cloudformation/startup/startup.yaml) - Nested stack, contains ElasticSearch, VPC, lambda layers and other core stacks
* [env-Backend](../deployment/cloudformation/cloudformationV2/backend_applications.yaml) - Operational related tasks and batch environment set-up
* [env-catalogue](../deployment/cloudformation/api_catalogue.yaml) - Shared Catalogue API definition and associated lambdas
* [env-authentication](../deployment/cloudformation/authentication.yaml) - Cognito user pool initialisation
* [env-core-fallback](../deployment/cloudformation/core_fallback.yaml) - Fallback related jobs and triggers
* [env-jobs](../deployment/cloudformation/jobs.yaml) - All job definitions. **NOTE**: if you update a job for a customer, deploy this stack
* [env-operational-ddb](../deployment/cloudformation/operational_ddb.yaml) - Shared DynamoDB tables relating to configuration, composers pages etc.
* [env-operational-gateway](../deployment/cloudformation/operational_gateway.yaml) - API gateway for operational use

#### Other region core stacks

* [env-vpc](../deployment/cloudformation/vpc.yaml) - Creates the VPC for use with the 24iQ Platform
* [env-database](../deployment/cloudformation/database.yaml) - Deploys the ElasticSearch metadata clust
* [env-keys](../deployment/cloudformation/keys.yaml) - Creates keys for CloudWatch alarms
* [env-messaging](../deployment/cloudformation/messaging.yaml) - Queues relating to messaging across the platform
* [env-core-operational](../deployment/cloudformation/core_operational.yaml) - Near clone of `backend_applications.yaml`, include the lambda layers definition
* [env-consumers](../deployment/cloudformation/consumers.yaml) - Consumer lambdas to read from the queues created by messaging
* [env-catalogue](../deployment/cloudformation/api_catalogue.yaml) - Shared Catalogue API definition and associated lambdas
* [env-jobs](../deployment/cloudformation/jobs.yaml) - All job definitions. **NOTE**: if you update a job for a customer, deploy this stack
* [env-authentication](../deployment/cloudformation/authentication.yaml) - Cognito user pool initialisation
* [env-operational-auth](../deployment/cloudformation/operational_auth.yaml) - Auth set-up related to operational API and users
* [env-core-fallback](../deployment/cloudformation/core_fallback.yaml) - Fallback related jobs and triggers

### Customer stacks

Unlike the core stacks, each of the following stacks is deployed once per customer, with some
optional extension stacks for those customers that require it.

* [env-customer-initial](../deployment/cloudformation/customers/initial.yaml) - Creates initial infrastructure for a customer, like S3 buckets
* [env-customer-backup](../deployment/cloudformation/customers/backup.yaml) - Set-up backup plans for use with DynamoDB and other storage
* [env-customer-dynamodb](../deployment/cloudformation/customers/dynamodb.yaml) - Create customer related DynamoDB tables
* [env-customer-operational](../deployment/cloudformation/customers/operational.yaml) - Customer related triggers and RecsToDDB job
* [env-customer-api](../deployment/cloudformation/customers/api.yaml) - Customer API deployment include API Gateway and lambda definitions 
* [env-customer-fallback](../deployment/cloudformation/customers/fallback.yaml) - Customer fallback related infrastructure - **NOTE** This can only be deployed in `us-east-1`
* [env-customer-core-alarms](../deployment/cloudformation/customers/alarms.yaml) - A general set of Customer related alarms
* [env-customer-auth](../deployment/cloudformation/customers/auth.yaml) - Customer related auth for use with Cognito - **NOTE** Deploy in Cognito environment. `eu-west-2` in feature and pre, `us-east-1` in production

#### Customer extension stacks

* [env-customer-api-extension](../deployment/cloudformation/customers/epix/api_extension.yaml) - API extension for a specific customer, adds new routes and lambda code. MGM+ chosen as an example
* [env-customer-alarms](../deployment/cloudformation/customers/epix/alarms.yaml) - Alarms extension for a specific customer, adds further alarms and notification. MGM+ chosen as an example

## Build Process

The build process is fully automated within GitHub actions, and is covered with the workflow files
under [workflows](../.github/workflows). The following logic is in place for running builds:

* `feature` or `pre` prefix in tag - Bundle the lambda code and push Cloudformation to specified environment. This does not include the jobs.
* `feature_images` or `pre_images` prefix in tag - Bundle all lambda functions, jobs and push to specified environment.
* `hotfix` prefix in tag - Bundle all lambda functions and jobs. This will push the hotfix to both `preproduction` and `production` environments.
* `candidate` merge - When a PR is merged to the `candidate` branch a new `images` build is created for deployment to `feature` environment.
* `master` merge - When a PR is merged from the `candidate` branch to the `master` branch  a new `images` build is created for deployment to both `preproduction` and `production` environments.

When all PRs are raised, the unit tests are automatically run and are expected to pass before merging can take place.

The workflows available are as follows:
* [image_build.yaml](../.github/workflows/image_build.yaml) - Package lambda code and CloudFormation to S3, push job images to ECR, update configuration, notify `tm-data-dennis_deployment` Slack channel and optionally dry-run the deployment 
* [lambda_build.yaml](../.github/workflows/lambda_build.yaml) - Package lambda code and CloudFormation to S3, notify `tm-data-dennis_deployment` Slack channel
* [test.yaml](../.github/workflows/test.yaml) - Run the unit tests and report on coverage levels

## Support scripts

Alongside the standard deployment scripts, there are also a number of other supporting scripts
that help with specific deployment activities. 

* [fallback_deploy.py](../deployment/scripts/fallback_deploy.py) - Cut-down deployment script to add deploying fallback stacks to `us-east-1`
* [create_and_update_ssm_parameters.py](../deployment/scripts/create_and_update_ssm_parameters.py) - Creates SSM parameters from those deployed in AppConfig, rarely used
* [sync_ddb_config.py](../deployment/scripts/sync_ddb_config.py) - Sync local [centralised_config.json](../thefilter/config/centralised_config.json) to DDB
* [report_stack_drift.py](../deployment/scripts/report_stack_drift.py) - (Deprecated?) Script to report the drift between deployed stacks and their definition 
* [pre_deploy_new_customer.py](../deployment/scripts/new_customer/pre_deploy_new_customer.py) - Steps to set up a new customer, before deploying to AWS
* [post_deploy_new_customer.py](../deployment/scripts/new_customer/post_deploy_new_customer.py) - Step to finalise set-up of a new customer, after deployment to AWS
