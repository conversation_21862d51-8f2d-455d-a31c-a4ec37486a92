import unittest
from typing import List
from unittest.mock import Mock, patch

from thefilter.model.messages.request import Request
from thefilter.rules.conditions_evaluator import BaseConditionEvaluator, \
    ConditionEvaluator, ElasticSearchSeedConditionEvaluator, ParameterConditionEvaluator, \
    RulesetConditionsEvaluator, UserConditionEvaluator
from thefilter.rules.ruleset import Condition, ConditionalTuner, EventRule, EventTuner, \
    ParameterCondition, Rule, RuleState, Ruleset, Tuner, UserCondition


def get_request(
        query_string: dict = None,
        seed_ids: List[str] = None
):
    return Request(
        query_string=query_string,
        seedIds=seed_ids
    )


@patch.object(BaseConditionEvaluator, "__abstractmethods__", new_callable=set)
class TestBaseConditionEvaluator(unittest.TestCase):
    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_tuner"
    )
    def test_evaluate_tuner_returns_None(self, mock_evaluate_tuner,
                                         mock_abstract):
        mock_evaluate_tuner.return_value = None
        rule = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="", boolean="", rules=[rule])
        condition = ParameterCondition(tuners=[tuner])
        evaluator = BaseConditionEvaluator()
        result = evaluator.evaluate(condition, Mock())
        self.assertEqual(False, result)

    def test_exception_when_event_tuner_present(self, mock_abstract):
        rule = EventRule(
            event_type="",
            temporal_granularity="",
            temporal_multiplier=1,
            must_have=True,
            attribute="")
        tuner = EventTuner(rules=[rule], action="", boolean="")
        evaluator = BaseConditionEvaluator()
        with self.assertRaises(Exception):
            evaluator._evaluate_tuner(tuner)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_exception_when_unknown_bool(self, mock_evaluate_rule,
                                         mock_abstract):
        tuner = Tuner(action="", boolean="NOT_A_BOOLEAN", rules=["", ""])
        evaluator = BaseConditionEvaluator()
        with self.assertRaises(Exception):
            evaluator._evaluate_tuner(tuner)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_exception_when_unknown_action(self, mock_evaluate_rule,
                                           mock_abstract):
        tuner = Tuner(action="NOT_AN_ACTION", boolean="OR", rules=["", ""])
        evaluator = BaseConditionEvaluator()
        with self.assertRaises(Exception):
            evaluator._evaluate_tuner(tuner)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_include(self, mock_evaluate_rule, mock_abstract):
        mock_evaluate_rule.return_value = True
        rule = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="INCLUDE", boolean="AND", rules=[rule])
        evaluator = BaseConditionEvaluator()
        result = evaluator._evaluate_tuner(tuner, get_request())
        self.assertEqual(True, result)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_exclude(self, mock_evaluate_rule, mock_abstract):
        mock_evaluate_rule.return_value = True
        rule = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="EXCLUDE", boolean="AND", rules=[rule])
        evaluator = BaseConditionEvaluator()
        result = evaluator._evaluate_tuner(tuner, get_request())
        self.assertEqual(False, result)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_and(self, mock_evaluate_rule, mock_abstract):
        mock_evaluate_rule.side_effect = [True, False]
        rule1 = Rule(attribute="", operator="", value="")
        rule2 = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="INCLUDE", boolean="AND", rules=[rule1, rule2])
        evaluator = BaseConditionEvaluator()
        result = evaluator._evaluate_tuner(
            tuner,
            get_request())
        self.assertEqual(False, result)

    @patch(
        "thefilter.rules.conditions_evaluator.BaseConditionEvaluator._evaluate_rule"
    )
    def test_or(self, mock_evaluate_rule, mock_abstract):
        mock_evaluate_rule.side_effect = [True, False]
        rule1 = Rule(attribute="", operator="", value="")
        rule2 = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="INCLUDE", boolean="OR", rules=[rule1, rule2])
        evaluator = BaseConditionEvaluator()
        result = evaluator._evaluate_tuner(tuner, get_request())
        self.assertEqual(True, result)


@patch.object(ConditionEvaluator, "__abstractmethods__", new_callable=set)
class TestConditionEvaluator(unittest.TestCase):
    def test_unknown_operator_raises_exception(self, mock_abstract):
        rule = Rule(attribute="", operator="not-an-operator", value="")
        evaluator = ConditionEvaluator()
        with self.assertRaises(Exception):
            evaluator._evaluate_rule(rule)


class TestParameterConditionEvaluator(unittest.TestCase):
    def test_equals(self):
        evaluator = ParameterConditionEvaluator()
        result = evaluator._equals("attribute_name",
                                   "attribute_value", get_request(
                query_string={"attribute_name": "attribute_value"}))
        self.assertEqual(True, result)

    def test_is_in_list(self):
        evaluator = ParameterConditionEvaluator()
        result = evaluator._is_in_list("attribute_name",
                                       "attribute_value1,"
                                       "attribute_value2",
                                       get_request(query_string={
                                           "attribute_name":
                                               "attribute_value1"}))
        self.assertEqual(True, result)

    def test_is_not_in_list(self):
        evaluator = ParameterConditionEvaluator()
        result = evaluator._is_not_in_list(
            "attribute_name", "attribute_value1,attribute_value2",
            get_request(
                query_string={"attribute_name": "attribute_value1"}))
        self.assertEqual(False, result)

    def test_contains(self):
        evaluator = ParameterConditionEvaluator()
        result = evaluator._contains("attribute_name", "bute_",
                                     get_request(query_string={
                                         "attribute_name":
                                             "attribute_value"}))
        self.assertEqual(True, result)

    def test_starts_with(self):
        evaluator = ParameterConditionEvaluator()
        result = evaluator._starts_with("attribute_name", "attrib",
                                        get_request(query_string={
                                            "attribute_name":
                                                "attribute_value"}))
        self.assertEqual(True, result)

    def test_evaluate_two_true_tuners(self):
        rule1 = Rule(
            attribute="attribute_name1",
            operator="EQUALS",
            value="attribute_value1")
        rule2 = Rule(
            attribute="attribute_name2",
            operator="EQUALS",
            value="attribute_value2")
        tuner1 = Tuner(action="INCLUDE", boolean="AND", rules=[rule1, rule2])
        rule3 = Rule(
            attribute="attribute_name3",
            operator="EQUALS",
            value="attribute_value3")
        rule4 = Rule(
            attribute="attribute_name4",
            operator="EQUALS",
            value="attribute_value4")
        tuner2 = Tuner(action="INCLUDE", boolean="OR", rules=[
            rule3, rule4])
        condition = ParameterCondition(tuners=[tuner1, tuner2])
        evaluator = ParameterConditionEvaluator()
        result = evaluator.evaluate(
            condition,
            get_request(
                query_string={
                    "attribute_name1": "attribute_value1",
                    "attribute_name2": "attribute_value2",
                    "attribute_name3": "attribute_value3",
                }
            )
        )
        self.assertEqual(True, result)

    def test_evaluate_true_and_false_tuners(self):
        rule1 = Rule(
            attribute="attribute_name1",
            operator="EQUALS",
            value="attribute_value1")
        rule2 = Rule(
            attribute="attribute_name2",
            operator="EQUALS",
            value="attribute_value2")
        tuner1 = Tuner(action="INCLUDE", boolean="AND", rules=[rule1, rule2])
        rule3 = Rule(
            attribute="attribute_name3",
            operator="EQUALS",
            value="attribute_value4")
        rule4 = Rule(
            attribute="attribute_name4",
            operator="EQUALS",
            value="attribute_value4")
        tuner2 = Tuner(action="INCLUDE", boolean="OR", rules=[
            rule3, rule4])
        condition = ParameterCondition(tuners=[tuner1, tuner2])
        evaluator = ParameterConditionEvaluator()
        result = evaluator.evaluate(
            condition,
            get_request(
                query_string={
                    "attribute_name1": "attribute_value1",
                    "attribute_name2": "attribute_value2",
                    "attribute_name3": "attribute_value3"
                }
            )
        )
        self.assertEqual(False, result)

    def test_evaluate_non_existent_attribute(self):
        rule = Rule(
            attribute="attribute_name2",
            operator="EQUALS",
            value="attribute_value2")
        tuner = Tuner(action="INCLUDE", boolean="OR", rules=[rule])
        condition = ParameterCondition(tuners=[tuner])
        evaluator = ParameterConditionEvaluator()
        result = evaluator.evaluate(condition, get_request(
            query_string={"attribute_name1": "attribute_value1"}))
        self.assertEqual(False, result)


class TestElasticSearchSeedConditionEvaluator(unittest.TestCase):
    def test_get_value_from_seed(self):
        seed_condition_evaluator = ElasticSearchSeedConditionEvaluator
        seed = {
            "thing": {"test": {"attribute": 12345}}
        }
        value = seed_condition_evaluator._get_value_from_seed(seed, "test.attribute")
        self.assertEqual(12345, value)

    def test_get_value_from_seed_content_rating(self):
        seed_condition_evaluator = ElasticSearchSeedConditionEvaluator
        seed = {
            "thing": {"test": {"contentRating": [{"name": "12", "id": None}]}}
        }
        value = seed_condition_evaluator._get_value_from_seed(seed, "test.contentRating")
        self.assertEqual("12", value)


class TestUserConditionEvaluator(unittest.TestCase):
    def test_evaluate_rule(self):
        rule = Rule(
            attribute="", operator="", value="", state=RuleState.TRUE.value)
        evaluator = UserConditionEvaluator()
        result = evaluator._evaluate_rule(rule, get_request())
        self.assertEqual(True, result)

    def test_evaluate_rule_exception_on_no_state(self):
        rule = Rule(attribute="", operator="", value="")
        evaluator = UserConditionEvaluator()
        with self.assertRaises(Exception):
            evaluator._evaluate_rule(rule)

    def test_evaluate(self):
        rule1 = Rule(
            attribute="", operator="", value="", state=RuleState.TRUE.value)
        rule2 = Rule(
            attribute="", operator="", value="", state=RuleState.FALSE.value)
        tuner = Tuner(action="INCLUDE", boolean="OR", rules=[rule1, rule2])
        condition = UserCondition(tuners=[tuner])
        evaluator = UserConditionEvaluator()
        result = evaluator.evaluate(condition, Mock())
        self.assertEqual(True, result)


class TestRulesetConditionsEvaluator(unittest.TestCase):
    def test_evaluate_conditions_calls_evaluate_on_condition_evaluators(self):
        mock_parameter_evaluator = Mock()
        mock_seed_evaluator = Mock()
        mock_user_evaluator = Mock()
        conditions = Condition(
            parameter="param_call",
            time="time_call",
            seed="seed_call",
            user="user_call")
        evaluator = RulesetConditionsEvaluator(
            mock_parameter_evaluator, mock_seed_evaluator, mock_user_evaluator)
        req = get_request()
        evaluator._evaluate_conditions(conditions, req)
        mock_parameter_evaluator.evaluate.assert_called_with(
            "param_call", req)
        mock_seed_evaluator.evaluate.assert_called_with("seed_call",
                                                        req)
        mock_user_evaluator.evaluate.assert_called_with("user_call",
                                                        req)

    def test_evaluate_conditions_true(self):
        mock_parameter_evaluator = Mock()
        mock_parameter_evaluator.evaluate.return_value = True
        mock_seed_evaluator = Mock()
        mock_seed_evaluator.evaluate.return_value = True
        mock_user_evaluator = Mock()
        mock_user_evaluator.evaluate.return_value = True
        conditions = Condition(
            parameter="param_call",
            time="time_call",
            seed="seed_call",
            user="user_call")
        evaluator = RulesetConditionsEvaluator(
            mock_parameter_evaluator, mock_seed_evaluator, mock_user_evaluator)
        result = evaluator._evaluate_conditions(conditions, Mock())
        self.assertEqual(True, result)

    def test_evaluate_conditions_false(self):
        mock_parameter_evaluator = Mock()
        mock_parameter_evaluator.evaluate.return_value = True
        mock_seed_evaluator = Mock()
        mock_seed_evaluator.evaluate.return_value = False
        mock_user_evaluator = Mock()
        mock_user_evaluator.evaluate.return_value = True
        conditions = Condition(
            parameter="param_call",
            time="time_call",
            seed="seed_call",
            user="user_call")
        evaluator = RulesetConditionsEvaluator(
            mock_parameter_evaluator, mock_seed_evaluator, mock_user_evaluator)
        result = evaluator._evaluate_conditions(conditions, Mock())
        self.assertEqual(False, result)

    @patch(
        "thefilter.rules.conditions_evaluator.RulesetConditionsEvaluator._evaluate_conditions"
    )
    def test_evaluate(self, mock_evaluate_conditions):
        mock_evaluate_conditions.side_effect = [False, True]
        tuner1 = ConditionalTuner(
            action="1", boolean="", rules="", conditions="")
        tuner2 = ConditionalTuner(
            action="2", boolean="", rules="", conditions="")
        ruleset = Ruleset(id="", size=5, tuners=[tuner1, tuner2])
        evaluator = RulesetConditionsEvaluator("", "", "")
        evaluator.evaluate(ruleset, Mock())
        self.assertEqual(1, len(ruleset.tuners))
        self.assertEqual("2", ruleset.tuners[0].action)
