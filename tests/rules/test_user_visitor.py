import unittest
from dataclasses import asdict
from datetime import datetime
from unittest.mock import Mock, patch

from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository
from thefilter.rules.rule_evaluator import CodeRuleSetEvaluator
from thefilter.rules.ruleset import EventRule, EventTuner, Rule, Ruleset, Tuner
from thefilter.rules.user_visitor import UserVisitor


class TestUserVisitor(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_user_repository(self):
        return Mock()

    def get_request(
            self,
            user_id: str = None,
            request_start: datetime = None):
        return Request(
            userId=user_id,
            request_start=request_start
        )

    def test_get_past_datetime_days(self):
        now = datetime(2019, 5, 13, 1, 2, 3, 4)
        expected_datetime = datetime(2019, 5, 8, 1, 2, 3, 4)
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        result = visitor._get_past_datetime(5, "DAYS", now)
        self.assertEqual(expected_datetime, result)

    def test_get_past_datetime_weeks(self):
        now = datetime(2019, 5, 13, 1, 2, 3, 4)
        expected_datetime = datetime(2019, 4, 8, 1, 2, 3, 4)
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        result = visitor._get_past_datetime(5, "WEEKS", now)
        self.assertEqual(expected_datetime, result)

    def test_get_past_datetime_months(self):
        now = datetime(2019, 5, 13, 1, 2, 3, 4)
        expected_datetime = datetime(2018, 12, 14, 1, 2, 3, 4)
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        result = visitor._get_past_datetime(5, "MONTHS", now)
        self.assertEqual(expected_datetime, result)

    def test_get_past_datetime_years(self):
        now = datetime(2019, 5, 13, 1, 2, 3, 4)
        expected_datetime = datetime(2014, 5, 14, 1, 2, 3, 4)
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        result = visitor._get_past_datetime(5, "YEARS", now)
        self.assertEqual(expected_datetime, result)

    def test_evaluate_event_rule_throws_exception_when_no_Id(self):
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        with self.assertRaises(Exception):
            visitor._evaluate_event_rule("")

    @patch(
        "thefilter.rules.user_visitor.UserVisitor._evaluate_event_rule",
        return_value=True)
    def test_evaluate_event_tuner_set_rule_true(self,
                                                mock_evaluate_event_rule):
        rule = EventRule(
            attribute="",
            event_type="",
            temporal_granularity="",
            temporal_multiplier=0,
            must_have=True)
        tuner = EventTuner(action="", boolean="", rules=[rule])
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        visitor._evaluate_event_tuner(tuner, self.get_request())
        self.assertEqual(True, tuner.rules[0].state)

    @patch(
        "thefilter.rules.user_visitor.UserVisitor._evaluate_event_rule",
        return_value=False)
    def test_evaluate_event_tuner_set_rule_false(self,
                                                 mock_evaluate_event_rule):
        rule = EventRule(
            attribute="",
            event_type="",
            temporal_granularity="",
            temporal_multiplier=0,
            must_have=True)
        tuner = EventTuner(action="", boolean="", rules=[rule])
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        visitor._evaluate_event_tuner(tuner, self.get_request())
        self.assertEqual(False, tuner.rules[0].state)

    @patch(
        "thefilter.rules.user_visitor.UserVisitor._transform_event_rule_to_rule",
        return_value=Rule(attribute="4", operator="5", value="6"))
    def test_replace_event_rule_calls_transform_event_rule_to_rule(
            self, mock_transform_event_rule_to_rule):
        rule1 = Rule(attribute="1", operator="2", value="3")
        rule2 = EventRule(
            attribute="",
            event_type="",
            temporal_multiplier=5,
            temporal_granularity="",
            must_have=True)
        tuner = EventTuner(action="", boolean="", rules=[rule1, rule2])
        ruleset = Ruleset(id="", size=10, tuners=[tuner])
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        visitor._replace_event_rules(ruleset, self.get_request())
        expected_rules = [
            Rule(attribute="1", operator="2", value="3"),
            Rule(attribute="4", operator="5", value="6")
        ]
        self.assertEqual(expected_rules, ruleset.tuners[0].rules)

    @patch(
        "thefilter.rules.user_visitor.UserVisitor._transform_event_rule_to_rule"
    )
    def test_replace_event_rule_does_not_call_transform_event_rule_to_rule(
            self, mock_transform_event_rule_to_rule):
        rule = Rule(attribute="", operator="", value="")
        tuner = Tuner(action="", boolean="", rules=[rule])
        ruleset = Ruleset(id="", size=10, tuners=[tuner])
        visitor = UserVisitor(self.get_user_repository(),
                              metadata_repository=StorageMetadataRepository({}),
                              rule_evaluator=CodeRuleSetEvaluator(
                                  metadata_repository=StorageMetadataRepository({})),
                              tracer_factory=NoOpTracerFactory())
        visitor._replace_event_rules(ruleset, self.get_request())
        mock_transform_event_rule_to_rule.assert_not_called()

    def test_transform_event_rule_to_rule(self):
        rule = EventRule(
            attribute="brand.name",
            event_type="play",
            temporal_multiplier=0,
            temporal_granularity="DAYS",
            must_have=True,
            operator=None,
            value=None)
        visitor = UserVisitor(StorageUserRepository(
            [{"thing": [{"id": "a"}], "action": "play"}, {"thing": [{"id": "b"}], "action": "play"},
             {"thing": [{"id": "c"}], "action": "play"}]),
            metadata_repository=StorageMetadataRepository(
                {"a": {"thing": {"brand": {"name": "1"}}},
                 "b": {"thing": {"brand": {"name": "2"}}},
                 "c": {"thing": {"brand": {"name": "3"}}}}),
            rule_evaluator=CodeRuleSetEvaluator(
                metadata_repository=StorageMetadataRepository({})),
            tracer_factory=NoOpTracerFactory())
        new_rule = visitor._transform_event_rule_to_rule(
            rule,
            self.get_request(user_id="123",
                             request_start=datetime(2222, 2, 22, 22, 22, 22)))
        self.assertDictEqual({
            'attribute': 'brand.name',
            'operator': 'IS_IN_LIST',
            'state': None,
            'value': '1,2,3'
        }, asdict(new_rule))

    def test_get_results_event_rule_value_none(self):
        rule = EventRule(
            attribute="brand.name",
            event_type="play",
            temporal_multiplier=10,
            temporal_granularity="DAYS",
            must_have=True,
            operator=None,
            value=None)
        visitor = UserVisitor(user_repository=StorageUserRepository(
            [{"thing": [{"id": "a"}], "action": "play"}, {"thing": [{"id": "b"}], "action": "play"},
             {"thing": [{"id": "c"}], "action": "play"}]),
            metadata_repository=StorageMetadataRepository(
                {"a": {"thing": {"brand": [{"name": "1"}]}},
                 "b": {"thing": {"brand": [{"name": "2"}]}},
                 "c": {"thing": {"brand": [{"name": "3"}]}}}),
            rule_evaluator=CodeRuleSetEvaluator(
                metadata_repository=StorageMetadataRepository({})),
            tracer_factory=NoOpTracerFactory())
        results = visitor._get_results(rule, self.get_request(
            user_id="1234", request_start=datetime(2222, 2, 22, 22, 22, 22)))
        self.assertEqual(["1", "2", "3"], results)

    @patch("thefilter.rules.rule_evaluator.CodeRuleSetEvaluator.evaluate_rule",
           return_value=[{"id": "1"}, {"id": "2"}])
    def test_get_results_event_rule_value(self, mock_evaluate_rule):
        rule = EventRule(
            attribute="brand.name",
            event_type="play",
            temporal_multiplier=10,
            temporal_granularity="DAYS",
            must_have=True,
            operator="EQUALS",
            value="big brand")
        visitor = UserVisitor(user_repository=StorageUserRepository(
            [{"thing": [{"id": "a"}]}, {"thing": [{"id": "b"}]},
             {"thing": [{"id": "c"}]}]),
            metadata_repository=StorageMetadataRepository(
                {"a": {"thing": {"brand": [{"name": "1"}]}},
                 "b": {"thing": {"brand": [{"name": "2"}]}},
                 "c": {"thing": {"brand": [{"name": "3"}]}}}),
            rule_evaluator=CodeRuleSetEvaluator(
                metadata_repository=StorageMetadataRepository({})),
            tracer_factory=NoOpTracerFactory())
        results = visitor._get_results(rule, self.get_request(
            user_id="1234", request_start=datetime(2222, 2, 22, 22, 22, 22)))
        self.assertEqual([{"id": "1"}, {"id": "2"}], results)
