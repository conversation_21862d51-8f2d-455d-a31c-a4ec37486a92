[{"id": "1411569087", "brandId": "1411569087", "brandName": null, "recommendationId": null, "name": "Hollywood Ending: <PERSON><PERSON><PERSON> & <PERSON>", "alternateName": null, "typeName": "CreativeWork", "genre": [{"name": "Film pro dospělé", "id": "18"}], "subGenre": [], "contentRating": [{"name": "*18"}], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "Dusk TV", "id": 6047, "startDate": "2022-12-27T20:32:00+00:00", "endDate": "2022-12-27T20:56:00+00:00"}], "keywords": [], "episodeNumber": 0, "seasonNumber": 0, "numberOfSeasons": null, "description": "Childhood lovers <PERSON> and <PERSON> are finally getting the acting success they always wanted. But their love is put to the test when fame, fortune, and false friends conspire to tear a couple apart who were once everything to each other. Porn for women by Wicked Pictures.", "duration": "PT24M", "datePublished": "1970-01-01T01:01:01+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/18/18_2_v_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": true, "isKids": false}, {"id": "320207", "brandId": "320207", "brandName": null, "recommendationId": "17193161803588", "name": "Z ohnivé výhně", "alternateName": "Forged in Fire", "typeName": "TVSeries", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-02T22:55:00+00:00", "endDate": "2022-12-02T23:50:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": "Č<PERSON><PERSON><PERSON> kováři se ponoří do hudby, <PERSON><PERSON><PERSON>tano<PERSON> za úkol vytvořit čepele San-Mai z hudebních nástrojů. Po problémech s kovářskými sváry a tenkými polotovary se tři z nich postaví na scénu ve druhém kole, kde musí své nože doladit.", "duration": "PT55M", "datePublished": "2019-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/HISTORY/Z%20ohniv%c3%a9%20v%c3%bdhn%c4%9b/_Aco1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44633", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506018", "brandId": "320207", "brandName": null, "recommendationId": null, "name": "Z ohnivé výhně", "alternateName": "Forged in Fire", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "320207", "name": "Z ohnivé výhně"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-02T22:55:00+00:00", "endDate": "2022-12-02T23:50:00+00:00"}], "keywords": [], "episodeNumber": 16, "seasonNumber": 0, "numberOfSeasons": null, "description": "Č<PERSON><PERSON><PERSON> kováři se ponoří do hudby, <PERSON><PERSON><PERSON>tano<PERSON> za úkol vytvořit čepele San-Mai z hudebních nástrojů. Po problémech s kovářskými sváry a tenkými polotovary se tři z nich postaví na scénu ve druhém kole, kde musí své nože doladit.", "duration": "PT55M", "datePublished": "2019-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/HISTORY/Z%20ohniv%c3%a9%20v%c3%bdhn%c4%9b/_Aco1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44633", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "320197", "brandId": "320197", "brandName": null, "recommendationId": "17193161806888", "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>", "alternateName": "Storage Wars", "typeName": "TVSeries", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-02T23:50:00+00:00", "endDate": "2022-12-03T00:15:00+00:00"}, {"name": "History HD", "id": 4003, "startDate": "2022-12-03T00:15:00+00:00", "endDate": "2022-12-03T00:45:00+00:00"}, {"name": "History HD", "id": 4003, "startDate": "2022-12-03T00:45:00+00:00", "endDate": "2022-12-03T01:10:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": "Aukce skladovacích jednotek jsou nejnovějším a největším nevyužitým zdrojem skrytých pokladů, a tak oportunisté přihazují na nevyzvednuté nemovitosti v naději, že narazí na zlato.", "duration": "PT25M", "datePublished": "2021-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/V%c3%a1lka%20sklad%c5%af%20I/_ac0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44613", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506019", "brandId": "320197", "brandName": null, "recommendationId": null, "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>", "alternateName": "Storage Wars", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "320197", "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-02T23:50:00+00:00", "endDate": "2022-12-03T00:15:00+00:00"}], "keywords": [], "episodeNumber": 3, "seasonNumber": 0, "numberOfSeasons": null, "description": "Aukce skladovacích jednotek jsou nejnovějším a největším nevyužitým zdrojem skrytých pokladů, a tak oportunisté přihazují na nevyzvednuté nemovitosti v naději, že narazí na zlato.", "duration": "PT25M", "datePublished": "2021-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/V%c3%a1lka%20sklad%c5%af%20I/_ac0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44613", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506020", "brandId": "320197", "brandName": null, "recommendationId": null, "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>", "alternateName": "Storage Wars", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "320197", "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T00:15:00+00:00", "endDate": "2022-12-03T00:45:00+00:00"}], "keywords": [], "episodeNumber": 8, "seasonNumber": 0, "numberOfSeasons": null, "description": "Ivy přivede svého syna do Moreno Valley, kde se vždy dějí do<PERSON> vě<PERSON>, a odj<PERSON>ždí s nečekaným jo-jo. <PERSON> a <PERSON>, <PERSON><PERSON> jej<PERSON> boty jsou více „vzdušné“ a méně „Jordan“.", "duration": "PT30M", "datePublished": "2021-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/V%c3%a1lka%20sklad%c5%af%20I/_ac0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44613", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506021", "brandId": "320197", "brandName": null, "recommendationId": null, "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>", "alternateName": "Storage Wars", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "320197", "name": "<PERSON><PERSON><PERSON><PERSON> sklad<PERSON>"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T00:45:00+00:00", "endDate": "2022-12-03T01:10:00+00:00"}], "keywords": [], "episodeNumber": 2, "seasonNumber": 0, "numberOfSeasons": null, "description": "Aukce skladovacích jednotek jsou nejnovějším a největším nevyužitým zdrojem skrytých pokladů, a tak oportunisté přihazují na nevyzvednuté nemovitosti v naději, že narazí na zlato.", "duration": "PT25M", "datePublished": "2021-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/V%c3%a1lka%20sklad%c5%af%20I/_ac0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44613", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "320172", "brandId": "320172", "brandName": null, "recommendationId": "17193161811688", "name": "Hv<PERSON><PERSON><PERSON> zastavárny", "alternateName": "Pawn Stars", "typeName": "TVSeries", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Mark <PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "director": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T01:10:00+00:00", "endDate": "2022-12-03T01:35:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": "Hvěz<PERSON> zastavárny získají vlastní<PERSON>, k<PERSON><PERSON> se k nim dostane mluvící loutka Hermana Munstera. <PERSON>, atmosféra zhoustne s basovou kytarou s podpisem Johna Entwistla.", "duration": "PT25M", "datePublished": "2015-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/HISTORY/Hv%c4%9bzdy%20zastav%c3%a1rny%20Ji%c5%ben%c3%ad%20Afrika/_av1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44603", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506022", "brandId": "320172", "brandName": null, "recommendationId": null, "name": "Hv<PERSON><PERSON><PERSON> zastavárny", "alternateName": "Pawn Stars", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "Mark <PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "director": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "320172", "name": "Hv<PERSON><PERSON><PERSON> zastavárny"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T01:10:00+00:00", "endDate": "2022-12-03T01:35:00+00:00"}], "keywords": [], "episodeNumber": 11, "seasonNumber": 0, "numberOfSeasons": null, "description": "Hvěz<PERSON> zastavárny získají vlastní<PERSON>, k<PERSON><PERSON> se k nim dostane mluvící loutka Hermana Munstera. <PERSON>, atmosféra zhoustne s basovou kytarou s podpisem Johna Entwistla.", "duration": "PT25M", "datePublished": "2015-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/HISTORY/Hv%c4%9bzdy%20zastav%c3%a1rny%20Ji%c5%ben%c3%ad%20Afrika/_av1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "44603", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "149469", "brandId": "149469", "brandName": null, "recommendationId": "17193161813188", "name": "<PERSON><PERSON><PERSON><PERSON>", "alternateName": "Auction Hunters", "typeName": "TVSeries", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T01:35:00+00:00", "endDate": "2022-12-03T02:00:00+00:00"}, {"name": "History HD", "id": 4003, "startDate": "2022-12-03T02:00:00+00:00", "endDate": "2022-12-03T02:20:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": "Allen a Ton se snaží získat informace o své konkurenci tím, že si povídají s místními kupci na aukcích. Pánové najdou těžké stroje na těžbu zlata z 50. let a amat<PERSON>rsk<PERSON> raketu, kter<PERSON> dokáže letět rychleji než zvuk!", "duration": "PT25M", "datePublished": "2012-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/Mist%c5%99i%20aukc%c3%ad/_Av1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "MSTV-*********", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506023", "brandId": "149469", "brandName": null, "recommendationId": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "alternateName": "Auction Hunters", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "149469", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T01:35:00+00:00", "endDate": "2022-12-03T02:00:00+00:00"}], "keywords": [], "episodeNumber": 20, "seasonNumber": 0, "numberOfSeasons": null, "description": "Allen a Ton se snaží získat informace o své konkurenci tím, že si povídají s místními kupci na aukcích. Pánové najdou těžké stroje na těžbu zlata z 50. let a amat<PERSON>rsk<PERSON> raketu, kter<PERSON> dokáže letět rychleji než zvuk!", "duration": "PT25M", "datePublished": "2012-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/Mist%c5%99i%20aukc%c3%ad/_Av1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "MSTV-*********", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1701506024", "brandId": "149469", "brandName": null, "recommendationId": null, "name": "<PERSON><PERSON><PERSON><PERSON>", "alternateName": "Auction Hunters", "typeName": "TVEpisode", "genre": [{"name": "Dokument", "id": "23"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "149469", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "publication": [{"name": "History HD", "id": 4003, "startDate": "2022-12-03T02:00:00+00:00", "endDate": "2022-12-03T02:20:00+00:00"}], "keywords": [], "episodeNumber": 21, "seasonNumber": 0, "numberOfSeasons": null, "description": "Allen přijde na aukci pozdě a nechá Tona, aby se sám utkal s divokou kupující! Kluci najdou plně funkční plamenomet a vzácný ručně vyřezávaný luk a sadu šípů z Jižní Ameriky!", "duration": "PT20M", "datePublished": "2012-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/NOVA/Mist%c5%99i%20aukc%c3%ad/_Av1_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": "MSTV-*********", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "2073992177", "brandId": "2073992177", "brandName": null, "recommendationId": null, "name": "Kill Bill 2. - akciófilm", "alternateName": null, "typeName": "CreativeWork", "genre": [{"name": "Film", "id": "10"}], "subGenre": [], "contentRating": [{"name": "*18"}], "actor": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "director": [{"name": "<PERSON>"}], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "Film 4", "id": 4541, "startDate": "2022-12-17T00:40:00+00:00", "endDate": "2022-12-17T01:00:00+00:00"}], "keywords": [], "episodeNumber": 0, "seasonNumber": 0, "numberOfSeasons": null, "description": "A Menyasszony végzett két egykori t<PERSON>, majd tov<PERSON>bb folytatja bosszúhadjáratát. A halállistáján Budd és Elle következik. <PERSON><PERSON><PERSON> rendezi a számláját Bill öccsével és Elle-lel, aki a bérgyilkosokból á<PERSON> csa<PERSON>ton belül a legfőbb vetélytársa volt, Mexikóba megy, hogy megtalálja utolsó áldozata rejtekhelyét. Már csak egyetlen cél lebeg előtte: me<PERSON><PERSON><PERSON><PERSON>, az egykori főnökét és szeretőjét, gyermekének a<PERSON>.", "duration": "PT20M", "datePublished": "2004-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/10/_G2_V_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "WmVpdCBpbSBCaWxk", "brandId": "WmVpdCBpbSBCaWxk", "brandName": null, "recommendationId": "25817458191656", "name": "Zeit im Bild", "alternateName": null, "typeName": "TVSeries", "genre": [{"name": "Film", "id": "10"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "ORF1", "id": 6011, "startDate": "2022-12-26T18:30:00+00:00", "endDate": "2022-12-26T18:48:00+00:00"}, {"name": "ORF1", "id": 6011, "startDate": "2022-12-27T18:30:00+00:00", "endDate": "2022-12-27T18:51:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": " ", "duration": "PT18M", "datePublished": "1970-01-01T01:01:01+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/10/_G2_V_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1061598265", "brandId": "WmVpdCBpbSBCaWxk", "brandName": null, "recommendationId": null, "name": "Zeit im Bild", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Film", "id": "10"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "WmVpdCBpbSBCaWxk", "name": "Zeit im Bild"}, "publication": [{"name": "ORF1", "id": 6011, "startDate": "2022-12-26T18:30:00+00:00", "endDate": "2022-12-26T18:48:00+00:00"}], "keywords": [], "episodeNumber": 0, "seasonNumber": 0, "numberOfSeasons": null, "description": " ", "duration": "PT18M", "datePublished": "1970-01-01T01:01:01+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/10/_G2_V_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1061661775", "brandId": "WmVpdCBpbSBCaWxk", "brandName": null, "recommendationId": null, "name": "Zeit im Bild", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Film", "id": "10"}], "subGenre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "WmVpdCBpbSBCaWxk", "name": "Zeit im Bild"}, "publication": [{"name": "ORF1", "id": 6011, "startDate": "2022-12-27T18:30:00+00:00", "endDate": "2022-12-27T18:51:00+00:00"}], "keywords": [], "episodeNumber": 0, "seasonNumber": 0, "numberOfSeasons": null, "description": " ", "duration": "PT21M", "datePublished": "1970-01-01T01:01:01+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/10/_G2_V_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1114132747", "brandId": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM=", "brandName": null, "recommendationId": null, "name": "Football Stars - Rapinoe Movies Varan", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Sport", "id": "40"}], "subGenre": [], "contentRating": [{"name": "*12"}], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"name": "Football Stars", "id": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM="}, "publication": [{"name": "Trace Sport HD", "id": 4120, "startDate": "2022-12-27T13:15:00+00:00", "endDate": "2022-12-27T13:45:00+00:00"}], "keywords": [], "episodeNumber": 10, "seasonNumber": 0, "numberOfSeasons": null, "description": "<PERSON> fought against <PERSON>. <PERSON> attempted to break through in Hollywood. And after having tried their luck elsewhere, <PERSON><PERSON><PERSON> and <PERSON> have gone back where they belong.", "duration": "PT30M", "datePublished": "2022-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/40/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}, "inferred": true}, "space": null, "isAdult": false, "isKids": false}, {"id": "1114132754", "brandId": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM=", "brandName": null, "recommendationId": null, "name": "Football Stars - Neuer Maldini Alaba", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Sport", "id": "40"}], "subGenre": [], "contentRating": [{"name": "*12"}], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"name": "Football Stars", "id": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM="}, "publication": [{"name": "Trace Sport HD", "id": 4120, "startDate": "2022-12-27T16:30:00+00:00", "endDate": "2022-12-27T16:55:00+00:00"}], "keywords": [], "episodeNumber": 6, "seasonNumber": 0, "numberOfSeasons": null, "description": "<PERSON> is underestimated, the Maldinis are peerless as a father-son duo and <PERSON> has fulfilled the promise of his youth.", "duration": "PT25M", "datePublished": "2022-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/40/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}, "inferred": true}, "space": null, "isAdult": false, "isKids": false}, {"id": "1114132760", "brandId": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM=", "brandName": null, "recommendationId": null, "name": "Football Stars - Rapinoe Movies Varan", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Sport", "id": "40"}], "subGenre": [], "contentRating": [{"name": "*12"}], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"name": "Football Stars", "id": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM="}, "publication": [{"name": "Trace Sport HD", "id": 4120, "startDate": "2022-12-27T19:15:00+00:00", "endDate": "2022-12-27T19:40:00+00:00"}], "keywords": [], "episodeNumber": 10, "seasonNumber": 0, "numberOfSeasons": null, "description": "<PERSON> fought against <PERSON>. <PERSON> attempted to break through in Hollywood. And after having tried their luck elsewhere, <PERSON><PERSON><PERSON> and <PERSON> have gone back where they belong.", "duration": "PT25M", "datePublished": "2022-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/40/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}, "inferred": true}, "space": null, "isAdult": false, "isKids": false}, {"id": "10254", "brandId": "10254", "brandName": null, "recommendationId": "30472346872", "name": "Ten okamžik", "alternateName": null, "typeName": "TVSeries", "genre": [{"name": "Dokumentární seriál", "id": "1I"}], "subGenre": [], "contentRating": [], "actor": [], "director": [{"name": "<PERSON><PERSON>"}], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [{"name": "ČT2", "id": 7, "startDate": "2022-12-01T07:30:00+00:00", "endDate": "2022-12-01T08:00:00+00:00"}], "keywords": [], "episodeNumber": null, "seasonNumber": null, "numberOfSeasons": null, "description": "V roce 1968 zažili okupaci Československa, teď se vracejí do okamžiku, v němž je zachytil fotoaparát. Překladatelka <PERSON>ová osobně znala nejlepší sovětské filmaře 60. let. <PERSON><PERSON> se s kamarády po obrně vracel třemi trabanty z dovolené na Krymu (skryté titulky)", "duration": "PT30M", "datePublished": "2018-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/CT2/Ten%20okam%c5%beik/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": true, "qualityIndex": "0", "seriesId": "MSTV-2088821934", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "1052714283", "brandId": "10254", "brandName": null, "recommendationId": null, "name": "Ten okamžik", "alternateName": null, "typeName": "TVEpisode", "genre": [{"name": "Dokumentární seriál", "id": "1I"}], "subGenre": [], "contentRating": [], "actor": [], "director": [{"name": "<PERSON><PERSON>"}], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"id": "10254", "name": "Ten okamžik"}, "publication": [{"name": "ČT2", "id": 7, "startDate": "2022-12-01T07:30:00+00:00", "endDate": "2022-12-01T08:00:00+00:00"}], "keywords": [], "episodeNumber": 2, "seasonNumber": 0, "numberOfSeasons": null, "description": "V roce 1968 zažili okupaci Československa, teď se vracejí do okamžiku, v němž je zachytil fotoaparát. Překladatelka <PERSON>ová osobně znala nejlepší sovětské filmaře 60. let. <PERSON><PERSON> se s kamarády po obrně vracel třemi trabanty z dovolené na Krymu (skryté titulky)", "duration": "PT30M", "datePublished": "2018-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/CT2/Ten%20okam%c5%beik/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": true, "qualityIndex": "0", "seriesId": "MSTV-2088821934", "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}}, "space": null, "isAdult": false, "isKids": false}, {"id": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM=", "brandId": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM=", "brandName": null, "recommendationId": "1114132760", "name": "Football Stars", "alternateName": "Football Stars", "typeName": "TVSeries", "genre": [{"name": "Sport", "id": "40"}], "subGenre": [], "contentRating": [{"name": "*12"}], "actor": [], "director": [], "producer": [], "crew": [], "partOfSeason": {}, "partOfSeries": {"name": "Football Stars", "id": "dHZzZXJpZXNfRm9vdGJhbGwgU3RhcnM="}, "publication": [{"name": "Trace Sport HD", "id": 4120, "startDate": "2022-12-27T13:15:00+00:00", "endDate": "2022-12-27T13:45:00+00:00"}], "keywords": [], "episodeNumber": 10, "seasonNumber": 0, "numberOfSeasons": null, "description": "<PERSON> fought against <PERSON>. <PERSON> attempted to break through in Hollywood. And after having tried their luck elsewhere, <PERSON><PERSON><PERSON> and <PERSON> have gone back where they belong.", "duration": "PT30M", "datePublished": "2022-01-01T00:00:00+00:00", "image": {"url": "https://image.cdn.magio.tv/eimg/_GENRESDVB/40/_av0_VERT.jpg"}, "inLanguage": "sk-SK", "isLiveBroadcast": true, "productionCompany": null, "custom": {"cc": false, "qualityIndex": "0", "seriesId": null, "active": {"state": false, "stateUpdateTimestamp": "2022-09-16T04:31:00+00:00"}, "inferred": true}, "space": null, "isAdult": false, "isKids": false}]