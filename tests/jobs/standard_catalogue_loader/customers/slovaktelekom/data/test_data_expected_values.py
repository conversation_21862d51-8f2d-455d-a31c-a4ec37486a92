from thefilter.model.schemaorg import Thing

expected_vod_converted_data = {
    '489898999': Thing(id='489898999', brandId='dHZzZXJpZXNfTkZM', recommendationId=None,
                       name='NFL: Chicago Bears - Washington Commanders',
                       alternateName='NFL: Chicago Bears - Washington Commanders',
                       typeName='TVEpisode', genre=[{'name': 'sport'}, {'name': 'nfl'}],
                       subGenre=[], contentRating=[{'name': '*7'}], actor=[],
                       director=[], producer=[], crew=[], partOfSeason={},
                       partOfSeries={'name': 'NFL', 'id': 'dHZzZXJpZXNfTkZM'},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=0, seasonNumber=0,
                       numberOfSeasons=None,
                       description='Match of the 6th round of the National Football League.',
                       duration='PT2H35M53S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/da70344a-027f-41d7-83b4-9210518b5495/a2d0fe66_c967_4143_95eb_5b36e25d3ffa11844461919280419369.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='PREMIERSPORT',
                       custom={'csfd_rating': '80', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '116100',
                               'added_in_brands': 'GODIGITMCZ_GO',
                               'genres_en': 'Sport, NFL', 'active': {'state': True,
                                                                     'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'},
                               'inferred': True}, space=None, isAdult=False,
                       isKids=False),
    '21252948': Thing(id='21252948', brandId='dHZzZXJpZXNfTkZM', recommendationId=None,
                      name='NFL: Kansas City Chiefs - Las Vegas Raiders',
                      alternateName='NFL: Kansas City Chiefs - Las Vegas Raiders',
                      typeName='TVEpisode', genre=[{'name': 'sport'}, {'name': 'nfl'}],
                      subGenre=[], contentRating=[{'name': '*7'}], actor=[], director=[],
                      producer=[], crew=[], partOfSeason={},
                      partOfSeries={'name': 'NFL', 'id': 'dHZzZXJpZXNfTkZM'},
                      publication=[{'name': 'vod', 'id': '',
                                    'startDate': '1970-01-01T00:00:01+00:00',
                                    'endDate': '2038-01-19T00:00:00+00:00'}],
                      keywords=[], episodeNumber=0, seasonNumber=0, numberOfSeasons=None,
                      description='Match of the 5th round of the National Football League.',
                      duration='PT2H52M59S', datePublished='2022-01-01T00:00:00+00:00',
                      image={
                          'url': 'https://image.cdn.magio.tv/cmsimages/1cfcbbc3-5f38-476d-ab3c-9166f0067101/c367e3b5_b3e3_41b0_9bf5_1c07a1a7886914547605165339348756.jpg'},
                      inLanguage=None, isLiveBroadcast=False,
                      productionCompany='PREMIERSPORT',
                      custom={'csfd_rating': '80', 'package_name': 'Free',
                              'added_to_package': '', 'vod_id': '116101',
                              'added_in_brands': 'TMCZ_GODIGIGO',
                              'genres_en': 'Sport, NFL', 'active': {'state': True,
                                                                    'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'},
                              'inferred': True}, space=None, isAdult=False,
                      isKids=False),
    '1983804962770100000': Thing(id='1983804962770100000', brandId='1983804962770100000',
                                 recommendationId='117162', name="Raven's Home",
                                 alternateName=None, typeName='TVSeries',
                                 genre=[{'name': 'child'}], subGenre=[],
                                 contentRating=[{'name': '*D'}],
                                 actor=[{'name': ' Raven-Symoné'},
                                        {'name': 'Issac Ryan Brown'},
                                        {'name': 'Navia Ziraili Robinson'}],
                                 director=[{'name': 'Victor Gonzalez'}], producer=[],
                                 crew=[], partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=None,
                                 seasonNumber=None, numberOfSeasons=None,
                                 description='A trip to Alcatraz gets Booker, Ivy and Neil into trouble. After a vision of unsafe practices at the Chill Grill, Raven decides to run the restaurant to prevent a catastrophe.',
                                 duration='PT23M20S',
                                 datePublished='2022-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/730f6ac0-2a07-4923-baca-286df98c5494/adecd717_c028_4afc_bbb4_5f2c3d64747012158546082270801289.jpg'},
                                 inLanguage=None, isLiveBroadcast=False,
                                 productionCompany=None,
                                 custom={'csfd_rating': '70', 'package_name': 'Free',
                                         'added_to_package': '', 'vod_id': '117162',
                                         'added_in_brands': 'GOTMCZ_GO',
                                         'genres_en': 'Child', 'active': {'state': True,
                                                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                                 space=None, isAdult=False, isKids=True),
    '*********': Thing(id='*********', brandId='1983804962770100000',
                       recommendationId=None, name='83. Útek z Alcatrazu',
                       alternateName='Escape from Pal-catraz', typeName='TVEpisode',
                       genre=[{'name': 'child'}], subGenre=[],
                       contentRating=[{'name': '*D'}],
                       actor=[{'name': ' Raven-Symoné'}, {'name': 'Issac Ryan Brown'},
                              {'name': 'Navia Ziraili Robinson'}],
                       director=[{'name': 'Victor Gonzalez'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1983804962770100000',
                                                      'name': "Raven's Home"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=83, seasonNumber=5,
                       numberOfSeasons=None,
                       description='A trip to Alcatraz gets Booker, Ivy and Neil into trouble. After a vision of unsafe practices at the Chill Grill, Raven decides to run the restaurant to prevent a catastrophe.',
                       duration='PT23M20S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/730f6ac0-2a07-4923-baca-286df98c5494/adecd717_c028_4afc_bbb4_5f2c3d64747012158546082270801289.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Disney',
                       custom={'csfd_rating': '70', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '117162',
                               'added_in_brands': 'GOTMCZ_GO', 'genres_en': 'Child',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=True),
    '*********': Thing(id='*********', brandId='1983804962770100000',
                       recommendationId=None, name='84. Auto menom Sprisahanie',
                       alternateName='Streetcar Named Conspire, A', typeName='TVEpisode',
                       genre=[{'name': 'child'}], subGenre=[],
                       contentRating=[{'name': '*D'}],
                       actor=[{'name': ' Raven-Symoné'}, {'name': 'Issac Ryan Brown'},
                              {'name': 'Navia Ziraili Robinson'}],
                       director=[{'name': 'Victor Gonzalez'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1983804962770100000',
                                                      'name': "Raven's Home"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=84, seasonNumber=5,
                       numberOfSeasons=None,
                       description="Booker's visit is ending but Raven wants him to stay. Booker is more interested in a girl than spending time with Raven. Victor builds Alice the doll house of her dreams.",
                       duration='PT22M31S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/97a3eac0-3036-46c3-b176-756fd2b81c8c/30cb01b3_b8d1_4b1d_a3c2_47c87a585d408314539501832848435.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Disney',
                       custom={'csfd_rating': '70', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '117163',
                               'added_in_brands': 'GOTMCZ_GO', 'genres_en': 'Child',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=True),
    '*********': Thing(id='*********', brandId='1983804962770100000',
                       recommendationId=None, name='85. Návnada na kliknutie',
                       alternateName='Clique Bait', typeName='TVEpisode',
                       genre=[{'name': 'child'}], subGenre=[],
                       contentRating=[{'name': '*D'}],
                       actor=[{'name': ' Raven-Symoné'}, {'name': 'Issac Ryan Brown'},
                              {'name': 'Navia Ziraili Robinson'}],
                       director=[{'name': 'Victor Gonzalez'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1983804962770100000',
                                                      'name': "Raven's Home"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=85, seasonNumber=5,
                       numberOfSeasons=None,
                       description="Booker tries to impress a girl, but it takes a turn for the worse. Raven attempts to prove to Victor that Alice is lying to him about something but he doesn't believe her.",
                       duration='PT23M20S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/67fd7225-5edb-4fdb-b85a-398a25e78b3a/f12d2287_295e_4d17_bf10_c0142e1775da16678465464268427104.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Disney',
                       custom={'csfd_rating': '70', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '117164',
                               'added_in_brands': 'GOTMCZ_GO', 'genres_en': 'Child',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=True),
    '*********': Thing(id='*********', brandId='*********', recommendationId=None,
                       name='S láskou, Rosie', alternateName='Love, Rosie',
                       typeName='Movie', genre=[{'name': 'xmas'}, {'name': 'comedy'},
                                                {'name': 'romantic'}], subGenre=[],
                       contentRating=[{'name': '*12'}],
                       actor=[{'name': 'Lily Collins'}, {'name': 'Sam Claflin'},
                              {'name': 'Tamsin Egerton'}, {'name': 'Jaime Winstone'}],
                       director=[{'name': 'Christian Ditter'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=0,
                       seasonNumber=0, numberOfSeasons=None,
                       description="Rosie and Alex have been best friends since they were 5, so they couldn't possibly be right for one another...or could they? When it comes to love, life and making the right choices, these two are their own worst enemies.",
                       duration='PT1H42M36S', datePublished='2014-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/e5277786-d303-459d-935e-b03c5499de1f/212c9d8e_99cd_42f1_ac2f_a084a4159fad1101999499265055421.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Bontonfilm ',
                       custom={'csfd_rating': '77', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '62',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'xmas, Comedy, Romantic',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '1974419835': Thing(id='1974419835', brandId='1974419835', recommendationId=None,
                        name='Na Stojáka v kině', alternateName='Na Stojáka v kině',
                        typeName='Movie',
                        genre=[{'name': 'comedy'}, {'name': 'Czech and Slovak'}],
                        subGenre=[], contentRating=[{'name': '*12'}],
                        actor=[{'name': 'Iva Pazderková'}, {'name': 'Lukáš Pavlásek'},
                               {'name': 'Karel Hynek'}, {'name': 'Richard Nedvěd'}],
                        director=[{'name': 'Iva Pazderková'}], producer=[], crew=[],
                        partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=0,
                        seasonNumber=0, numberOfSeasons=None,
                        description='Nejzábavnější podzimní nabídka kin proběhne Na Stojáka a v úplně novém exklusivním formátu 17D. 17 debilů a mikrofon. Parta komiků Na Stojáka připravila jeden jediný a nikdy neopakovaný speciální a úplně nejvíc unikátní večer nabitý humorem, natočila ho a teď uvádí na velkých plátnech do kin jako Na Stojáka v kině. Vtipy, které jste ještě neslyšeli, fóry připravené speciálně pro tuto příležitost, témata, která čekala na správný moment, pohotovost komiků s rychlostí blesku a doslova na milimetr přesné a dokonale časované pointy. Začínali z ničeho v době, kdy stand up v České republice neexistoval, na první konkurzy chodilo deset diváků, kteří zároveň byli i účinkujícími, prošli castingem, generálkou, natáčením, pak přišli první vystoupení v malých klubech pro spoustu kamarádů, kteří přivedli první diváky, pak přišli větší kluby, větší sály, vyprodaná vystoupení, a dnes? Dnes je Na Stojáka největší a nejznámější stand up formace v České republice, která má přes 200 živých vystoupení ročně, a nyní máte možnost i v kině zažít neopakovatelnou a neskutečně úžasnou atmosféru stand upu. Proč? Protože vůbec první formát stand up comedy v Česku známý pod hlavičkou Na Stojáka v příštím roce slaví 15 let. Kdo jiný by měl jako první přinést stand up do kin? Správně, ti nejznámější, nejstarší, největší a nejvtipnější stand up komedianti v Čechách.',
                        duration='PT1H30M17S', datePublished='2018-01-01T00:00:00+00:00',
                        image={
                            'url': 'https://image.cdn.magio.tv/cmsimages/4f7232cb-03bf-477a-a410-8c253528e63d/c0808c6a_4aef_4269_9d6e_bddf2b32c2516345038613697314895.jpg'},
                        inLanguage=None, isLiveBroadcast=False,
                        productionCompany='Bontonfilm ',
                        custom={'csfd_rating': '46', 'package_name': 'MAGIO_GO_VOD_M',
                                'added_to_package': '', 'vod_id': '64',
                                'added_in_brands': 'GOTMCZ_GO',
                                'genres_en': 'Comedy, Czech and Slovak',
                                'active': {'state': True,
                                           'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                        space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='*********', recommendationId=None,
                       name='Vianoce zas a znovu',
                       alternateName='Christmas All Over Again', typeName='Movie',
                       genre=[{'name': 'xmas'}, {'name': 'comedy'}], subGenre=[],
                       contentRating=[{'name': '*12'}],
                       actor=[{'name': 'Joseph Lawrence'},
                              {'name': 'Christy Romano Carlson'},
                              {'name': 'Amber Montana'}, {'name': 'Sean Ryan Fox'}],
                       director=[{'name': 'Christy Carlson Romano'}], producer=[],
                       crew=[], partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=0,
                       seasonNumber=0, numberOfSeasons=None,
                       description='Je štědrý večer a chlapec Eddie se těší, že dostane zbrusu nové vytoužené tenisky Breezy 3000. Eddie je totiž zakoukaný do dívky od sousedů, do Cindy, a doufá, že s novými teniskami by v jejích očích mohl stoupnout. Jenže když je konečně čas přijít k vánočnímu stromku, nejsou pod ním žádné dárky. A je tomu tak každý další den, každé ráno je stromeček bez dárků. Aby se Eddie vymanil ze zakleté vánoční smyčky bez dárků, vydává se za záhadným majitelem obuvi, s jehož pomocí pochopí, že Vánoce nejsou jen o dárcích.',
                       duration='PT1H15M34S', datePublished='2016-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/a7c605b1-dee6-410f-bdcc-47eab828ffe6/b8bbbfc7_0f10_4dc8_ab14_2df32c8d83c31974259859852832883.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Bontonfilm ',
                       custom={'csfd_rating': '56', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '68',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'xmas, Comedy', 'active': {'state': True,
                                                                       'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '1996060662858400000': Thing(id='1996060662858400000', brandId='1996060662858400000',
                                 recommendationId='4176', name='Harlots',
                                 alternateName=None, typeName='TVSeries',
                                 genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                                 contentRating=[{'name': '*15'}],
                                 actor=[{'name': 'Dorothy Atkinson'},
                                        {'name': 'Kate Fleetwood'},
                                        {'name': 'Eloise Smyth'}],
                                 director=[{'name': 'Coky Giedroyc'}], producer=[],
                                 crew=[], partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=None,
                                 seasonNumber=None, numberOfSeasons=None,
                                 description="Trapped inside Golden Square, Charlotte's future looks bleak unless she can convince Lydia of her loyalty. Meanwhile, Lucy makes a terrible mistake and Margaret must risk everything.",
                                 duration='PT51M49S',
                                 datePublished='2018-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/7375dd66-a105-4cf1-b611-1f6d10969d8f/072ac131_0167_43ac_b236_99616293cfac10108256799223206245.jpg'},
                                 inLanguage=None, isLiveBroadcast=False,
                                 productionCompany=None,
                                 custom={'csfd_rating': '72', 'package_name': 'Free',
                                         'added_to_package': '', 'vod_id': '4176',
                                         'added_in_brands': 'GOTMCZ_GO',
                                         'genres_en': 'actionadventuredrama',
                                         'active': {'state': True,
                                                    'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                                 space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1996060662858400000',
                       recommendationId=None, name='Epizóda 6',
                       alternateName='Harlots S2E6', typeName='TVEpisode',
                       genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                       contentRating=[{'name': '*15'}],
                       actor=[{'name': 'Dorothy Atkinson'}, {'name': 'Kate Fleetwood'},
                              {'name': 'Eloise Smyth'}],
                       director=[{'name': 'Coky Giedroyc'}], producer=[], crew=[],
                       partOfSeason={},
                       partOfSeries={'id': '1996060662858400000', 'name': 'Harlots'},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=6, seasonNumber=2,
                       numberOfSeasons=None,
                       description="Trapped inside Golden Square, Charlotte's future looks bleak unless she can convince Lydia of her loyalty. Meanwhile, Lucy makes a terrible mistake and Margaret must risk everything.",
                       duration='PT51M49S', datePublished='2018-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/7375dd66-a105-4cf1-b611-1f6d10969d8f/072ac131_0167_43ac_b236_99616293cfac10108256799223206245.jpg'},
                       inLanguage=None, isLiveBroadcast=False, productionCompany='ITV',
                       custom={'csfd_rating': '72', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '4176',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'actionadventuredrama',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '1926036474628100000': Thing(id='1926036474628100000', brandId='1926036474628100000',
                                 recommendationId='4186', name='Bangers Cash',
                                 alternateName=None, typeName='TVSeries',
                                 genre=[{'name': 'historical'}], subGenre=[],
                                 contentRating=[{'name': '*7'}], actor=[], director=[],
                                 producer=[], crew=[], partOfSeason={}, partOfSeries={},
                                 publication=[{'name': 'vod', 'id': '',
                                               'startDate': '1970-01-01T00:00:01+00:00',
                                               'endDate': '2038-01-19T00:00:00+00:00'}],
                                 keywords=[], episodeNumber=None, seasonNumber=None,
                                 numberOfSeasons=None,
                                 description="A Cold War Soviet classic goes under the hammer and a dealer the Mathewsons call 'MG Rob' proves that he's worthy of the nickname with a spending spree on classic MGBs.",
                                 duration='PT43M11S',
                                 datePublished='2018-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/7b905bc6-572d-4ba5-813b-8d9d3eed82ef/57cc64b9_50a0_4c11_abc9_d5687ea12c8311468124348024783158.jpg'},
                                 inLanguage=None, isLiveBroadcast=False,
                                 productionCompany=None,
                                 custom={'csfd_rating': '70', 'package_name': 'Free',
                                         'added_to_package': '', 'vod_id': '4186',
                                         'added_in_brands': 'GOTMCZ_GO',
                                         'genres_en': 'Historical',
                                         'active': {'state': True,
                                                    'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                                 space=None, isAdult=False, isKids=False),
    '2021476479': Thing(id='2021476479', brandId='1926036474628100000',
                        recommendationId=None, name='Epizóda 6',
                        alternateName='BangersCash S1E6', typeName='TVEpisode',
                        genre=[{'name': 'historical'}], subGenre=[],
                        contentRating=[{'name': '*7'}], actor=[], director=[],
                        producer=[], crew=[], partOfSeason={},
                        partOfSeries={'id': '1926036474628100000',
                                      'name': 'Bangers Cash'}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=6,
                        seasonNumber=1, numberOfSeasons=None,
                        description="A Cold War Soviet classic goes under the hammer and a dealer the Mathewsons call 'MG Rob' proves that he's worthy of the nickname with a spending spree on classic MGBs.",
                        duration='PT43M11S', datePublished='2018-01-01T00:00:00+00:00',
                        image={
                            'url': 'https://image.cdn.magio.tv/cmsimages/7b905bc6-572d-4ba5-813b-8d9d3eed82ef/57cc64b9_50a0_4c11_abc9_d5687ea12c8311468124348024783158.jpg'},
                        inLanguage=None, isLiveBroadcast=False,
                        productionCompany='Viasat Explore CEE',
                        custom={'csfd_rating': '70', 'package_name': 'Free',
                                'added_to_package': '', 'vod_id': '4186',
                                'added_in_brands': 'GOTMCZ_GO',
                                'genres_en': 'Historical', 'active': {'state': True,
                                                                      'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                        space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1983804962770100000',
                       recommendationId=None, name='89. Pomsta pána Petracelliho',
                       alternateName="Mr. Petracelli's Revenge", typeName='TVEpisode',
                       genre=[{'name': 'child'}], subGenre=[],
                       contentRating=[{'name': '*D'}],
                       actor=[{'name': ' Raven-Symoné'}, {'name': 'Issac Ryan Brown'},
                              {'name': 'Navia Ziraili Robinson'}],
                       director=[{'name': 'Victor Gonzalez'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1983804962770100000',
                                                      'name': "Raven's Home"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=89, seasonNumber=5,
                       numberOfSeasons=None,
                       description='In order to inherit Victor’s old classic car, Booker must get straight A’s. But Raven’s old teacher, Mr. Petracelli, swears there’s no way Booker will earn an A in his class.',
                       duration='PT24M10S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/48ade526-6e21-4502-9f70-3ff127f5f897/0525ba3f_7a8c_466c_8f8d_fa01e02fec0e7039083779812627262.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Disney',
                       custom={'csfd_rating': '70', 'package_name': 'MAGIO_GO_VOD_M',
                               'added_to_package': '', 'vod_id': '117169',
                               'added_in_brands': 'GOTMCZ_GO', 'genres_en': 'Child',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=True),
    '1973301618119700000': Thing(id='1973301618119700000', brandId='1973301618119700000',
                                 recommendationId='117176',
                                 name="AMERICA'S FUNNIEST HOME VIDEOS: ANIMAL EDITION 2",
                                 alternateName=None, typeName='TVSeries',
                                 genre=[{'name': 'popular_science'}], subGenre=[],
                                 contentRating=[{'name': '*7'}],
                                 actor=[{'name': 'Bob Saget'}, {'name': 'Tom Bergeron'},
                                        {'name': 'Jess Harnell'}],
                                 director=[{'name': 'Erik Fleming'}], producer=[],
                                 crew=[], partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=None,
                                 seasonNumber=None, numberOfSeasons=None,
                                 description='Cheeky animals keep the good times rolling and we have it all on video! Watch as a curious monkey breaks a window, a black bear drops by for a pool party, a boy narrowly escapes sharks in the Bahamas, a baby alligator makes a surprise appearance, a squirrel ploughs through thick snow, a helpful cockatoo cleans out gutters, and a dog lands one amazing jump.',
                                 duration='PT42M22S',
                                 datePublished='2022-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/45e3b914-4ecd-4c20-ad77-9a6ed5fcb3f4/5453a832_c4b5_4063_aef0_77e2034c91f618278178270675241840.jpg'},
                                 inLanguage=None, isLiveBroadcast=False,
                                 productionCompany=None,
                                 custom={'csfd_rating': '33', 'package_name': 'Free',
                                         'added_to_package': '', 'vod_id': '117176',
                                         'added_in_brands': 'GOTMCZ_GO',
                                         'genres_en': 'popular_science',
                                         'active': {'state': True,
                                                    'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                                 space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1973301618119700000',
                       recommendationId=None, name='7. Medvedia párty pri bazéne',
                       alternateName="AMERICA'S FUNNIEST HOME VIDEOS: ANIMAL EDITION 2 S1 Ep7",
                       typeName='TVEpisode', genre=[{'name': 'popular_science'}],
                       subGenre=[], contentRating=[{'name': '*7'}],
                       actor=[{'name': 'Bob Saget'}, {'name': 'Tom Bergeron'},
                              {'name': 'Jess Harnell'}],
                       director=[{'name': 'Erik Fleming'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1973301618119700000',
                                                      'name': "AMERICA'S FUNNIEST HOME VIDEOS: ANIMAL EDITION 2"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=7, seasonNumber=1,
                       numberOfSeasons=None,
                       description='Cheeky animals keep the good times rolling and we have it all on video! Watch as a curious monkey breaks a window, a black bear drops by for a pool party, a boy narrowly escapes sharks in the Bahamas, a baby alligator makes a surprise appearance, a squirrel ploughs through thick snow, a helpful cockatoo cleans out gutters, and a dog lands one amazing jump.',
                       duration='PT42M22S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/45e3b914-4ecd-4c20-ad77-9a6ed5fcb3f4/5453a832_c4b5_4063_aef0_77e2034c91f618278178270675241840.jpg'},
                       inLanguage=None, isLiveBroadcast=False, productionCompany='NGC',
                       custom={'csfd_rating': '33', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '117176',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'popular_science', 'active': {'state': True,
                                                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1973301618119700000',
                       recommendationId=None, name='8. Labky a poriadok',
                       alternateName="AMERICA'S FUNNIEST HOME VIDEOS: ANIMAL EDITION 2 S1 Ep8",
                       typeName='TVEpisode', genre=[{'name': 'popular_science'}],
                       subGenre=[], contentRating=[{'name': '*7'}],
                       actor=[{'name': 'Bob Saget'}, {'name': 'Tom Bergeron'},
                              {'name': 'Jess Harnell'}],
                       director=[{'name': 'Erik Fleming'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1973301618119700000',
                                                      'name': "AMERICA'S FUNNIEST HOME VIDEOS: ANIMAL EDITION 2"},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=8, seasonNumber=1,
                       numberOfSeasons=None,
                       description='Adventurous animals mixed with clumsy mishaps means only one thing - hilarious animal videos! The fun gets going as a chicken jumps into a delivery truck, a bulldog slips and slides on a frozen lake, a monkey steals a package of food, a flamingo bites a woman, a dog hides its guilt with a cheeky smile, a cat’s leap of faith goes wrong, a stowaway lizard surprises a lady, and a bear breaks into a yard.',
                       duration='PT42M22S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/4a3cdc05-5652-4b5c-aacf-7eb0ff8c6ab6/08788270_2e2f_46f8_8777_6dc040c2dd1a17285097475716012184.jpg'},
                       inLanguage=None, isLiveBroadcast=False, productionCompany='NGC',
                       custom={'csfd_rating': '33', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '117177',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'popular_science', 'active': {'state': True,
                                                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '1010778430': Thing(id='1010778430', brandId='1010778430', recommendationId=None,
                        name='Žena v čiernom', alternateName='The Woman in Black',
                        typeName='Movie', genre=[{'name': 'dramahoror'}], subGenre=[],
                        contentRating=[{'name': '*15'}],
                        actor=[{'name': 'Daniel Radcliffe'}, {'name': 'Ciarán Hinds'},
                               {'name': 'Janet McTeer'}],
                        director=[{'name': 'James Watkins'}], producer=[], crew=[],
                        partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=0,
                        seasonNumber=0, numberOfSeasons=None,
                        description='In London, solicitor Arthur Kipps still grieves over the death of his beloved wife Stella on the delivery of their son Joseph four years before. His employer gives him a last chance to keep his job, and he is assigned to travel to the remote village of Crythin Gifford to examine the documentation of the Eel Marsh House that belonged to the recently deceased Mrs. Drablow. Arthur befriends Daily on the train and the man offers a ride to him to the Gifford Arms inn. Arthur has a cold reception and the owner of the inn tells that he did not receive the request of reservation and there is no available room. The next morning, Arthur meets solicitor Jerome who advises him to return to London. However, Arthur goes to the isolated manor and soon he finds that Eel Marsh House is haunted by the vengeful ghost of a woman dressed in black. He also learns that the woman lost her son, drowned in the marsh, and she seeks revenge, taking the children of the terrified locals',
                        duration='PT1H35M30S', datePublished='2012-01-01T00:00:00+00:00',
                        image={
                            'url': 'https://image.cdn.magio.tv/cmsimages/65f3dc8c-621d-4c81-9cd2-8cde3ca60367/3c013925_4784_4459_a7ac_a0f501fa01a91916926503899005791.jpeg'},
                        inLanguage=None, isLiveBroadcast=False,
                        productionCompany='Bontonfilm',
                        custom={'csfd_rating': '71', 'package_name': 'MAGIO_GO_VOD_L',
                                'added_to_package': '', 'vod_id': '864',
                                'added_in_brands': 'GOTMCZ_GO',
                                'genres_en': 'dramahorror', 'active': {'state': True,
                                                                       'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                        space=None, isAdult=False, isKids=False),
    '1976520422468000000': Thing(id='1976520422468000000', brandId='1976520422468000000',
                                 recommendationId='127114', name='Grantchester',
                                 alternateName=None, typeName='TVSeries',
                                 genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                                 contentRating=[{'name': '*15'}],
                                 actor=[{'name': 'James Norton'},
                                        {'name': 'Robson Green'},
                                        {'name': 'Morven Christie'}],
                                 director=[{'name': 'Harry Bradbeer'}], producer=[],
                                 crew=[], partOfSeason={}, partOfSeries={}, publication=[
            {'name': 'vod', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
             'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=None,
                                 seasonNumber=None, numberOfSeasons=None,
                                 description='In the Cambridgeshire village of Grantchester, the funeral of a parishioner leads to local vicar Sidney Chambers becoming embroiled in a murder investigation.',
                                 duration='PT46M12S',
                                 datePublished='2015-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/21e3fb08-5931-4f26-97ba-047d77b71288/7dadaebb_2c7a_416c_b7df_f112848bfa4d4808997628342606639.jpg'},
                                 inLanguage=None, isLiveBroadcast=False,
                                 productionCompany=None,
                                 custom={'csfd_rating': '74', 'package_name': 'Free',
                                         'added_to_package': '', 'vod_id': '127114',
                                         'added_in_brands': 'GOTMCZ_GO',
                                         'genres_en': 'actionadventuredrama',
                                         'active': {'state': True,
                                                    'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                                 space=None, isAdult=False, isKids=False),
    '1234454327': Thing(id='1234454327', brandId='1976520422468000000',
                        recommendationId=None, name='Epizóda 1',
                        alternateName='Episode 1', typeName='TVEpisode',
                        genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                        contentRating=[{'name': '*15'}],
                        actor=[{'name': 'James Norton'}, {'name': 'Robson Green'},
                               {'name': 'Morven Christie'}],
                        director=[{'name': 'Harry Bradbeer'}], producer=[], crew=[],
                        partOfSeason={}, partOfSeries={'id': '1976520422468000000',
                                                       'name': 'Grantchester'},
                        publication=[{'name': 'vod', 'id': '',
                                      'startDate': '1970-01-01T00:00:01+00:00',
                                      'endDate': '2038-01-19T00:00:00+00:00'}],
                        keywords=[], episodeNumber=1, seasonNumber=1,
                        numberOfSeasons=None,
                        description='In the Cambridgeshire village of Grantchester, the funeral of a parishioner leads to local vicar Sidney Chambers becoming embroiled in a murder investigation.',
                        duration='PT46M12S', datePublished='2015-01-01T00:00:00+00:00',
                        image={
                            'url': 'https://image.cdn.magio.tv/cmsimages/21e3fb08-5931-4f26-97ba-047d77b71288/7dadaebb_2c7a_416c_b7df_f112848bfa4d4808997628342606639.jpg'},
                        inLanguage=None, isLiveBroadcast=False,
                        productionCompany='Lovely Day',
                        custom={'csfd_rating': '74', 'package_name': 'Free',
                                'added_to_package': '', 'vod_id': '127114',
                                'added_in_brands': 'GOTMCZ_GO',
                                'genres_en': 'actionadventuredrama',
                                'active': {'state': True,
                                           'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                        space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1976520422468000000',
                       recommendationId=None, name='Epizóda 3',
                       alternateName='Episode 3', typeName='TVEpisode',
                       genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                       contentRating=[{'name': '*12'}],
                       actor=[{'name': 'James Norton'}, {'name': 'Robson Green'},
                              {'name': 'Morven Christie'}],
                       director=[{'name': 'Harry Bradbeer'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1976520422468000000',
                                                      'name': 'Grantchester'},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=3, seasonNumber=1,
                       numberOfSeasons=None,
                       description='After the death of two elderly sisters, Sidney and Geordie try to track down a man on the run; will they be able to catch the killer before he strikes again?',
                       duration='PT44M29S', datePublished='2015-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/384e2598-fe3a-45f6-8771-e3863c335156/de3ec7ba_fa75_42f9_a6cd_af76f995d37010245305778971520283.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Lovely Day',
                       custom={'csfd_rating': '74', 'package_name': 'MAGIO_GO_VOD_M',
                               'added_to_package': '', 'vod_id': '127116',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'actionadventuredrama',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='1976520422468000000',
                       recommendationId=None, name='Epizóda 5',
                       alternateName='Episode 5', typeName='TVEpisode',
                       genre=[{'name': 'actionadventuredrama'}], subGenre=[],
                       contentRating=[{'name': '*15'}],
                       actor=[{'name': 'James Norton'}, {'name': 'Robson Green'},
                              {'name': 'Morven Christie'}],
                       director=[{'name': 'Harry Bradbeer'}], producer=[], crew=[],
                       partOfSeason={}, partOfSeries={'id': '1976520422468000000',
                                                      'name': 'Grantchester'},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=5, seasonNumber=1,
                       numberOfSeasons=None,
                       description="Sidney and Geordie travel to London for a night at Johnny Johnson's jazz club, but they soon discover that policemen and clergymen can never truly be off duty.",
                       duration='PT46M12S', datePublished='2015-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/10333c80-6fbb-4e8f-b0ed-47d626e16fb5/01f0c657_5712_4bf1_aa5c_f56987b4fcc61901464845002313571.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='Lovely Day',
                       custom={'csfd_rating': '74', 'package_name': 'MAGIO_GO_VOD_L',
                               'added_to_package': '', 'vod_id': '127118',
                               'added_in_brands': 'GOTMCZ_GO',
                               'genres_en': 'actionadventuredrama',
                               'active': {'state': True,
                                          'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'}},
                       space=None, isAdult=False, isKids=False),
    '*********': Thing(id='*********', brandId='dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM=',
                       recommendationId=None,
                       name='UFC Highlights: UFC 273 - Volkanovski vs. The Korean Zombie',
                       alternateName='UFC Highlights: UFC 273 - Volkanovski vs. The Korean Zombie',
                       typeName='TVEpisode', genre=[{'name': 'sport'}, {'name': 'ufc'}],
                       subGenre=[], contentRating=[{'name': '*15'}], actor=[],
                       director=[], producer=[], crew=[], partOfSeason={}, partOfSeries=
            {'name': 'UFC Highlights', 'id': 'dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM='},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=0, seasonNumber=0,
                       numberOfSeasons=None,
                       description='Sestřih UFC 273 s hlavním zápasem večeře mezi Alexandrem Volkanovským a The Korean Zombie, ale i se zajímavými souboji mezi Petrem Yanem a Aljamainem Sterlingem a Khamzat Chimaevov a Gilbertem Burnsem.',
                       duration='PT1H29M46S', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/237f623c-d770-4db0-9a97-2bfd8fb06bf4/bb3ceeb6_2467_4570_b13e_628fbb28b4d012826251976517717298.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='PREMIERSPORT',
                       custom={'csfd_rating': '70', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '92037',
                               'added_in_brands': 'DIGIGOTMCZ_GO',
                               'genres_en': 'Sport, ufc', 'active': {'state': True,
                                                                     'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'},
                               'inferred': True}, space=None, isAdult=False,
                       isKids=False),
    '*********': Thing(id='*********', brandId='dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM=',
                       recommendationId=None,
                       name='UFC Highlights: UFC 276 - Adesanya vs. Cannonier',
                       alternateName='UFC Highlights: UFC 276 - Adesanya vs. Cannonier',
                       typeName='TVEpisode', genre=[{'name': 'sport'}, {'name': 'ufc'}],
                       subGenre=[], contentRating=[{'name': '*15'}], actor=[],
                       director=[], producer=[], crew=[], partOfSeason={}, partOfSeries=
            {'name': 'UFC Highlights', 'id': 'dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM='},
                       publication=[{'name': 'vod', 'id': '',
                                     'startDate': '1970-01-01T00:00:01+00:00',
                                     'endDate': '2038-01-19T00:00:00+00:00'}],
                       keywords=[], episodeNumber=0, seasonNumber=0,
                       numberOfSeasons=None,
                       description='Na dalším číslovaném turnaji UFC popáté v kariéře obhajoval titul ve střední váze neporažený šampion Israel Adesanya a dočkali jsme se i završení trilogie mezi Alexanderem Volkanovským a Maxem Hollowayem.',
                       duration='PT1H30M', datePublished='2022-01-01T00:00:00+00:00',
                       image={
                           'url': 'https://image.cdn.magio.tv/cmsimages/c46dc8b6-1ee8-4aeb-ad95-359ff2b2b3d6/7c048019_d381_497f_8046_f6ea9444de9316506990499352210774.jpg'},
                       inLanguage=None, isLiveBroadcast=False,
                       productionCompany='PREMIERSPORT',
                       custom={'csfd_rating': '70', 'package_name': 'Free',
                               'added_to_package': '', 'vod_id': '103041',
                               'added_in_brands': 'GODIGITMCZ_GO',
                               'genres_en': 'Sport, ufc', 'active': {'state': True,
                                                                     'stateUpdateTimestamp': '2022-09-16T04:31:00+00:00'},
                               'inferred': True}, space=None, isAdult=False,
                       isKids=False),
    'dHZzZXJpZXNfTkZM': Thing(id='dHZzZXJpZXNfTkZM', brandId='dHZzZXJpZXNfTkZM',
                              recommendationId='21252948', name='NFL',
                              alternateName='NFL', typeName='TVSeries',
                              genre=[{'name': 'sport'}, {'name': 'nfl'}], subGenre=[],
                              contentRating=[{'name': '*7'}], actor=[], director=[],
                              producer=[], crew=[], partOfSeason={},
                              partOfSeries={'name': 'NFL', 'id': 'dHZzZXJpZXNfTkZM'},
                              publication=[{'name': 'vod', 'id': '',
                                            'startDate': '1970-01-01T00:00:01+00:00',
                                            'endDate': '2038-01-19T00:00:00+00:00'}],
                              keywords=[], episodeNumber=0, seasonNumber=0,
                              numberOfSeasons=None,
                              description='Match of the 6th round of the National Football League.',
                              duration='PT2H35M53S',
                              datePublished='2022-01-01T00:00:00+00:00', image={
            'url': 'https://image.cdn.magio.tv/cmsimages/da70344a-027f-41d7-83b4-9210518b5495/a2d0fe66_c967_4143_95eb_5b36e25d3ffa11844461919280419369.jpg'},
                              inLanguage=None, isLiveBroadcast=False,
                              productionCompany='PREMIERSPORT',
                              custom={'csfd_rating': '80', 'package_name': 'Free',
                                      'added_to_package': '', 'vod_id': '116100',
                                      'added_in_brands': 'GODIGITMCZ_GO',
                                      'genres_en': 'Sport, NFL',
                                      'active': {'state': True,
                                                 'stateUpdateTimestamp': '2023-01-09T12:30:00+00:00'},
                                      'inferred': True}, space=None, isAdult=False,
                              isKids=False),
    'dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM=': Thing(id='dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM=',
                                              brandId='dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM=',
                                              recommendationId='*********',
                                              name='UFC Highlights',
                                              alternateName='UFC Highlights',
                                              typeName='TVSeries',
                                              genre=[{'name': 'sport'}, {'name': 'ufc'}],
                                              subGenre=[],
                                              contentRating=[{'name': '*15'}], actor=[],
                                              director=[], producer=[], crew=[],
                                              partOfSeason={}, partOfSeries=
            {'name': 'UFC Highlights', 'id': 'dHZzZXJpZXNfVUZDIEhpZ2hsaWdodHM='},
                                              publication=[{'name': 'vod', 'id': '',
                                                            'startDate': '1970-01-01T00:00:01+00:00',
                                                            'endDate': '2038-01-19T00:00:00+00:00'}],
                                              keywords=[], episodeNumber=0,
                                              seasonNumber=0, numberOfSeasons=None,
                                              description='Sestřih UFC 273 s hlavním zápasem večeře mezi Alexandrem Volkanovským a The Korean Zombie, ale i se zajímavými souboji mezi Petrem Yanem a Aljamainem Sterlingem a Khamzat Chimaevov a Gilbertem Burnsem.',
                                              duration='PT1H29M46S',
                                              datePublished='2022-01-01T00:00:00+00:00',
                                              image={
                                                  'url': 'https://image.cdn.magio.tv/cmsimages/237f623c-d770-4db0-9a97-2bfd8fb06bf4/bb3ceeb6_2467_4570_b13e_628fbb28b4d012826251976517717298.jpg'},
                                              inLanguage=None, isLiveBroadcast=False,
                                              productionCompany='PREMIERSPORT',
                                              custom={'csfd_rating': '70',
                                                      'package_name': 'Free',
                                                      'added_to_package': '',
                                                      'vod_id': '92037',
                                                      'added_in_brands': 'DIGIGOTMCZ_GO',
                                                      'genres_en': 'Sport, ufc',
                                                      'active': {'state': True,
                                                                 'stateUpdateTimestamp': '2023-01-09T12:30:00+00:00'},
                                                      'inferred': True}, space=None,
                                              isAdult=False, isKids=False)}
