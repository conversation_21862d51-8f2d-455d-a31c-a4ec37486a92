from copy import deepcopy
from dataclasses import asdict
from unittest import TestCase

from tests.jobs.standard_catalogue_loader.core_loader.data.standard_cat_loader_config_generator import \
    generate_default_catalogue_loader_config
from tests.jobs.standard_catalogue_loader.customers.uktv.data.categories import \
    categories
from tests.jobs.standard_catalogue_loader.customers.uktv.data.channels import channels
from tests.jobs.standard_catalogue_loader.customers.uktv.data.episodes import \
    test_episodes
from thefilter.jobs.standard_catalogue_loader.customers.uktv.dynamic_types.brands import \
    Brands

test_brand_2156 = {
    "item_type": "brand",
    "id": 2156,
    "name": "Zapped",
    "slug": "zapped",
    "description": "Fantasy comedy series in which a bored office worker is transported to a parallel world.",
    "medium_description": "Fantasy comedy series in which an online data marketing assistant is transported to a parallel world by a magic amulet.",
    "image": "https://uktv-res.cloudinary.com/image/upload/v1650383446/itvow9ri4jclslwd8b38.jpg",
    "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531379/hexqtdvzdvsgexzkaxdf.jpg",
    "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730156/is7o9aafhhdldpb3w8xu.jpg",
    "hide_episode_title": False,
    "primary_subcategory": "UK Comedy",
    "primary_category": "Comedy",
    "subcategories": [
        4
    ],
    "channel": "dave",
    "sponsorlogos": [
        {
            "id": "1",
            "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png",
            "sponsored_by_title": "Sponsors of Dave",
            "sponsor_url": "",
            "sponsor_type": "NORM",
            "name": "Dave Sponsor - Citroen",
            "description": "2022 Sponsors of Dave",
            "sponsor_name": "Citroen",
            "transform_sizes": {
                "_1080p": {
                    "width": 85,
                    "height": 70,
                    "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"
                },
                "_720p": {
                    "width": 57,
                    "height": 47,
                    "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"
                },
                "_540p": {
                    "width": 42,
                    "height": 35,
                    "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"
                }
            },
            "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"
        }
    ],
    "has_subtitles": True,
    "is_feature": False,
    "use_episode_grids": False,
    "series": [
        {
            "id": 15264,
            "number": "1"
        },
        {
            "id": 15702,
            "number": "2"
        },
        {
            "id": 16149,
            "number": "3"
        }
    ],
    "landing_episode": {
        "item_type": "episode",
        "id": 113443,
        "house_number": "CTON403A",
        "image": "https://uktv-res.cloudinary.com/image/upload/v1475667120/xs89skopm4agrqywmlu5.jpg",
        "name": "Mr Weaver",
        "synopsis": "The Inbetweeners' James Buckley stars in this original fantasy-comedy. A bored desk worker is transported to a parallel world of wizards and warriors by a magic amulet.",
        "synopsis_short": "Brian, a data marketing assistant, is transported to a parallel world.",
        "episode_number": 1,
        "series_number": "1",
        "series_id": 15264,
        "series_image": "https://uktv-res.cloudinary.com/image/upload/v1473414281/mxbyineehuetlyqdfisl.jpg",
        "show_support_link": False,
        "video_id": 5815704195001,
        "content_duration": 1809,
        "variant": "82h",
        "definition": "HD",
        "guidance_age": "15",
        "guidance_text": "Contains Strong Language",
        "available_start": "2023-01-21T00:00:00Z",
        "available_end": "2023-10-12T22:59:00Z",
        "available_start_unix": 1674259200,
        "available_end_unix": 1697151540,
        "channel": "dave",
        "is_feature": False,
        "duration": 40,
        "has_subtitles": True,
        "watch_online_link": "https://uktvplay.co.uk/shows/zapped/watch-online/5815704195001",
        "credits_cuepoint": 1775.08,
        "brand_id": 2156,
        "brand_slug": "zapped",
        "brand_name": "Zapped",
        "brand_description": "Fantasy comedy series in which a bored office worker is transported to a parallel world.",
        "brand_medium_description": "Fantasy comedy series in which an online data marketing assistant is transported to a parallel world by a magic amulet.",
        "hide_episode_title": False,
        "available_episodes": 15,
        "brand_is_feature": False,
        "brand_subcategories": [
            4
        ],
        "brand_image": "https://uktv-res.cloudinary.com/image/upload/v1650383446/itvow9ri4jclslwd8b38.jpg",
        "brand_primary_category": "Comedy",
        "brand_primary_subcategory": "UK Comedy",
        "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730156/is7o9aafhhdldpb3w8xu.jpg"
    },
    "available_episodes": 15,
    "synopsis_long": None,
    "parent_child_collection": {
        "parent_brand": {
            "item_type": "brand",
            "id": 523,
        }
    }
}


class TestBrands(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        customer = 'uktv'

        config = generate_default_catalogue_loader_config(customer=customer)

        brand_list = [2131, 2156]
        self._brands_api = Brands(
            config=config,
            channels=channels,
            categories=categories,
            brand_list=brand_list,
            episodes=test_episodes
        )

    def test_create_brand_to_episode_map(self):
        expected = {
            '2156_brand': ['CTON404T', 'CTON403A'],
            '2131_brand': ['CTON291E']
        }
        self.assertEqual(expected, self._brands_api._brand_to_episode_map)

    def test_create_brands(self):
        converted_item = self._brands_api._convert_brands(test_brand_2156)
        expected = {
            'actor': [
                {'name': 'James Buckley'},
                {'name': 'Paul Kaye'},
                {'name': 'Sharon Rooney'},
                {'name': 'Kenneth Collard'},
                {'name': 'Louis Emerick'},
                {'name': 'Fred Dibnah'}
            ],
            'alternateName': 'zapped',
            'brandId': '2156_brand',
            'brandName': "Zapped",
            'contentRating': [{'name': '15'}],
            'crew': [],
            'custom': {'brand': [{'id': 2156, 'name': 'Zapped'}],
                       'parentBrandId': 523,
                       'category': [{'id': 1, 'name': 'Comedy'}],
                       'channel': [{'id': 3854, 'name': 'dave'}],
                       'durationMinutes': 29,
                       'subCategory': [{'id': 4, 'name': 'UK Comedy'}],
                       'subGenre': []},
            'datePublished': '2000-01-01T00:00:00+00:00',
            'description': 'Fantasy comedy series in which an online data marketing '
                           'assistant is transported to a parallel world by a magic '
                           'amulet.',
            'director': [],
            'duration': 'PT29M35S',
            'episodeNumber': None,
            'genre': [{'id': '1_category', 'name': 'Comedy'}],
            'id': '2156',
            'image': {
                'url': 'https://uktv-res.cloudinary.com/image/upload/v1650383446/itvow9ri4jclslwd8b38.jpg'},
            'inLanguage': 'en-GB',
            'isAdult': None,
            'isKids': None,
            'isLiveBroadcast': None,
            'keywords': ['sitcom', 'fantasy'],
            'name': 'Zapped',
            'numberOfSeasons': None,
            'partOfSeason': [],
            'partOfSeries': [],
            'producer': [],
            'productionCompany': None,
            'publication': [
                {'endDate': '2022-10-03T23:59:00+00:00',
                 'location': 'web',
                 'publishedOn': {'id': 3854, 'name': 'dave'},
                 'startDate': '2017-02-01T00:00:00+00:00', 'notes': 'vod'}
            ],
            'recommendationId': 'CTON403A',
            'seasonNumber': None,
            'space': 'brand',
            'subGenre': [{'id': '4_subcategory', 'name': 'UK Comedy'}],
            'typeName': 'Brand'
        }

        self.assertEqual(expected, asdict(converted_item))

    def test_extend_publications(self):
        publications = [
            {
                "number": 1,
                "startDate": "1999-02-01T00:00:00+00:00",
                "endDate": "1999-02-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 3854,
                        "name": "dave"
                    }
            },
            {
                "number": 2,
                "startDate": "2015-05-01T00:00:00+00:00",
                "endDate": "2025-10-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 3854,
                        "name": "dave"
                    }
            },
            {
                "number": 3,
                "startDate": "2021-02-01T00:00:00+00:00",
                "endDate": "2021-09-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 3854,
                        "name": "dave"
                    }
            },
            {
                "number": 4,
                "startDate": "2020-01-01T00:00:00+00:00",
                "endDate": "2030-11-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 3854,
                        "name": "dave"
                    }
            },
            {
                "number": 5,
                "startDate": "2017-02-01T00:00:00+00:00",
                "endDate": "2022-10-03T23:59:00+00:00",
                "location": "elsewhere",
                "publishedOn":
                    {
                        "id": 3854,
                        "name": "dave"
                    }
            },
            {
                "number": 6,
                "startDate": "2017-02-01T00:00:00+00:00",
                "endDate": "2022-10-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 2222,
                        "name": "different_channel"
                    }
            },
            {
                "number": 7,
                "startDate": "2033-02-01T00:00:00+00:00",
                "endDate": "2033-10-03T23:59:00+00:00",
                "location": "web",
                "publishedOn":
                    {
                        "id": 2222,
                        "name": "different_channel"
                    }
            }
        ]
        expected = [
            {'endDate': '1999-02-03T23:59:00+00:00',
             'location': 'web',
             'number': 1,
             'publishedOn': {'id': 3854, 'name': 'dave'},
             'startDate': '1999-02-01T00:00:00+00:00'},
            {'endDate': '2030-11-03T23:59:00+00:00',
             'location': 'web',
             'number': 2,
             'publishedOn': {'id': 3854, 'name': 'dave'},
             'startDate': '2015-05-01T00:00:00+00:00'},
            {'endDate': '2022-10-03T23:59:00+00:00',
             'location': 'elsewhere',
             'number': 5,
             'publishedOn': {'id': 3854, 'name': 'dave'},
             'startDate': '2017-02-01T00:00:00+00:00'},
            {'endDate': '2022-10-03T23:59:00+00:00',
             'location': 'web',
             'number': 6,
             'publishedOn': {'id': 2222, 'name': 'different_channel'},
             'startDate': '2017-02-01T00:00:00+00:00'},
            {'endDate': '2033-10-03T23:59:00+00:00',
             'location': 'web',
             'number': 7,
             'publishedOn': {'id': 2222, 'name': 'different_channel'},
             'startDate': '2033-02-01T00:00:00+00:00'}
        ]

        result = self._brands_api._update_uktv_publications(publications)
        self.assertEqual(expected, result)

    def test_cat_lookup_from_only_subcats(self):
        # Some items are missing category information
        test_brand_2156_copy = deepcopy(test_brand_2156)
        test_brand_2156_copy["primary_subcategory"] = None
        test_brand_2156_copy["primary_category"] = None

        converted_item = self._brands_api._convert_brands(test_brand_2156_copy)

        expected_genre = [{'name': 'Comedy', 'id': '1_category'}]
        self.assertEqual(expected_genre, converted_item.genre)

        expected_subgenre = [{'id': '4_subcategory', 'name': 'UK Comedy'}]
        self.assertEqual(expected_subgenre, converted_item.subGenre)

        expected_custom_category = [{'name': 'Comedy', 'id': 1}]
        self.assertEqual(expected_custom_category, converted_item.custom["category"])

        expected_custom_subcategory = [{'id': 4, 'name': 'UK Comedy'}]
        self.assertEqual(
            expected_custom_subcategory, converted_item.custom["subCategory"]
        )
