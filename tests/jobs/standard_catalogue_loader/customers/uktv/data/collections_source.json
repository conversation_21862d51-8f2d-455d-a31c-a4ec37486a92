{"collections": {"collections": [{"id": 222, "name": "The Stacey <PERSON> Collection", "slug": "stacey-dooley-collection", "subtitle": null, "shows_list_image": null, "page_header_image": null, "full_width_image": "https://uktv-res.cloudinary.com/image/upload/v1676549336/tvwiufixuseqaoskhutt.jpg", "use_portrait_images": false, "portrait_image": null, "type": "standard", "banner_background_colour": null, "subcategories": [51, 80, 79, 81], "items": [{"item_type": "brand", "id": 4088, "name": "<PERSON> Sleeps Over: Family and Me", "slug": "stacey-dooley-sleeps-over-family-and-me", "image": "https://uktv-res.cloudinary.com/image/upload/v1688814612/djypumlcfwhqrfgbez2r.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1690542923/ooiv0ckuaqtlq6hpv7km.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1688814938/g5beu14dixgjktbpdil2.jpg", "hide_episode_title": false, "primary_subcategory": "Family Life", "primary_category": "Real Life", "subcategories": [79, 80], "available_episodes": 1, "description": "<PERSON> reflects on whether her status as a new mother has changed her perspective.", "medium_description": "<PERSON> watches clips from previous episodes and reflects on whether her status as a new mother has changed her perspective on the parenting methods she witnessed first-hand.", "channel": "w", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}, {"item_type": "brand", "id": 4103, "name": "<PERSON> Sleeps Over: More Family and Me", "slug": "stacey-dooley-sleeps-over-more-family-and-me", "image": "https://uktv-res.cloudinary.com/image/upload/v1689419445/vwjp6owgaiwietajjhcq.jpg", "portrait_image": null, "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1689419759/qya5sc54mk4z23l9g31e.jpg", "hide_episode_title": false, "primary_subcategory": "Family Life", "primary_category": "Real Life", "subcategories": [79, 80], "available_episodes": 1, "description": "Following her life-changing event of having a baby, <PERSON> looks back at more parenting experiences.", "medium_description": "Following her life-changing event of having a baby, <PERSON> looks back at more parenting experiences she came across when meeting families for the Stacey Dooley Sleeps Over series.", "channel": "w", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}, {"item_type": "brand", "id": 3252, "name": "<PERSON> Sleeps Over", "slug": "stacey-dooley-sleeps-over", "image": "https://uktv-res.cloudinary.com/image/upload/v1671010674/flheoecqyr5kmsiidq9b.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1681729903/eouig8x7j6uqrqtljpuy.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730513/ieyallsa6jfvd7b2xhcs.jpg", "hide_episode_title": false, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80], "available_episodes": 15, "description": "The investigative reporter spends 72 hours in the company of extraordinary characters.", "medium_description": "The investigative reporter aims to find out what life is like in modern Britain by spending 72 hours in the company of a wide range of extraordinary characters and families.", "channel": "w", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}, {"item_type": "brand", "id": 3953, "name": "<PERSON>s Over USA", "slug": "stacey-dooley-sleeps-over-usa", "image": "https://uktv-res.cloudinary.com/image/upload/v1677087663/mub1p4cex39xyzui3oc5.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685790921/nup0uv7sreat0q71gkzr.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730960/v9svbmc0i5cd0e3zepjb.jpg", "hide_episode_title": false, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80, 79], "available_episodes": 3, "description": "Award-winning documentary maker <PERSON> meets extraordinary families in the USA.", "medium_description": "Award-winning documentary maker <PERSON> meets a variety of extraordinary families in the USA, lifting the lid on more fascinating and unusual lifestyles.", "channel": "w", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}, {"item_type": "brand", "id": 3787, "name": "<PERSON> Sleeps Over: Body Positive Warrior", "slug": "stacey-dooley-sleeps-over-body-positive-warrior", "image": "https://uktv-res.cloudinary.com/image/upload/v1688480150/l0mqesdycbjuxpmqxb6b.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1690365818/mcfbpvzykd4q1mlenvbz.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730865/njws5opxbkw7bgblpptp.jpg", "hide_episode_title": true, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80], "available_episodes": 1, "description": "<PERSON> spends the weekend with inspirational body positive advocate <PERSON><PERSON><PERSON>.", "medium_description": "<PERSON> spends the weekend with inspirational body positive advocate <PERSON><PERSON><PERSON> in this International Women's Day special.", "channel": "w", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}, {"item_type": "brand", "id": 2741, "name": "Brainwashing <PERSON>", "slug": "brainwashing-stacey", "image": "https://uktv-res.cloudinary.com/image/upload/v1681478196/njjmtjnywbcz5ovjysyq.jpg", "portrait_image": null, "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681902898/dbauchayxduip5nluzmt.jpg", "hide_episode_title": false, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80], "available_episodes": 2, "description": "<PERSON> seeks to understand the beliefs of some of the world's most controversial groups", "medium_description": "<PERSON> immerses herself deeper than ever before into extreme worlds, joining some of the world's most controversial groups to try to understand their unique ideologies", "channel": "w", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": null}, {"item_type": "brand", "id": 3220, "name": "<PERSON> Investigates - Fashion's Dirty Secrets", "slug": "stacey-dooley-investigates-fashions-dirty-secrets", "image": "https://uktv-res.cloudinary.com/image/upload/v1650455675/i3l0rxoipojaxi7axqek.jpg", "portrait_image": null, "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681816644/ziqrd4rq9xm3wsmdff1i.jpg", "hide_episode_title": false, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80], "available_episodes": 1, "description": "The environmental impact of the trade in cheap clothing.", "medium_description": "The investigate journalist examines the environmental impact caused by the trade in cheap clothing, revealing how toxic chemical byproducts are polluting waterways.", "channel": "w", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": null}, {"item_type": "brand", "id": 2108, "name": "<PERSON> in the USA", "slug": "stacey-dooley-in-the-usa", "image": "https://uktv-res.cloudinary.com/image/upload/v1650383387/gvdyzjbt4vss2mlkoguz.jpg", "portrait_image": null, "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681816606/ziad4ny0zxxqhu8gfgrr.jpg", "hide_episode_title": false, "primary_subcategory": "Real Stories", "primary_category": "Real Life", "subcategories": [80, 79], "available_episodes": 4, "description": "<PERSON> reveals some shocking faces of youth in America.", "medium_description": "<PERSON> reveals some shocking faces of youth in America.", "channel": "w", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [], "synopsis_long": ""}]}, {"id": 83, "name": "<PERSON> Comedy", "slug": "original-comedy", "subtitle": "Original Comedy", "channel_id": 3854, "channel_slug": "dave", "shows_list_image": null, "page_header_image": null, "full_width_image": "https://uktv-res.cloudinary.com/image/upload/v1664794984/oigmmoqx5865pbdxmpma.jpg", "use_portrait_images": false, "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1560442872/ckfyfjntl3g0rjrzeczh.jpg", "type": "standard", "banner_background_colour": null, "subcategories": [4, 6, 1, 57, 51, 75], "items": [{"item_type": "brand", "id": 3357, "name": "Meet the <PERSON>s", "slug": "meet-the-rich<PERSON><PERSON>", "image": "https://uktv-res.cloudinary.com/image/upload/v1680769591/yuopebvyobziiefgkcph.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531519/jpc7uow1cw8fc856u5it.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730570/qq0piy9bsfjtrqitlmvl.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 34, "description": "Comedy series starring real-life married couple <PERSON> and <PERSON>.", "medium_description": "Comedy series starring real-life married couple <PERSON> and <PERSON> as they offer a fictional view of their life together.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3661, "name": "<PERSON>'s Outsiders", "slug": "outsiders", "image": "https://uktv-res.cloudinary.com/image/upload/v1676306006/pq0qx321phhjxaybwl0b.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1681729993/i1kv70akviyhuv3cjalf.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1685556601/h3iiux2isdzrrml3odgu.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 12, "description": "Three pairs of comedians are challenged to prove that they've got the mettle and skills to thrive outdoors.", "medium_description": "Three pairs of comedians are challenged to prove that they've got the mettle and skills to thrive in the great outdoors.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3526, "name": "<PERSON>: Unforgivable", "slug": "mel-giedroyc-unforgivable", "image": "https://uktv-res.cloudinary.com/image/upload/v1663690815/x6ecekr9j0hlkpfcddfp.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1681729960/djxk0rsnqiisflfo15dy.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1685604159/bmmewvywzv9ddb1oohwy.jpg", "hide_episode_title": true, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [4, 6], "available_episodes": 24, "description": "Contestants try to convince <PERSON> that they are the most unforgiveable person in the room.", "medium_description": "The nation's favourite comics and celebrities compete to convince <PERSON> and her assistant <PERSON> that they are the most unforgivable person in the room.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3879, "name": "We Are Not Alone", "slug": "we-are-not-alone", "image": "https://uktv-res.cloudinary.com/image/upload/v1665659293/mqffuafwrwudo8swmkxa.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531654/fgixuohvjhd41fjoiiup.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730922/oomzsvkjgf6fnm9ltyfu.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 3, "description": "A comedy that explores the culture clash between humankind and aliens who have just conquered Earth.", "medium_description": "A comedy, set six weeks after aliens have invaded and completely conquered Earth, that explores the culture clash between humankind and its new masters as they struggle with their new subjects.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 1223, "name": "<PERSON>: As Yet Untitled", "slug": "alan-davies-as-yet-untitled", "image": "https://uktv-res.cloudinary.com/image/upload/v1678788556/zfwydfi6xcaokiippqgy.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531506/myqkglcg8nhuiinpv0he.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730530/gc0z5tixkdl7ljuquttq.jpg", "hide_episode_title": false, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [4, 6], "available_episodes": 33, "description": "Comedy chat show in which <PERSON> and four comedians have an unscripted conversation.", "medium_description": "Comedy chat show in which <PERSON> joins four of his fellow comedians for an unscripted and agenda-free conversation.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3806, "name": "Sneakerhead", "slug": "sneakerhead", "image": "https://uktv-res.cloudinary.com/image/upload/v1660558572/hvrrzuvwqkt492jo0wjc.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531634/ecpyznzlrr9wtaf9s4qx.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730883/gvy4wqea89pchwvqb7ut.jpg", "hide_episode_title": true, "primary_subcategory": "Classic Comedy & Sitcom", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 3, "description": "A hilarious and contemporary workplace sitcom, set in Peterborough, that shines a light on young working life.", "medium_description": "A hilarious and contemporary workplace sitcom, set in Peterborough, that shines a light on young working life in Britain through <PERSON>, one of the many longsuffering employees of Sports Depot.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 2997, "name": "Hypothetical", "slug": "hypothetical", "image": "https://uktv-res.cloudinary.com/image/upload/v1654766737/zp9cexg4fl8amvijbxge.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1681729865/ycvvph7jq4ytk6ldthkl.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1685604736/tp8upu7wud8n4xbqb3zn.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 32, "description": "Comedy game show hosted by <PERSON> and <PERSON>.", "medium_description": "Comedy game show, hosted by <PERSON> and <PERSON>, in which leading comedians are posed with increasingly absurd hypothetical situations.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3401, "name": "Red Dwarf: The Promised Land", "slug": "red-dwarf-the-promised-land", "image": "https://uktv-res.cloudinary.com/image/upload/v1688722628/u9t9bfciccgtplxvvrpo.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531531/lujqb7d5ri2evqyd6phj.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730601/jyclg9sqwimcgpy3iwjs.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 2, "description": "<PERSON><PERSON> helps three cat clerics who worship him when their leader vows to wipe them out.", "medium_description": "The posse meet three cat clerics who worship <PERSON><PERSON>. <PERSON><PERSON> vows to help them as they're being hunted by <PERSON><PERSON>, the feral cat leader who wants to wipe out cats who worship anyone but him.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3677, "name": "Question Team", "slug": "question-team", "image": "https://uktv-res.cloudinary.com/image/upload/v1663017906/pnrvlilteyxtqz4jmhtm.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1686309395/yodw8lugnsmlfomh41ti.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730794/p1yxrypcqacwwdowfwln.jpg", "hide_episode_title": true, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 16, "description": "<PERSON> rewrites the panel show rulebook, making his guests set the questions.", "medium_description": "<PERSON> hosts the series that sets out to rewrite the panel show rulebook by making its players set the questions.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3878, "name": "Live At The Moth Club", "slug": "live-at-the-moth-club", "image": "https://uktv-res.cloudinary.com/image/upload/v1666264210/wejejouhp9l8nyoezyhm.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531653/rnugap4hcneoibyvd8kg.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730917/dnggem41jib90xhgicpb.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 75], "available_episodes": 5, "description": "Supreme stand-up and a cheeky glimpse behind the scenes at Hackney's iconic Moth Club.", "medium_description": "Supreme stand-up and a cheeky glimpse behind the scenes at Hackney's iconic Moth Club.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3249, "name": "Comedians Giving Lectures", "slug": "comedians-giving-lectures", "image": "https://uktv-res.cloudinary.com/image/upload/v1671179509/jo29qrkv1zvlhadzb8yf.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531502/am6p59egyqjpwvvt4jl7.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730508/ylh7q2jxpg2wxbutsaui.jpg", "hide_episode_title": true, "primary_subcategory": "Stand Up Comedy", "primary_category": "Comedy", "subcategories": [4, 75], "available_episodes": 20, "description": "Comedians are given the titles of real lectures and invited to give their own version.", "medium_description": "A selection of stand-up comedians are given the titles of real lectures and invited to give their own version of that lecture. Presented by <PERSON>.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3674, "name": "Late Night Mash", "slug": "late-night-mash", "image": "https://uktv-res.cloudinary.com/image/upload/v1667308653/icgkfaxd0wlwy3qahzpy.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1688469389/tbjavwuvsgbye8744md2.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730797/fqz3ppthyobkwiy9uxfj.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 17, "description": "The satirical topical comedy, hosted by <PERSON>, that makes sense of the world.", "medium_description": "The satirical topical comedy, hosted by <PERSON>, that makes sense of all the things that are happening in the world that probably shouldn't... and those that aren't that should be happening.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3820, "name": "<PERSON>'s Disability Comedy Extravaganza", "slug": "rosie-jones-disability-comedy-showcase", "image": "https://uktv-res.cloudinary.com/image/upload/v1692693982/lurbgtqwrple5ijnra9w.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1690024465/asub9hxsvvi9eatyc41s.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730892/ihjers1hcj5a2l4babxk.jpg", "hide_episode_title": true, "primary_subcategory": "Stand Up Comedy", "primary_category": "Comedy", "subcategories": [4, 75], "available_episodes": 5, "description": "<PERSON> hosts a celebration of disability and laughs, exclusively for UKTV Play.", "medium_description": "<PERSON> is joined by a host of fellow comedians for a celebration of disability and laughs, exclusively for UKTV Play.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3834, "name": "Dead Canny", "slug": "dead-canny", "image": "https://uktv-res.cloudinary.com/image/upload/v1689092297/wz5jxtf7nvylckcuqg4g.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875588/uhuopjjtnb2e1tqywmxi.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1685556962/rwommqwt8ofdnpxh0mdm.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 1, "description": "Comedy following a questionable young psychic whose ability to see dead people earns her money down the pub.", "medium_description": "Comedy about a questionable young psychic whose ability to see dead people earns her a few quid cash-in-hand down the local pub - unfortunately, it turns out people are just as annoying dead...", "channel": "dave", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3831, "name": "The Other Half", "slug": "the-other-half", "image": "https://uktv-res.cloudinary.com/image/upload/v1688479834/nup0qmlv3cmg1igjyazs.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692270803/ned0ar6cagbgc4ajtzak.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730899/cbqjp4j3zb9zuajiw6vc.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 1, "description": "<PERSON><PERSON> gets the chance to escape his small Welsh town, but he is running away from the secrets of his old life.", "medium_description": "<PERSON><PERSON> gets the chance to escape his small Welsh town when he lands an internship in Bristol, but as he runs headlong into his new life, he's also running away from the secrets of his old life.", "channel": "dave", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3832, "name": "Holier Than <PERSON>", "slug": "holier-than-thou", "image": "https://uktv-res.cloudinary.com/image/upload/v1696238189/ncnnhodot9yofrcnm2xf.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875585/k83isoaulc42a6mn88zc.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730899/cguaouv8t6ipvekbdv0s.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 1, "description": "Three young adults navigate sex, faith, and relationships at their conservative African evangelical church.", "medium_description": "Three young adults navigate sex, faith, and relationships while attending the conservative African evangelical church that they were raised in.", "channel": "dave", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3660, "name": "British as Folk", "slug": "british-as-folk", "image": "https://uktv-res.cloudinary.com/image/upload/v1659703448/pbpbd0va6yfkpojvizje.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1681729992/eyaueeusoxnnxbrw5fxj.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730778/aybxruaianjwke1bhmee.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 51], "available_episodes": 6, "description": "Three comics discover the stereotypes and traditions that make up British life.", "medium_description": "Three comics travel across the nation discovering and interrogating the stereotypes and traditions that make up British life today and create their very own brutally honest guide to the UK.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3101, "name": "Sliced", "slug": "sliced", "image": "https://uktv-res.cloudinary.com/image/upload/v1667308539/entk0k9daw0cvk7vc6ak.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531481/ket0pcpx6vuesglmptun.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730462/wbjdi14cdc48hqk88f6a.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 9, "description": "Comedy set amongst the hustle and bustle of a low-rent, backstreet pizza parlour.", "medium_description": "Comedy set amongst the hustle and bustle of a low-rent, backstreet pizza parlour, following the adventures of delivery riders <PERSON> (<PERSON>) and <PERSON> (<PERSON>).", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3833, "name": "Perfect", "slug": "perfect", "image": "https://uktv-res.cloudinary.com/image/upload/v1660648153/xxbgbuzy2gl47ygtiwic.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875587/tacc1s4rk6382ahk9t2e.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681902921/dov5j8nd06mftcxsu0z5.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 1, "description": "An irreverent comedy following three young wheelchair users on their quest for jobs and love.", "medium_description": "An irreverent comedy following three young wheelchair users on their quest for jobs and love.", "channel": "dave", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3309, "name": "<PERSON>: Terms and Conditions Apply", "slug": "dave-gorman-terms-and-conditions-apply", "image": "https://uktv-res.cloudinary.com/image/upload/v1655379486/bxgvdzxbzjxs58ltzgsq.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875493/ee6vrsfqdbuk9quhexbl.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730562/u6hpsqsnmwhtgcwxedfp.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 8, "description": "<PERSON> invites three comic minds to help him make sense of our insensible world.", "medium_description": "Using his trademark Powerpoint comedy and a series of mischievous games, <PERSON> invites three of the country's finest comic minds to help him make sense of our insensible world.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 1664, "name": "Taskmaster", "slug": "taskmaster", "image": "https://uktv-res.cloudinary.com/image/upload/v1650381272/llxtub9fyael9krxwjql.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531602/b7kqoypisi6ipkdrql1z.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730826/paqi6ifurjz3np99kaly.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 72, "description": "Award-winning comedy challenge show in which <PERSON> sets comedians tough tasks.", "medium_description": "The award-winning, offbeat comedy challenge show in which <PERSON> and <PERSON> set fellow comedians a series of tough tasks.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3377, "name": "Comedy Against Living Miserably", "slug": "comedy-against-living-miserably", "image": "https://uktv-res.cloudinary.com/image/upload/v1650462560/iufyhlhqlmyllpageece.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1691579514/mubl30cztaptgynqd3fp.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730584/vxgbbenwtshokvxrxj4u.jpg", "hide_episode_title": true, "primary_subcategory": "Stand Up Comedy", "primary_category": "Comedy", "subcategories": [4, 75], "available_episodes": 1, "description": "Stand-up comedy special, with <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>.", "medium_description": "Stand-up comedy special, hosted and headlined by <PERSON>, with support from <PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 2747, "name": "Judge <PERSON><PERSON>", "slug": "judge-romesh", "image": "https://uktv-res.cloudinary.com/image/upload/v1650450768/zjrwr8tlrilgepq4box0.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531450/m2b53kqxrh134m3qoq44.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730338/qmc2bumw2jxzbqxux971.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 22, "description": "Judge <PERSON><PERSON> presides over a batch of hilarious cases.", "medium_description": "Bailiff <PERSON> and Clerk <PERSON> assist Judge <PERSON><PERSON> as he presides over a batch of hilarious but real cases.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 2408, "name": "Porters", "slug": "porters", "image": "https://uktv-res.cloudinary.com/image/upload/v1679924755/ycmk8rajjmhac9wcnujy.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1686309201/wnnuhmtibdmwnryu9kk8.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730251/fri7w1zhnqv31c6yec2m.jpg", "hide_episode_title": true, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 9, "description": "Medical sitcom about the unseen world of hospitals.", "medium_description": "Medical sitcom about the unseen world of hospitals, following a wannabe doctor who starts work as a porter.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 819, "name": "<PERSON>: Modern Life is Goodish", "slug": "dave-gorman-modern-life-is-goodish", "image": "https://uktv-res.cloudinary.com/image/upload/v1678464353/fylmpq0vhc1sqibbwrb5.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875451/hx6musefykkczfhwwnab.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730375/pzisyku9hwd8v9sjvmoa.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 16, "description": "Comedian <PERSON> takes a sideways look at modern life in a series of stand-up shows.", "medium_description": "Award-winning comedian <PERSON> takes a sideways look at modern life in a series of stand-up performances powered by his laptop.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 3773, "name": "The Island", "slug": "the-island", "image": "https://uktv-res.cloudinary.com/image/upload/v1645441732/iohdatvqno5ld0ekynqg.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531618/xtiggqgvb5zlyxf194h9.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730842/koktanxx1kqxxywyakag.jpg", "hide_episode_title": true, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 8, "description": "<PERSON> hosts the comedy game show in which four comedians battle to create their own dream desert islands.", "medium_description": "<PERSON> hosts the comedy game show in which four comedians battle to make their own personal dream desert island the best and to attract the most passengers to their island.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3473, "name": "Red Dwarf: The First Three Million Years", "slug": "red-dwarf-the-first-three-million-years", "image": "https://uktv-res.cloudinary.com/image/upload/v1650465684/jqc8pu6mgb6truk6cf6u.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692875517/ghiuqt1unrlza8dfp5vb.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730649/rgo5m88swz5rb4btz2if.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4, 1], "available_episodes": 3, "description": "Three-part series charting the origins, production and legacy of everything associated with the sci-fi comedy.", "medium_description": "The definitive overview of the adventures of the legendary Boys from the Dwarf. This three-part series charts the origins, production and legacy of everything associated with the sci-fi comedy.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 2575, "name": "Taskmaster: Champion of Champions", "slug": "taskmaster-champion-of-champions", "image": "https://uktv-res.cloudinary.com/image/upload/v1650443917/tlkhlm2kmv2vv49ldyzt.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692789009/u44eqzd13ns7riaqvxbi.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730290/p6ln1hianmtbrtwjr9kz.jpg", "hide_episode_title": false, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 2, "description": "A two-part special of the celebrity challenge show hosted by <PERSON> and <PERSON>.", "medium_description": "A two-part special of the celebrity challenge show hosted by <PERSON> and <PERSON>. All five of the show's series champions return for some more confounding assignments.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 2661, "name": "<PERSON>: <PERSON>", "slug": "jon-rich<PERSON>on-ultimate-worrier", "image": "https://uktv-res.cloudinary.com/image/upload/v1650445327/bdwau2rzix5qdbaacjum.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1690314050/yp3cbe0tzxdkiwbpsakf.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730314/tewa5u1grvwkl8boxyha.jpg", "hide_episode_title": false, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 10, "description": "<PERSON> is joined by celebrity guests to discuss the issues that prey on his mind.", "medium_description": "Comedian <PERSON> is joined by celebrity guests to address, discuss and rank the issues that prey on his mind.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}, {"item_type": "brand", "id": 3432, "name": "Comedy Against Living Miserably 2", "slug": "comedy-against-living-miserably-2", "image": "https://uktv-res.cloudinary.com/image/upload/v1650464196/ciejwtcuiqdegfd5afwd.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1691579527/bymga4mkhzdsbv3dtvbp.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1685555464/ipjtlksnc3b65f5zyul7.jpg", "hide_episode_title": true, "primary_subcategory": "Stand Up Comedy", "primary_category": "Comedy", "subcategories": [4, 75], "available_episodes": 1, "description": "Stand-up comedy special, with <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>.", "medium_description": "Stand-up comedy special, hosted and headlined by <PERSON><PERSON>, with support from <PERSON><PERSON>, <PERSON> and <PERSON><PERSON>.", "channel": "dave", "has_subtitles": true, "is_feature": true, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 2131, "name": "<PERSON><PERSON>'s Go 8 Bit", "slug": "dara-o-briains-go-8-bit", "image": "https://uktv-res.cloudinary.com/image/upload/v1650383419/dlszycb8vzsgzcfa7oe0.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1692788982/ph0twoueqqfzsur1qgk4.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730148/rjuzimugtghaxhotypgy.jpg", "hide_episode_title": true, "primary_subcategory": "Panel Shows", "primary_category": "Comedy", "subcategories": [6, 4], "available_episodes": 20, "description": "Comedy game show presented by <PERSON><PERSON> in which celebrities play old video games.", "medium_description": "Comedy game show - based on the live Edinburgh stage show - presented by <PERSON><PERSON> in which celebrities play old video games.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": ""}, {"item_type": "brand", "id": 2156, "name": "Zapped", "slug": "zapped", "image": "https://uktv-res.cloudinary.com/image/upload/v1650383446/itvow9ri4jclslwd8b38.jpg", "portrait_image": "https://uktv-res.cloudinary.com/image/upload/v1685531379/hexqtdvzdvsgexzkaxdf.jpg", "hero_image_4k": "https://uktv-res.cloudinary.com/image/upload/v1681730156/is7o9aafhhdldpb3w8xu.jpg", "hide_episode_title": false, "primary_subcategory": "UK Comedy", "primary_category": "Comedy", "subcategories": [4], "available_episodes": 15, "description": "Fantasy comedy series in which a bored office worker is transported to a parallel world.", "medium_description": "Fantasy comedy series in which an online data marketing assistant is transported to a parallel world by a magic amulet.", "channel": "dave", "has_subtitles": true, "is_feature": false, "use_episode_grids": false, "sponsorlogos": [{"id": "1", "logo": "//s3-eu-west-1.amazonaws.com/uktv-adman/adman_assets/img/CITROEN_Bloc_Marque_2016_white_RGB.png", "sponsored_by_title": "Sponsors of Dave", "sponsor_url": "", "sponsor_type": "NORM", "name": "<PERSON> - <PERSON><PERSON><PERSON><PERSON>", "description": "2022 Sponsors of Dave", "sponsor_name": "Citroen", "transform_sizes": {"_1080p": {"width": 85, "height": 70, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_1080/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_720p": {"width": 57, "height": 47, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_720/v1676975406/gypjj4lzthljkl7cqlg6.png"}, "_540p": {"width": 42, "height": 35, "logourl": "https://uktv-res.cloudinary.com/image/upload/t_sponsor_logo_540/v1676975406/gypjj4lzthljkl7cqlg6.png"}}, "thirdlight_sponsor_logo_url": "https://uktv-res.cloudinary.com/image/upload/v1676975406/gypjj4lzthljkl7cqlg6.png"}], "synopsis_long": null}]}]}}