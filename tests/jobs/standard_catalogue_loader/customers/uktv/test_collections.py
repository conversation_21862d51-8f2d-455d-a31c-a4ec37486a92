import json
import os
from dataclasses import asdict
from unittest import TestCase

import freezegun

from tests.jobs.standard_catalogue_loader.customers.uktv.data.categories import \
    categories
from thefilter.jobs.standard_catalogue_loader.customers.uktv.static_types.collections \
    import Collections


class TestCollections(TestCase):

    @freezegun.freeze_time("2023-10-06T16:07:32+00:00")
    def test_convert_static_data(self):
        collections_source_file_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)), "data/collections_source.json")
        with open(collections_source_file_path) as json_file:
            source = json.load(json_file)

        converted = Collections(source).convert_static_data(categories=categories)

        expected = {
            "222_collection": {
                "id": "222",
                "typeName": "Collection",
                "space": "collection",
                "name": "The Stacey Dooley Collection",
                "alternateName": "stacey-dooley-collection",
                "description": "",
                "episodeNumber": None,
                "seasonNumber": None,
                "genre": [
                    {
                        "id": "3_category",
                        "name": "Entertainment"
                    },
                    {
                        "id": "7_category",
                        "name": "Real Life"
                    }
                ],
                "subGenre": [
                    {
                        "id": "51_subcategory",
                        "name": "Factual Entertainment"
                    },
                    {
                        "id": "80_subcategory",
                        "name": "Real Stories"
                    },
                    {
                        "id": "79_subcategory",
                        "name": "Family Life"
                    },
                    {
                        "id": "81_subcategory",
                        "name": "Emergency Services"
                    }
                ],
                "keywords": [],
                'datePublished': '2000-01-01T00:00:00+00:00',
                'duration': '',
                "contentRating": [],
                "publication": [],
                'isAdult': None,
                'isKids': None,
                'isLiveBroadcast': None,
                'numberOfSeasons': None,
                'partOfSeason': [],
                'partOfSeries': [],
                'productionCompany': None,
                'recommendationId': None,
                "inLanguage": "en-GB",
                "image": {
                    "url": "https://uktv-res.cloudinary.com/image/upload/v1676549336/tvwiufixuseqaoskhutt.jpg"
                },
                "custom": {
                    "category": [
                        {
                            "id": 3,
                            "name": "Entertainment"
                        },
                        {
                            "id": 7,
                            "name": "Real Life"
                        }
                    ],
                    "subCategory": [
                        {
                            "id": 51,
                            "name": "Factual Entertainment"
                        },
                        {
                            "id": 80,
                            "name": "Real Stories"
                        },
                        {
                            "id": 79,
                            "name": "Family Life"
                        },
                        {
                            "id": 81,
                            "name": "Emergency Services"
                        }
                    ],
                    "active": {
                        "state": True,
                        "stateUpdateTimestamp": "2023-10-06T16:07:32+00:00"
                    }
                },
                "actor": [],
                "director": [],
                "crew": [],
                "producer": [],
                "brandId": "222_collection",
                "brandName": "The Stacey Dooley Collection"
            },
            "83_collection": {
                "id": "83",
                "typeName": "Collection",
                "space": "collection",
                "name": "Dave Original Comedy",
                "alternateName": "original-comedy",
                "description": "",
                "episodeNumber": None,
                "seasonNumber": None,
                "genre": [
                    {
                        "id": "1_category",
                        "name": "Comedy"
                    },
                    {
                        "id": "3_category",
                        "name": "Entertainment"
                    }
                ],
                "subGenre": [
                    {
                        "id": "4_subcategory",
                        "name": "UK Comedy"
                    },
                    {
                        "id": "6_subcategory",
                        "name": "Panel Shows"
                    },
                    {
                        "id": "1_subcategory",
                        "name": "Classic Comedy & Sitcom"
                    },
                    {
                        "id": "57_subcategory",
                        "name": "Music"
                    },
                    {
                        "id": "51_subcategory",
                        "name": "Factual Entertainment"
                    },
                    {
                        "id": "75_subcategory",
                        "name": "Stand Up Comedy"
                    }
                ],
                "keywords": [],
                'datePublished': '2000-01-01T00:00:00+00:00',
                'duration': '',
                "contentRating": [],
                "publication": [],
                'isAdult': None,
                'isKids': None,
                'isLiveBroadcast': None,
                'numberOfSeasons': None,
                'partOfSeason': [],
                'partOfSeries': [],
                'productionCompany': None,
                'recommendationId': None,
                "inLanguage": "en-GB",
                "image": {
                    "url": "https://uktv-res.cloudinary.com/image/upload/v1664794984/oigmmoqx5865pbdxmpma.jpg"
                },
                "custom": {
                    "category": [
                        {
                            "id": 1,
                            "name": "Comedy"
                        },
                        {
                            "id": 3,
                            "name": "Entertainment"
                        }
                    ],
                    "subCategory": [
                        {
                            "id": 4,
                            "name": "UK Comedy"
                        },
                        {
                            "id": 6,
                            "name": "Panel Shows"
                        },
                        {
                            "id": 1,
                            "name": "Classic Comedy & Sitcom"
                        },
                        {
                            "id": 57,
                            "name": "Music"
                        },
                        {
                            "id": 51,
                            "name": "Factual Entertainment"
                        },
                        {
                            "id": 75,
                            "name": "Stand Up Comedy"
                        }
                    ],
                    "active": {
                        "state": True,
                        "stateUpdateTimestamp": "2023-10-06T16:07:32+00:00"
                    }
                },
                "actor": [],
                "director": [],
                "crew": [],
                "producer": [],
                "brandId": "83_collection",
                "brandName": "Dave Original Comedy"
            }
        }

        self.assertEqual(2, len(converted.keys()), "Converted count matches")
        self.assertEqual(expected["222_collection"],
                         asdict(converted["222_collection"]),
                         "Converted Collection 222 match")
        self.assertEqual(expected["83_collection"],
                         asdict(converted["83_collection"]),
                         "Converted Collection 83 match")
