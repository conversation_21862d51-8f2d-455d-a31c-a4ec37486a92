from dataclasses import asdict
from unittest import TestCase

import freezegun

from tests.jobs.standard_catalogue_loader.customers.uktv.data.categories import \
    categories
from thefilter.jobs.standard_catalogue_loader.customers.uktv.static_types.channels import \
    Channels


class TestChannels(TestCase):

    @freezegun.freeze_time("2023-10-06T16:07:32+00:00")
    def test_convert_static_data(self):
        source = {
            "channels": [
                {
                    "id": 3856,
                    "name": "<PERSON><PERSON>",
                    "slug": "alibi",
                    "description": "Exclusively dedicated to the best in crime drama.",
                    "hero_image": "https://uktv-res.cloudinary.com/image/upload/v1541416009/g0szqn7r60mryjt0qo79.jpg",
                    "registration_background": None,
                    "square_image": "https://uktv-res.cloudinary.com/image/upload/v1536659792/eqbadoh2sobrktu2nigw.jpg",
                    "carousel_image": "https://uktv-res.cloudinary.com/image/upload/v1536659792/zngv45siwtzezfbqagzi.jpg",
                    "has_vod": False,
                    "channel_platform_numbers": [
                        {"slug": "sky", "channel_number": 109},
                        {"slug": "virginmedia", "channel_number": 114}
                    ],
                    "subcategories": [7, 11, 35, 10, 56],
                    "cta_text": "",
                    "where_to_watch": "Alibi is available on <channel number>.",
                    "background_colour": "#4D0101",
                    "channel_logo": "https://uktv-res.cloudinary.com/image/upload/v1536845066/o1jvixu3xmahcm1tgqdi.jpg",
                    "png_logo": None,
                    "sub_navigation_manual_title": "",
                    "sub_navigation_hero_image": None,
                    "sub_navigation_synopsis": "",
                    "logo_homepage_hero_image": None,
                    "channel_rail_bground": None,
                    "brand_count": None
                },
                {
                    "id": 3854,
                    "name": "Dave",
                    "slug": "dave",
                    "description": "Award-winning entertainment and original comedy hits",
                    "hero_image": "https://uktv-res.cloudinary.com/image/upload/v1689180137/kqhhkp0mat3yly3ppah1.jpg",
                    "registration_background": "https://uktv-res.cloudinary.com/image/upload/v1689686131/aehoohbjnbrult0uidol.jpg",
                    "square_image": "https://uktv-res.cloudinary.com/image/upload/v1536659834/dx4y9h0zysxixykbhixr.jpg",
                    "carousel_image": "https://uktv-res.cloudinary.com/image/upload/v1536659834/qnrby5tvejkaq5zbxkbm.jpg",
                    "has_vod": True,
                    "channel_platform_numbers": [
                        {"slug": "freeview", "channel_number": 19},
                        {"slug": "sky", "channel_number": 111},
                        {"slug": "virginmedia", "channel_number": 127},
                        {"slug": "youview", "channel_number": 19},
                        {"slug": "freesat", "channel_number": 157},
                        {"slug": "bt", "channel_number": 19},
                        {"slug": "talktalk", "channel_number": 19}
                    ],
                    "subcategories": [4, 6, 51, 14, 72, 1, 81, 11, 74, 75],
                    "cta_text": "",
                    "where_to_watch": "",
                    "background_colour": "#0A1721",
                    "channel_logo": "https://uktv-res.cloudinary.com/image/upload/v1536844980/h5gp1oyq0id3llfmwvvu.jpg",
                    "png_logo": "https://uktv-res.cloudinary.com/image/upload/v1650882172/zv02y2ldav3yhejpfcyo.png",
                    "sub_navigation_manual_title": "David Mitchell's Outsiders",
                    "sub_navigation_hero_image": "https://uktv-res.cloudinary.com/image/upload/v1695113728/k4ztrbgedjbp6pc1y7fk.jpg",
                    "sub_navigation_synopsis": "Series 3 Coming Soon",
                    "logo_homepage_hero_image": None,
                    "channel_rail_bground": None,
                    "brand_count": 166,
                    "hero_collection_id": 92
                },
                {
                    "id": 4646,
                    "name": "Drama",
                    "slug": "drama",
                    "description": "A treasure trove of some of the greatest stories ever told",
                    "hero_image": "https://uktv-res.cloudinary.com/image/upload/v1689180081/a6uttppdmnbeyqlsrdhi.jpg",
                    "registration_background": "https://uktv-res.cloudinary.com/image/upload/v1689686079/iqytdlxlphwaefk36yar.jpg",
                    "square_image": "https://uktv-res.cloudinary.com/image/upload/v1536659514/ndihagw9pqjf5pndggzt.jpg",
                    "carousel_image": "https://uktv-res.cloudinary.com/image/upload/v1539084061/zktgq1udwkzmehwvboje.jpg",
                    "has_vod": True,
                    "channel_platform_numbers": [
                        {"slug": "freeview", "channel_number": 20},
                        {"slug": "sky", "channel_number": 143},
                        {"slug": "virginmedia", "channel_number": 130},
                        {"slug": "youview", "channel_number": 20},
                        {"slug": "freesat", "channel_number": 158},
                        {"slug": "bt", "channel_number": 20},
                        {"slug": "talktalk", "channel_number": 20}
                    ],
                    "subcategories": [35, 7, 10, 74, 11, 1, 4, 56, 82],
                    "cta_text": "Watch great drama now",
                    "where_to_watch": "",
                    "background_colour": "#3C1565",
                    "channel_logo": "https://uktv-res.cloudinary.com/image/upload/v1536845011/joohealqrtjedggbbt6n.jpg",
                    "png_logo": "https://uktv-res.cloudinary.com/image/upload/v1650882115/fmdbt3occpskdaoelpqc.png",
                    "sub_navigation_manual_title": "The Brokenwood Mysteries",
                    "sub_navigation_hero_image": "https://uktv-res.cloudinary.com/image/upload/v1689180081/a6uttppdmnbeyqlsrdhi.jpg",
                    "sub_navigation_synopsis": "Crime Drama &#x2022; 9 Series",
                    "logo_homepage_hero_image": None,
                    "channel_rail_bground": None,
                    "brand_count": 136,
                    "hero_collection_id": 91
                }
            ]
        }
        converted = Channels(source).convert_static_data(categories=categories)

        expected = {
            '3854_broadcastchannel': {
                "id": "3854",
                "typeName": "BroadcastChannel",
                "space": "broadcastchannel",
                "name": "Dave",
                "alternateName": "dave",
                "description": "Award-winning entertainment and original comedy hits",
                "episodeNumber": None,
                "seasonNumber": None,
                "genre": [
                    {
                        "id": "1_category",
                        "name": "Comedy"
                    },
                    {
                        "id": "3_category",
                        "name": "Entertainment"
                    },
                    {
                        "id": "6_category",
                        "name": "Lifestyle"
                    },
                    {
                        "id": "7_category",
                        "name": "Real Life"
                    },
                    {
                        "id": "2_category",
                        "name": "Drama"
                    }
                ],
                "subGenre": [
                    {
                        "id": "4_subcategory",
                        "name": "UK Comedy"
                    },
                    {
                        "id": "6_subcategory",
                        "name": "Panel Shows"
                    },
                    {
                        "id": "51_subcategory",
                        "name": "Factual Entertainment"
                    },
                    {
                        "id": "14_subcategory",
                        "name": "Motoring"
                    },
                    {
                        "id": "72_subcategory",
                        "name": "Food"
                    },
                    {
                        "id": "1_subcategory",
                        "name": "Classic Comedy & Sitcom"
                    },
                    {
                        "id": "81_subcategory",
                        "name": "Emergency Services"
                    },
                    {
                        "id": "11_subcategory",
                        "name": "Period Drama"
                    },
                    {
                        "id": "74_subcategory",
                        "name": "International Drama"
                    },
                    {
                        "id": "75_subcategory",
                        "name": "Stand Up Comedy"
                    }
                ],
                "keywords": [],
                "datePublished": '2000-01-01T00:00:00+00:00',
                "duration": "",
                "contentRating": [],
                "publication": [],
                'isAdult': None,
                'isKids': None,
                'isLiveBroadcast': None,
                'numberOfSeasons': None,
                'partOfSeason': [],
                'partOfSeries': [],
                'productionCompany': None,
                'recommendationId': None,
                "inLanguage": "en-GB",
                "image": {
                    "url": "https://uktv-res.cloudinary.com/image/upload/v1536844980/h5gp1oyq0id3llfmwvvu.jpg"
                },
                "custom": {
                    "hasVod": True,
                    "freeToAir": True,
                    "category": [
                        {
                            "id": 1,
                            "name": "Comedy"
                        },
                        {
                            "id": 3,
                            "name": "Entertainment"
                        },
                        {
                            "id": 6,
                            "name": "Lifestyle"
                        },
                        {
                            "id": 7,
                            "name": "Real Life"
                        },
                        {
                            "id": 2,
                            "name": "Drama"
                        }
                    ],
                    "subCategory": [
                        {
                            "id": 4,
                            "name": "UK Comedy"
                        },
                        {
                            "id": 6,
                            "name": "Panel Shows"
                        },
                        {
                            "id": 51,
                            "name": "Factual Entertainment"
                        },
                        {
                            "id": 14,
                            "name": "Motoring"
                        },
                        {
                            "id": 72,
                            "name": "Food"
                        },
                        {
                            "id": 1,
                            "name": "Classic Comedy & Sitcom"
                        },
                        {
                            "id": 81,
                            "name": "Emergency Services"
                        },
                        {
                            "id": 11,
                            "name": "Period Drama"
                        },
                        {
                            "id": 74,
                            "name": "International Drama"
                        },
                        {
                            "id": 75,
                            "name": "Stand Up Comedy"
                        }
                    ],
                    "active": {
                        "state": True,
                        "stateUpdateTimestamp": "2023-10-06T16:07:32+00:00"
                    }
                },
                "actor": [],
                "director": [],
                "crew": [],
                "producer": [],
                "brandId": "3854_broadcastchannel",
                "brandName": "Dave"
            },
            '3856_broadcastchannel': {
                "id": "3856",
                "typeName": "BroadcastChannel",
                "space": "broadcastchannel",
                "name": "Alibi",
                "alternateName": "alibi",
                "description": "Exclusively dedicated to the best in crime drama.",
                "episodeNumber": None,
                "seasonNumber": None,
                "genre": [
                    {
                        "id": "2_category",
                        "name": "Drama"
                    },
                    {
                        "id": "3_category",
                        "name": "Entertainment"
                    }
                ],
                "subGenre": [
                    {
                        "id": "7_subcategory",
                        "name": "Crime Drama"
                    },
                    {
                        "id": "11_subcategory",
                        "name": "Period Drama"
                    },
                    {
                        "id": "35_subcategory",
                        "name": "UK Drama"
                    },
                    {
                        "id": "10_subcategory",
                        "name": "Medical Drama"
                    },
                    {
                        "id": "56_subcategory",
                        "name": "Movies"
                    }
                ],
                "keywords": [],
                "datePublished": '2000-01-01T00:00:00+00:00',
                "duration": "",
                "contentRating": [],
                "publication": [],
                "inLanguage": "en-GB",
                'isAdult': None,
                'isKids': None,
                'isLiveBroadcast': None,
                'numberOfSeasons': None,
                'partOfSeason': [],
                'partOfSeries': [],
                'productionCompany': None,
                'recommendationId': None,
                "image": {
                    "url": "https://uktv-res.cloudinary.com/image/upload/v1536845066/o1jvixu3xmahcm1tgqdi.jpg"
                },
                "custom": {
                    "hasVod": False,
                    "freeToAir": False,
                    "category": [
                        {
                            "id": 2,
                            "name": "Drama"
                        },
                        {
                            "id": 3,
                            "name": "Entertainment"
                        }
                    ],
                    "subCategory": [
                        {
                            "id": 7,
                            "name": "Crime Drama"
                        },
                        {
                            "id": 11,
                            "name": "Period Drama"
                        },
                        {
                            "id": 35,
                            "name": "UK Drama"
                        },
                        {
                            "id": 10,
                            "name": "Medical Drama"
                        },
                        {
                            "id": 56,
                            "name": "Movies"
                        }
                    ],
                    "active": {
                        "state": True,
                        "stateUpdateTimestamp": "2023-10-06T16:07:32+00:00"
                    }
                },
                "actor": [],
                "director": [],
                "crew": [],
                "producer": [],
                "brandId": "3856_broadcastchannel",
                "brandName": "Alibi"
            },
            '4646_broadcastchannel': {
                "id": "4646",
                "typeName": "BroadcastChannel",
                "space": "broadcastchannel",
                "name": "Drama",
                "alternateName": "drama",
                "description": "A treasure trove of some of the greatest stories ever told",
                "episodeNumber": None,
                "seasonNumber": None,
                "genre": [
                    {
                        "id": "2_category",
                        "name": "Drama"
                    },
                    {
                        "id": "1_category",
                        "name": "Comedy"
                    },
                    {
                        "id": "3_category",
                        "name": "Entertainment"
                    },
                    {
                        "id": "7_category",
                        "name": "Real Life"
                    }
                ],
                "subGenre": [
                    {
                        "id": "35_subcategory",
                        "name": "UK Drama"
                    },
                    {
                        "id": "7_subcategory",
                        "name": "Crime Drama"
                    },
                    {
                        "id": "10_subcategory",
                        "name": "Medical Drama"
                    },
                    {
                        "id": "74_subcategory",
                        "name": "International Drama"
                    },
                    {
                        "id": "11_subcategory",
                        "name": "Period Drama"
                    },
                    {
                        "id": "1_subcategory",
                        "name": "Classic Comedy & Sitcom"
                    },
                    {
                        "id": "4_subcategory",
                        "name": "UK Comedy"
                    },
                    {
                        "id": "56_subcategory",
                        "name": "Movies"
                    },
                    {
                        "id": "82_subcategory",
                        "name": "True Crime"
                    }
                ],
                "keywords": [],
                'datePublished': '2000-01-01T00:00:00+00:00',
                "duration": "",
                "contentRating": [],
                "publication": [],
                "inLanguage": "en-GB",
                'isAdult': None,
                'isKids': None,
                'isLiveBroadcast': None,
                'numberOfSeasons': None,
                'partOfSeason': [],
                'partOfSeries': [],
                'productionCompany': None,
                'recommendationId': None,
                "image": {
                    "url": "https://uktv-res.cloudinary.com/image/upload/v1536845011/joohealqrtjedggbbt6n.jpg"
                },
                "custom": {
                    "hasVod": True,
                    "freeToAir": True,
                    "category": [
                        {
                            "id": 2,
                            "name": "Drama"
                        },
                        {
                            "id": 1,
                            "name": "Comedy"
                        },
                        {
                            "id": 3,
                            "name": "Entertainment"
                        },
                        {
                            "id": 7,
                            "name": "Real Life"
                        }
                    ],
                    "subCategory": [
                        {
                            "id": 35,
                            "name": "UK Drama"
                        },
                        {
                            "id": 7,
                            "name": "Crime Drama"
                        },
                        {
                            "id": 10,
                            "name": "Medical Drama"
                        },
                        {
                            "id": 74,
                            "name": "International Drama"
                        },
                        {
                            "id": 11,
                            "name": "Period Drama"
                        },
                        {
                            "id": 1,
                            "name": "Classic Comedy & Sitcom"
                        },
                        {
                            "id": 4,
                            "name": "UK Comedy"
                        },
                        {
                            "id": 56,
                            "name": "Movies"
                        },
                        {
                            "id": 82,
                            "name": "True Crime"
                        }
                    ],
                    "active": {
                        "state": True,
                        "stateUpdateTimestamp": "2023-10-06T16:07:32+00:00"
                    }
                },
                "actor": [],
                "director": [],
                "crew": [],
                "producer": [],
                "brandId": "4646_broadcastchannel",
                "brandName": "Drama"
            },
        }

        self.assertEqual(3, len(converted), "Converted count matches")
        self.assertEqual(expected["3854_broadcastchannel"],
                         asdict(converted["3854_broadcastchannel"]),
                         "Converted Channel 3854 match")
        self.assertEqual(expected["3856_broadcastchannel"],
                         asdict(converted["3856_broadcastchannel"]),
                         "Converted Channel 3856 match")
        self.assertEqual(expected["4646_broadcastchannel"],
                         asdict(converted["4646_broadcastchannel"]),
                         "Converted Channel 4646 match")
