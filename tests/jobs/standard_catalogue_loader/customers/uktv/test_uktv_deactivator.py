import unittest
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock

# from thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator import Brand
# from thefilter.jobs.uktv_catalogue.model.channel import Channel
from thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator import \
    ElasticSearchItemDeactivator
from thefilter.model.schemaorg import Thing


class DeactivatorTests(unittest.TestCase):
    now = datetime(2019, 9, 4, 9, 28, 23, tzinfo=timezone.utc)

    @patch(
        'thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator.ElasticSearchItemDeactivator._obtain_deactivation_percentage')
    def test_deactivate_raises(self, mock_deactivation_percentage):
        mock_deactivation_percentage.return_value = 90
        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)

        with self.assertRaises(Exception) as e:
            deactivator.deactivate(items=[], type_name="Test", space="Test")

        self.assertTrue(
            "Deactivation of Tests would deactivate 90% of items of this type!"
            in str(e.exception))

    def test_construct_cleanup_query(self):
        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)
        query_obj = deactivator._construct_cleanup_query(['100_brand', '200_brand'],
                                                         "Brand")

        expected_query_obj = {
            'query': {
                'bool': {
                    'must_not': [
                        {
                            'ids': {
                                'values': [
                                    '100_brand',
                                    '200_brand'
                                ]
                            }
                        }
                    ],
                    'must': [
                        {
                            'term': {
                                'thing.custom.active.state': True
                            }
                        },
                        {
                            'term': {
                                'thing.typeName.lowercase_keyword': 'brand'
                            }
                        }
                    ]
                }
            },
            'script': {
                'source': '\n'
                    '            '
                    'ctx._source.thing.custom.active.stateUpdateTimestamp = params.now;\n'
                    '            '
                    'ctx._source.thing.custom.active.state = '
                    'false\n'
                    '            ',
                'lang': 'painless',
                'params': {
                    'now': self.now
                }
            }
        }
        self.maxDiff = None
        self.assertDictEqual(expected_query_obj, query_obj.to_dict())

    def test_extract_episode_ids(self):
        items = ElasticSearchItemDeactivator._extract_processed_item_ids(
            items=[Thing(id=100), Thing(id=200)],
            type_name="Episode", space="Episode")

        expected = ['100', '200']
        self.assertEqual(expected, items)

    def test_extract_brand_ids(self):
        items = ElasticSearchItemDeactivator._extract_processed_item_ids(
            items=[Thing(id=100, typeName="Brand", space="brand"),
                   Thing(id='200a_brand', typeName="Brand", space="brand")],
            type_name="Brand", space="brand")
        expected = ['100_brand', '200a_brand']
        self.assertEqual(expected, items)

    def test_extract_ids_space_different_from_typename(self):
        items = ElasticSearchItemDeactivator._extract_processed_item_ids(
            items=[Thing(id=100, typeName="BroadcastChannel", space="broadcastchannel"),
                   Thing(id='200', typeName="BroadcastChannel",
                         space="broadcastchannel")],
            type_name="BroadcastChannel", space="broadcastchannel")
        expected = ['100_broadcastchannel', '200_broadcastchannel']
        self.assertEqual(expected, items)

    @patch(
        'thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator.ElasticSearchItemDeactivator._obtain_typeName_total')
    @patch('elasticsearch_dsl.search.Search.execute')
    def test_obtain_deactivation_percentage(self, mock_search_execute,
                                            mock_obtain_typeName_total):
        mock_response_obj = MagicMock()
        mock_response_obj.to_dict.return_value = {'hits': {'total': 3}}

        mock_search_execute.return_value = mock_response_obj
        mock_obtain_typeName_total.return_value = 4

        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)

        percentage = deactivator._obtain_deactivation_percentage(['100'], "Test")
        expected_percentage = 75.0
        self.assertEqual(expected_percentage, percentage)

    @patch(
        'thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator.ElasticSearchItemDeactivator._obtain_typeName_total')
    @patch('elasticsearch_dsl.search.Search.execute')
    def test_obtain_deactivation_percentage_raises(self, mock_search_execute,
                                                   mock_obtain_typeName_total):
        mock_response_obj = MagicMock()
        mock_response_obj.to_dict.return_value = {'hits': {'total': 3}}

        mock_search_execute.return_value = mock_response_obj
        mock_obtain_typeName_total.return_value = 0

        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)

        with self.assertRaises(Exception) as e:
            deactivator._obtain_deactivation_percentage(['100'], "Test")

        self.assertIn(
            "For items with typeName Test, deactivation percentage could not be retrieved",
            str(e.exception))

    @patch(
        'thefilter.jobs.standard_catalogue_loader.customers.uktv.uktv_deactivator.ElasticSearchItemDeactivator._obtain_typeName_total')
    @patch('elasticsearch_dsl.search.Search.execute')
    def test_obtain_deactivation_percentage_zero(self, mock_search_execute,
                                                 mock_obtain_typeName_total):
        mock_response_obj = MagicMock()
        mock_response_obj.to_dict.return_value = {'hits': {'total': 0}}

        mock_search_execute.return_value = mock_response_obj
        mock_obtain_typeName_total.return_value = 0

        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)

        percentage = deactivator._obtain_deactivation_percentage(['100'], "Test")
        expected_percentage = 0
        self.assertEqual(expected_percentage, percentage)

    @patch('elasticsearch_dsl.search.Search.execute')
    def test_obtain_typeName_total(self, mock_search_execute):
        mock_response_obj = MagicMock()
        mock_response_obj.to_dict.return_value = {'hits': {'total': 7}}

        mock_search_execute.return_value = mock_response_obj

        deactivator = ElasticSearchItemDeactivator('endpoint', 'index', self.now)

        total = deactivator._obtain_typeName_total("Test")
        self.assertEqual(7, total)
