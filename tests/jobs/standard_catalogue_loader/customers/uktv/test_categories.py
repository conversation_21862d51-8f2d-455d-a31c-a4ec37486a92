from dataclasses import asdict
from unittest import TestCase

import freezegun

from thefilter.jobs.standard_catalogue_loader.customers.uktv.static_types.categories \
    import Categories

expected_category = {
    "id": "7",
    "brandId": "7_category",
    "brandName": "Real Life",
    "recommendationId": None,
    "name": "Real Life",
    "alternateName": "real-life",
    "typeName": "Category",
    "genre": [{'id': '7_category', 'name': 'Real Life'}],
    "subGenre": [
        {"id": "78_subcategory", "name": "Dating & Relationships"},
        {"id": "79_subcategory", "name": "Family Life"},
        {"id": "80_subcategory", "name": "Real Stories"},
        {"id": "81_subcategory", "name": "Emergency Services"},
        {"id": "82_subcategory", "name": "True Crime"}
    ],
    "contentRating": [],
    "actor": [],
    "director": [],
    "producer": [],
    "crew": [],
    "partOfSeason": [],
    "partOfSeries": [],
    "publication": [],
    "keywords": [],
    "episodeNumber": None,
    "seasonNumber": None,
    "numberOfSeasons": None,
    "description": "",
    'datePublished': '2000-01-01T00:00:00+00:00',
    'duration': '',
    "image": {
        "url": "https://uktv-res.cloudinary.com/image/upload/v1695113378/ry2e94hsanvdcfegyxpb.jpg"
    },
    "inLanguage": "en-GB",
    "isLiveBroadcast": None,
    "productionCompany": None,
    "custom": {
        "subCategory": [
            {
                "name": "Dating & Relationships",
                "id": 78
            },
            {
                "name": "Family Life",
                "id": 79
            },
            {
                "name": "Real Stories",
                "id": 80
            },
            {
                "name": "Emergency Services",
                "id": 81
            },
            {
                "name": "True Crime",
                "id": 82
            }
        ],
        'category': [{'id': 7, 'name': 'Real Life'}],
        'channel': [{'id': 4665, 'name': 'Watch'},
                    {'id': 3854, 'name': 'Dave'},
                    {'id': 3866, 'name': 'Yesterday'},
                    {'id': 4646, 'name': 'Drama'}],
        "active": {
            "state": True,
            "stateUpdateTimestamp": "2023-10-05T14:05:25+00:00"
        }
    },
    "space": "category",
    "isAdult": None,
    "isKids": None
}

expected_sub_category = {
    'actor': [],
    'alternateName': 'true-crime',
    'brandId': '7_category',
    'brandName': 'Real Life',
    'contentRating': [],
    'crew': [],
    'custom': {'active': {'state': True,
                          'stateUpdateTimestamp': '2023-10-05T14:05:25+00:00'},
               'brandCount': 2,
               'category': [{'id': 7, 'name': 'Real Life'}],
               'channel': [{'id': 4665, 'name': 'Watch'},
                           {'id': 3866, 'name': 'Yesterday'},
                           {'id': 4646, 'name': 'Drama'}],
               'slug': 'true-crime'},
    'datePublished': '2000-01-01T00:00:00+00:00',
    'director': [],
    'duration': '',
    'description': '',
    'episodeNumber': None,
    'genre': [{'id': "7_category", 'name': 'Real Life'}],
    'id': '82',
    'image': None,
    'inLanguage': 'en-GB',
    'isAdult': None,
    'isKids': None,
    'isLiveBroadcast': None,
    'keywords': [],
    'name': 'True Crime',
    'numberOfSeasons': None,
    'partOfSeason': [],
    'partOfSeries': [],
    'producer': [],
    'productionCompany': None,
    'publication': [],
    'recommendationId': None,
    'seasonNumber': None,
    'space': 'subcategory',
    'subGenre': [],
    'typeName': 'SubCategory'
}


class TestCategories(TestCase):
    @freezegun.freeze_time("2023-10-05T14:05:25+00:00")
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        category_static_data = {
            "categories": {
                "categories": [
                    {
                        "brand_count": 0,
                        "hero_image": "https://uktv-res.cloudinary.com/image/upload/v1695113378/ry2e94hsanvdcfegyxpb.jpg",
                        "icon_image": None,
                        "id": 7,
                        "name": "Real Life",
                        "nav_hero_title": "Gemma & Gorka: Life Behind the Lens",
                        "slug": "real-life",
                        "subcategories":
                            [
                                {
                                    "brand_count": 11,
                                    "channels":
                                        [
                                            {
                                                "4665": "Watch"
                                            }
                                        ],
                                    "id": 78,
                                    "name": "Dating & Relationships",
                                    "slug": "dating-relationships"
                                },
                                {
                                    "brand_count": 26,
                                    "channels":
                                        [
                                            {
                                                "4665": "Watch"
                                            }
                                        ],
                                    "id": 79,
                                    "name": "Family Life",
                                    "slug": "family-life"
                                },
                                {
                                    "brand_count": 35,
                                    "channels":
                                        [
                                            {
                                                "4665": "Watch"
                                            }
                                        ],
                                    "id": 80,
                                    "name": "Real Stories",
                                    "slug": "real-stories"
                                },
                                {
                                    "brand_count": 18,
                                    "channels":
                                        [
                                            {
                                                "4665": "Watch"
                                            },
                                            {
                                                "3854": "Dave"
                                            }
                                        ],
                                    "id": 81,
                                    "name": "Emergency Services",
                                    "slug": "emergency-services"
                                },
                                {
                                    "brand_count": 2,
                                    "channels":
                                        [
                                            {
                                                "4665": "Watch"
                                            },
                                            {
                                                "3866": "Yesterday"
                                            },
                                            {
                                                "4646": "Drama"
                                            }
                                        ],
                                    "id": 82,
                                    "name": "True Crime",
                                    "slug": "true-crime"
                                }
                            ],
                        "synopsis": "UKTV Original &#x2022; 1 Series"
                    }
                ]
            }
        }
        self._categories_api = Categories(static_data=category_static_data)

    def test_convert_categories(self):
        converted = self._categories_api.convert_static_data()

        expected_converted_keys = [
            "78_subcategory",
            "79_subcategory",
            "80_subcategory",
            "81_subcategory",
            "82_subcategory",
            "7_category"
        ]
        self.assertEqual(expected_converted_keys, list(converted.keys()))

        self.assertEqual(expected_category, asdict(converted["7_category"]))

        self.assertEqual(expected_sub_category, asdict(converted["82_subcategory"]))
