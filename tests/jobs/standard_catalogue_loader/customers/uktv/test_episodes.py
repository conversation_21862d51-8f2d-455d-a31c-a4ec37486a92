from dataclasses import asdict
from unittest import TestCase

import freezegun

from tests.jobs.standard_catalogue_loader.core_loader.data.standard_cat_loader_config_generator import \
    generate_default_catalogue_loader_config
from tests.jobs.standard_catalogue_loader.customers.uktv.data.categories import \
    categories
from tests.jobs.standard_catalogue_loader.customers.uktv.data.channels import channels
from tests.jobs.standard_catalogue_loader.customers.uktv.data.platforms import platforms
from thefilter.jobs.standard_catalogue_loader.customers.uktv.dynamic_types.episodes import \
    Episodes

test_episodes = [
    {
        "house_num": "CTON291E",
        "title": "<PERSON> and <PERSON>",
        "availability_start": "2017-02-01T00:00:00",
        "availability_end": "2099-12-31T23:59:00",
        "brand_id": 2131,
        "brand_name": "<PERSON><PERSON>'s Go 8 Bit",
        "series_id": 15147,
        "series_txt": "1",
        "episode_txt": 1,
        "updated": "2022-07-05T01:01:00",
        "guidance_age": "PG",
        "guidance_text": "",
        "teaser_text": "<PERSON><PERSON> hosts a gloriously geeky game show in which celebrities duel on video games. There are lots of laughs as <PERSON> and <PERSON> play <PERSON><PERSON> and <PERSON>tris.",
        "channel": "dave",
        "actors": "",
        "keywords": [
            "computers",
            "comedy",
            "Game/Quiz show"
        ],
        "subcategories": [
            6,
            4
        ],
        "episode_image": "https://uktv-res.cloudinary.com/image/upload/v1501063909/ty1xafwqfnxnbbxfgewj.jpg",
        "duration": 2711
    },
    {
        "house_num": "CTON404T",
        "title": "Mr Charisma",
        "availability_start": "2017-02-01T00:00:00",
        "availability_end": "2022-10-03T23:59:00",
        "brand_id": 2156,
        "brand_name": "Zapped",
        "series_id": 15264,
        "series_txt": "1",
        "episode_txt": 2,
        "updated": "2022-07-05T01:01:00",
        "guidance_age": "15",
        "guidance_text": "Contains Strong Language",
        "teaser_text": "James Buckley stars in the comedy so fantastical, even his Inbetweeners alter-ego Jay couldn't dream it up. A charisma potion may help Brian retrieve the amulet.",
        "channel": "dave",
        "actors": "James Buckley,Paul Kaye,Sharon Rooney,Kenneth Collard,Louis Emerick",
        "keywords": [
            "sitcom",
            "fantasy"
        ],
        "subcategories": [
            4
        ],
        "episode_image": "https://uktv-res.cloudinary.com/image/upload/v1476977294/riynhapgc53blgsomheu.jpg",
        "duration": 1742
    },
    {
        "house_num": "CTON403A",
        "title": "Mr Weaver",
        "availability_start": "2017-02-01T00:00:00",
        "availability_end": "2022-10-03T23:59:00",
        "brand_id": 2156,
        "brand_name": "Zapped",
        "series_id": 15264,
        "series_txt": "1",
        "episode_txt": 1,
        "updated": "2022-07-05T01:01:00",
        "guidance_age": "15",
        "guidance_text": "Contains Strong Language",
        "teaser_text": "The Inbetweeners' James Buckley stars in this original fantasy-comedy. A bored desk worker is transported to a parallel world of wizards and warriors by a magic amulet.",
        "channel": "dave",
        "actors": "James Buckley,Paul Kaye,Sharon Rooney,Kenneth Collard,Louis Emerick",
        "keywords": [
            "sitcom",
            "fantasy"
        ],
        "subcategories": [
            4
        ],
        "episode_image": "https://uktv-res.cloudinary.com/image/upload/v1475667120/xs89skopm4agrqywmlu5.jpg",
        "duration": 1809
    }
]


class TestEpisodes(TestCase):
    @freezegun.freeze_time("2023-10-05T14:05:25+00:00")
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        customer = 'uktv'
        environment = "pre"

        config = generate_default_catalogue_loader_config(
            customer=customer, environment=environment
        )

        self._episode_api = Episodes(
            config=config,
            channels=channels,
            platform_list=platforms,
            categories=categories
        )

    def test_episodes(self):
        data = test_episodes
        platform = "web"
        channel = channels["3854_broadcastchannel"]
        self._episode_api._create_episodes(data, platform, channel)

        expected_episode_ids = ['CTON291E', 'CTON404T', 'CTON403A']
        self.assertEqual(expected_episode_ids, list(self._episode_api._episodes.keys()))

        expected_episode_1 = {
            'actor': [{'name': 'James Buckley'},
                      {'name': 'Paul Kaye'},
                      {'name': 'Sharon Rooney'},
                      {'name': 'Kenneth Collard'},
                      {'name': 'Louis Emerick'}],
            'alternateName': '',
            'brandId': '2156_brand',
            'brandName': 'Zapped',
            'contentRating': [{'name': '15'}],
            'crew': [],
            'custom': {'brand': [{'id': 2156, 'name': 'Zapped'}],
                       'category': [{'id': 1, 'name': 'Comedy'}],
                       'channel': [{'id': 3854, 'name': 'dave'}],
                       'durationMinutes': 29,
                       'subCategory': [{'id': 4, 'name': 'UK Comedy'}],
                       'subGenre': []},
            'datePublished': '2000-01-01T00:00:00+00:00',
            'description': 'James Buckley stars in the comedy so fantastical, even his '
                           "Inbetweeners alter-ego Jay couldn't dream it up. A charisma "
                           'potion may help Brian retrieve the amulet.',
            'director': [],
            'duration': 'PT29M2S',
            'episodeNumber': 2,
            'genre': [{'id': '1_category', 'name': 'Comedy'}],
            'id': 'CTON404T',
            'image': {
                'url': 'https://uktv-res.cloudinary.com/image/upload/v1476977294/riynhapgc53blgsomheu.jpg'},
            'inLanguage': 'en-GB',
            'isAdult': None,
            'isKids': None,
            'isLiveBroadcast': None,
            'keywords': ['sitcom', 'fantasy'],
            'name': 'Mr Charisma',
            'numberOfSeasons': None,
            'partOfSeason': [{'id': 15264, 'name': '1'}],
            'partOfSeries': [{'id': 2156, 'name': 'Zapped'}],
            'producer': [],
            'productionCompany': None,
            'publication': [{'endDate': '2022-10-03T23:59:00+00:00',
                             'location': 'web',
                             'notes': 'vod',
                             'publishedOn': {'id': 3854, 'name': 'dave'},
                             'startDate': '2017-02-01T00:00:00+00:00'}],
            'recommendationId': None,
            'seasonNumber': 1,
            'space': None,
            'subGenre': [{'id': '4_subcategory', 'name': 'UK Comedy'}],
            'typeName': 'Episode'
        }

        self.assertEqual(
            expected_episode_1,
            asdict(self._episode_api._episodes["CTON404T"])
        )

        # new_item is the same episode, just on a different platform & channel, and may
        # have different publication availability

        new_item = [test_episodes[0]]
        platform = "mobile"
        channel = channels["3866_broadcastchannel"]
        self._episode_api._create_episodes(new_item, platform, channel)

        expected_publication = [
            {
                'startDate': '2017-02-01T00:00:00+00:00',
                'endDate': '2099-12-31T23:59:00+00:00',
                'publishedOn': {'name': 'dave', 'id': 3854},
                'location': 'web',
                "notes": "vod"
            },
            {
                'startDate': '2017-02-01T00:00:00+00:00',
                'endDate': '2099-12-31T23:59:00+00:00',
                'publishedOn': {'name': 'yesterday', 'id': 3866},
                'location': 'mobile',
                "notes": "vod"
            }
        ]

        self.assertEqual(
            expected_publication,
            asdict(self._episode_api._episodes["CTON291E"]).get('publication')
        )
