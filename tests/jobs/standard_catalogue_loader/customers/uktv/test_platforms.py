from unittest import TestCase

from thefilter.jobs.standard_catalogue_loader.customers.uktv.static_types.platforms \
    import Platforms


class TestBrandList(TestCase):
    def test_convert_static_data(self):
        source = {
            "platforms": [
                "web",
                "mobile",
                "youview",
                "sky",
                "talktalk",
                "virginireland",
                "virginmedia",
                "bt",
                "eir"
            ]
        }

        converted = Platforms(source).convert_static_data()

        expected = [
            "web",
            "mobile",
            "youview",
            "sky",
            "talktalk",
            "virginireland",
            "virginmedia",
            "bt",
            "eir"
        ]

        self.assertEqual(expected, converted, "Converted platforms matches")
