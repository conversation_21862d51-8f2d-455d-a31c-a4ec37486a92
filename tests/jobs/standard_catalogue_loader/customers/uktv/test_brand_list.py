from unittest import TestCase

from thefilter.jobs.standard_catalogue_loader.customers.uktv.static_types.brandlist import \
    BrandList


class TestBrandList(TestCase):
    def test_convert_static_data(self):
        source = {
            "brand_list": {
                "success": True,
                "brands": [
                    {
                        "id": 4,
                        "name": "Test Brand 4",
                        "slug": "test-brand-4",
                        "subcategories": []
                    },
                    {
                        "id": 5,
                        "name": "Test Brand 5",
                        "slug": "test-brand-5",
                        "subcategories": []
                    },
                    {
                        "id": 6,
                        "name": "Test Brand 6",
                        "slug": "test-brand-6",
                        "subcategories": []
                    },
                    {
                        "id": 7,
                        "name": "Test Brand 7",
                        "slug": "test-brand-7",
                        "subcategories": []
                    },
                    {
                        "id": 8,
                        "name": "Test Brand 8",
                        "slug": "test-brand-8",
                        "subcategories": []
                    },
                    {
                        "id": 9,
                        "name": "Test Brand 9",
                        "slug": "test-brand-9",
                        "subcategories": []
                    },
                    {
                        "id": 10,
                        "name": "Test Brand 10",
                        "slug": "test-brand-10",
                        "subcategories": []
                    },
                ]
            }
        }

        converted = BrandList(source).convert_static_data()

        expected = ["4", "5", "6", "7", "8", "9", "10"]

        self.assertEqual(expected, converted, "Converted brand list matches")
