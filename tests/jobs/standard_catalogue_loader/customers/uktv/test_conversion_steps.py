from unittest import TestCase

from tests.jobs.standard_catalogue_loader.core_loader.data.standard_cat_loader_config_generator import \
    generate_default_catalogue_loader_config
from thefilter.jobs.standard_catalogue_loader.customers.uktv.conversion_steps import \
    UKTVConversion


class TestUKTVConversion(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        customer = 'uktv'

        config = generate_default_catalogue_loader_config(customer=customer)

        self._uktv_conversion_api = UKTVConversion(config)

    def test_converter(self):
        pass
        # Uncomment this if you wish to debug the UKTV converter
        # self._uktv_conversion_api.converter()
