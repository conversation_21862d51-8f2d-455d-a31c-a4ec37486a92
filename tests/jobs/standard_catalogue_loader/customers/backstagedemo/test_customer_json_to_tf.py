from unittest import TestCase

import freezegun

from tests.jobs.standard_catalogue_loader.core_loader.data.standard_cat_loader_config_generator import \
    generate_default_catalogue_loader_config
from tests.jobs.standard_catalogue_loader.customers.backstagedemo.data.expected_conversion import \
    expected_backstagedemo_customer_json, expected_backstagedemo_tf_metadata
from thefilter.jobs.standard_catalogue_loader.core_loader.new_source import \
    LocalJSONDataSource
from thefilter.jobs.standard_catalogue_loader.customers.backstage_api.conversion_steps import \
    BackstageAPIConversion

customer = 'backstagedemo'
environment = "pre"
region = 'eu-west-2'
source_directory = \
    'tests/jobs/standard_catalogue_loader/customers/backstagedemo/data/backstagedemo_customer_json.json'

source = LocalJSONDataSource(
    source_directory=source_directory,
    customer=customer,
    environment=environment,
    region=region
)

test_config = generate_default_catalogue_loader_config(customer=customer, source=source)

@freezegun.freeze_time("2022-10-13T14:03:00+00:00")
class TestBackstagedemoConversion(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def test_get_data(self):
        customer_json = test_config.source.get_data()
        self.maxDiff = None
        self.assertEqual(expected_backstagedemo_customer_json, customer_json)

    def test_backstage_conversion(self):
        backstagedemo_conversion = BackstageAPIConversion(test_config)
        tf_metadata = backstagedemo_conversion.converter()
        self.maxDiff = None
        self.assertEqual(expected_backstagedemo_tf_metadata, tf_metadata)

    def test_distinct_genres(self):
        # TODO
        pass
