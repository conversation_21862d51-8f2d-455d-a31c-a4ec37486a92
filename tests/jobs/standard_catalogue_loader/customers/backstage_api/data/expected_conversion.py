expected_backstage_customer_json = [
    {
        "type": "movie",
        "description": "Chicago hip hop sensations The Q Brothers heat up the holidays with Christmas Carol: The Remix! Performed at Chicago Shakespeare Theater, this ingenious take on <PERSON><PERSON> classic mashes up hip hop, reggae, dancehall, dubstep and rock, infusing it with cheeky spirit and a lot of heart as the ghosts of hip hop past, present and future lead <PERSON><PERSON><PERSON> on a journey of rhythm, rhyme and redemption.",
        "duration": 5091,
        "release_date": "2022-12-15",
        "rating": None,
        "minimum_price": None,
        'genres': [
            {'id': 'AW2GrakCpx3F9_4AqerK', 'name': 'Musicals'},
            {'id': 'AW7HiaYESYI49LFJG-Zo', 'name': 'Holiday'},
            {'id': 'AYTZPUpVjRWTegtFrJGo', 'name': 'Companies - HMS Media'}
        ],
        "crew": [
            {
                "id": "c66cc7a0-72c4-11ed-835e-2749b0e036e9",
                "role": "actor"
            },
            {
                "id": "c66ccd20-72c4-11ed-86c8-c90ad9c97451",
                "role": "actor"
            },
            {
                "id": "c66cd880-72c4-11ed-896f-2f14458e6209",
                "role": "actor"
            },
            {
                "id": "c66ccd20-72c4-11ed-86c8-c90ad9c97451",
                "role": "director"
            },
            {
                "id": "c66cf1e0-72c4-11ed-a77b-d73ee4c611b7",
                "role": "director"
            },
            {
                "id": "c66cf660-72c4-11ed-aa7b-47f93c781660",
                "role": "producer"
            },
            {
                "id": "c66d0800-72c4-11ed-bc8c-370ea2587a3e",
                "role": "producer"
            },
            {
                "id": "c66d0cc0-72c4-11ed-ac54-c335a92be1d1",
                "role": "writer"
            },
        ],
        "year": 2022,
        "geoblock": {
            "allow": [
                "AD",
            ],
            "deny": [
                "IR",
                "KP"
            ]
        },
        "advertisementStrategyId": None,
        "external_id": "z4sscdgDrKwG",
        "checksum": None,
        "sourceId": "AXuV_GUnon5fjzGk8Jjo",
        "externalAuthDetails": None,
        "id": "c66d1cb0-72c4-11ed-b112-4d31ef22ecbd",
        "label": "Christmas Carol: The Remix by The Q Brothers",
        "deepLink": None,
        "images": {
            "still": [
                {
                    "size": {
                        "width": 426,
                        "height": 240
                    },
                    "url": "https://cdn.backstage-api.com?key=backstage-cms-production-uploads/426x240/29ed8750-c986-11e9-87f8-65266d441520/assets/images/94fd8d7a-c494-481d-9d05-dafdfc4a070c.jpg"
                }
            ],
            "poster": [
                {
                    "size": {
                        "width": 1200,
                        "height": 1800
                    },
                    "url": "https://cdn.backstage-api.com?key=backstage-cms-production-uploads/1200x1800/29ed8750-c986-11e9-87f8-65266d441520/assets/images/85e72878-a175-4fd8-99c5-b91020d47ac4.jpg"
                }
            ],
            "background": [
                {
                    "size": {
                        "width": 1920,
                        "height": 1080
                    },
                    "url": "https://cdn.backstage-api.com?key=backstage-cms-production-uploads/1920x1080/29ed8750-c986-11e9-87f8-65266d441520/assets/images/5d97db83-5023-41ab-8cac-cbbced1d64da.jpg"
                }
            ],
            "highlight": [
                {
                    "size": {
                        "width": 3840,
                        "height": 1280
                    },
                    "url": "https://cdn.backstage-api.com?key=backstage-cms-production-uploads/3840x1280/29ed8750-c986-11e9-87f8-65266d441520/assets/images/1ff7c9c5-3372-4a4a-9eaa-76c56df31ce9.jpg"
                }
            ]
        },
        "translations": None,
        "short_description": "Chicago hip hop sensations The Q Brothers heat up the holidays with Christmas Carol: The Remix! Performed at Chicago Shakespeare Theater, this ingenious take on Dickens’ classic mashes up hip hop, reggae, dancehall, dubstep and rock, infusing it with cheeky spirit and a lot of heart as the ghosts of hip hop past, present and future lead Scrooge on a journey of rhythm, rhyme and redemption.",
        "age_classification": None,
        "subscriptionTags": [
            {
                "id": "subscription"
            }
        ],
        "windows": [
            {
                "title": "Availability Window",
                "startsAt": "1671091200",
                "endsAt": None,
                "blocked": False,
                "applications": [],
                "countries": []
            }
        ],
        "isHidden": None,
        "tags": [],
        "assetLabel": None,
        "updatedByUserAt": 1670451420,
        "averageUserRating": None,
        "userRatingCount": None,
        "isAdult": False,
        "workflowTags": [],
        "externalRefs": [],
        "productIds": None,
        "createdAt": 1670042499,
        "updatedAt": 1670451421,
        "origin": None,
        "clipIds": None
    }
]

expected_backstage_tf_metadata = [
    {'actor': [
        {'id': 'c66cc7a0-72c4-11ed-835e-2749b0e036e9', 'name': None},
        {'id': 'c66ccd20-72c4-11ed-86c8-c90ad9c97451', 'name': None},
        {'id': 'c66cd880-72c4-11ed-896f-2f14458e6209', 'name': None},
        {'id': 'c66d0cc0-72c4-11ed-ac54-c335a92be1d1', 'name': None}
    ],
        'alternateName': '',
        'brandId': 'c66d1cb0-72c4-11ed-b112-4d31ef22ecbd',
        'brandName': 'Christmas Carol: The Remix by The Q Brothers',
        'contentRating': [],
        'crew': [],
        'custom': {'active': {'state': True,
                              'stateUpdateTimestamp': '2023-08-17T12:01:00+00:00'},
                   'isHidden': False,
                   'rating': 'None'},
        'datePublished': '2022-12-15T00:00:00+00:00',
        'description': 'Chicago hip hop sensations The Q Brothers heat up the '
                       'holidays with Christmas Carol: The Remix! Performed at '
                       'Chicago Shakespeare Theater, this ingenious take on Dickens’ '
                       'classic mashes up hip hop, reggae, dancehall, dubstep and '
                       'rock, infusing it with cheeky spirit and a lot of heart as '
                       'the ghosts of hip hop past, present and future lead Scrooge '
                       'on a journey of rhythm, rhyme and redemption.',
        'director': [{'id': 'c66ccd20-72c4-11ed-86c8-c90ad9c97451', 'name': None},
                     {'id': 'c66cf1e0-72c4-11ed-a77b-d73ee4c611b7', 'name': None}],
        'duration': 'PT1H24M51S',
        'episodeNumber': 0,
        'genre': [{'id': 'AW2GrakCpx3F9_4AqerK', 'name': 'Musicals'},
                  {'id': 'AW7HiaYESYI49LFJG-Zo', 'name': 'Holiday'}],
        'id': 'c66d1cb0-72c4-11ed-b112-4d31ef22ecbd',
        'image': {
            'url': 'https://cdn.backstage-api.com?key=backstage-cms-production-uploads/1200x1800/29ed8750-c986-11e9-87f8-65266d441520/assets/images/85e72878-a175-4fd8-99c5-b91020d47ac4.jpg'},
        'inLanguage': None,
        'isAdult': None,
        'isKids': None,
        'isLiveBroadcast': False,
        'keywords': [],
        'name': 'Christmas Carol: The Remix by The Q Brothers',
        'numberOfSeasons': None,
        'partOfSeason': {},
        'partOfSeries': {},
        'producer': [{'id': 'c66cf660-72c4-11ed-aa7b-47f93c781660', 'name': None},
                     {'id': 'c66d0800-72c4-11ed-bc8c-370ea2587a3e', 'name': None}],
        'productionCompany': 'HMS Media',
        'publication': [{'endDate': '2038-01-19T00:00:00+00:00',
                         'id': '',
                         'name': 'VOD',
                         'startDate': '1970-01-01T00:00:01+00:00'}],
        'recommendationId': None,
        'seasonNumber': 0,
        'space': None,
        'subGenre': [],
        'typeName': 'Movie'}
]
