import json
from unittest import TestCase, mock

from tests.jobs.standard_catalogue_loader.customers.backstage_api.data.backstage_broadcasts import \
    backstage_broadcasts_assets, expected_backstage_broadcasts_assets
from thefilter.jobs.standard_catalogue_loader.customers.backstage_api.backstage_api import \
    BackstageAPI
from thefilter.logs.logclient import NoOpLogger

movies = "https://dapi.backstage-api.com/movies?sortField=updatedAt"
movies_initial_response = json.dumps(
    {
        "offset": 0,
        "size": 4,
        "total": 6,
        "items": [
            {
                "type": "movie",
                "id": "id_0",
                'genres': {'id': 'test_genre_id_0'}
            },
            {
                "type": "movie",
                "id": "id_1",
            },
            {
                "type": "movie",
                "id": "id_2",
            }
        ]
    }
)

movies_next_response = json.dumps(
    {
        "offset": 4,
        "size": 2,
        "total": 6,
        "items": [
            {
                "type": "movie",
                "id": "id_3",
            },
            {
                "type": "movie",
                "id": "id_4",
            }
        ]
    }
)

crew_initial = 'https://dapi.backstage-api.com/crew-roles?sortField=updatedAt'
crew_initial_response = '{}'
persons_initial = 'https://dapi.backstage-api.com/persons?sortField=updatedAt'
persons_initial_response = '{}'
genres_initial = 'https://dapi.backstage-api.com/genres?sortField=updatedAt'
genres_initial_response = json.dumps(
    {
        "offset": 0,
        "size": 1,
        "total": 1,
        "items": [
            {
                "id": "test_genre_id_0",
                "label": "WWE Spring Showcase",
                "isHidden": False,
                "image": [],
                "translations": None,
                "createdAt": 1683214211,
                "updatedAt": None
            }
        ]
    }
)
broadcasts_initial = 'https://dapi.backstage-api.com/broadcasts?sortField=updatedAt'
broadcasts_initial_response = json.dumps(
    {
        "offset": 0,
        "size": 1,
        "total": 1,
        "items": [
            {
                'id': 'kz71JYoBbFJh7u4X-YHg',
                'type': 'broadcast',
                'channelId': 'nkYtAYkBvVhhRpGLsCY7',
                'startsAt': 1693417500,
                'endsAt': 1693421100,
                'program': [],
                'genres': [
                    {'id': 'gameshow'}
                ],
                'label': 'Fear Factor USA',
                'description': 'In each pulse-racing Fear Factor episode, contestants recruited across the country battle in extreme stunts.',
                'images': {
                    'background': [],
                    'still': [],
                    'poster': [],
                    'highlight': []
                },
                'isHidden': None,
                'isAdult': False,

                'createdAt': 1692853991,
                'updatedAt': None
            }
        ]
    }
)

channel_initial = 'https://dapi.backstage-api.com/channels?sortField=updatedAt'
channel_initial_response = json.dumps(
    {
        "offset": 0,
        "size": 1,
        "total": 1,
        "items": [
            {
                "type": "channel",
                "number": "222",
                "id": "n85YAYkBVUM7Ygs7v6MC",
                "label": "Mystery TV"
            }
        ]
    }
)


def mocked_requests_get(**kwargs):
    class MockResponse:
        def __init__(self, json_data, status_code):
            self.content = json_data
            self.status_code = status_code

        def json(self):
            return self.json_data

    endpoint = kwargs.get('url')

    if endpoint == movies and kwargs.get('params') == {'offset': 0}:
        response = MockResponse(movies_initial_response, 200)
    elif endpoint == movies \
            and kwargs.get('params') == {'offset': 0, 'size': 50}:
        response = MockResponse(movies_initial_response, 200)
    elif endpoint == movies \
            and kwargs.get('params') == {'offset': 50, 'size': 50}:
        response = MockResponse(movies_next_response, 200)
    elif endpoint == movies \
            and kwargs.get('params') == {'offset': 50, 'size': 50}:
        response = MockResponse('{}')
    elif endpoint == crew_initial and kwargs.get('params') == {'offset': 0}:
        response = MockResponse(crew_initial_response, 200)
    elif endpoint == persons_initial and kwargs.get('params') == {'offset': 0}:
        response = MockResponse(persons_initial_response, 200)
    elif endpoint == genres_initial and kwargs.get('params') == {'offset': 0}:
        response = MockResponse(genres_initial_response, 200)
    elif endpoint == genres_initial \
            and kwargs.get('params') == {'offset': 0, 'size': 50}:
        response = MockResponse(genres_initial_response, 200)
    elif endpoint == broadcasts_initial:
        response = MockResponse(broadcasts_initial_response, 200)
    elif endpoint == channel_initial:
        response = MockResponse(channel_initial_response, 200)
    else:
        response = MockResponse(None, 404)

    return response


@mock.patch('requests.get', side_effect=mocked_requests_get)
class TestBackstageAPI(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        customer = 'test_customer'
        consumer_key = 'abcd'
        x_service_id = 'efgh'
        host = 'https://dapi.backstage-api.com'

        self._backstage_api = BackstageAPI(
            customer=customer,
            host=host,
            consumer_key=consumer_key,
            x_service_id=x_service_id,
            logger=NoOpLogger()
        )

    def test_get_data(self, mock_get):
        enriched_media = self._backstage_api.get_data()
        expected = [
            {'type': 'movie', 'id': 'id_0', 'genres': {'id': 'test_genre_id_0'}},
            {'type': 'movie', 'id': 'id_1'}, {'type': 'movie', 'id': 'id_2'},
            {'type': 'channel', 'number': '222', 'id': 'n85YAYkBVUM7Ygs7v6MC',
             'label': 'Mystery TV'},
            {'id': 'kz71JYoBbFJh7u4X-YHg', 'type': 'broadcast',
             'channelId': 'nkYtAYkBvVhhRpGLsCY7',
             'startsAt': 1693417500, 'endsAt': 1693421100,
             'program': [], 'genres': [],
             'label': 'Fear Factor USA',
             'description': 'In each pulse-racing Fear Factor episode, contestants recruited across the country battle in extreme stunts.',
             'images': {'background': [], 'still': [],
                        'poster': [], 'highlight': []},
             'isHidden': None, 'isAdult': False,
             'createdAt': 1692853991, 'updatedAt': None
             }
        ]
        self.assertEqual(expected, enriched_media)

        self.assertIn(
            mock.call(
                url='https://dapi.backstage-api.com/movies?sortField=updatedAt',
                headers={'Consumer-Key': 'abcd', 'X-Service-ID': 'efgh'},
                params={'offset': 0}
            ),
            mock_get.call_args_list
        )
        self.assertIn(
            mock.call(
                url='https://dapi.backstage-api.com/movies?sortField=updatedAt',
                headers={'Consumer-Key': 'abcd', 'X-Service-ID': 'efgh'},
                params={'offset': 00, 'size': 50}
            ),
            mock_get.call_args_list
        )
        # etcetera...

    def test_handle_item_broadcasts(self, mock_get):
        crew_roles = {}
        persons = {}
        genres = {
            'gameshow': {
                'id': 'gameshow', 'label': 'Game show', 'isHidden': False, 'image': [],
                'translations': [], 'createdAt': 1690995389, 'updatedAt': None
            }
        }

        enriched_media = self._backstage_api._handle_item(
            backstage_broadcasts_assets,
            crew_roles,
            genres,
            persons
        )

        self.assertEqual(
            expected_backstage_broadcasts_assets,
            enriched_media
        )
