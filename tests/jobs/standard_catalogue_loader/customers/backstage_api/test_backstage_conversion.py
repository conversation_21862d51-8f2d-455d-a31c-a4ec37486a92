import os
import unittest
from unittest import TestCase

import freezegun

from tests.jobs.standard_catalogue_loader.core_loader.data.standard_cat_loader_config_generator import \
    generate_default_catalogue_loader_config
from tests.jobs.standard_catalogue_loader.customers.backstage_api.data.expected_conversion import \
    expected_backstage_customer_json, expected_backstage_tf_metadata
from thefilter.jobs.standard_catalogue_loader.core_loader.new_source import \
    LocalJSONDataSource
from thefilter.jobs.standard_catalogue_loader.customers.backstage_api.conversion_steps import \
    BackstageAPIConversion
from thefilter.jobs.standard_catalogue_loader.customers.backstage_api.customer_json_to_tf import \
    CustomerJsonToTF
from thefilter.model.schemaorg import Thing

customer = 'broadwayhd'
environment = "pre"
region = 'eu-west-2'

source_directory = \
    'tests/jobs/standard_catalogue_loader/customers/backstage_api/data/backstageapi_customer_json.json'
source = LocalJSONDataSource(
    source_directory=source_directory,
    customer=customer,
    environment=environment,
    region=region
)
test_config = generate_default_catalogue_loader_config(customer=customer, source=source)


class TestBackstageAPIConversion(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.converter_api = CustomerJsonToTF(test_config)

    def test_get_data(self):
        customer_json = test_config.source.get_data()
        self.maxDiff = None
        self.assertEqual(expected_backstage_customer_json, customer_json)

    @freezegun.freeze_time('2023-08-17T12:01:00+00:00')
    def test_backstage_conversion(self):
        backstage_conversion = BackstageAPIConversion(test_config)
        tf_metadata = backstage_conversion.converter()
        self.maxDiff = None
        self.assertEqual(expected_backstage_tf_metadata, tf_metadata)

    def test__new_handle_genre(self):
        thing = Thing()
        item = {
            'genres': [
                {'id': 'AX19-I5bzv4NKGwM-u-A', 'name': 'Companies - Liberator'},
                {'id': 'AW2GrakCpx3F9_4AqerK', 'name': 'Musicals'},
                {'id': 'abc123', 'name': 'Leggy Goat'}
            ]
        }

        self.converter_api._handle_cleaning_genre(thing, item)

        expected_genres = [{'name': 'Musicals', 'id': 'AW2GrakCpx3F9_4AqerK'}]
        expected_keywords = [{'name': 'Leggy Goat'}]
        expected_production_company = 'Liberator'

        self.assertEqual(expected_genres, thing.genre)
        self.assertEqual(expected_keywords, thing.keywords)
        self.assertEqual(expected_production_company, thing.productionCompany)

    @unittest.skipIf(os.environ.get('CI'), "Don't run this in GitHub")
    def test_handle_broadcasts(self):
        channel_item = {
            "type": "channel",
            "number": "241",
            "id": "channel_id",
            "label": "channel_name",
        }

        broadcast_item = {
            'id': 'QxoH4YkBqYI2Kwryj76M',
            'type': 'broadcast',
            'channelId': 'channel_id',
            'startsAt': **********,
            'endsAt': **********,
            'program': [],
            'genres': [],
            'external_id': None,
            'checksum': None,
            'sourceId': None,
            'externalAuthDetails': None,
            'label': 'Anger Management',
            'deepLink': None,
            'description': "A disturbed patient joins Charlie's therapy group.",
            'images': {
                'background': [],
                'still': [
                    {
                        'size': {
                            'height': 1080,
                            'width': 1920
                        },
                        'url': 'https://db6iftd8li11q.cloudfront.net/AngerManagement_landscape.jpg'
                    }
                ],
                'poster': [],
                'highlight': []
            },
            'translations': None,
            'short_description': "A disturbed patient joins Charlie's therapy group.",
            'age_classification': [],
            'subscriptionTags': None,
            'crew': [],
            'windows': [],
            'isHidden': None,
            'tags': None,
            'assetLabel': None,
            'updatedByUserAt': 0,
            'averageUserRating': None,
            'userRatingCount': None,
            'isAdult': False,
            'workflowTags': [],
            'externalRefs': {
                'IMDB': [], 'DAI': []
            },
            'productIds': None,
            'createdAt': **********,
            'updatedAt': None
        }
        # we need to run the channel item first, so that the channel map is populated.
        # this does affect the state, which is causing the test to fail on GitHub.
        _channel_item = self.converter_api.create_item(channel_item)

        broadcast_item = self.converter_api.create_item(broadcast_item)

        expected_broadcast_thing = Thing(
            id='QxoH4YkBqYI2Kwryj76M',
            brandId='QxoH4YkBqYI2Kwryj76M',
            brandName='Anger Management',
            recommendationId=None,
            name='Anger Management',
            alternateName='',
            typeName='CreativeWork',
            genre=[], subGenre=[], contentRating=[], actor=[],
            director=[], producer=[], crew=[], partOfSeason={}, partOfSeries={},
            publication=[
                {
                    'id': 'channel_id',
                    'name': 'channel_name',
                    'startDate': '2023-08-15T23:51:42+00:00',
                    'endDate': '2023-08-16T00:16:44+00:00'
                }
            ],
            keywords=[], episodeNumber=0, seasonNumber=0, numberOfSeasons=None,
            description="A disturbed patient joins Charlie's therapy group.",
            duration='PT25M2S',
            datePublished='1970-01-01T00:00:01+00:00',
            image={
                'url': 'https://db6iftd8li11q.cloudfront.net/AngerManagement_landscape.jpg'
            },
            inLanguage=None,
            isLiveBroadcast=True,
            productionCompany='',
            custom={'rating': '', 'isHidden': False},
            space=None,
            isAdult=None,
            isKids=None
        )

        self.assertEqual(expected_broadcast_thing, broadcast_item)

    def test_handle_brand_name(self):
        episode_thing = Thing(
            id="test_id",
            brandId="test_brandId",
            brandName="test_brandName",
            name="test_name",
            alternateName="test_alternateName",
            typeName="TVEpisode",
            partOfSeries="test_partOfSeries",
        )
        brand_name = self.converter_api._handle_brand_name(episode_thing)

        unknown_brand_name = "Unknown Series"
        self.assertEqual(unknown_brand_name, self.converter_api._unknown_brand_name)
        self.assertEqual(unknown_brand_name, brand_name)

    def test_episode(self):
        item = {
            'type': 'episode', 'series_type': 'series', 'episode_number': 1,
            'season_number': 1, 'next_episode_id': None, 'duration': 1320,
            'release_date': '2022-03-23', 'rating': None,
            'series': 'f863d903-4c7c-4e85-96a9-b0d8e5023a42', 'minimum_price': None,
            'genres': [], 'crew': [], 'year': 2022, 'geoblock': None,
            'advertisementStrategyId': None, 'external_id': '55568',
            'checksum': 'b08855fced82582332cdac1039c35392', 'sourceId': None,
            'externalAuthDetails': None, 'id': 'f80b7497-ab5d-44db-ade4-708346aaa277',
            'label': 'Mi Padre y Yo', 'deepLink': None,
            'description': 'Alejandro Toro llega a Puerto Rico y se reencuentra con su padre, Junior Alvarez, para comenzar su viaje de fortalecer su relación. En este episodio, visitan a la familia, comparten recuerdos y disfrutan de la mejor comida de la isla, como el arroz casero con habichuelas y bistec.',
            'images': {'background': [], 'backgroundPortrait': [], 'still': [
                {'size': {'width': 1920, 'height': 1080},
                 'url': 'https://assets.tastemadecdn.net/images/18da20/0f2c6d4e3db700636e45/5eb243.png'}],
                       'poster': [], 'heroLandscape': [], 'heroPortrait': []},
            'translations': None,
            'short_description': 'Alejandro se encuentra con su padre en Puerto Rico para comenzar su viaje de fortalecer la relación',
            'age_classification': None, 'subscriptionTags': [], 'windows': [
                {'blocked': False, 'blockedMessage': None,
                 'countries': ['AR', 'BO', 'CL', 'CO', 'CR', 'CU', 'DO', 'EC', 'ES',
                               'GQ', 'GT', 'HN', 'MX', 'NI', 'PA', 'PE', 'PR', 'PY',
                               'SV', 'UY', 'VE'],
                 'applications': ['70c09c58-e03c-11ed-b5ea-0242ac120002',
                                  'dde0a50f-90bb-489d-b5fc-b69e10447eab',
                                  '13cb82a2-53bd-4268-aa70-a77b612f385a',
                                  '82069454-e03c-11ed-b5ea-0242ac120002',
                                  '29899b1e-d0d1-413b-8439-8c097e46aaea',
                                  'd8f21996-f83c-465f-9e35-b5a202b27b46',
                                  'e5ea12d4-54c9-4b6a-bf75-814dfafdb316',
                                  'f4fc743f-7dfd-4960-a182-ffc63e3d2df2'],
                 'startsAt': 1648060664, 'endsAt': None, 'title': None}],
            'isHidden': None, 'tags': None, 'assetLabel': None,
            'updatedByUserAt': 1696021583, 'averageUserRating': None,
            'userRatingCount': None, 'isAdult': False, 'workflowTags': [],
            'externalRefs': [], 'productIds': None, 'createdAt': 1682700313,
            'updatedAt': 1702404070, 'origin': None, 'clipIds': None
        }
        self.converter_api._customer_json_d = {i['id']: i for i in [item]}
        result = self.converter_api.create_item(item)

        expected = Thing(
            id='f80b7497-ab5d-44db-ade4-708346aaa277',
            brandId='f863d903-4c7c-4e85-96a9-b0d8e5023a42', brandName='Unknown Series',
            recommendationId=None, name='Mi Padre y Yo', alternateName='',
            typeName='TVEpisode', genre=[], subGenre=[], contentRating=[], actor=[],
            director=[], producer=[], crew=[], partOfSeason={},
            partOfSeries={'name': 'Unknown Series',
                          'id': 'f863d903-4c7c-4e85-96a9-b0d8e5023a42'}, publication=[
                {'name': 'VOD', 'id': '', 'startDate': '1970-01-01T00:00:01+00:00',
                 'endDate': '2038-01-19T00:00:00+00:00'}], keywords=[], episodeNumber=1,
            seasonNumber=1, numberOfSeasons=None,
            description='Alejandro Toro llega a Puerto Rico y se reencuentra con su padre, Junior Alvarez, para comenzar su viaje de fortalecer su relación. En este episodio, visitan a la familia, comparten recuerdos y disfrutan de la mejor comida de la isla, como el arroz casero con habichuelas y bistec.',
            duration='PT22M', datePublished='2022-03-23T00:00:00+00:00', image={
                'url': 'https://assets.tastemadecdn.net/images/18da20/0f2c6d4e3db700636e45/5eb243.png'},
            inLanguage=None, isLiveBroadcast=False, productionCompany='',
            custom={'rating': 'None', 'isHidden': False}, space=None, isAdult=None,
            isKids=None
        )
        self.assertEqual(expected, result)

    def test_live_item(self):
        item = {
            'type': 'live_event', 'startsAt': 1707677400, 'endsAt': 1707689400,
            'geoblock': {'allow': [], 'deny': []}, 'rating': None, 'duration': None,
            'year': None, 'release_date': None, 'external_id': None, 'checksum': None,
            'sourceId': None, 'externalAuthDetails': None, 'id': 'JLh-X40B2-1Og56_M5Pw',
            'label': 'Feyenoord vs Sparta Rotterdam', 'deepLink': None,
            'description': '.',
            'images': {'background': [], 'still': [], 'poster': [], 'highlight': [],
                       'square': []}, 'translations': None, 'short_description': None,
            'age_classification': {'types': []}, 'genres': [], 'subscriptionTags': [],
            'crew': [], 'windows': [
                {'blocked': False, 'blockedMessage': None, 'countries': ['IT'],
                 'applications': ['513099a0-1bf8-11ee-987a-1d1dafbbb592',
                                  '514aa700-1bf8-11ee-bf72-19ecc79a22de',
                                  '512dc180-1bf8-11ee-b76b-a1ec667e5336',
                                  '5147da40-1bf8-11ee-8d3b-9fba3d3bfbcd'],
                 'endsAt': None, 'startsAt': 1706704082, 'title': None}],
            'isHidden': True, 'tags': [], 'assetLabel': None,
            'updatedByUserAt': 1706704122, 'averageUserRating': None,
            'userRatingCount': None, 'isAdult': False, 'workflowTags': [],
            'externalRefs': {'IMDB': {'id': None},
                             'DAI': {'liveAssetKey': None, 'vodVideoId': None,
                                     'vodCMSId': None}}, 'productIds': None,
            'createdAt': 1706704122, 'updatedAt': None, 'origin': None, 'clipIds': []
        }
        result = self.converter_api.create_item(item)
        expected = Thing(
            id='JLh-X40B2-1Og56_M5Pw', brandId='JLh-X40B2-1Og56_M5Pw',
            brandName='Feyenoord vs Sparta Rotterdam', recommendationId=None,
            name='Feyenoord vs Sparta Rotterdam', alternateName='',
            typeName='CreativeWork', genre=[], subGenre=[], contentRating=[], actor=[],
            director=[], producer=[], crew=[], partOfSeason={}, partOfSeries={},
            publication=[{'name': 'live_event', 'id': None,
                          'startDate': '2024-02-11T18:50:00+00:00',
                          'endDate': '2024-02-11T22:10:00+00:00'}], keywords=[],
            episodeNumber=0, seasonNumber=0, numberOfSeasons=None, description='.',
            duration='', datePublished='1970-01-01T00:00:01+00:00', image={'url': ''},
            inLanguage=None, isLiveBroadcast=True, productionCompany='',
            custom={'rating': 'None', 'isHidden': True}, space=None, isAdult=None,
            isKids=None
        )

        self.assertEqual(expected, result)
