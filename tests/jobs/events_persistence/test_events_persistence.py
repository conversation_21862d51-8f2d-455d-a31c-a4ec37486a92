import json
import os
import shutil
from copy import deepcopy
from unittest import TestCase
from unittest.mock import MagicMock

import freezegun
import pyarrow as pa

from thefilter.aws.athena import AthenaQueryClientStub
from thefilter.aws.dynamoDB import ContinueWatchingStubRepository
from thefilter.aws.glue import GlueClient<PERSON>tub
from thefilter.aws.s3 import S3DirectoryToolStub, S3TransferStub
from thefilter.aws.sts import StsClientStub
from thefilter.jobs.events_persistence.core.repersistence import EventsRepersistence
from thefilter.jobs.events_persistence.events_persistence import EventsPersistence
from thefilter.jobs.events_persistence.events_persistence_config import \
    EventsPersistenceConfig
from thefilter.jobs.file_builders import StubFileBuilder
from thefilter.logs.logclient import NoOpLogger
from thefilter.repositories import StubSlotsRepository, InformationTableRepositoryStub
from thefilter.utils import ndjson
from thefilter.utils.datetime import DurationUtils
from thefilter.utils.slackbot import SlackBotStub

# the stub_files file paths mirror those found in S3
stub_files = [
    "data/raw-events/2023/02/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-02-02-09-30-38-cfb9bad0-4215-4b11-a175-fb2fb15a9f94",
    "data/raw-events/2023/03/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-03-03-21-37-39-4f018d56-95be-4e8f-ba58-486268a991c5"
]

metadata_repo = [
    {'thing_id': 'bW92aWU7MTA0MQ==', 'thing_brandid': 'thing_brand_id_0'},
    {'thing_id': 'ZXBpc29kZTsxODY5', 'thing_brandid': 'thing_brand_id_1'},
    {'thing_id': 'bW92aWU7MTc4MTc=', 'thing_brandid': 'thing_brand_id_2'},
    {'thing_id': "2761310", 'thing_brandid': 'thing_brand_id_3',
     'thing_duration': 'PT4M46S'},
    {'thing_id': "3216844", 'thing_brandid': 'thing_brand_id_4',
     'thing_duration': 'PT1H4M3S'},
    {'thing_id': "2879074", 'thing_brandid': 'thing_brand_id_5'},
    {'thing_id': "2760081", 'thing_brandid': 'thing_brand_id_6'},
    {'thing_id': "1028180", 'thing_brandid': 'thing_brand_id_7'},
    {'thing_id': "2879079", 'thing_brandid': 'thing_brand_id_8'},
    {'thing_id': "2889521", 'thing_brandid': 'thing_brand_id_9'},
    {'thing_id': "1005248", 'thing_brandid': 'thing_brand_id_10'},
    {'thing_id': "4083535", 'thing_brandid': 'thing_brand_id_11'},
    {'thing_id': "uktv_thing_id_0", 'thing_brandid': 'thing_brand_id_12',
     'thing_duration': 'PT1H28M44S'
     }
]


@freezegun.freeze_time("2023-03-08T12:33:00+00:00")
class TestEventsPersistence(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        AWS_REGION = 'eu-west-2'
        ENVIRONMENT = 'pre'
        CUSTOMER = 'test-customer'
        self.CUSTOMER = CUSTOMER
        LOG_CLIENT = NoOpLogger()
        S3_BUCKET_NAME = f'thefilter-{ENVIRONMENT}-{AWS_REGION}-{CUSTOMER}'
        SOURCE_FOLDER = "raw-events"
        S3_BUCKET_PATH = f'data/{SOURCE_FOLDER}'
        s3_client = S3DirectoryToolStub(
            stub_files=stub_files,
            s3_bucket_name=f'thefilter-{ENVIRONMENT}-{AWS_REGION}-{CUSTOMER}',
            s3_bucket_path=f'data/{SOURCE_FOLDER}'
        )
        athena_client = AthenaQueryClientStub(metadata_repo)
        identifier_operationid = 'abcd1234-ab12-cd34-ef56-abcdef123456'
        data_replay = False
        slack_bot_stub = SlackBotStub()
        sts_client = StsClientStub(ENVIRONMENT, slack_bot_stub)
        role_name = 'test_role'
        s3_transfer_client = S3TransferStub(sts_client, role_name)
        self._log_client = LOG_CLIENT
        glue_client = GlueClientStub(
            AWS_REGION, ENVIRONMENT, CUSTOMER, LOG_CLIENT, slack_bot_stub
        )
        config = EventsPersistenceConfig(
            region=AWS_REGION,
            environment=ENVIRONMENT,
            customer=CUSTOMER,
            log_client=LOG_CLIENT,
            start_date="2023-03-01",
            end_date="2023-03-10",
            days_to_process=60,  # required, but start_date & end_date override this!
            s3_bucket_name=S3_BUCKET_NAME,
            s3_bucket_path=S3_BUCKET_PATH,
            s3_client=s3_client,
            file_builder=StubFileBuilder(),
            athena_client=athena_client,
            identifier_operationid=identifier_operationid,
            data_replay=data_replay,
            s3_transfer_client=s3_transfer_client,
            repersist=False,
            source_folder=SOURCE_FOLDER,
            slackbot_client=slack_bot_stub,
            glue_client=glue_client,
            continue_watching_repo=ContinueWatchingStubRepository(
                region=AWS_REGION, environment=ENVIRONMENT, customer=CUSTOMER
            ),
            slot_repo=StubSlotsRepository(),
            info_repo=InformationTableRepositoryStub(
                region=AWS_REGION, environment=ENVIRONMENT, customer=CUSTOMER
            )
        )
        self._config = config
        self._api = EventsPersistence(config=config)
        self._api._customer_config_api.repo_type = "local"
        self._schema = self._api._schema_events

    def test_schema_types(self):
        expected_schema = {
            "action": pa.string(),
            "attribution_id": pa.string(),
            "brand_id": pa.string(),
            'context_environment': pa.string(),
            'device_appversion': pa.string(),
            "device_id": pa.string(),
            "device_manufacturer": pa.string(),
            "device_model": pa.string(),
            "device_platform": pa.string(),
            "device_timezone": pa.string(),
            "device_type": pa.string(),
            "duration": pa.int32(),
            'error_code': pa.string(),
            'error_message': pa.string(),
            "headers_referer": pa.string(),
            "headers_user_agent": pa.string(),
            "identifier_eventid": pa.string(),
            "identifier_operationid": pa.string(),
            "ignore": pa.string(),
            "paid": pa.string(),
            "progress_pct": pa.float64(),
            "search_success": pa.bool_(),
            "search_term": pa.string(),
            "session_id": pa.string(),
            "slot_id": pa.string(),
            "source_channel_id": pa.string(),
            "source_channel_name": pa.string(),
            'source_item_position': pa.int32(),
            "source_page": pa.string(),
            "source_rail": pa.string(),
            'source_rail_position': pa.int32(),
            "source_type": pa.string(),
            "source_url": pa.string(),
            'stream_bitrate': pa.int32(),
            "thing_id": pa.string(),
            "timestamp_initiated": pa.string(),
            "timestamp_received": pa.string(),
            "user_account_id": pa.string(),
            "user_anon_id": pa.string(),
            "user_country": pa.string(),
            'user_email': pa.string(),
            'user_id': pa.string(),
            'user_phone': pa.string(),
            "user_primary_id": pa.string(),
            "user_profile_id": pa.string(),
            "user_subscription_type": pa.string()
        }

        actual_schema = self._api._set_parquet_schema_events()
        self.assertEqual(expected_schema, actual_schema)

    def test_get_all_raw_events_by_date(self):
        # For the original implementation we used uncompressed files.
        local_file_paths = [
            'tests/jobs/events_persistence/data/raw-events/2023/02/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-02-02-09-30-38-cfb9bad0-4215-4b11-a175-fb2fb15a9f94'
        ]
        all_raw_personalisation_on_date, all_raw_events_on_date = self._api.get_all_raw_events_by_date(
            local_file_paths, is_personalisation=True)
        expected = [
            {'identifier_eventid': 'abaa53e4-a407-4d5b-b082-f209136facf5',
             'identifier_operationid': '24e51808-8b53-4b44-b6a2-9eeeee9b6ab9',
             'customer_name': 'test-customer',
             'user_userid': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
             'user_primaryid': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:07+00:00',
             'thing_id': ['bW92aWU7MTA0MQ=='], 'action': 'view',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'},
            {'identifier_eventid': '9f378d70-c34f-486a-9467-18d3ddd9d508',
             'identifier_operationid': 'efafcc0f-5470-4f41-a4d8-3504d771efa7',
             'customer_name': 'test-customer',
             'user_userid': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8',
             'user_primaryid': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:07+00:00',
             'thing_id': ['ZXBpc29kZTsxODY5'], 'action': 'play',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'},
            {'identifier_eventid': 'cd0629cc-e539-4707-bf12-ca358dfe90c8',
             'identifier_operationid': '95984b22-c87b-4f9f-8ef5-0dfb6247c193',
             'customer_name': 'test-customer',
             'user_userid': 'eb7b3dd0d0d21414b3a9cc1c8a1c1d33c0ebbee211752de86071347668e3d018',
             'user_primaryid': 'eb7b3dd0d0d21414b3a9cc1c8a1c1d33c0ebbee211752de86071347668e3d018',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:08+00:00',
             'thing_id': ['bW92aWU7MTc4MTc='], 'action': 'play',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'}
        ]

        self.assertEqual(expected, all_raw_events_on_date)

    def test_get_all_gzip_raw_events_by_date(self):
        local_file_paths = [
            'tests/jobs/events_persistence/data/raw-events/2023/02/03/21/test_gzip_file.gz'
        ]
        all_raw_personalisation_on_date, all_raw_events_on_date = self._api.get_all_raw_events_by_date(
            local_file_paths, is_personalisation=True)

        expected = [
            {'identifier_eventid': 'abaa53e4-a407-4d5b-b082-f209136facf5',
             'identifier_operationid': '24e51808-8b53-4b44-b6a2-9eeeee9b6ab9',
             'customer_name': 'test-customer',
             'user_userid': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
             'user_primaryid': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:07+00:00',
             'thing_id': ['bW92aWU7MTA0MQ=='], 'action': 'view',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'},
            {'identifier_eventid': '9f378d70-c34f-486a-9467-18d3ddd9d508',
             'identifier_operationid': 'efafcc0f-5470-4f41-a4d8-3504d771efa7',
             'customer_name': 'test-customer',
             'user_userid': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8',
             'user_primaryid': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:07+00:00',
             'thing_id': ['ZXBpc29kZTsxODY5'], 'action': 'play',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'},
            {'identifier_eventid': 'cd0629cc-e539-4707-bf12-ca358dfe90c8',
             'identifier_operationid': '95984b22-c87b-4f9f-8ef5-0dfb6247c193',
             'customer_name': 'test-customer',
             'user_userid': 'eb7b3dd0d0d21414b3a9cc1c8a1c1d33c0ebbee211752de86071347668e3d018',
             'user_primaryid': 'eb7b3dd0d0d21414b3a9cc1c8a1c1d33c0ebbee211752de86071347668e3d018',
             'user_accountid': 'None', 'user_anonid': 'None',
             'timestamp_received': '2023-03-01T04:46:09+00:00',
             'timestamp_initiated': '2023-03-01T04:46:08+00:00',
             'thing_id': ['bW92aWU7MTc4MTc='], 'action': 'play',
             'context_application': 'DataReplayLambda', 'context_site': 'AWS',
             'context_environment': 'production', 'custom_replayed': '**************',
             'custom_replayedfrom': 'production'}
        ]

        self.assertEqual(expected, all_raw_events_on_date)

    def test_combo_gzip_raw_events_by_date(self):
        local_file_paths = [
            'tests/jobs/events_persistence/data/raw-events/2023/02/03/21/bad_gz_combo_events_file.gz'
        ]
        all_raw_personalisation_on_date, all_raw_events_on_date = self._api.get_all_raw_events_by_date(
            local_file_paths, is_personalisation=True)

        expected = []

        self.assertEqual(expected, all_raw_events_on_date)

    def test_non_compressed_gzip_raw_events_by_date(self):
        local_file_paths = [
            'tests/jobs/events_persistence/data/raw-events/2023/02/03/21/non_compreseed_events_file.gz'
        ]
        all_raw_personalisation_on_date, all_raw_events_on_date = self._api.get_all_raw_events_by_date(
            local_file_paths, is_personalisation=True)

        expected = [
            {'action': 'play',
             'context_application': 'DataReplayLambda',
             'context_environment': 'pre',
             'context_site': 'AWS',
             'custom_replayed': '**************',
             'custom_replayedfrom': 'pre',
             'customer_name': 'epix',
             'identifier_eventid': '5a051cdd-f8b9-47ff-a807-809a8a348384',
             'identifier_operationid': '4cb23117-1aa2-4cc6-9410-9a90ae90d962',
             'thing_id': ['bW92aWU7MTg5MjE='],
             'timestamp_initiated': '2024-04-12T16:18:27+00:00',
             'timestamp_received': '2024-04-12T16:18:29+00:00',
             'user_accountid': 'None',
             'user_anonid': 'None',
             'user_primaryid': '41d1ba65d020fc38b6f26ec47fb8c5e72d8d7bf74f205c9e7d96d9b8ad05480f',
             'user_userid': '41d1ba65d020fc38b6f26ec47fb8c5e72d8d7bf74f205c9e7d96d9b8ad05480f'}
        ]

        self.assertEqual(expected, all_raw_events_on_date)

    def test_get_date_part_formatted(self):
        s3_path = 'data/raw-events/2023/03/08/04/backstagedemo-Raw-Events-Delivery-Stream-1-2023-03-08-04-09-13-54db6219-a5d0-4410-ac01-97f8efb5cddb'
        date_part_formatted = self._api._get_date_part_formatted(s3_path)
        expected = '2023-03-08-04'
        self.assertEqual(expected, date_part_formatted)

    def test_start_and_end_date(self):
        start_date = '2023-02-26'
        end_date = '2023-03-02'
        list_of_dates = self._api._set_date_range(
            days_to_process=0,
            start_date=start_date,
            end_date=end_date
        )
        expected = ['2023-02-26', '2023-02-27', '2023-02-28', '2023-03-01', '2023-03-02']
        self.assertEqual(expected, list_of_dates)

    def test_start_and_end_date_one_day(self):
        start_date = '2023-05-21'
        end_date = '2023-05-21'
        list_of_dates = self._api._set_date_range(
            days_to_process=0,
            start_date=start_date,
            end_date=end_date
        )
        expected = ['2023-05-21']
        self.assertEqual(expected, list_of_dates)

    def test_existing_customer_beacons(self):
        # Reminder that the paths are based on the customer and dates set on the api.
        expected_dates_to_process = {
            '2023-02-03': [
                'data/raw-events/2023/02/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-02-02-09-30-38-cfb9bad0-4215-4b11-a175-fb2fb15a9f94'],
            '2023-03-03': [
                'data/raw-events/2023/03/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-03-03-21-37-39-4f018d56-95be-4e8f-ba58-486268a991c5']
        }
        files_list = self._api._get_raw_events()
        self.assertEqual(expected_dates_to_process, files_list)

        # Check we can just read one of the files that we expect to process
        file_path = files_list['2023-02-03'][0]
        file_prefix = 'tests/jobs/events_persistence/'
        ndjson_file = os.path.join(file_prefix, file_path)

        with open(ndjson_file) as f:
            data = ndjson.load(f)
        self.assertEqual(3, len(data))

    def test_create_df(self):
        raw_events_filepath = "tests/jobs/events_persistence/data/raw-events.ndjson"

        with open(raw_events_filepath) as f:
            raw_events = ndjson.load(f)
        df = self._api.create_df(raw_events, self._schema)

        expected_dataframe_as_json = {
            'action': {'0': 'view', '1': 'play'},
            'attribution_id': {'0': None, '1': None},
            'brand_id': {'0': None, '1': None},
            'context_application': {'0': 'DataReplayLambda', '1': 'DataReplayLambda'},
            'context_environment': {'0': 'production', '1': 'production'},
            'context_site': {'0': 'AWS', '1': 'AWS'},
            'custom_replayed': {'0': '**************', '1': '**************'},
            'custom_replayedfrom': {'0': 'production', '1': 'production'},
            'customer_name': {'0': 'test-customer', '1': 'test-customer'},
            'device_appversion': {'0': None, '1': None},
            'device_id': {'0': None, '1': None},
            'device_manufacturer': {'0': None, '1': None},
            'device_model': {'0': None, '1': None},
            'device_platform': {'0': None, '1': None},
            'device_timezone': {'0': None, '1': None},
            'device_type': {'0': None, '1': None},
            'duration': {'0': None, '1': None},
            'error_code': {'0': None, '1': None},
            'error_message': {'0': None, '1': None},
            'headers_referer': {'0': None, '1': None},
            'headers_user_agent': {'0': None, '1': None},
            'identifier_eventid': {'0': 'abaa53e4-a407-4d5b-b082-f209136facf5',
                                   '1': '9f378d70-c34f-486a-9467-18d3ddd9d508'},
            'identifier_operationid': {'0': '24e51808-8b53-4b44-b6a2-9eeeee9b6ab9',
                                       '1': 'efafcc0f-5470-4f41-a4d8-3504d771efa7'},
            'ignore': {'0': None, '1': None},
            'paid': {'0': None, '1': None},
            'progress_pct': {'0': None, '1': None},
            'search_success': {'0': None, '1': None},
            'search_term': {'0': None, '1': None},
            'session_id': {'0': None, '1': None},
            'slot_id': {'0': None, '1': None},
            'source_channel_id': {'0': None, '1': None},
            'source_channel_name': {'0': None, '1': None},
            'source_item_position': {'0': None, '1': None},
            'source_page': {'0': None, '1': None},
            'source_rail': {'0': None, '1': None},
            'source_rail_position': {'0': None, '1': None},
            'source_type': {'0': None, '1': None},
            'source_url': {'0': None, '1': None},
            'stream_bitrate': {'0': None, '1': None},
            'thing_id': {'0': ['bW92aWU7MTA0MQ=='], '1': ['ZXBpc29kZTsxODY5']},
            'timestamp_initiated': {'0': '2023-03-01T04:46:07+00:00',
                                    '1': '2023-03-01T04:46:07+00:00'},
            'timestamp_received': {'0': '2023-03-01T04:46:09+00:00',
                                   '1': '2023-03-01T04:46:09+00:00'},
            'user_account_id': {'0': None, '1': None},
            'user_accountid': {'0': 'None', '1': 'None'},
            'user_anon_id': {'0': None, '1': None},
            'user_anonid': {'0': 'None', '1': 'None'},
            'user_country': {'0': None, '1': None},
            'user_email': {'0': None, '1': None},
            'user_id': {'0': None, '1': None},
            'user_phone': {'0': None, '1': None},
            'user_primary_id': {'0': None, '1': None},
            'user_primaryid': {
                '0': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
                '1': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8'},
            'user_profile_id': {'0': None, '1': None},
            'user_subscription_type': {'0': None, '1': None},
            'user_userid': {
                '0': '6cdbdd0611ae730641104b5a184090fd64005aaa6af9f3efdd6e415665345a15',
                '1': 'd0d805c4c813e2e3e4e39d0f8afcf83aa20583b59182a553358f156d76e27fe8'}
        }
        self.assertEqual(expected_dataframe_as_json, json.loads(df.to_json()))

    def test_normalise_fields(self):
        raw_events = [
            {'thing_id': 'thing_id_0'},
            {'thing_id': ['thing_id_1']}
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        expected = [
            {'source_type': 'VOD', 'thing_id': 'thing_id_0'},
            {'source_type': 'VOD', 'thing_id': 'thing_id_1'}
        ]

        self.assertEqual(expected, normalised_fields)

    def test_metadata_enrichment(self):
        raw_events = [
            {'thing_id': 'ZXBpc29kZTsxODY5'}
        ]
        enriched_events = self._api._metadata_enrichment(raw_events)
        expected = [
            {
                'thing_id': 'ZXBpc29kZTsxODY5',
                'brand_id': 'thing_brand_id_1',
                'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456'
            }
        ]
        self.assertEqual(expected, enriched_events)

    @freezegun.freeze_time("2024-01-10T17:21:00+00:00")
    def test_create_beacons(self):
        self._api._customer = 'test_customer'
        raw_events = [
            {
                'thing_id': '2761310',
                'user_id': 'test_user_id',
                'action': 'play',
                'duration': 1,
                'timestamp_initiated': '2024-01-10T17:21:00+00:00'
            }
        ]
        enriched_events = self._api._metadata_enrichment(raw_events)
        beacons = self._api._create_beacons(enriched_events)
        expected = {
            '2024-01-10': [
                {
                    'action': 'play',
                    'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
                    'thing_id': '2761310',
                    'thing_brandid': 'thing_brand_id_3',
                    'timestamp_initiated': '2024-01-10T17:21:00+00:00',
                    'user_primaryid': 'test_user_id',
                    'user_userid': 'test_user_id',
                    'duration_totaldurationsec': 60,
                    'duration_videocompletionpct': '0.33'
                }
            ]
        }
        self.assertEqual(expected, beacons)

    def test_create_uktv_events(self):
        self._api._customer = 'uktv'
        raw_events = [
            {
                'thing_id': 'uktv_thing_id_0',
                'user_id': 'test_user_id',
                'action': 'play',
                'duration': 100  # we won't actually see it like this, just for testing!
            },
            {
                'thing_id': 'uktv_thing_id_0',
                'user_id': 'test_user_id',
                'action': 'vodprogstart',
            },
            {
                'thing_id': 'uktv_thing_id_0',
                'user_id': 'test_user_id',
                'action': 'vodprogress_75',
            }
        ]
        enriched_events = self._api._metadata_enrichment(raw_events)
        expected = [
            {'action': 'play',
             'brand_id': 'thing_brand_id_12',
             'duration': 100,
             'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
             'progress_pct': 1.87,
             'thing_id': 'uktv_thing_id_0',
             'user_id': 'test_user_id'},
            {'action': 'vodprogstart',
             'brand_id': 'thing_brand_id_12',
             'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
             'progress_pct': 5,
             'thing_id': 'uktv_thing_id_0',
             'user_id': 'test_user_id'},
            {'action': 'vodprogress_75',
             'brand_id': 'thing_brand_id_12',
             'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
             'progress_pct': 75,
             'thing_id': 'uktv_thing_id_0',
             'user_id': 'test_user_id'}
        ]
        self.assertEqual(expected, enriched_events)

    def test_create_events(self):
        raw_events = [
            {
                "identifier_eventid": "abaa53e4-a407-4d5b-b082-f209136facf5",
                "identifier_operationid": "24e51808-8b53-4b44-b6a2-9eeeee9b6ab9",
                "customer_name": "test-customer",
                "user_userid": "user_userid_0",
                "user_primary_id": "user_primary_id_0",
                "user_accountid": "None",
                "user_anonid": "None",
                "timestamp_received": "2023-03-01T04:46:09+00:00",
                "timestamp_initiated": "2023-03-01T04:46:07+00:00",
                "thing_id": ["bW92aWU7MTA0MQ=="],
                "action": "view",
                "context_application": "DataReplayLambda",
                "context_site": "AWS",
                "context_environment": "production",
                "custom_replayed": "**************",
                "custom_replayedfrom": "production"
            }
        ]

        cleaned_event = self._api._create_events(raw_events)
        expected = {
            '2023-03-01': [
                {
                    'identifier_operationid': '24e51808-8b53-4b44-b6a2-9eeeee9b6ab9',
                    'identifier_eventid': 'abaa53e4-a407-4d5b-b082-f209136facf5',
                    'user_primary_id': 'user_primary_id_0',
                    'user_accountid': 'None',
                    'user_anonid': 'None',
                    'timestamp_received': '2023-03-01T04:46:09+00:00',
                    'timestamp_initiated': '2023-03-01T04:46:07+00:00',
                    'thing_id': ['bW92aWU7MTA0MQ=='],
                    'action': 'view',
                    'context_environment': 'production',
                    'user_id': 'user_userid_0'}
            ]
        }
        self.assertEqual(expected, cleaned_event)

    def test_handle_user_primaryid(self):
        # user_id
        event = {'user_id': 'test_person'}
        self._api._handle_user_primary_id(event)
        expected = {
            'user_primary_id': 'test_person',
            'user_id': 'test_person'
        }
        self.assertEqual(expected, event)

        # user_anon_id
        event = {'user_anon_id': 'test_person'}
        self._api._handle_user_primary_id(event)
        expected = {
            'user_primary_id': 'test_person',
            'user_anon_id': 'test_person'
        }
        self.assertEqual(expected, event)

        # user_account_id
        event = {'user_account_id': 'test_person'}
        self._api._handle_user_primary_id(event)
        expected = {
            'user_primary_id': 'test_person',
            'user_account_id': 'test_person'
        }
        self.assertEqual(expected, event)

        # user_profile_id
        event = {'user_profile_id': 'test_person'}
        self._api._handle_user_primary_id(event)
        expected = {
            'user_primary_id': 'test_person',
            'user_profile_id': 'test_person'
        }
        self.assertEqual(expected, event)

    def test_uktv_raw_event(self):
        self._api._customer = 'uktv'
        raw_events = [
            {
                'identifier_eventid': '45fd6c24-9c6f-49a8-9345-008584888bef',
                'identifier_operationid': 'e0a17ec1-3db3-4627-913b-83560d1c159d',
                'custom_itemid': '2753', 'custom_sourcepage': 'search',
                'custom_eventtype': 'view', 'custom_userid': '********',
                'custom_platform': 'amazonfire',
                'custom_anonid': 'fc79b0b3588a974b7d2421b389107959',
                'context_environment': 'production',
                'context_application': 'api_beacon_lite', 'action': 'view',
                'user_primaryid': '********', 'user_userid': '********',
                'user_anonid': 'fc79b0b3588a974b7d2421b389107959',
                'thing_id': '2753_brand',
                'timestamp_initiated': '2023-03-14T23:29:09+00:00',
                'timestamp_received': '2023-03-14T23:29:09+00:00',
                'custom_space': 'brand',
                'custom_sourceid': 'Search Popular',
                'custom_platform-name': 'test_custom_platform-name',
                'custom_rule_id': '0db88157-374f-4880-9491-96fc6fa4d7f0',
                'custom_responseid': '3f777d34-0842-4682-8e03-9954906ece11',
                'sourceoperationid': '3f777d34-0842-4682-8e03-9954906ece11',
                'custom_enriched_time': '2023-03-15T04:00:35+00:00',
                'custom_no_enrich_flag': 'false',
                'custom_parent_referer': 'https://tvappfiretv.uktv.co.uk/'
            },
            {
                'action': 'play',
                'user_anonid': 'fc79b0b3588a974b7d2421b389107959',
                'thing_id': '2753_brand',
                'timestamp_initiated': '2023-03-14T23:30:09+00:00',
            },
            {
                'action': 'vodprogress_75',
                'user_anonid': 'test_user',
                'thing_id': '1001_brand',
                'timestamp_initiated': '2023-03-15T00:05:09+00:00',
            }
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        enriched_raw_events = self._api._metadata_enrichment(normalised_fields)
        actual = self._api._create_events(enriched_raw_events)
        expected = {
            '2023-03-14': [
                {
                    'action': 'view',
                    'attribution_id': '3f777d34-0842-4682-8e03-9954906ece11',
                    'context_environment': 'production',
                    'custom_space': 'brand',
                    'device_platform': 'test_custom_platform-name',
                    'device_type': 'amazonfire',
                    'headers_referer': 'https://tvappfiretv.uktv.co.uk/',
                    'identifier_eventid': '45fd6c24-9c6f-49a8-9345-008584888bef',
                    'identifier_operationid': 'e0a17ec1-3db3-4627-913b-83560d1c159d',
                    'progress_pct': None,
                    'slot_id': '0db88157-374f-4880-9491-96fc6fa4d7f0',
                    'source_page': 'search',
                    'source_rail': 'Search Popular',
                    'source_type': 'VOD',
                    'thing_id': '2753_brand',
                    'timestamp_initiated': '2023-03-14T23:29:09+00:00',
                    'timestamp_received': '2023-03-14T23:29:09+00:00',
                    'user_anon_id': 'fc79b0b3588a974b7d2421b389107959',
                    'user_id': '********',
                    'user_primary_id': '********',
                    'user_subscription_type': 'registered'
                },
                {
                    'action': 'play',
                    'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
                    'progress_pct': None,
                    'source_type': 'VOD',
                    'thing_id': '2753_brand',
                    'timestamp_initiated': '2023-03-14T23:30:09+00:00',
                    'user_anon_id': 'fc79b0b3588a974b7d2421b389107959',
                    'user_primary_id': 'fc79b0b3588a974b7d2421b389107959',
                    'user_subscription_type': 'anon'
                }
            ],
            '2023-03-15': [
                {
                    'action': 'vodprogress_75',
                    'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
                    'progress_pct': 75,
                    'source_type': 'VOD',
                    'thing_id': '1001_brand',
                    'timestamp_initiated': '2023-03-15T00:05:09+00:00',
                    'user_anon_id': 'test_user',
                    'user_primary_id': 'test_user',
                    'user_subscription_type': 'anon'
                }
            ]
        }
        self.assertEqual(expected, actual)

    def test_epix_raw_event(self):
        self._api._customer = 'epix'
        raw_events = [
            {
                'identifier_eventid': 'ec9a3db5-863e-40b9-b780-48cede510656',
                'identifier_operationid': 'e9cc2743-e414-45f7-a3ed-66e666d6508b',
                'custom_epix_accountstatus': 'Free Trial',
                'custom_epix_timezoneoffset': '0',
                'custom_epix_billingprovider': 'Amazon',
                'custom_epix_startingplayheadseconds': 70.0,
                'custom_epix_appname': 'EPIX',
                'custom_epix_localtimestampinitiated': '2023-03-14T02:13:47.303+00:00',
                'custom_epix_useragent': 'mParticle Ruby client/1.1.1',
                'custom_epix_producttrialperioddays': 7.0,
                'custom_epix_billingcycle': 'Monthly',
                'custom_epix_reportingdate': '2023-03-13',
                'context_environment': 'production',
                'context_application': 'mParticleLambda/epix',
                'action': 'play',
                'user_primaryid': 'test_user_primary_id',
                'user_userid': 'test_user_userid',
                'thing_id': 'bW92aWU7MTA0MQ==',
                'timestamp_initiated': '2023-03-14T02:13:47+00:00',
                'timestamp_received': '2023-03-14T02:13:49+00:00',
                'custom_epix_platform': 'test_custom_epix_platform',
                'custom_epix_streamdurationpercent': 0.1,
                'custom_epix_streamdurationseconds': 10,
                'custom_epix_tablet': None,
                'custom_epix_selection_rail': 'test_custom_epix_selection_rail',
                'custom_epix_selection_name': 'test_custom_epix_selection_name',
                'custom_epix_selection_position': None,
                'custom_epix_selection_type': None,
                'custom_epix_productprice': 'test_custom_epix_productprice',
                'custom_epix_freetrialtype': None,
                'custom_rule_id': 'test_slot_id',
                'sourceoperationid': 'test_sourceoperationid',
                'custom_epix_churntype': None
            }
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        enriched_raw_events = self._api._metadata_enrichment(normalised_fields)
        actual = self._api._create_events(enriched_raw_events)
        expected = {
            '2023-03-14': [
                {
                    'action': 'play',
                    'attribution_id': 'test_sourceoperationid',
                    'context_environment': 'production',
                    'custom_epix_appname': 'EPIX',
                    'custom_epix_billingcycle': 'Monthly',
                    'custom_epix_billingprovider': 'Amazon',
                    'custom_epix_churntype': None,
                    'custom_epix_freetrialtype': None,
                    'custom_epix_localtimestampinitiated': '2023-03-14T02:13:47.303+00:00',
                    'custom_epix_producttrialperioddays': 7.0,
                    'custom_epix_reportingdate': '2023-03-13',
                    'custom_epix_selection_position': None,
                    'custom_epix_selection_type': None,
                    'custom_epix_startingplayheadseconds': 70.0,
                    'custom_epix_tablet': None,
                    'custom_rule_id': 'test_slot_id',
                    'device_platform': 'test_custom_epix_platform',
                    'device_timezone': '0',
                    'duration': 10.0,
                    'headers_user_agent': 'mParticle Ruby client/1.1.1',
                    'identifier_eventid': 'ec9a3db5-863e-40b9-b780-48cede510656',
                    'identifier_operationid': 'e9cc2743-e414-45f7-a3ed-66e666d6508b',
                    'paid': 'test_custom_epix_productprice',
                    'progress_pct': 0.1,
                    'source_page': 'test_custom_epix_selection_rail',
                    'source_rail': 'test_custom_epix_selection_name',
                    'source_type': 'VOD',
                    'thing_id': 'bW92aWU7MTA0MQ==',
                    'brand_id': 'thing_brand_id_0',
                    'timestamp_initiated': '2023-03-14T02:13:47+00:00',
                    'timestamp_received': '2023-03-14T02:13:49+00:00',
                    'user_id': 'test_user_userid',
                    'user_primary_id': 'test_user_primary_id',
                    'user_subscription_type': 'Free Trial'
                }
            ]
        }
        self.assertEqual(expected, actual)

    def test_null_thing_duration(self):
        raw_events = [
            {
                'identifier_eventid': 'ec9a3db5-863e-40b9-b780-48cede510656',
                'identifier_operationid': 'e9cc2743-e414-45f7-a3ed-66e666d6508b',
                'action': 'play',
                'user_userid': 'test_user_userid',
                'thing_id': 'bW92aWU7MTA0MQ==',
                'timestamp_initiated': '2023-03-14T02:13:47+00:00',
                'timestamp_received': '2023-03-14T02:13:49+00:00',
                'duration': 10
            },
            {
                'identifier_eventid': 'ec9a3db5-863e-40b9-b780-48cede510656',
                'identifier_operationid': 'e9cc2743-e414-45f7-a3ed-66e666d6508b',
                'action': 'play',
                'user_userid': 'test_user_userid',
                'thing_id': 'bW92aWU7MTA0MQ==',
                'timestamp_initiated': '2023-03-14T02:13:47+00:00',
                'timestamp_received': '2023-03-14T02:13:49+00:00',
                'duration': 10
            }
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        enriched_raw_events = self._api._metadata_enrichment(normalised_fields)
        actual = self._api._create_events(enriched_raw_events)
        self.assertEqual(None, actual["2023-03-14"][0]['progress_pct'])

    def test_empty_events(self):
        raw_events = [
            {
                'thing_id': 123,
            },
            None,
            None
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        expected = [{'source_type': 'VOD', 'thing_id': '123'}]
        self.assertEqual(expected, normalised_fields)

    def test_long_durations(self):
        raw_events = [
            {
                'action': 'play',
                'user_anonid': 'test_anonid',
                'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                'thing_id': '2761310',
                'duration': 60,
            }
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        enriched_raw_events = self._api._metadata_enrichment(normalised_fields)
        actual = self._api._create_events(enriched_raw_events)
        expected = {
            '2024-01-15': [
                {'action': 'play',
                 'brand_id': 'thing_brand_id_3',
                 'duration': 60,
                 'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
                 'progress_pct': 20.0,
                 'source_type': 'VOD',
                 'thing_id': '2761310',
                 'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                 'user_anonid': 'test_anonid'}
            ]
        }
        self.assertEqual(expected, actual)

    def test_customer_custom_fields(self):
        raw_events = [
            {
                'action': 'play',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
                'timestamp_initiated': '2024-01-10T17:21:00+00:00',
                'custom_event_trigger': 'test_custom_event_trigger',
                'custom_percent_scrolled': 'test_percent_scrolled',
                'custom_player_advertisment_name': 'testplayer_advertisment_name',
            }
        ]
        normalised_fields = self._api._normalise_fields(raw_events)
        enriched_raw_events = self._api._metadata_enrichment(normalised_fields)
        actual = self._api._create_events(enriched_raw_events)
        expected = {
            '2024-01-10': [
                {'action': 'play', 'user_id': 'test_user_id',
                 'thing_id': 'test_thing_id',
                 'timestamp_initiated': '2024-01-10T17:21:00+00:00',
                 'custom_event_trigger': 'test_custom_event_trigger',
                 'custom_percent_scrolled': 'test_percent_scrolled',
                 'custom_player_advertisment_name': 'testplayer_advertisment_name',
                 'source_type': 'VOD',
                 'identifier_operationid': 'abcd1234-ab12-cd34-ef56-abcdef123456',
                 'user_primary_id': 'test_user_id'}
            ]
        }
        self.assertEqual(expected, actual)

    def test_data_replay(self):
        # just ensures the method can run.
        test_file_map = {
            '20230203': [
                'data/raw-events/2023/02/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-02-02-09-30-38-cfb9bad0-4215-4b11-a175-fb2fb15a9f94'
            ],
            '20230303': [
                'data/raw-events/2023/03/03/21/test-customer-Raw-Events-Delivery-Stream-1-2023-03-03-21-37-39-4f018d56-95be-4e8f-ba58-486268a991c5'
            ]
        }
        return_value = self._api._copy_files_to_lower_environment(test_file_map)
        self.assertEqual(None, return_value)

    def test_get_dst_bucket_name(self):
        file = 'thefilter-production-us-east-1-test-customer'
        source_environment = 'production'
        destination_environment = 'pre'
        dst_bucket_name = self._api._get_dst_bucket_name(
            file, source_environment, destination_environment
        )
        expected = 'thefilter-pre-eu-west-2-test-customer'
        self.assertEqual(expected, dst_bucket_name)

    def test_appstage_action_remapping(self):
        fresh_config = deepcopy(self._config)
        fresh_config.customer = 'backstagedemo'
        events_persistence_api = EventsPersistence(
            config=fresh_config
        )
        test_events = [
            {
                'action': 'video_stop',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
            },
            {
                'action': 'unknown_action',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
            },
            {
                'action': 'scene_view',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
            }
        ]
        remapped_events = \
            events_persistence_api._action_renaming(test_events)

        expected = [
            {'action': 'view', 'thing_id': 'test_thing_id', 'user_id': 'test_user_id'},
            {'action': 'unknown_action',
             'thing_id': 'test_thing_id',
             'user_id': 'test_user_id'},
            {'action': 'play', 'thing_id': 'test_thing_id', 'user_id': 'test_user_id'}
        ]

        self.assertEqual(expected, remapped_events)

    def test_persist_ignore_flag(self):
        fresh_config = deepcopy(self._config)
        fresh_config.customer = 'test'
        events_persistence_api = EventsPersistence(
            config=fresh_config
        )
        test_events = [
            {
                'action': 'video_stop',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
                'ignore': 'persist_ignore'
            },
            {
                'action': 'video_stop',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
                'ignore': 'persist_ignore'
            },
            {
                'action': 'video_stop',
                'user_id': 'test_user_id',
                'thing_id': 'test_thing_id',
                'ignore': 'persist_ignore'
            }
        ]

        raw_events = events_persistence_api._normalise_fields(test_events)
        self.assertEqual(0, len(raw_events), "All persist_ignore events removed")

    def test_sample_data_replay(self):
        sample_events = [
            {
                'user_userid': 'abcdefj',
                'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                'thing_id': ['c2VyaWVzOzEwNjU=']
            },
            {
                'user_userid': 'defg',
                'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                'thing_id': ['c2VyaWVzOzEwNjg=']
            },
            {
                'user_userid': 'abcd',
                'timestamp_initiated': '2024-01-16T00:05:51+00:00',
                'thing_id': ['c2VyaWVzOzEwNjg=']
            }
        ]

        sample = self._api._sample_events_for_data_replay(sample_events)
        expected = [
            {
                'user_userid': 'abcdefj',
                'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                'thing_id': ['c2VyaWVzOzEwNjU=']
            }
        ]
        self.assertEqual(expected, sample)

        self._api._data_replay_hashed = True
        sample = self._api._sample_events_for_data_replay(sample_events)
        expected = [
            {
                'user_userid': '18b8907c7d146e20df275dcb02455589',
                'timestamp_initiated': '2024-01-15T00:05:51+00:00',
                'thing_id': ['c2VyaWVzOzEwNjU=']
            }
        ]
        self.assertEqual(expected, sample)

    def test_generate_events_and_persist(self):
        # Remove any files in these location before running test
        shutil.rmtree("/tmp/parquet", ignore_errors=True)
        shutil.rmtree("/tmp/error", ignore_errors=True)

        enriched_raw_events = [
            {
                'timestamp_initiated': '2023-08-04T04:27:28+00:00',
                'user_id': '139896951',
                'action': 'buffering_start',
                'thing_id': ''
            },
            {
                'timestamp_initiated': '2023-03-08T04:27:28+00:00',
                'user_id': '139896951',
                'action': 'scene_view',
                'thing_id': ''
            },
            {
                'timestamp_initiated': '2023-03-10T04:27:30+00:00',
                'user_id': '139896951',
                'action': 'buffering_stop',
                'thing_id': ''
            },
            {
                'timestamp_initiated': '2023-03-08T04:27:34+00:00',
                'user_id': '139896951',
                'action': 'scroll',
                'thing_id': ''
            },
            {
                'timestamp_initiated': '2023-03-07T04:27:39+00:00',
                'user_id': '139896951',
                'action': 'scroll',
                'thing_id': ''
            }
        ]

        bucket_events = self._api._create_events(enriched_raw_events)
        expected_keys = ['2023-03-07', '2023-03-08', '2023-03-10', '2023-08-04']
        self.assertEqual(expected_keys, sorted(list(bucket_events.keys())),
                         'Bucketed keys match')

        self._api._generate_and_persist_events('2023-03-08', enriched_raw_events)

        # Check events output files 'tmp/parquet'
        event_files = os.listdir('/tmp/parquet')
        expected_files = [
            'datepartition=2023-03-07',
            'datepartition=2023-03-08',
        ]
        self.assertEqual(expected_files, sorted(event_files), "Output files match")

        # Check error events output '/tmp/error/'
        error_files = os.listdir('/tmp/error')
        expected_files = [
            'datepartition=2023-03-10',
            'datepartition=2023-08-04',
        ]
        self.assertEqual(expected_files, sorted(error_files), "Output files match")

        # Remove any files in these location after running test
        shutil.rmtree("/tmp/parquet")
        shutil.rmtree("/tmp/error")

    def test_create_files_from_sample_events(self):
        # If this test fails, make sure you delete content from /tmp/data/...
        sample_events = [
            {"timestamp_initiated": "2024-02-20T10:00:00", "data": "event1"},
            {"timestamp_initiated": "2024-02-20T11:00:00", "data": "event2"},
            {"timestamp_initiated": "2024-02-21T10:00:00", "data": "event3"}
        ]

        result = self._api._create_files_from_sample_events(
            sample_events, "test_uuid")

        expected_file_path = "/tmp/data/raw-events/2024/02/20/00"
        expected_file_name = f"{expected_file_path}/{self.CUSTOMER}_data_replay_events_20240220_test_uuid.ndjson"

        self.assertTrue(os.path.exists(expected_file_path))
        self.assertTrue(os.path.exists(expected_file_name))

        with open(expected_file_name, "r") as f:
            content = f.readlines()
            content = [json.loads(line.strip()) for line in content]

        self.assertEqual(len(content), 2)
        self.assertEqual(content[0]["data"], "event1")
        self.assertEqual(content[1]["data"], "event2")

        expected_result = {
            "20240220": [expected_file_name],
            "20240221": [
                f"/tmp/data/raw-events/2024/02/21/00/{self.CUSTOMER}_data_replay_events_20240221_test_uuid.ndjson"
            ]
        }
        self.assertEqual(result, expected_result)

        for key, _ in \
                self._api._create_files_from_sample_events(sample_events, "test_uuid").items():
            file_path = f"/tmp/data/raw-events/2024/02/{key[6:8]}/00"
            file_name = f"{file_path}/{self.CUSTOMER}_data_replay_events_{key}_test_uuid.ndjson"
            os.remove(file_name)
            os.rmdir(file_path)

    def test_get_personalisation_map(self):
        attribution_ids = {"id1"}
        date = "2024-02-20"
        mock_athena_client = MagicMock()
        mock_athena_client.query_athena.return_value = [
            {
                "operation_id": "test_operation_id",
                "rule_id": "test_rule_id",
                "referer": "test_referer"
            }
        ]
        self._api._athena_client = mock_athena_client

        result = self._api._get_personalisation_map(attribution_ids, date)

        self.assertEqual(len(result), 1)
        self.assertEqual(
            result["test_operation_id"],
            {"rule_id": "test_rule_id", "referer": "test_referer"}
        )

        mock_athena_client.query_athena.assert_called_once()
        args, kwargs = mock_athena_client.query_athena.call_args
        query = args[0]
        expected_query = """
                    SELECT
                        identifier_eventid AS operation_id,
                        parameters_rule_id AS rule_id,
                        parameters_headers_referer AS referer
                    FROM
                        personalisation
                    WHERE
                            datepartition BETWEEN '2023-02-16' AND '2024-02-20'
                        AND identifier_eventid IN ('id1')
                """
        self.assertEqual(query, expected_query)
        mock_athena_client.reset_mock()

    def test_handle_personalisation_message(self):
        self._api._personalisation_notification_message = [
            {"key1": "value1", "key2": "value2", "key3": "value3"},
            {"key2": "value2"},
            {"key3": "value3"}
        ]
        mock_log_client = MagicMock()
        self._api._log_client = mock_log_client

        self._api._handle_personalisation_message()

        expected_message = "3 personalisation logs were identified with keys not in schema: ['key1', 'key2', 'key3']"
        mock_log_client.info.assert_called_once_with(expected_message)

        mock_log_client.reset_mock()

    def test_repersistence(self):
        repersistence_api = EventsRepersistence(self._config)
        repersistence_api.check_repersistence()

        self.my_class_instance = repersistence_api
        self.mock_config = MagicMock()
        repersistence_api._config = self.mock_config

        # test repersist with dates
        self.mock_config.repersist = True
        self.mock_config.start_date = "2024-01-01"
        self.mock_config.end_date = "2024-01-31"
        self.mock_config.log_client = MagicMock()

        self.my_class_instance.check_repersistence()

        actual_argument = self.mock_config.log_client.info.call_args[0][0].strip()
        expected_argument = f"Running Repersistence using {self.mock_config.s3_bucket_path}"
        self.assertEqual(actual_argument, expected_argument)

        # test repersist without dates
        self.mock_config.repersist = True
        self.mock_config.start_date = None
        self.mock_config.end_date = None
        self.mock_config.log_client = MagicMock()

        with self.assertRaises(SystemExit) as context:
            self.my_class_instance.check_repersistence()

        self.assertEqual(context.exception.code, 0)
        self.mock_config.log_client.error.assert_called_once_with(
            "Exiting. REPERSIST is set, please pass a START_DATE and END_DATE"
        )

        # test not repersist
        self.mock_config.repersist = False
        self.mock_config.log_client = MagicMock()

        self.my_class_instance.check_repersistence()

        self.assertEqual(self.mock_config.log_client.info.call_count, 0)
        self.assertEqual(self.mock_config.log_client.error.call_count, 0)

    def test_report_stats(self):
        stats = {
            "personalisation": {
                "2024-02-21": 0,
                "2024-02-22": 101
            },
            "events": {
                "2024-02-21": 1
            }
        }
        formatted_stats = self._api._report_stats(stats)
        expected = """Overall Stats:
- 2024-02-21: personalisation: 0, events: 1
- 2024-02-22: personalisation: 101, events: 0
"""
        self.assertEqual(expected, formatted_stats)

    def test_calculate_progress_pct_from_duration(self):
        self._api._metadata_cache = {
            "thing_0": {"thing_duration": "PT1H"}
        }
        test_event = {
            "thing_id": "thing_0",
            "duration": "1800"  # 30 minutes out of 60 minutes
        }
        progress_pct = self._api._calculate_progress_pct_from_duration(test_event)
        expected = 50.0
        self.assertEqual(expected, progress_pct)

    def test_convert_seconds_to_duration(self):
        value = 1740  # 1740 seconds is 29 minutes
        result = DurationUtils().convert_seconds_to_duration(value)
        expected = "PT29M"
        self.assertEqual(expected, result)

    @freezegun.freeze_time("2024-05-08T12:33:00+00:00")
    def test_continue_watching_persistence(self):
        self._api._metadata_cache = {
            "thing_id_0": {
                "thing_brandid": "thing_brandid_0"
            },
            "thing_id_1": {
                "thing_brandid": "thing_brandid_1"
            },
            "thing_id_2": {
                "thing_brandid": "thing_brandid_0"
            }
        }
        test_events = [
            {
                'timestamp_initiated': '2024-05-09T04:28:39+00:00',
                'action': 'play',
                'thing_id': 'thing_id_0',
                "thing_brandid": "thing_brandid_0",
                'progress_pct': 55.3  # bad event, missing user_primary_id
            },
            {
                'timestamp_initiated': '2024-05-08T04:28:39+00:00',
                'user_primary_id': 'user_id_2',
                'action': 'play',
                "thing_brandid": "thing_brandid_x",
                'progress_pct': 55.345  # bad event, missing thing_id
            },
            {
                # include in continue watching
                'timestamp_initiated': '2024-05-07T04:28:39+00:00',
                'thing_id': 'thing_id_99',  # no brand_id
                'user_primary_id': 'user_id_4',
                'action': 'play',
                'progress_pct': 55.345
            },
            {
                # include in continue watching
                'timestamp_initiated': '2024-05-06T04:27:28+00:00',
                'user_primary_id': 'user_id_0',
                'action': 'play',
                'thing_id': 'thing_id_0',
                "thing_brandid": "thing_brandid_0",
                'progress_pct': 60,
                'duration': 100,
                "some_other_field": "test_data"  # not required in CW data
            },
            {
                # include in continue watching
                'timestamp_initiated': '2024-05-05T04:27:29+00:00',
                'user_primary_id': 'user_id_0',
                'action': 'play',
                'thing_id': 'thing_id_1',
                "thing_brandid": "thing_brandid_1",
                'progress_pct': 90,  # highest for user_id_0 & thing_id_1
                'duration': 100
            },
            {
                'timestamp_initiated': '2024-05-04T04:27:30+00:00',
                'user_primary_id': 'user_id_0',
                'action': 'play',
                'thing_id': 'thing_id_0',
                "thing_brandid": "thing_brandid_0",
                'progress_pct': 55.0,  # already seen this user_id_0 & thing_id_0
                'duration': 100
            },
            {
                'timestamp_initiated': '2024-05-03T04:27:32+00:00',
                'user_primary_id': 'user_id_0',
                'action': 'play',
                'thing_id': 'thing_id_2',  # dedupe on brand
                "thing_brandid": "thing_brandid_0",
                'progress_pct': 51,
                'duration': 100
            },
            {
                # include in continue watching
                'timestamp_initiated': '2024-05-02T04:27:34+00:00',
                'user_primary_id': 'user_id_1',
                'action': 'play',
                'thing_id': 'thing_id_0',
                "thing_brandid": "thing_brandid_0",
                'progress_pct': 99,
                'duration': 100
            },
            {
                'timestamp_initiated': '2024-05-01T05:27:39+00:00',
                'user_primary_id': 'user_id_5',
                'action': 'play',
                'thing_id': 'thing_id_5',
                "thing_brandid": "thing_brandid_5",
                'progress_pct': 10,  # progress_pct below thresh
                'duration': 100
            },
            {}
        ]
        continue_watching_data = self._api._get_continue_watching_data(test_events)
        expected = {
            'user_id_0': {'thing_id_0': {'dateCreated': '2024-05-08',
                                         'duration': 100,
                                         'progress_pct': 60,
                                         'thing_id': 'thing_id_0',
                                         'timeToLiveEpoch': 1720441980,
                                         'timestamp_initiated': '2024-05-06T04:27:28+00:00',
                                         'user_primary_id': 'user_id_0'},
                          'thing_id_1': {'dateCreated': '2024-05-08',
                                         'duration': 100,
                                         'progress_pct': 90,
                                         'thing_id': 'thing_id_1',
                                         'timeToLiveEpoch': 1720441980,
                                         'timestamp_initiated': '2024-05-05T04:27:29+00:00',
                                         'user_primary_id': 'user_id_0'}},
            'user_id_1': {'thing_id_0': {'dateCreated': '2024-05-08',
                                         'duration': 100,
                                         'progress_pct': 99,
                                         'thing_id': 'thing_id_0',
                                         'timeToLiveEpoch': 1720441980,
                                         'timestamp_initiated': '2024-05-02T04:27:34+00:00',
                                         'user_primary_id': 'user_id_1'}},
            'user_id_4': {'thing_id_99': {'dateCreated': '2024-05-08',
                                          'duration': None,
                                          'progress_pct': 55.345,
                                          'thing_id': 'thing_id_99',
                                          'timeToLiveEpoch': 1720441980,
                                          'timestamp_initiated': '2024-05-07T04:28:39+00:00',
                                          'user_primary_id': 'user_id_4'}
                          }
        }

        self.assertEqual(expected, continue_watching_data)
