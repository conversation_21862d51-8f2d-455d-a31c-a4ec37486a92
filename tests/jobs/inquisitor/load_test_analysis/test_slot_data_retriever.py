import pandas as pd
from unittest import TestCase

from thefilter.jobs.inquisitor.load_test_analysis.slot_data_retriever import SlotDataRetriever
from thefilter.logs.logclient import NoOpLogger
from thefilter.repositories import StubSlotRepository


class TestSlotDataRetriever(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger = NoOpLogger()
        slots = {
            "064bbfeb-d46d-4731-8569-aff8c1981293": {
                "lastUpdatedBy": "pre-adamp-cognito",
                "dateCreated": "2024-08-27T09:58:03+01:00",
                "fallbackChart": "",
                "status": "Live",
                "responseSize": "25",
                "createdBy": "pre-adamp-cognito",
                "slotId": "064bbfeb-d46d-4731-8569-aff8c1981293",
                "slotName": "Trending Movies",
                "experiments": "[\n  {\n    \"id\": \"f78ee9ec-00be-417c-b678-c3c8400dbe2c\",\n    \"userPercentage\": 100,\n    \"isBaseRecipe\": true,\n    \"size\": 25,\n    \"title\": \"Trending Movies A: Recent\",\n    \"notes\": \"trending-recent-movies\",\n    \"modelDefinitions\": [\n      {\n        \"key\": \"trending-{variant}\",\n        \"source\": \"TrendingModel\",\n        \"version\": 1,\n        \"fulfilment\": {\n          \"ranges\": [\n            {\n              \"start\": 0,\n              \"end\": 11\n            }\n          ]\n        },\n        \"parameters\": {\n          \"defaultVariant\": \"recent\",\n          \"defaultOverlay\": \"movies\",\n          \"chartName\": \"trending-{variant}-{overlay}\",\n          \"use_recommendation_id\": true\n        }\n      }\n    ]\n  }\n]",
                "isDefault": False,
                "customer": "epix",
                "zeroResultsOk": False,
                "description": "Trending Movies",
                "dateUpdated": "2024-08-27T09:58:03+01:00",
                "type": "trending"
            }
        }
        slots_repo = StubSlotRepository(slots=slots)

        self._api = SlotDataRetriever(
            slots_repo, logger
        )

    def test_get_slot_id_to_name_map(self):
        df = pd.DataFrame({
            "Name": [
                "/v0/slots/064bbfeb-d46d-4731-8569-aff8c1981293/items?userId=002940a575d620a8335127b2bbcfa36df5d3b9191245e6b2b745b0077dde7a0b&ignore=persist_ignore%0A",
                "/v0/slots/064bbfeb-d46d-4731-8569-aff8c1981293/items?userId=b6e2e3676de6886f00bdca1db8dd57247be9dfb96ddc00b417eb96d59c33c5ce&ignore=persist_ignore%0A"
            ]
        })
        expected = {"064bbfeb-d46d-4731-8569-aff8c1981293": "Trending Movies"}
        actual = self._api.get_slot_id_to_name_map(df)
        self.assertEqual(expected, actual)
