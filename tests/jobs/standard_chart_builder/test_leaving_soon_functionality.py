import unittest
from unittest.mock import MagicMock

from thefilter.jobs.standard_chart_builder.core.chart_configuration import (
    ChartConfiguration, 
    PopularityVariant, 
    TrendingVariant,
    create_default_config,
    create_custom_config
)
from thefilter.jobs.standard_chart_builder.core.query_builders.availability_cte_builder import AvailabilityCTEBuilder
from thefilter.jobs.standard_chart_builder.core.query_builders.normalization_cte_builder import NormalizationCTEBuilder


class TestLeavingSoonConfiguration(unittest.TestCase):
    """Test leaving soon configuration functionality."""

    def test_default_config_has_leaving_soon_disabled(self):
        """Test that default configuration has leaving soon disabled."""
        config = create_default_config()

        self.assertFalse(config.enable_leaving_soon)
        self.assertEqual(config.leaving_soon_metric, "leaving_soon_score")
        self.assertEqual(config.leaving_soon_window_days, 7)
        self.assertEqual(config.leaving_on_field, "leaving_on")

    def test_custom_config_can_enable_leaving_soon(self):
        """Test that custom configuration can enable leaving soon."""
        config = create_custom_config(
            enable_leaving_soon=True,
            leaving_soon_window_days=14,
            leaving_soon_metric="custom_leaving_soon",
            leaving_on_field="custom_leaving_on"
        )

        self.assertTrue(config.enable_leaving_soon)
        self.assertEqual(config.leaving_soon_metric, "custom_leaving_soon")
        self.assertEqual(config.leaving_soon_window_days, 14)
        self.assertEqual(config.leaving_on_field, "custom_leaving_on")

    def test_all_metrics_for_output_includes_leaving_soon_when_enabled(self):
        """Test that all_metrics_for_output includes leaving soon metrics when enabled."""
        config = create_custom_config(enable_leaving_soon=True)

        metrics = config.all_metrics_for_output
        self.assertIn("leaving_soon_score", metrics)
        # leaving_on is now in filters, not metrics, so it shouldn't be in all_metrics_for_output

    def test_all_metrics_for_output_excludes_leaving_soon_when_disabled(self):
        """Test that all_metrics_for_output excludes leaving soon metrics when disabled."""
        config = create_default_config()

        metrics = config.all_metrics_for_output
        self.assertNotIn("leaving_soon_score", metrics)
        # leaving_on is now in filters, not metrics, so it shouldn't be in all_metrics_for_output

    def test_validation_fails_with_invalid_leaving_soon_window(self):
        """Test that validation fails with invalid leaving soon window."""
        config = create_default_config()
        config.enable_leaving_soon = True
        config.leaving_soon_window_days = 0
        
        errors = config.validate()
        self.assertIn("Leaving soon window days must be positive", errors)

    def test_validation_fails_with_empty_leaving_soon_metric(self):
        """Test that validation fails with empty leaving soon metric."""
        config = create_default_config()
        config.enable_leaving_soon = True
        config.leaving_soon_metric = ""

        errors = config.validate()
        self.assertIn("Leaving soon metric name cannot be empty", errors)

    def test_validation_fails_with_empty_leaving_on_field(self):
        """Test that validation fails with empty leaving on field."""
        config = create_default_config()
        config.enable_leaving_soon = True
        config.leaving_on_field = ""

        errors = config.validate()
        self.assertIn("Leaving on field name cannot be empty", errors)

    def test_validation_passes_with_valid_leaving_soon_config(self):
        """Test that validation passes with valid leaving soon configuration."""
        config = create_custom_config(
            enable_leaving_soon=True,
            leaving_soon_window_days=14
        )
        
        errors = config.validate()
        self.assertEqual([], errors)


class TestAvailabilityCTEBuilderLeavingSoon(unittest.TestCase):
    """Test AvailabilityCTEBuilder leaving soon functionality."""

    def setUp(self):
        """Set up test configuration."""
        self.config_with_leaving_soon = create_custom_config(
            enable_leaving_soon=True,
            leaving_soon_window_days=7,
            leaving_soon_metric="leaving_soon_score"
        )
        
        self.config_without_leaving_soon = create_default_config()

    def test_build_includes_leaving_soon_cte_when_enabled(self):
        """Test that build includes leaving soon CTE when enabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_with_leaving_soon)

        result = builder.build()

        self.assertIn("availability_leaving_soon", result)
        self.assertIn("leaving_soon_score", result)
        self.assertIn("leaving_on", result)
        self.assertIn("thing_publication_enddate", result)

    def test_build_excludes_leaving_soon_cte_when_disabled(self):
        """Test that build excludes leaving soon CTE when disabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_without_leaving_soon)
        
        result = builder.build()
        
        self.assertNotIn("availability_leaving_soon", result)

    def test_get_select_fields_includes_leaving_soon_when_enabled(self):
        """Test that get_select_fields includes leaving soon fields when enabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_with_leaving_soon)

        fields = builder.get_select_fields()

        self.assertIn("als.leaving_soon_score", fields)
        self.assertIn("als.leaving_on", fields)

    def test_get_select_fields_excludes_leaving_soon_when_disabled(self):
        """Test that get_select_fields excludes leaving soon fields when disabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_without_leaving_soon)

        fields = builder.get_select_fields()

        self.assertNotIn("als.leaving_soon_score", fields)
        self.assertNotIn("als.leaving_on", fields)

    def test_get_join_clause_includes_leaving_soon_when_enabled(self):
        """Test that get_join_clause includes leaving soon join when enabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_with_leaving_soon)
        
        join_clause = builder.get_join_clause()
        
        self.assertIn("availability_leaving_soon AS als", join_clause)

    def test_has_metrics_returns_true_when_leaving_soon_enabled(self):
        """Test that has_metrics returns True when leaving soon is enabled."""
        builder = AvailabilityCTEBuilder(chart_config=self.config_with_leaving_soon)
        
        self.assertTrue(builder.has_metrics())

    def test_leaving_soon_cte_uses_correct_window_days(self):
        """Test that leaving soon CTE uses correct window days from configuration."""
        config = create_custom_config(
            enable_leaving_soon=True,
            leaving_soon_window_days=14
        )
        builder = AvailabilityCTEBuilder(chart_config=config)
        
        result = builder.build()
        
        # Check that the window days value (14) appears in the SQL
        self.assertIn("14", result)


class TestNormalizationCTEBuilderLeavingSoon(unittest.TestCase):
    """Test NormalizationCTEBuilder leaving soon functionality."""

    def setUp(self):
        """Set up test configuration."""
        self.config_with_leaving_soon = create_custom_config(
            enable_leaving_soon=True,
            leaving_soon_metric="leaving_soon_score"
        )
        
        self.config_without_leaving_soon = create_default_config()

    def test_build_availability_fields_includes_leaving_soon_when_enabled(self):
        """Test that _build_availability_fields includes leaving soon fields when enabled."""
        builder = NormalizationCTEBuilder(chart_config=self.config_with_leaving_soon)

        fields = builder._build_availability_fields()

        self.assertIn("als.leaving_soon_score", fields)
        self.assertIn("als.leaving_on", fields)

    def test_build_availability_fields_excludes_leaving_soon_when_disabled(self):
        """Test that _build_availability_fields excludes leaving soon fields when disabled."""
        builder = NormalizationCTEBuilder(chart_config=self.config_without_leaving_soon)

        fields = builder._build_availability_fields()

        self.assertNotIn("als.leaving_soon_score", fields)
        self.assertNotIn("als.leaving_on", fields)

    def test_build_availability_join_includes_leaving_soon_when_enabled(self):
        """Test that _build_availability_join includes leaving soon join when enabled."""
        builder = NormalizationCTEBuilder(chart_config=self.config_with_leaving_soon)
        
        join_clause = builder._build_availability_join()
        
        self.assertIn("availability_leaving_soon AS als", join_clause)

    def test_build_availability_join_excludes_leaving_soon_when_disabled(self):
        """Test that _build_availability_join excludes leaving soon join when disabled."""
        builder = NormalizationCTEBuilder(chart_config=self.config_without_leaving_soon)
        
        join_clause = builder._build_availability_join()
        
        self.assertNotIn("availability_leaving_soon AS als", join_clause)


if __name__ == '__main__':
    unittest.main()
