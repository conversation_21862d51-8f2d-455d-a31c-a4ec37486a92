import unittest
from datetime import datetime

from thefilter.model.context import Context
from thefilter.model.ddb_metadata_updater import DDBItemMetadataUpdater
from thefilter.model.identifier import Identifier
from thefilter.model.messages.message import BaseMessage, Message
from thefilter.model.schemaorg import Thing
from thefilter.model.timestamp import Timestamp
from thefilter.model.user import User
from thefilter.repositories import StubMetadataLiteRepository


class TestDDBItemMetadataUpdater(unittest.TestCase):
    def setUp(self):
        metadata_items = {
            "test_id_1": {
                "thing": {
                    "id": "test_id_1",
                    "brandId": "test_brandid_1",
                    "typeName": "Episode"
                }
            },
            "test_id_2": {
                "thing": {
                    "id": "test_id_2",
                    "brandId": "test_brandid_2",
                    "typeName": "Movie"
                }
            }
        }
        self._repo = StubMetadataLiteRepository(metadata_items)
        self._updater = DDBItemMetadataUpdater(self._repo)

    def test_set_item_metadata(self):
        identifier = Identifier(
            eventId="test_eventId",
            operationId="test_operationId"
        )
        customer = {"name": "example_customer"}
        user = User(userId="user", anonId="anon")
        timestamp = Timestamp(initiated=datetime(2024, 1, 1),
                              received=datetime(2024, 1, 1))
        action = "play"
        context = Context(application="test_application",
                          environment="test_environment",
                          server=None, site=None,
                          code_version="test_version")
        custom = {"space": "subcategory"}

        base_messages = [
            Message(
                identifier=identifier, customer=customer, user=user, timestamp=timestamp,
                action=action, context=context, custom=custom,
                thing=[
                    Thing(
                        id="test_id_1"
                    )
                ]
            ),
            Message(
                identifier=identifier, customer=customer, user=user, timestamp=timestamp,
                action=action, context=context, custom=custom,
                thing=[
                    Thing(
                        id="test_id_2"
                    )
                ]
            ),
            Message(
                identifier=identifier, customer=customer, user=user, timestamp=timestamp,
                action=action, context=context, custom=custom,
                thing=[
                    Thing(
                        id="unknown_thing_id"
                    )
                ]
            )
        ]

        expected_result = [
            Message(customer={'name': 'example_customer'},
                    identifier=Identifier(eventId='test_eventId',
                                          operationId='test_operationId'),
                    user=User(accountId=None, userId='user', anonId='anon',
                              primaryId='user', clusterId=None),
                    timestamp=Timestamp(initiated=datetime(2024, 1, 1, 0, 0),
                                        received=datetime(2024, 1, 1, 0, 0)),
                    action='play', context=Context(application='test_application',
                                                   environment='test_environment',
                                                   server=None, site=None,
                                                   code_version='test_version'), thing=[
                    Thing(id='test_id_1', brandId='test_brandid_1', brandName=None,
                          recommendationId=None, name='', alternateName=None,
                          typeName='Episode', genre=[], subGenre=[], contentRating=[],
                          actor=[], director=[], producer=[], crew=[], partOfSeason={},
                          partOfSeries={}, publication=[], keywords=[],
                          episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                          description=None, duration=None, datePublished=None,
                          image=None, inLanguage=None, isLiveBroadcast=None,
                          productionCompany=None, custom={}, space=None, isAdult=None,
                          isKids=None)], custom={'space': 'subcategory'},
                    sourceOperationId=None, messageAttributes=None),
            Message(customer={'name': 'example_customer'},
                    identifier=Identifier(eventId='test_eventId',
                                          operationId='test_operationId'),
                    user=User(accountId=None, userId='user', anonId='anon',
                              primaryId='user', clusterId=None),
                    timestamp=Timestamp(initiated=datetime(2024, 1, 1, 0, 0),
                                        received=datetime(2024, 1, 1, 0, 0)),
                    action='play', context=Context(application='test_application',
                                                   environment='test_environment',
                                                   server=None, site=None,
                                                   code_version='test_version'), thing=[
                    Thing(id='test_id_2', brandId='test_brandid_2', brandName=None,
                          recommendationId=None, name='', alternateName=None,
                          typeName='Movie', genre=[], subGenre=[], contentRating=[],
                          actor=[], director=[], producer=[], crew=[], partOfSeason={},
                          partOfSeries={}, publication=[], keywords=[],
                          episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                          description=None, duration=None, datePublished=None,
                          image=None, inLanguage=None, isLiveBroadcast=None,
                          productionCompany=None, custom={}, space=None, isAdult=None,
                          isKids=None)], custom={'space': 'subcategory'},
                    sourceOperationId=None, messageAttributes=None),
            Message(customer={'name': 'example_customer'},
                    identifier=Identifier(eventId='test_eventId',
                                          operationId='test_operationId'),
                    user=User(accountId=None, userId='user', anonId='anon',
                              primaryId='user', clusterId=None),
                    timestamp=Timestamp(initiated=datetime(2024, 1, 1, 0, 0),
                                        received=datetime(2024, 1, 1, 0, 0)),
                    action='play', context=Context(application='test_application',
                                                   environment='test_environment',
                                                   server=None, site=None,
                                                   code_version='test_version'), thing=[
                    Thing(id='unknown_thing_id', brandId=None, brandName=None,
                          recommendationId=None, name=None, alternateName=None,
                          typeName=None, genre=[], subGenre=[], contentRating=[],
                          actor=[], director=[], producer=[], crew=[], partOfSeason={},
                          partOfSeries={}, publication=[], keywords=[],
                          episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                          description=None, duration=None, datePublished=None,
                          image=None, inLanguage=None, isLiveBroadcast=None,
                          productionCompany=None, custom={}, space=None, isAdult=None,
                          isKids=None)], custom={'space': 'subcategory'},
                    sourceOperationId=None, messageAttributes=None)
        ]

        result = self._updater.set_item_metadata(base_messages)
        self.assertEqual(result, expected_result)
