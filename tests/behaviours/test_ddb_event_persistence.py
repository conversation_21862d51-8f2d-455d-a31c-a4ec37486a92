from copy import deepcopy
from decimal import Decimal
from unittest import TestCase

import freezegun

from thefilter.aws.dynamoDB import Simple<PERSON>tubPersister
from thefilter.behaviours.ddb_event_persistence import EventToDDBUserHistory
from thefilter.logs.logclient import NoOpLogger


class TestEventToDDBUserHistory(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._api = EventToDDBUserHistory(
            region='eu-west-2',
            environment='test_environment',
            customer='customer',
            ndjson_dir_path='tests/jobs/customer_event_loader/data',
            logger=NoOpLogger(),
            ddb_persister=SimpleStubPersister()
        )
        self._api._batch_size = 3

        self._test_event = {
            "user_userid": "test_user",
            "user_primaryid": "test_primary",
            "user_id": "user_id",
            "thing_id": "thing123",
            "thing_typename": "Movie",
            "action": "play",
            "identifier_eventid": "test_event",
            "timestamp_initiated": "2024-08-07T15:26:27+00:00",
            "timestamp_received": "2024-08-07T15:26:28+00:00",
            "brand_id": "thing123",
            "progress_pct": 10.0,
            "duration": 2.0
        }

    def test_list_files_in_dir(self):
        expected = [
            'tests/jobs/customer_event_loader/data/ndjson_dir_path/test_20210617000000.ndjson'
        ]
        files = self._api._list_files_in_dir()
        self.assertEqual(expected, files)

    @freezegun.freeze_time("2024-08-07T15:30:00+00:00")
    def test_format_for_ddb_userid(self):
        expected_event = {
            "userId": "test_user_userId",
            "timestampKey": Decimal(1723044387.0),
            "action": "play",
            "beaconId": "test_event",
            "timestampInitiated": "2024-08-07T15:26:27+00:00",
            "timestampReceived": "2024-08-07T15:26:28+00:00",
            "thingId": "thing123",
            "thingTypeName": "Movie",
            "timeToLiveEpoch": 1770478200,
            "brandId": "thing123",
            "progressPct": 10.0,
            "duration": 2.0
        }

        result = self._api.format_for_ddb(self._test_event)
        self.assertEqual(result, expected_event, "Formatted event matches")

    @freezegun.freeze_time("2024-08-07T15:30:00+00:00")
    def test_format_for_ddb_primaryid(self):
        test_event = deepcopy(self._test_event)
        del test_event["user_userid"]

        expected_event = {
            "userId": "test_primary_userId",
            "timestampKey": Decimal(1723044387.0),
            "action": "play",
            "beaconId": "test_event",
            "timestampInitiated": "2024-08-07T15:26:27+00:00",
            "timestampReceived": "2024-08-07T15:26:28+00:00",
            "thingId": "thing123",
            "thingTypeName": "Movie",
            "timeToLiveEpoch": 1770478200,
            "brandId": "thing123",
            "progressPct": 10.0,
            "duration": 2.0
        }

        result = self._api.format_for_ddb(test_event)
        self.assertEqual(result, expected_event, "Formatted event matches")

    @freezegun.freeze_time("2024-08-07T15:30:00+00:00")
    def test_format_for_ddb_missing_fields(self):
        test_event = deepcopy(self._test_event)
        del test_event["user_userid"]
        del test_event["user_primaryid"]
        del test_event["thing_id"]
        del test_event["thing_typename"]
        del test_event["progress_pct"]
        del test_event["duration"]

        expected_event = {
            "userId": "user_id_userId",
            "timestampKey": Decimal("1723044387.0"),
            "action": "play",
            "beaconId": "test_event",
            "timestampInitiated": "2024-08-07T15:26:27+00:00",
            "timestampReceived": "2024-08-07T15:26:28+00:00",
            "thingId": "thing123",
            "thingTypeName": "CreativeWork",
            "timeToLiveEpoch": 1770478200,
            "brandId": "thing123"
        }

        result = self._api.format_for_ddb(test_event)
        self.assertEqual(result, expected_event, "Formatted event matches")

    @freezegun.freeze_time("2024-08-07T15:30:00+00:00")
    def test_load(self):
        self.maxDiff = None
        expected_output = ("Persisting [{'userId': 'test_user_userId', "
                           "'timestampKey': Decimal('1723044387.0'), 'action': 'play', "
                           "'beaconId': 'test_event', 'timestampInitiated': "
                           "'2024-08-07T15:26:27+00:00', 'timestampReceived': "
                           "'2024-08-07T15:26:28+00:00', 'thingId': 'thing123', "
                           "'thingTypeName': 'Movie', 'timeToLiveEpoch': 1770478200, "
                           "'brandId': 'thing123', 'progressPct': Decimal('10.0'), 'duration': Decimal('2.0')}] to "
                           "dynamodb.Table(name='test_environment-customer-user_history')")

        self._api.persist_from_events([self._test_event])

        self.assertEqual(
            expected_output,
            self._api._ddb_persister._persist_info
        )
