from unittest import TestCase

from thefilter.behaviours.genre_matcher.customers.example import example_genre_map
from thefilter.behaviours.genre_matcher.genre_matcher import GenreMatcher


class TestGenreMatcher(TestCase):
    def test_example_genre_matcher(self):
        self._api = GenreMatcher(example_genre_map.genre_map)

        user_history = [
            ['Thriller', 'Crime', 'Drama', 'Mystery'],
            ['Drama', 'Romance'],
            ['Documentary', 'History', 'Australian Television'],
            ['Biography', 'Drama', 'Music'],
            ['Dark Comedy', 'Drama'],
            ['Crime', 'Thriller'],
            ['Animation', 'Kids', 'Comedy'],
            ['Comedy']
        ]

        selected_items = [
            ['Mystery', 'Drama', 'Crime'],
            ['Biography', 'Music', 'Drama'],
            ['Mystery', 'Drama', 'Crime'],
            ['Drama', 'Comedy'],
            ['Drama', 'World Movies', 'Foreign Language'],
            ['Drama', 'Australian Television'],
            ['Sport', 'Motorsport'],
            ['Comedy', 'Drama', 'Romance', 'World Movies', 'Foreign Language'],
            ['Mystery', 'Drama', 'Crime'],
            ['Action', 'Drama'],
            ['Mystery', 'Drama', 'Crime']
        ]

        ordered = self._api.reorder(user_history, selected_items)
        expected = [3, 0, 2, 8, 10, 7, 9, 5, 1, 4, 6]
        self.assertEqual(expected, ordered)

    def test_no_history_no_reorder(self):
        self._api = GenreMatcher(example_genre_map.genre_map)

        user_history = [
        ]

        selected_items = [
            ['Crime'],
            ['Drama'],
            ['Unknown'],
            ['Comedy']
        ]

        ordered = self._api.reorder(user_history, selected_items)
        expected = [0, 1, 2, 3]
        self.assertEqual(expected, ordered)

    def test_unknown_genres(self):
        self._api = GenreMatcher(example_genre_map.genre_map)

        user_history = [
            ['Unknown', 'Crime', 'Drama', 'Mystery'],
            []
        ]

        selected_items = [
            ['Mystery', 'Drama', 'Crime'],
            ['Biography', 'Unknown', 'Drama'],
            [],
            ['Mystery', 'Drama'],
        ]

        ordered = self._api.reorder(user_history, selected_items)
        expected = [0, 3, 1, 2]
        self.assertEqual(expected, ordered)
