from unittest import TestCase

from thefilter.behaviours.genre_reordering.genre_reordering import GenreReordering


class TestGenreReordering(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._api = GenreReordering()

    def test_reorder_by_genre(self):
        rec_genres_map = {
            'a1': ['GENRE_1'],
            'a2': ['GENRE_x'],  # even
            'a3': ['GENRE_x'],
            'a4': ['GENRE_x'],  # even
            'a5': ['GENRE_2'],
            'a6': ['GENRE_x'],  # even
            'a7': ['GENRE_1', 'GENRE_x'],
            'a8': ['GENRE_2'],  # even
            'a9': ['GENRE_2'],
            'a10': ['GENRE_3'],  # even
            'a11': ['GENRE_x'],
            'a12': ['GENRE_1'],
        }
        user_history_genres_map = {
            'b1': ['GENRE_1'],
            'b2': ['GENRE_1'],
            'b3': ['GENRE_1'],
            'b4': ['GENRE_1'],
            'b5': ['GENRE_1'],
            'b6': ['GENRE_2'],
            'b7': ['GENRE_2'],
            'b8': ['GENRE_2'],
            'b9': ['GENRE_3'],
        }

        result = self._api.reorder_by_genre(
            rec_genres_map, user_history_genres_map, depth="6"
        )
        expected = [
            'a1', 'a2', 'a12', 'a3', 'a5', 'a4', 'a8', 'a6', 'a9', 'a10', 'a7', 'a11'
        ]
        self.assertEqual(expected, result)

    def test_reorder_by_genre_when_no_user_history(self):
        rec_genres_map = {
            'a1': ['GENRE_1'],
            'a2': ['GENRE_x'],  # even
            'a3': ['GENRE_x'],
            'a4': ['GENRE_x'],  # even
            'a5': ['GENRE_2'],
            'a6': ['GENRE_x'],  # even
            'a7': ['GENRE_1', 'GENRE_x'],
            'a8': ['GENRE_2'],  # even
            'a9': ['GENRE_2'],
            'a10': ['GENRE_3'],  # even
            'a11': ['GENRE_x'],
            'a12': ['GENRE_1'],
        }

        # Be aware that the code before this will just return the original results,
        # as using this method will still interleave the results.
        user_history_genres_map = {}

        result = self._api.reorder_by_genre(
            rec_genres_map, user_history_genres_map, depth="6"
        )

        expected = [
            'a1', 'a7', 'a2', 'a8', 'a3', 'a9', 'a4', 'a10', 'a5', 'a11', 'a6', 'a12'
        ]

        self.assertEqual(expected, result)

    def test_reorder_by_genre_when_genres_missing(self):
        rec_genres_map = {
            'a1': ['GENRE_1'],
            'a2': ['GENRE_x'],  # even
            'a3': ['GENRE_x'],
            'a4': ['GENRE_x'],  # even
            'a5': ['GENRE_2'],
            'a6': ['GENRE_x'],  # even
            'a7': ['GENRE_1', 'GENRE_x'],
            'a8': ['GENRE_2'],  # even
            'a9': ['GENRE_2'],
            'a10': ['GENRE_3'],  # even
            'a11': ['GENRE_x'],
            'a12': ['GENRE_1'],
        }
        user_history_genres_map = {
            'b1': [None],
            'b2': [],
            'b3': ['GENRE_1', None],
        }

        result = self._api.reorder_by_genre(
            rec_genres_map, user_history_genres_map, depth="6"
        )

        expected = [
            'a1', 'a5', 'a12', 'a6', 'a7', 'a8', 'a2', 'a9', 'a3', 'a10', 'a4', 'a11'
        ]

        self.assertEqual(expected, result)
