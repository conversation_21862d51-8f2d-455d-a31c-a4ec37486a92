import logging
from copy import deepcopy
from datetime import datetime, timezone
from unittest import <PERSON><PERSON><PERSON>
from unittest.mock import Mock

from tests.inference.test_models import TestModel
from thefilter.aws.sqs import StubSqsPublisher
from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.personalisation import PersonalisationAPI, \
    TersePersonalisationResponseFormatter
from thefilter.behaviours.recommendation import StubRecommendationRepository
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.inference.logic.configuration import InvocationResult, ModelDefinition, \
    Fulfilment, RangeFulfilment, InvocationItem
from thefilter.inference.logic.invokable_models import StubModelFactory, StubModel, \
    AutoPlayMLTModel
from thefilter.inference.logic.query_builder import ElasticSearchQueryBuilder
from thefilter.inference.models import RichModelInvoker
from thefilter.logs.logclient import NoOpLogger
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.context import Context
from thefilter.model.customer import Customer
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubRecipeConfigurationRepository, \
    StubEntityAssociationRepository, StubChartReadRepository
from thefilter.model.selectors import HashingExperimentSelector
from thefilter.repositories import StubMetadataRepository, StubSlotRepository, \
    StorageUserRepository, StubEditorialRepository, \
    StubPromotionRepository, StubMetadataLiteRepository
from thefilter.rules.conditions_evaluator import RulesetConditionsEvaluator, \
    ParameterConditionEvaluator, ElasticSearchSeedConditionEvaluator, \
    UserConditionEvaluator
from thefilter.rules.expressions import ExpressionEvaluator, ExpressionHandlerFactory
from thefilter.rules.query_string_visitor import QueryStringVisitor
from thefilter.rules.rule_evaluator import CodeRuleSetEvaluator
from thefilter.rules.ruleset_builder import RulesetBuilder, RuleInterpreter
from thefilter.rules.ruleset_visitor import RulesetProcessor
from thefilter.rules.seed_visitor import ElasticSearchSeedVisitor
from thefilter.rules.time_ruleset_visitor import TimeRulesetVisitor
from thefilter.rules.user_visitor import UserVisitor


class TestPersonalisationAPI(TestCase):
    """See tests/test_api_personalisation.py for a driver that accesses the cloud"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._logger = NoOpLogger()
        self._api = self.set_up_personalisation_api()
        self._customer = "test_customer"
        self._context = Context(
            application=None,
            environment=None,
            server=None,
            site=None,
            code_version='test_version'
        )
        self._test_request = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'ignore': 'test_ignore',
                'seedIds': 'test_seed_id',
                'userId': 'test_user_id'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )

        self._zero_results_no_log_test_request = Request(
            rule_id='zero_results_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'ignore': 'test_ignore',
                'seedIds': 'test_seed_id',
                'userId': 'test_user_id'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )

        self._zero_results_log_test_request = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'seedIds': 'test_seed_id',
                'userId': 'test_user_id'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )

        self._zero_results_ignore_test_request = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'ignore': 'test_ignore',
                'seedIds': 'test_seed_id',
                'userId': 'ignore_user_id'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )

    def set_up_personalisation_api(self):
        """Here we go..."""
        user_repository = Mock()
        tracer_factory = NoOpTracerFactory()
        user_history = [
            {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
             'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [
                 {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
            {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
             'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [{'id': 'series_4_episode_4', 'space': 'brand',
                        'typeName': 'TVEpisode'}]},
            {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
             'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                           'received': '2021-08-24T10:01:14+0000'},
             'thing': [{'id': 'movie_123', 'space': '', 'typeName': 'Movie'}]},
            {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
             'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]}
        ]
        entity_associations = [
            {
                "id": "test_id_0",
                "entityId": "test_entity_id",
                "entityName": "Hero",
                "entityType": "group",
                "thingId": "test_id",
                "thingName": "test_thingName",
                "thingType": "Movie"
            }
        ]
        promo_repo_contents = [
            {
                "promotionId": "5b7faa13-4814-4b54-ba10-8fa4881c3e59",
                "promotionName": "Avengers Assembled",
                "slot": "all",
                "endDate": "2021-07-22T10:29:46+00:00",
                "startDate": "2021-06-22T10:29:46+00:00",
                "createdBy": "user",
                "dateCreated": "2021-06-22T10:29:46+00:00",
                "updatedBy": "user",
                "dateUpdated": "2021-06-22T10:29:46+00:00",
                "content": {
                    "metadata": [
                        {
                            "id": "test_id",
                            "position": 0.0
                        }
                    ]
                }
            }
        ]
        slots = {
            'test_slot_id': {
                "slotId": "test_slot_id",
                "createdBy": "auto_ES_to_DDB",
                "dateCreated": "2021-07-06T11:32:09+00:00",
                "dateUpdated": "2021-07-06T11:32:09+00:00",
                "fallbackChart": "uktv_trending_drama",
                "lastUpdatedBy": "auto_ES_to_DDB",
                "experiments": "[{\"id\": \"c9e94f66-0135-46bd-9c4e-2f0091eef87f\", \"userPercentage\": 100, \"isBaseRecipe\": true, \"size\": 12, \"title\": \"UKTV Trending Drama\", \"notes\": \"\", \"modelDefinitions\": [{\"key\": \"uktv_trending_drama\", \"version\": 1, \"source\": \"PrebuiltModel\", \"fulfilment\": {\"ranges\": [{\"start\": 0, \"end\": 11}]}, \"parameters\": {\"deduplicate_path\": \"custom.parentBrandId\"}}]}]",
                "slotName": "UKTV Trending Drama"
            },
            'fallback_slot_id': {
                "slotId": "fallback_slot_id",
                "createdBy": "auto_ES_to_DDB",
                "dateCreated": "2021-07-06T11:32:09+00:00",
                "dateUpdated": "2021-07-06T11:32:09+00:00",
                "fallbackChart": "all",
                "lastUpdatedBy": "auto_ES_to_DDB",
                "experiments": "[{\"id\": \"3fb98e4a-524b-4c3d-884c-bd3f0b35b793\", \"userPercentage\": 100, \"isBaseRecipe\": true, \"size\": 49, \"title\": \"Fallback\", \"notes\": \"\", \"modelDefinitions\": [{\"key\": \"StubModel\", \"version\": 1, \"source\": \"StubModel\", \"fulfilment\": {\"ranges\": [{\"start\": 0, \"end\": 49}]}}]}]",
                "slotName": "Fallback"
            },
            'autoplay_slot_id': {
                "slotId": "autoplay_slot_id",
                "createdBy": "dev-adam-poolman",
                "dateCreated": "2022-04-27T11:38:05+01:00",
                "dateUpdated": "2022-04-27T11:38:05+01:00",
                "fallbackChart": "",
                "lastUpdatedBy": "dev-adam-poolman",
                "experiments": "[\n  {\n    \"id\": \"37f69ff7-c80e-472e-8870-d496fea6f2f0\",\n    \"recipe\": {\n      \"id\": \"6b9b8409-d25a-4ba0-bbdb-275db2b64bf5\",\n      \"name\": null\n    },\n    \"userPercentage\": 100,\n    \"isBaseRecipe\": true,\n    \"size\": 1,\n    \"title\": \"AutoPlay MLT Model\",\n    \"notes\": \"PrebuiltModel version_20220317\",\n    \"modelDefinitions\": [\n      {\n        \"key\": \"version_20220317\",\n        \"key_strategy\": {\n          \"name\": \"UKTVMLTKeyStrategy\"\n        },\n        \"version\": 1,\n        \"source\": \"AutoPlayMLTModel\",\n        \"fulfilment\": {\n          \"ranges\": [\n            {\n              \"start\": 0,\n              \"end\": 1\n            }\n          ]\n        },\n        \"parameters\": {\n          \"useSeedId\": true,\n          \"deduplicate_path\": \"\",\n          \"returnType\": \"TVEpisode\"\n        }\n      }\n    ]\n  }\n]",
                "slotName": "UKTV AutoPlay End-of-Series"
            },
            'autoplay_score_slot_id': {
                "slotId": "autoplay_score_slot_id",
                "createdBy": "dev-adam-poolman",
                "dateCreated": "2022-04-27T11:38:05+01:00",
                "dateUpdated": "2022-04-27T11:38:05+01:00",
                "fallbackChart": "",
                "lastUpdatedBy": "dev-adam-poolman",
                "experiments": "[\n  {\n    \"id\": \"37f69ff7-c80e-472e-8870-d496fea6f2f0\",\n    \"recipe\": {\n      \"id\": \"6b9b8409-d25a-4ba0-bbdb-275db2b64bf5\",\n      \"name\": null\n    },\n    \"userPercentage\": 100,\n    \"isBaseRecipe\": true,\n    \"size\": 1,\n    \"title\": \"AutoPlay MLT Model\",\n    \"notes\": \"PrebuiltModel version_20220317\",\n    \"modelDefinitions\": [\n      {\n        \"key\": \"version_20220317\",\n        \"key_strategy\": {\n          \"name\": \"UKTVMLTKeyStrategy\"\n        },\n        \"version\": 1,\n        \"source\": \"AutoPlayMLTModel\",\n        \"fulfilment\": {\n          \"ranges\": [\n            {\n              \"start\": 0,\n              \"end\": 1\n            }\n          ]\n        },\n        \"parameters\": {\n          \"useSeedId\": true,\n          \"deduplicate_path\": \"\",\n          \"returnType\": \"TestScore\"\n        }\n      }\n    ]\n  }\n]",
                "slotName": "UKTV AutoPlay End-of-Series"
            },
            'use_recommendation_id_slot_id': {
                "slotId": "use_recommendation_id_slot_id",
                "createdBy": "dev-adam-poolman",
                "dateCreated": "2022-04-27T11:38:05+01:00",
                "dateUpdated": "2022-04-27T11:38:05+01:00",
                "fallbackChart": "",
                "lastUpdatedBy": "test_dev",
                "experiments": "[{\"id\": \"c9e94f66-0135-46bd-9c4e-2f0091eef87f\", \"userPercentage\": 100, \"isBaseRecipe\": true, \"size\": 12, \"title\": \"UKTV Trending Drama\", \"notes\": \"\", \"modelDefinitions\": [{\"key\": \"uktv_trending_drama\", \"version\": 1, \"source\": \"AutoPlayMLTModel\", \"fulfilment\": {\"ranges\": [{\"start\": 0, \"end\": 11}]}, \"parameters\": {\"deduplicate_path\": \"custom.parentBrandId\", \"use_recommendation_id\": true}}]}]",
                "slotName": "Swap out RecommendationId"
            },
            'zero_results_slot_id': {
                "slotId": "use_recommendation_id_slot_id",
                "createdBy": "dev-adam-poolman",
                "dateCreated": "2024-01-08T13:38:05+01:00",
                "dateUpdated": "2024-01-08T13:38:05+01:00",
                "fallbackChart": "",
                "lastUpdatedBy": "test_dev",
                "experiments": "[]",
                "slotName": "Zero Results Ok slot",
                "zeroResultsOk": True
            }
        }
        items = [
            {
                "id": f"item_{i}"
            }
            for i in range(10)
        ]

        expected_items = [
            "test_seed_id",
            "item_9"
        ]

        metadata_lite_items = {}

        for item in items:
            metadata_lite_items[item["id"]] = {
                "thing": {
                    "id": item["id"],
                    "name": item["id"],
                    "typeName": "Movie"
                }
            }

        for item in expected_items:
            metadata_lite_items[item] = {
                "thing": {
                    "id": item,
                    "name": item,
                    "typeName": "Movie"
                }
            }

        metadata_lite_items["stub_autoplay_first_episode_id"] = {
            "thing": {
                "id": "stub_autoplay_first_episode_id",
                "name": "stub_autoplay_first_episode_id",
                "typeName": "TVEpisode"
            }
        }

        metadata_repository = StubMetadataRepository(
            get_item_response={'thing': {
                "id": 'test_seed_id',
                "brandId": 'test_brandid',
                "name": '2001: A Space Odyssey',
                "typeName": "Movie",
                "recommendationId": "thing_recommendation_id"
            }
            },
            get_items_response={
                item["id"]: {
                    "thing": {
                        "id": item["id"],
                        "name": item["id"],
                        "typeName": "Movie"
                    }
                }
                for item in items
                if item in expected_items
            }
        )
        metadata_lite_repository = StubMetadataLiteRepository(metadata_lite_items)
        chart_read_repository = StubChartReadRepository()
        ruleset_builder = RulesetBuilder(
            StubRecipeConfigurationRepository("test_rule_folder"),
            RuleInterpreter()
        )
        ruleset_processor = RulesetProcessor(
            [
                TimeRulesetVisitor(
                    ExpressionEvaluator(ExpressionHandlerFactory())),
                QueryStringVisitor(),
                ElasticSearchSeedVisitor(metadata_repository),
                UserVisitor(
                    user_repository=user_repository,
                    metadata_repository=metadata_repository,
                    rule_evaluator=CodeRuleSetEvaluator(
                        metadata_repository=metadata_repository),
                    tracer_factory=tracer_factory)
            ],
            RulesetConditionsEvaluator(
                ParameterConditionEvaluator(),
                ElasticSearchSeedConditionEvaluator(
                    metadata_repository=metadata_repository
                ),
                UserConditionEvaluator()
            ),
            tracer_factory)
        model_invoker = RichModelInvoker(
            tracer_factory=tracer_factory,
            metadata_repository=metadata_repository,
            metadata_lite_repository=metadata_lite_repository,
            model_factory=StubModelFactory({
                "TestModel": TestModel(
                    things_to_return=[],
                    customer="test_customer",
                    tracer_factory=NoOpTracerFactory(),
                    metadata_repository=metadata_repository,
                    metadata_lite_repository=metadata_lite_repository,
                    user_repository=user_repository
                ),
                "StubModel": StubModel(
                    customer="test_customer",
                    tracer_factory=NoOpTracerFactory(),
                    metadata_repository=metadata_repository,
                    user_repository=user_repository),
                "AutoPlayMLTModel": AutoPlayMLTModel(
                    query_builder=ElasticSearchQueryBuilder(),
                    metadata_repository=metadata_repository,
                    metadata_lite_repository=metadata_lite_repository,
                    tracer_factory=tracer_factory,
                    user_repository=user_repository,
                    page_size=1,
                    endpoint='test_endpoint',
                    customer='test_customer',
                    chart_read_repository=StubChartReadRepository(),
                    editorial_repository=StubEditorialRepository(),
                    recommendation_repository=StubRecommendationRepository(
                        results_to_return=[
                            {
                                "id": "test_item_0",
                                "typeName": "TVSeries",
                                "score": 0.999
                            },
                            {
                                "id": "test_item_1",
                                "typeName": "Movie",
                                "score": 0.876
                            }
                        ]
                    ),
                    key_strategy_factory=KeyStrategyFactory()
                ),

            }),
            chart_read_repository=chart_read_repository
        )
        tracer_factory = tracer_factory
        publisher = StubSqsPublisher("test_dead_letter_queue", "test_fast_lane_queue")
        customer = "test_customer"
        code_version = "test_code_version"
        slot_repository = StubSlotRepository(slots=slots)
        experiment_selector = HashingExperimentSelector()
        user_repository = StorageUserRepository(user_history)
        metadata_repository = metadata_repository
        editorial_repository = StubEditorialRepository()
        entity_association_repository = StubEntityAssociationRepository(
            entity_associations=entity_associations
        )
        metadata_lite_repository = StubMetadataLiteRepository()
        response_formatter = TersePersonalisationResponseFormatter()
        promotion_repository = StubPromotionRepository(promo_repo_contents)
        key_strategy_factory = KeyStrategyFactory()

        customer_config = CentralisedConfig(
            region="eu-west-2",
            environment="pre",
            logger=self._logger,
            repo_type="local"  # Remove this if you want to check the DDB Config Table
        )

        personalisation_api = PersonalisationAPI(
            ruleset_builder,
            ruleset_processor,
            model_invoker,
            tracer_factory,
            publisher,
            customer,
            code_version,
            slot_repository,
            experiment_selector,
            user_repository,
            metadata_repository,
            editorial_repository,
            entity_association_repository,
            metadata_lite_repository,
            response_formatter,
            promotion_repository,
            key_strategy_factory,
            chart_read_repository,
            self._logger,
            customer_config
        )

        return personalisation_api

    def test_get_response_with_unknown_slot(self):
        expected = {
            'code': 500, 'responseId': '916f578e-3e53-4b7b-a6f5-e7a032372c12',
            'title': '', 'items': [], 'errors': [
                {'message': 'Internal Server Error', 'severity': 'CriticalError',
                 'details': [{'message': 'Internal Server Error',
                              'severity': 'CriticalError'}]}]
        }

        self._api._slot_repository = StubSlotRepository(slots={})
        response = self._api.get_response(request=self._test_request,
                                          rule_version="test_rule_version",
                                          context=self._context,
                                          customer=Customer.get(self._customer))
        response['responseId'] = expected['responseId']

        self.assertEqual(expected, response)

    def test_get_successful_empty_response(self):
        expected = {
            'code': 200,
            'responseId': '916f578e-3e53-4b7b-a6f5-e7a032372c12',
            'title': '',
            'items': []
        }

        response = self._api.get_response(request=self._test_request,
                                          rule_version="test_rule_version",
                                          context=self._context,
                                          customer=Customer.get(self._customer))
        response['responseId'] = expected['responseId']

        self.assertEqual(expected, response)

    def test_get_successful_fallback_response(self):
        expected = {
            'code': 200,
            'responseId': '916f578e-3e53-4b7b-a6f5-e7a032372c12',
            'title': '',
            'items': [{'id': f"{self._customer}_movie_1", 'typeName': 'Movie'},
                      {'id': f"{self._customer}_movie_2", 'typeName': 'Movie'},
                      {'id': f"{self._customer}_tv_series_A", 'typeName': 'TVSeries'},
                      {'id': f"{self._customer}_movie_3", 'typeName': 'Movie'},
                      {'id': f"{self._customer}_movie_4", 'typeName': 'Movie'},
                      {'id': f"{self._customer}_tv_series_B", 'typeName': 'TVSeries'}],
            'modelInfo': {
                'seed': {
                    'id': 'test_seed_id',
                    'name': '2001: A Space Odyssey',
                    'title': ''
                }
            }
        }

        self._test_request.rule_id = 'fallback_slot_id'

        response = self._api.get_response(request=self._test_request,
                                          rule_version="test_rule_version",
                                          context=self._context,
                                          customer=Customer.get(self._customer))
        response['responseId'] = expected['responseId']

        self.assertEqual(expected, response)

    def test_get_uktv_autoplay_response(self):
        expected = {
            'code': 200,
            'responseId': 'ab9ab79d-adc1-44f0-9289-bd44b72fbaab',
            'title': '',
            'items': [{'id': 'stub_autoplay_first_episode_id', 'typeName': 'TVEpisode'}]
        }

        self._test_request.rule_id = 'autoplay_slot_id'

        response = self._api.get_response(request=self._test_request,
                                          rule_version="test_rule_version",
                                          context=self._context,
                                          customer=Customer.get(self._customer))

        response['responseId'] = expected['responseId']

        self.assertEqual(expected, response)

    def test_use_recommendation_id_but_no_swap(self):
        model_definitions = [
            ModelDefinition(key='version_20210805', version=1, source='AutoPlayMLTModel',
                            fulfilment=Fulfilment(
                                ranges=[RangeFulfilment(start=0, end=50)]),
                            parameters={'useSeedId': True, 'deduplicate_path': '',
                                        'returnType': 'TVEpisode'}, block=None,
                            key_strategy={'name': 'UKTVMLTKeyStrategy'})
        ]
        invocation_result = InvocationResult(
            model=ModelDefinition(key='version_20210805', version=1,
                                  source='AutoPlayMLTModel', fulfilment=Fulfilment(
                    ranges=[RangeFulfilment(start=0, end=50)]),
                                  parameters={'useSeedId': True, 'deduplicate_path': '',
                                              'returnType': 'TVEpisode'}, block=None,
                                  key_strategy={'name': 'UKTVMLTKeyStrategy'}),
            model_results=[
                InvocationItem(thing={
                    'image': {'typeName': 'ImageObject',
                              'url': 'https://uktv-res.cloudinary.com/image/upload/v1521560559/rfnvyw64d8r6s35ndrpv.jpg'},
                    'partOfSeason': {'name': '4', 'id': 4466},
                    'keywords': ['police', 'crime',
                                 'drama'], 'director': [],
                    'custom': {'subGenre': [],
                               'durationMinutes': 45,
                               'subCategory': [
                                   {'name': 'Crime Drama',
                                    'id': 7}],
                               'channel': {'name': 'Drama',
                                           'id': 4646},
                               'active': {
                                   'stateUpdateTimestamp': '2022-08-18T15:13:06+00:00',
                                   'state': True},
                               'category': [
                                   {'name': 'Drama',
                                    'id': 2}], 'brand': [
                            {'name': 'Bergerac',
                             'id': 273}], 'modelInfo': {
                            'modelName': 'version_20210805',
                            'positionInModel': 0,
                            'positionOverall': 0}},
                    'typeName': 'Episode',
                    'description': "Harry Hoffman might be Hollywood's favourite `German', but he is nobody else's. Threats to his life come as no surprise, least of all his fellow actors.",
                    'episodeNumber': 8, 'actor': [
                        {'name': 'Terence Alexander', 'id': None},
                        {'name': 'Deborah Grant', 'id': None},
                        {'name': 'John Nettles', 'id': None},
                        {'name': 'Sean Arnold', 'id': None}], 'duration': None,
                    'datePublished': None, 'partOfSeries':
                        {'name': 'Bergerac', 'id': 273}, 'publication': [
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'youview', 'id': None,
                         'startDate': '2022-06-06T08:40:00'},
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'mobile', 'id': None,
                         'startDate': '2022-06-06T08:40:00'},
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'web',
                         'id': None, 'startDate': '2022-06-06T08:40:00'}],
                    'name': 'Sins of the Fathers',
                    'genre': [], 'contentRating': [
                        {'name': 'PG', 'id': None}], 'id': 'LDLH179A', 'crew': [],
                    'subGenre': [], 'numberOfSeasons': None,
                    'inLanguage': None,
                    'alternateName': None,
                    'seasonNumber': None,
                    'brandId': '273_brand', 'producer': [],
                    'isLiveBroadcast': None},
                    position_in_model_result=0)], model_info=None,
            is_empty=False
        )
        copied_invocation_result = deepcopy(invocation_result)
        updated_invocation_result = self._api._check_and_use_recommendation_id(
            model_definitions,
            copied_invocation_result
        )
        self.assertEqual(invocation_result, updated_invocation_result)

    def test_use_recommendation_id_with_swap(self):
        model_definitions = [
            ModelDefinition(key='version_20210805', version=1, source='AutoPlayMLTModel',
                            fulfilment=Fulfilment(
                                ranges=[RangeFulfilment(start=0, end=50)]),
                            parameters={'useSeedId': True,
                                        'deduplicate_path': '',
                                        'use_recommendation_id': True,
                                        'returnType': 'TVEpisode'}, block=None,
                            key_strategy={'name': 'UKTVMLTKeyStrategy'})
        ]
        invocation_result = InvocationResult(
            model=ModelDefinition(key='version_20210805', version=1,
                                  source='AutoPlayMLTModel', fulfilment=Fulfilment(
                    ranges=[RangeFulfilment(start=0, end=50)]),
                                  parameters={'useSeedId': True, 'deduplicate_path': '',
                                              'returnType': 'TVEpisode'}, block=None,
                                  key_strategy={'name': 'UKTVMLTKeyStrategy'}),
            model_results=[
                InvocationItem(thing={
                    'recommendationId': 'test_recommendation_id',
                    'image': {'typeName': 'ImageObject',
                              'url': 'https://uktv-res.cloudinary.com/image/upload/v1521560559/rfnvyw64d8r6s35ndrpv.jpg'},
                    'partOfSeason': {'name': '4', 'id': 4466},
                    'keywords': ['police', 'crime',
                                 'drama'], 'director': [],
                    'custom': {'subGenre': [],
                               'durationMinutes': 45,
                               'subCategory': [
                                   {'name': 'Crime Drama',
                                    'id': 7}],
                               'channel': {'name': 'Drama',
                                           'id': 4646},
                               'active': {
                                   'stateUpdateTimestamp': '2022-08-18T15:13:06+00:00',
                                   'state': True},
                               'category': [
                                   {'name': 'Drama',
                                    'id': 2}], 'brand': [
                            {'name': 'Bergerac',
                             'id': 273}], 'modelInfo': {
                            'modelName': 'version_20210805',
                            'positionInModel': 0,
                            'positionOverall': 0}},
                    'typeName': 'Episode',
                    'description': "Harry Hoffman might be Hollywood's favourite `German', but he is nobody else's. Threats to his life come as no surprise, least of all his fellow actors.",
                    'episodeNumber': 8, 'actor': [
                        {'name': 'Terence Alexander', 'id': None},
                        {'name': 'Deborah Grant', 'id': None},
                        {'name': 'John Nettles', 'id': None},
                        {'name': 'Sean Arnold', 'id': None}], 'duration': None,
                    'datePublished': None, 'partOfSeries':
                        {'name': 'Bergerac', 'id': 273}, 'publication': [
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'youview', 'id': None,
                         'startDate': '2022-06-06T08:40:00'},
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'mobile', 'id': None,
                         'startDate': '2022-06-06T08:40:00'},
                        {'notes': 'vod', 'endDate': '2022-07-06T07:40:00',
                         'custom': {'vodAvailability': ['NoMatch'],
                                    'linearAvailability': ['NoMatch']}, 'name': None,
                         'publishedOn': [{'name': 'drama', 'id': 4646}],
                         'location': 'web',
                         'id': None, 'startDate': '2022-06-06T08:40:00'}],
                    'name': 'Sins of the Fathers',
                    'genre': [], 'contentRating': [
                        {'name': 'PG', 'id': None}], 'id': 'LDLH179A', 'crew': [],
                    'subGenre': [], 'numberOfSeasons': None,
                    'inLanguage': None,
                    'alternateName': None,
                    'seasonNumber': None,
                    'brandId': '273_brand', 'producer': [],
                    'isLiveBroadcast': None},
                    position_in_model_result=0)], model_info=None,
            is_empty=False
        )
        copied_invocation_result = deepcopy(invocation_result)
        updated_invocation_result = self._api._check_and_use_recommendation_id(
            model_definitions,
            copied_invocation_result
        )

        self.assertEqual('test_recommendation_id',
                         updated_invocation_result.model_results[0].thing.get('id'))

    def test_zero_results_reporting(self):

        # Verify that a slot with `zeroResultsOk` = True doesn't log out
        with self.assertNoLogs(self._logger, logging.INFO):
            self._api.zero_results_reporting(
                invocation_result=InvocationResult(
                    model=ModelDefinition(
                        key='stub_model', version=1,
                        source='StubModel', fulfilment=Fulfilment(
                            ranges=[RangeFulfilment(start=0, end=50)]
                        ),
                        parameters={}, block=None, key_strategy=None),
                    model_results=[], is_empty=True),
                operation_id="test_op_id",
                request=self._zero_results_no_log_test_request
            )

        # Verify that a slot with `zeroResultsOk` = False does log out
        with self.assertNoLogs(self._logger, logging.INFO):
            self._api.zero_results_reporting(
                invocation_result=InvocationResult(
                    model=ModelDefinition(
                        key='stub_model', version=1,
                        source='StubModel', fulfilment=Fulfilment(
                            ranges=[RangeFulfilment(start=0, end=50)]
                        ),
                        parameters={}, block=None, key_strategy=None),
                    model_results=[], is_empty=True),
                operation_id="test_op_id_2",
                request=self._zero_results_log_test_request
            )

    def test_zero_results_ignore_reporting(self):
        # Verify that a request with an ignore flag does not log out
        with self.assertNoLogs(self._logger, logging.INFO):
            self._api.zero_results_reporting(
                invocation_result=InvocationResult(
                    model=ModelDefinition(
                        key='stub_model', version=1,
                        source='StubModel', fulfilment=Fulfilment(
                            ranges=[RangeFulfilment(start=0, end=50)]
                        ),
                        parameters={}, block=None, key_strategy=None),
                    model_results=[], is_empty=True),
                operation_id="test_op_id",
                request=self._zero_results_ignore_test_request
            )

    def test_get_slot_title(self):
        mlt_slot_title = self._api._get_slot_title(slot={"type": "mlt"})
        self.assertEqual(mlt_slot_title, "More Like {seed_name}",
                         "MLT slot title matches")

        byw_slot_title = self._api._get_slot_title(slot={"type": "byw"})
        self.assertEqual(byw_slot_title, "Because You Watched {seed_name}",
                         "BYW slot title matches")

        other_slot_title = self._api._get_slot_title(slot={"type": "other"})
        self.assertEqual(other_slot_title, None, "Other slot title matches")

    def test_get_response_with_score(self):

        expected = {
            'code': 200,
            'responseId': 'ab9ab79d-adc1-44f0-9289-bd44b72fbaab',
            'title': '',
            'items': [{
                'id': 'test_item_0',
                'score': 0.999,
                'typeName': 'TVSeries'
            }]
        }

        test_request = deepcopy(self._test_request)
        test_request.rule_id = 'autoplay_score_slot_id'

        response = self._api.get_response(request=test_request,
                                          rule_version="test_rule_version",
                                          context=self._context,
                                          customer=Customer.get(self._customer))

        response['responseId'] = expected['responseId']

        self.assertEqual(expected, response)
