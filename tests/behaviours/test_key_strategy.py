from typing import Union, List
from unittest import TestCase
import datetime

from thefilter.behaviours.key_strategy import KeyStrategyFactory, KeyStrategy, \
    RecommendationKeyStrategy, Subscriber<PERSON>eyStrategy, TimeRangedKeyStrategy, \
    UKTVMLTKeyStrategy, SeedSubscriberKeyStrategy, \
    SubscriberPositionalTimeRangedKeyStrategy
from thefilter.inference.logic.configuration import ModelDefinition, \
    Fulfilment, RangeFulfilment
from thefilter.model.messages.request import Request


class TestKeyStrategyFactory(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.key_strategy_factory = KeyStrategyFactory()

    def infer_key(self, prioritised_model, request):
        key_strategy = self.key_strategy_factory.get_key_strategy(prioritised_model)
        key = key_strategy.generate_key(request=request,
                                        strategy=prioritised_model.key_strategy)
        return key

    def infer_positional_key(self, prioritised_model, request):
        key_strategy = self.key_strategy_factory.get_key_strategy(prioritised_model)
        key = key_strategy.generate_positional_key(request=request,
                                                   model_definition=prioritised_model)
        return key

    def test_get_key_strategy(self):
        request = Request(rule_id='a0182e54-72ee-4180-8a02-8b4289a64a04', size=6,
                          sort=None,
                          sortBy=None, seedIds=['bW92aWU7NjEz'], chart_Id=None,
                          message=None,
                          userId='site24x7', anonId=None, accountId=None,
                          query_string={'ignore': 'site24x7', 'seedIds': 'bW92aWU7NjEz',
                                        'userId': 'site24x7'},
                          request_start=datetime.datetime(2021, 7, 22, 13, 28, 27,
                                                          194822,
                                                          tzinfo=datetime.timezone.utc),
                          debug_metadata=None, exclude=[],
                          headers={'User-Agent': 'Site24x7'},
                          editorials=[], allowed_items=None, stats=None)

        prioritised_model = ModelDefinition(key='model_key', version=1,
                                            source='PrebuiltModel',
                                            fulfilment=Fulfilment(
                                                ranges=[
                                                    RangeFulfilment(start=0, end=5)]),
                                            parameters={'useSeedId': True,
                                                        'deduplicate_path': ''},
                                            block=None,
                                            key_strategy={
                                                "name": "RecommendationKeyStrategy",
                                                "type": "raw",
                                                "fields": [
                                                    "seedId"
                                                ]
                                            })

        key = self.infer_key(prioritised_model, request)

        expected = ['bW92aWU7NjEz']

        self.assertEqual(expected, key)

    def test_all_subclasses(self):
        # Any modifications to KeyStrategy classes must be reflected here:
        available_key_strategy_classes = [
            RecommendationKeyStrategy,
            UKTVMLTKeyStrategy,
            TimeRangedKeyStrategy,
            SubscriberKeyStrategy,
            SeedSubscriberKeyStrategy,
            SubscriberPositionalTimeRangedKeyStrategy
        ]

        self.assertEqual(available_key_strategy_classes,
                         list(self.key_strategy_factory._all_subclasses(KeyStrategy)))

        class StubKeyStrategy(KeyStrategy):
            @staticmethod
            def generate_key(request: Request, strategy: KeyStrategy, **kwargs) \
                    -> Union[List, None]:
                pass

        newly_available_key_strategy_classes = \
            available_key_strategy_classes + [StubKeyStrategy]

        self.assertEqual(newly_available_key_strategy_classes,
                         list(self.key_strategy_factory._all_subclasses(KeyStrategy)))

    def test_uktv_model_config(self):
        request = Request(rule_id='rule_id', size=6,
                          sort=None,
                          sortBy=None, seedIds=['3390'], chart_Id=None,
                          message=None,
                          userId='site24x7', anonId=None, accountId=None,
                          query_string={"anonId": "anon_id",
                                        "channelId": "3854",
                                        "excluded": "",
                                        "platform": "web",
                                        "seedIds": "3390",
                                        "sourcePage": "dave",
                                        "space": "brand",
                                        "userId": "********"},
                          request_start=datetime.datetime(2021, 7, 22, 13, 28, 27,
                                                          194822,
                                                          tzinfo=datetime.timezone.utc),
                          debug_metadata=None, exclude=[],
                          headers={'User-Agent': 'Site24x7'},
                          editorials=[], allowed_items=None, stats=None)

        prioritised_model = ModelDefinition(key='model_key', version=1,
                                            source='PrebuiltModel',
                                            fulfilment=Fulfilment(
                                                ranges=[
                                                    RangeFulfilment(start=0, end=5)]),
                                            parameters=None,
                                            block=None,
                                            key_strategy={"name": "UKTVMLTKeyStrategy"})

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_brand_dave_web']
        self.assertEqual(expected, key)

        #  No sourcePage, and android platform
        request.query_string = {
            "seedIds": "3390",
            "platform": "android",
            "space": "brand",
            "userId": "********"
        }

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_brand_all_mobile']
        self.assertEqual(expected, key)

        # No platform
        request.query_string = {
            "seedIds": "3390",
            "sourcePage": "dave",
            "space": "brand",
            "userId": "********"
        }

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_brand_dave_web']
        self.assertEqual(expected, key)

        # No space
        request.query_string = {
            "seedIds": "3390",
            "userId": "********"
        }

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_all_web']
        self.assertEqual(expected, key)

        #  No sourcePage, and virginmedia platform
        request.query_string = {
            "seedIds": "3390",
            "platform": "virginmedia",
            "space": "brand",
            "userId": "********"
        }

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_brand_all_web']
        self.assertEqual(expected, key)

        #  No sourcePage, and youview platform
        request.query_string = {
            "seedIds": "3390",
            "sourcePage": "yesterdayChannel",
            "platform": "youview",
            "space": "brand",
            "userId": "********"
        }

        key = self.infer_key(prioritised_model, request)
        expected = ['3390_brand_yesterday_youview']
        self.assertEqual(expected, key)

    def test_time_range_split(self):
        prioritised_model = ModelDefinition(
            key='test', version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(
                ranges=[
                    RangeFulfilment(start=0, end=5)]),
            parameters=None,
            block=None,
            key_strategy={"name": "TimeRangedKeyStrategy"}
        )

        request = Request(
            userId='test_user',
            request_start=datetime.datetime(2021, 7, 22, 13, 28, 27, 194822,
                                            tzinfo=datetime.timezone.utc),
            rule_id='rule_id', size=6, sort=None, sortBy=None, seedIds=['3390'],
            chart_Id=None, message=None, anonId=None, accountId=None,
            query_string={},
            debug_metadata=None, exclude=[],
            headers={'User-Agent': 'Site24x7'},
            editorials=[], allowed_items=None, stats=None
        )

        expected = ['12:00:00-18:00:00']
        key = self.infer_key(prioritised_model, request)
        self.assertEqual(expected, key)

        request = Request(
            userId='test_user',
            subscription_type='free',
            request_start=datetime.datetime(2021, 7, 22, 13, 28, 27, 194822,
                                            tzinfo=datetime.timezone.utc),
            rule_id='rule_id', size=6, sort=None, sortBy=None, seedIds=['3390'],
            chart_Id=None, message=None, anonId=None, accountId=None,
            query_string={},
            debug_metadata=None, exclude=[],
            headers={'User-Agent': 'Site24x7'},
            editorials=[], allowed_items=None, stats=None
        )

        expected = ['12:00:00-18:00:00_free']
        key = self.infer_key(prioritised_model, request)
        self.assertEqual(expected, key)

    def test_subscriber_postional_range_split(self):
        prioritised_model = ModelDefinition(
            key='test', version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(
                ranges=[
                    RangeFulfilment(start=0, end=5)]),
            parameters={"isHero": True},
            block=None,
            key_strategy={"name": "SubscriberPositionalTimeRangedKeyStrategy"}
        )

        request = Request(
            userId='test_user',
            request_start=datetime.datetime(2021, 7, 10, 13, 28, 27, 194822,
                                            tzinfo=datetime.timezone.utc),
            rule_id='rule_id', size=6, sort=None, sortBy=None, seedIds=['3390'],
            chart_Id=None, message=None, anonId=None, accountId=None,
            query_string={},
            debug_metadata=None, exclude=[],
            headers={'User-Agent': 'Site24x7'},
            editorials=[], allowed_items=None, stats=None
        )

        key = self.infer_key(prioritised_model, request)
        self.assertEqual(['test_user'], key)
        positional_key = self.infer_positional_key(prioritised_model, request)
        self.assertEqual(["test_hero_06:00:00-16:00:00"], positional_key)

        request = Request(
            userId='test_user',
            request_start=datetime.datetime(2021, 7, 22, 22, 28, 27, 194822,
                                            tzinfo=datetime.timezone.utc),
            rule_id='rule_id', size=6, sort=None, sortBy=None, seedIds=['3390'],
            chart_Id=None, message=None, anonId=None, accountId=None,
            query_string={'subscriptionType': 'free'},
            debug_metadata=None, exclude=[],
            headers={'User-Agent': 'Site24x7'},
            editorials=[], allowed_items=None, stats=None
        )

        key = self.infer_key(prioritised_model, request)
        self.assertEqual(['test_user_free'], key)

        positional_key = self.infer_positional_key(prioritised_model, request)
        self.assertEqual(["test_hero_16:00:00-06:00:00"], positional_key)
