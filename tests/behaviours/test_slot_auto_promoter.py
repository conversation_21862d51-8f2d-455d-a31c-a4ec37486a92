from copy import deepcopy
from unittest import TestCase

import freezegun

from thefilter.aws.athena import AthenaQueryClientStub
from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.behaviours.slot_auto_promoter.example_slot_auto_promoter import \
    ExampleSlotAutoPromoter
from thefilter.logs.logclient import NoO<PERSON>Logger
from thefilter.repositories import StubPromotionRepository

repo_contents = [
    {
        "promotionId": "5b7faa13-4814-4b54-ba10-8fa4881c3e59",
        "promotionName": "Example Auto Trending",
        "tag_name": 'automated',
        "slot": "e7c44f4f-bfaa-4313-a372-e15c4a3ce3fc",
        "endDate": "2021-07-22T10:29:46+00:00",
        "startDate": "2021-06-22T10:29:46+00:00",
        "createdBy": "user",
        "dateCreated": "2021-06-22T10:29:46+00:00",
        "updatedBy": "user",
        "dateUpdated": "2021-06-22T10:29:46+00:00",
        "content": {
            "metadata": [
                {
                    "id": "trending_10",
                    "position": 0
                },
                {
                    "id": "trending_11",
                    "position": 1
                },
                {
                    "id": "trending_12",
                    "position": 2
                },
                {
                    "id": "trending_13",
                    "position": 3
                }
            ]
        }
    },
    {
        "promotionId": "e1ab79a5-d58e-45f3-9fb9-03496da63bc5",
        "promotionName": "Bond, James Bond",
        "slot": "all",
        "startDate": "2021-06-22T10:29:46+00:00",
        "endDate": "2021-07-22T10:29:46+00:00",
        "dateCreated": "2021-06-22T10:29:46+00:00",
        "createdBy": "user",
        "updatedBy": "user",
        "dateUpdated": "2021-06-22T10:29:46+00:00",
        "content": {
            "metadata": [
                {
                    "id": "tt2379713",
                    "position": 0
                },
                {
                    "id": "tt1074638",
                    "position": 1
                }
            ]
        }
    }
]


class TestSlotAutoPromoter(TestCase):
    def setUp(self):
        self._repo_contents = deepcopy(repo_contents)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        environment = 'test_env'
        region = 'eu-west-2'

        ddb_query_return_value = {'Items': [{"items": [
            'trending_1', 'trending_2', 'trending_3', 'trending_4'
        ]}]}
        ddb_client = BotoDDBClientStub(region, ddb_query_return_value)

        self._repo_contents = deepcopy(repo_contents)
        promotions_repository = StubPromotionRepository(records=self._repo_contents)

        metadata_repo = [
            {'thing_id': 'trending_3', 'thing_brandid': 'trending_300',
             'thing_publication_startdate': '2023-06-23T00:00:00+00:00'},
            {'thing_id': 'trending_1', 'thing_brandid': 'trending_100',
             'thing_publication_startdate': '2023-01-01T00:00:00+00:00'},
            {'thing_id': 'trending_2', 'thing_brandid': 'trending_200',
             'thing_publication_startdate': '2022-01-01T00:00:00+00:00'},
            {'thing_id': 'trending_4', 'thing_brandid': 'trending_400',
             'thing_publication_startdate': '2021-01-01T00:00:00+00:00'},
        ]
        athena_client = AthenaQueryClientStub(metadata_repo)

        logger = NoOpLogger()
        self._slot_auto_promoter = ExampleSlotAutoPromoter(
            environment,
            ddb_client,
            promotions_repository,
            athena_client,
            logger
        )

    def test_auto_promote(self):
        # No assertions, just to see it can all run, useful for debugging.
        self._slot_auto_promoter.auto_promote()

    def test_get_example_trending_items(self):
        trending_items = self._slot_auto_promoter._get_example_trending_items()
        expected = ['trending_1', 'trending_2', 'trending_3', 'trending_4']
        self.assertEqual(expected, trending_items)

    def test_handle_promotions(self):
        trending_items = ['trending_1', 'trending_2', 'trending_3', 'trending_4']
        self._slot_auto_promoter._handle_promotions(trending_items)

    def test_delete_old_promos(self):
        self.assertEqual(2, len(self._slot_auto_promoter._promo_repo_api.records))
        self._slot_auto_promoter._delete_old_promos()
        self.assertEqual(1, len(self._slot_auto_promoter._promo_repo_api.records))

    @freezegun.freeze_time('2023-08-01T11:23:10+00:00')
    def test_generate_new_promo_block(self):
        trending_items = ['item_1', 'item_2', 'item_3', 'item_4', 'item_5']
        slot_id = 'guid_boi'
        promo_block = self._slot_auto_promoter._generate_new_promo_block(
            trending_items, slot_id
        )
        stub_promotion_id = 'guid_boi'
        expected = {
            'promotionId': stub_promotion_id,
            'promotionName': 'Example Auto Trending guid_boi', 'tag_name': 'automated',
            'slot': 'guid_boi',
            'endDate': '2023-08-06T11:23:10+00:00',
            'startDate': '2023-08-01T11:23:10+00:00',
            'createdBy': 'ExampleSlotAutoPromoter',
            'dateCreated': '2023-08-01T11:23:10+00:00',
            'updatedBy': 'ExampleSlotAutoPromoter',
            'dateUpdated': '2023-08-01T11:23:10+00:00', 'content': {
                'metadata': [{'id': 'item_1', 'position': 0},
                             {'id': 'item_2', 'position': 1},
                             {'id': 'item_3', 'position': 2},
                             {'id': 'item_4', 'position': 3}]}
        }
        promo_block['promotionId'] = stub_promotion_id

        self.assertEqual(expected, promo_block)
