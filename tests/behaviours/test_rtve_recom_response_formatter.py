from unittest import TestCase
from thefilter.behaviours.customer.rtve.rtve_recommendation_response_formatter import \
    RTVERecommendationResponseFormatter


class TestRTVERecommendationResponseFormatter(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def test_format_rtve_thing_with_paid_content(self):
        thing = {
            "id": "show-123",
            "title": "Show 123",
            "typeName": "TVEpisode",
            "thing_custom_availabilityarray": "FR",
            "thing_custom_subscriptionsarray": "FR,DE,IT"
        }

        country_code = "FR"

        result = RTVERecommendationResponseFormatter._format_rtve_thing(thing, country_code)

        self.assertTrue(result["paidContent"])


    def test_format_rtve_thing_without_availability_array(self):
        thing = {
            "id": "show-123",
            "title": "Show 123",
            "typeName": "TVEpisode",
            "thing_custom_subscriptionsarray": "FR,DE,IT"
        }

        country_code = "US"

        result = RTVERecommendationResponseFormatter._format_rtve_thing(thing, country_code)

        self.assertFalse(result["paidContent"])


