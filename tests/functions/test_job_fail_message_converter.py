from copy import deepcopy
from unittest import TestCase

from thefilter.aws.cloudwatch import CloudwatchLogsClientStub
from thefilter.aws.sns import SNSClientStub
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.functions.job_fail_message_converter.job_fail_message_converter import \
    JobFailMessageConverter
from thefilter.logs.logclient import NoOpLogger

test_event = {
    'version': '0',
    'id': '4f06998f-89f5-2be9-c270-c0fac738fb6f',
    'detail-type': 'Batch Job State Change',
    'source': 'aws.batch',
    'account': '************',
    'time': '2023-09-22T14:20:50Z',
    'region': 'eu-west-2',
    'resources': [
        'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810'
    ],
    'detail': {
        'jobArn': 'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810',
        'jobName': 'test_customer_sql_chart_builder-000',
        'jobId': '7ddc2f45-0c5c-4ce3-a07f-c1399cb66810',
        'jobQueue': 'arn:aws:batch:eu-west-2:************:job-queue/HighPriorityBatchJobqueue',
        'status': 'FAILED',
        'attempts': [
            {
                'container': {
                    'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/6885acf36e4c402f953015498e0684e5',
                    'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/59d6b5421e144cccb2a51733e632c79e',
                    'exitCode': 1,
                    'logStreamName': 'test_customer_sql_chart_builder/default/59d6b5421e144cccb2a51733e632c79e',
                    'networkInterfaces': []
                },
                'startedAt': 1695392179376,
                'stoppedAt': 1695392304139,
                'statusReason': 'Essential container in task exited'},
            {
                'container': {
                    'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/16efdeb2cbeb43b3b307f2b3ce599373',
                    'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/51334dcdf90844a6b4f1267fa69feb01',
                    'exitCode': 1,
                    'logStreamName': 'test_customer_sql_chart_builder/default/51334dcdf90844a6b4f1267fa69feb01',
                    'networkInterfaces': []
                },
                'startedAt': 1695392313144,
                'stoppedAt': 1695392450271,
                'statusReason': 'Essential container in task exited'}
        ],
        'statusReason': 'Essential container in task exited',
        'createdAt': 1695392002116,
        'retryStrategy': {'attempts': 2, 'evaluateOnExit': []},
        'startedAt': 1695392313144, 'stoppedAt': 1695392450271, 'dependsOn': [],
        'jobDefinition': 'arn:aws:batch:eu-west-2:************:job-definition/test_customer_sql_chart_builder:92',
        'parameters': {},
        'container': {
            'image': '************.dkr.ecr.eu-west-2.amazonaws.com/awsbatch/chart_builder:master_20230920155452',
            'command': [],
            'jobRoleArn': 'arn:aws:iam::************:role/TF-test_customer-ChartBuilder-pre-eu-west-2',
            'volumes': [],
            'environment': [
                {'name': 'S3_DESTINATION_BUCKET', 'value': 'test_s3_bucket'},
                {'name': 'TASK_NAME',
                 'value': 'test_customer_sql_chart_builder'},
                {'name': 'ENVIRONMENT', 'value': 'test_environment'},
                {'name': 'REGION', 'value': 'test_region'},
                {'name': 'CUSTOMER', 'value': 'test_customer'}
            ],
            'mountPoints': [], 'ulimits': [], 'exitCode': 1,
            'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/16efdeb2cbeb43b3b307f2b3ce599373',
            'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/51334dcdf90844a6b4f1267fa69feb01',
            'logStreamName': 'test_customer_sql_chart_builder/default/51334dcdf90844a6b4f1267fa69feb01',
            'networkInterfaces': [],
            'resourceRequirements': [
                {'value': '2', 'type': 'VCPU'},
                {'value': '2048', 'type': 'MEMORY'}
            ],
            'secrets': []},
        'timeout': {'attemptDurationSeconds': 21600},
        'tags': {
            'resourceArn': 'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810'},
        'propagateTags': False, 'platformCapabilities': [], 'eksAttempts': []
    }
}

test_event_missing_fields = {
    'version': '0',
    'id': '4f06998f-89f5-2be9-c270-c0fac738fb6f',
    'detail-type': 'Batch Job State Change',
    'source': 'aws.batch',
    'account': '************',
    'time': '2023-09-22T14:20:50Z',
    'region': 'eu-west-2',
    'resources': [
        'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810'
    ],
    'detail': {
        'jobArn': 'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810',
        'jobName': 'test_customer_sql_chart_builder-000',
        'jobId': '7ddc2f45-0c5c-4ce3-a07f-c1399cb66810',
        'jobQueue': 'arn:aws:batch:eu-west-2:************:job-queue/HighPriorityBatchJobqueue',
        'status': 'FAILED',
        'attempts': [
            {
                'container': {
                    'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/6885acf36e4c402f953015498e0684e5',
                    'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/59d6b5421e144cccb2a51733e632c79e',
                    'exitCode': 1,
                    'logStreamName': 'test_customer_sql_chart_builder/default/59d6b5421e144cccb2a51733e632c79e',
                    'networkInterfaces': []
                },
                'stoppedAt': 1695392304139,
                'statusReason': 'Task failed to start'}
        ],
        'statusReason': 'Task failed to start',
        'createdAt': 1695392002116,
        'retryStrategy': {'attempts': 2, 'evaluateOnExit': []},
        'startedAt': 1695392313144, 'stoppedAt': 1695392450271, 'dependsOn': [],
        'jobDefinition': 'arn:aws:batch:eu-west-2:************:job-definition/test_customer_sql_chart_builder:92',
        'parameters': {},
        'container': {
            'image': '************.dkr.ecr.eu-west-2.amazonaws.com/awsbatch/chart_builder:master_20230920155452',
            'command': [],
            'jobRoleArn': 'arn:aws:iam::************:role/TF-test_customer-ChartBuilder-pre-eu-west-2',
            'volumes': [],
            'environment': [
                {'name': 'S3_DESTINATION_BUCKET', 'value': 'test_s3_bucket'},
                {'name': 'TASK_NAME',
                 'value': 'test_customer_sql_chart_builder'},
                {'name': 'ENVIRONMENT', 'value': 'test_environment'},
                {'name': 'REGION', 'value': 'test_region'},
                {'name': 'CUSTOMER', 'value': 'test_customer'}
            ],
            'mountPoints': [], 'ulimits': [], 'exitCode': 1,
            'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/16efdeb2cbeb43b3b307f2b3ce599373',
            'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-Zk0GpysDSfLb1CKP_Batch_78999142-50c0-343d-bcf7-e42002e87d4d/51334dcdf90844a6b4f1267fa69feb01',
            'logStreamName': 'test_customer_sql_chart_builder/default/59d6b5421e144cccb2a51733e632c79e',
            'networkInterfaces': [],
            'resourceRequirements': [
                {'value': '2', 'type': 'VCPU'},
                {'value': '2048', 'type': 'MEMORY'}
            ],
            'secrets': []},
        'timeout': {'attemptDurationSeconds': 21600},
        'tags': {
            'resourceArn': 'arn:aws:batch:eu-west-2:************:job/7ddc2f45-0c5c-4ce3-a07f-c1399cb66810'},
        'propagateTags': False, 'platformCapabilities': [], 'eksAttempts': []
    }
}

test_recs_to_ddb_event = {
    'version': '0',
    'id': '4da4728e-a301-3684-3e29-24c7028e22f7',
    'detail-type': 'Batch Job State Change',
    'source': 'aws.batch',
    'account': '************',
    'time': '2024-09-02T13:25:23Z',
    'region': 'eu-west-2',
    'resources': [
        'arn:aws:batch:eu-west-2:************:job/2dd92b85-6c65-4963-bf0f-992cbbc60a18'
    ],
    'detail': {
        'jobArn': 'arn:aws:batch:eu-west-2:************:job/2dd92b85-6c65-4963-bf0f-992cbbc60a18',
        'jobName': 'testRecsToDDBatchJob',
        'jobId': '2dd92b85-6c65-4963-bf0f-992cbbc60a18',
        'jobQueue': 'arn:aws:batch:eu-west-2:************:job-queue/HighPriorityBatchJobqueue',
        'status': 'FAILED',
        'attempts': [
            {
                'container': {
                    'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-bBF5YKjpOLh4mcW0_Batch_1fe0fdf3-5a19-3194-bf52-449485540b8a/b5360b21acfc4646853cc778c361c0b3',
                    'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-bBF5YKjpOLh4mcW0_Batch_1fe0fdf3-5a19-3194-bf52-449485540b8a/41e660d20dfb4f7b9f0dbb087e909b2e',
                    'exitCode': 1,
                    'logStreamName': 'test_recs_to_ddb/default/41e660d20dfb4f7b9f0dbb087e909b2e',
                    'networkInterfaces': []}, 'startedAt': 1725283516199,
                'stoppedAt': 1725283523316,
                'statusReason': 'Essential container in task exited'
            }
        ],
        'statusReason': 'Essential container in task exited',
        'createdAt': 1725283501940,
        'retryStrategy': {'attempts': 1, 'evaluateOnExit': []},
        'startedAt': 1725283516199, 'stoppedAt': 1725283523316,
        'dependsOn': [],
        'jobDefinition': 'arn:aws:batch:eu-west-2:************:job-definition/test_recs_to_ddb:341',
        'parameters': {}, 'container': {
            'image': '************.dkr.ecr.eu-west-2.amazonaws.com/awsbatch/recs_to_ddb:feature_images-PRJ032DATA-2365-job-status-log-v12_20240902122203',
            'command': [],
            'jobRoleArn': 'arn:aws:iam::************:role/TF-test-RecsToDDB-feature-eu-west-2',
            'volumes': [], 'environment': [
                {'name': 'RECS_S3_BUCKET',
                 'value': 'thefilter-feature-eu-west-2-test'},
                {'name': 'RECS_TTL_DAYS', 'value': '60'},
                {'name': 'LOCAL_TEMP_DIRECTORY', 'value': '/tmp'},
                {'name': 'ENVIRONMENT', 'value': 'feature'},
                {'name': 'RECS_S3_PATH',
                 'value': 'model/recs/MLT-Initial-DDB/TEST-MLT-Initial-DDB-38853d0b-791a-947b-6a7c-16bcb/output/model.tar.gz'},
                {'name': 'CUSTOMER', 'value': 'test_recs_customer'},
                {'name': 'RECS_MODEL_KEY',
                 'value': 'MLT-Initial-DDB'},
                {'name': 'MANAGED_BY_AWS',
                 'value': 'STARTED_BY_STEP_FUNCTIONS'},
                {'name': 'REGION', 'value': 'eu-west-2'}],
            'mountPoints': [], 'ulimits': [], 'exitCode': 1,
            'containerInstanceArn': 'arn:aws:ecs:eu-west-2:************:container-instance/OnDemandComputeEnvironme-bBF5YKjpOLh4mcW0_Batch_1fe0fdf3-5a19-3194-bf52-449485540b8a/b5360b21acfc4646853cc778c361c0b3',
            'taskArn': 'arn:aws:ecs:eu-west-2:************:task/OnDemandComputeEnvironme-bBF5YKjpOLh4mcW0_Batch_1fe0fdf3-5a19-3194-bf52-449485540b8a/41e660d20dfb4f7b9f0dbb087e909b2e',
            'logStreamName': 'test_recs_to_ddb/default/41e660d20dfb4f7b9f0dbb087e909b2e',
            'networkInterfaces': [], 'resourceRequirements': [
                {'value': '8', 'type': 'VCPU'},
                {'value': '8192', 'type': 'MEMORY'}],
            'secrets': []},
        'timeout': {'attemptDurationSeconds': 21600}, 'tags': {
            'resourceArn': 'arn:aws:batch:eu-west-2:************:job/2dd92b85-6c65-4963-bf0f-992cbbc60a18'},
        'propagateTags': False, 'platformCapabilities': [],
        'eksAttempts': [], 'consumableResourceProperties': []}}


class TestJobFailMessageConverter(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        cw_client_stub = CloudwatchLogsClientStub()
        sns_client_stub = SNSClientStub()
        customer_config_stub = CentralisedConfig(
            region="eu-west-2",
            environment="feature",
            logger=NoOpLogger(),
            repo_type="local"  # Remove this if you want to check the DDB Config Table
        )

        self._notification_api = JobFailMessageConverter(
            'test_topic_arn', 'test_ds_topic_arn', 'test_da_topic_arn', sns_client_stub,
            cw_client_stub,
            customer_config_stub
        )

    def test_handle_notification(self):
        response = self._notification_api.handle_notification(test_event)
        expected = {'statusCode': 200, 'body': '"Success"'}
        self.assertEqual(expected, response)

    def test_inspect_event(self):
        self.maxDiff = None
        message, topics = self._notification_api.create_batch_fail_notification(
            test_event)
        expected = """Job test_customer_sql_chart_builder-000 has failed in eu-west-2 test_environment. Please see below for details.

----- BATCH JOB FAILURE DETAIL -----
Job Name: test_customer_sql_chart_builder-000
Reason: Essential container in task exited
Start: 2023-09-22T14:18:20+00:00
End: 2023-09-22T14:20:00+00:00
Total Time: 0 hours, 1 minutes, 40 seconds
Log Stream: test_customer_sql_chart_builder/default/51334dcdf90844a6b4f1267fa69feb01

----- Environment Variables -----
S3_DESTINATION_BUCKET=test_s3_bucket
TASK_NAME=test_customer_sql_chart_builder
ENVIRONMENT=test_environment
REGION=test_region
CUSTOMER=test_customer

----- Container Details -----
Container Image: ************.dkr.ecr.eu-west-2.amazonaws.com/awsbatch/chart_builder:master_20230920155452
Container vCPUs: 2
Container Memory: 2048

----- Log Events -----
Stub Logs from /aws/batch/job / test_customer_sql_chart_builder/default/51334dcdf90844a6b4f1267fa69feb01 in region eu-west-2"""
        self.assertEqual(expected, message)

        expected_topics = ['test_topic_arn']
        self.assertEqual(expected_topics, topics)

    def test_publish_notification(self):
        message = 'test_message'
        region = 'eu-west-2'
        response = self._notification_api.publish_notification(message, region,
                                                               ["test_topic_arn"],
                                                               "test_job")
        expected = {'statusCode': 200, 'body': '"Success"'}

        self.assertEqual(expected, response)

    def test_inspect_event_missing_fields(self):
        self.maxDiff = None
        message, topics = self._notification_api.create_batch_fail_notification(
            test_event_missing_fields
        )
        expected = """Job test_customer_sql_chart_builder-000 has failed in eu-west-2 test_environment. Please see below for details.

----- BATCH JOB FAILURE DETAIL -----
Job Name: test_customer_sql_chart_builder-000
Reason: Task failed to start
Start: 2023-09-22T14:13:20+00:00
End: 2023-09-22T14:18:20+00:00
Total Time: 0 hours, 5 minutes, 0 seconds
Log Stream: test_customer_sql_chart_builder/default/59d6b5421e144cccb2a51733e632c79e

----- Environment Variables -----
S3_DESTINATION_BUCKET=test_s3_bucket
TASK_NAME=test_customer_sql_chart_builder
ENVIRONMENT=test_environment
REGION=test_region
CUSTOMER=test_customer

----- Container Details -----
Container Image: ************.dkr.ecr.eu-west-2.amazonaws.com/awsbatch/chart_builder:master_20230920155452
Container vCPUs: 2
Container Memory: 2048

----- Log Events -----
Stub Logs from /aws/batch/job / test_customer_sql_chart_builder/default/59d6b5421e144cccb2a51733e632c79e in region eu-west-2"""
        self.assertEqual(expected, message)

        expected_topics = ['test_topic_arn']
        self.assertEqual(expected_topics, topics)

    def test_data_science_topic_selection(self):
        test_ds_event = deepcopy(test_event)

        test_ds_event["detail"][
            "jobDefinition"] = "arn:aws:batch:eu-west-2:************:job-definition/recs_to_ddb:99"

        message, topics = self._notification_api.create_batch_fail_notification(
            test_ds_event
        )

        expected_topics = ['test_topic_arn', 'test_ds_topic_arn']
        self.assertEqual(expected_topics, topics)

    def test_data_analytics_topic_selection(self):
        test_da_event = deepcopy(test_event)

        test_da_event["detail"][
            "jobDefinition"] = "arn:aws:batch:eu-west-2:************:job-definition/analytics_job:5"

        message, topics = self._notification_api.create_batch_fail_notification(
            test_da_event
        )

        expected_topics = ['test_topic_arn', 'test_da_topic_arn']
        self.assertEqual(expected_topics, topics)

    def test_get_customer_name(self):
        expected = "test_customer"
        actual = self._notification_api._get_customer(test_event, "eu-west-2", "test")

        self.assertEqual(expected, actual, "Extracted Customer Name matches")

    def test_get_customer_name_from_container_command(self):
        test_cmd_event = deepcopy(test_event)
        test_cmd_event["detail"]["container"]["environment"] = []
        test_cmd_event["detail"]["container"]["command"] = [
            "main.py", "--job", "test_job", "--customer", "test_command_customer"
        ]

        expected = "test_command_customer"
        actual = self._notification_api._get_customer(test_cmd_event,
                                                      "eu-west-2", "test")

        self.assertEqual(expected, actual, "Extracted Customer Name matches")

    def test_get_customer_name_from_customer_config(self):
        test_config_event = deepcopy(test_event)
        test_config_event["detail"]["jobName"] = "backstagedemo_test_job"
        test_config_event["detail"]["container"]["environment"] = []
        test_config_event["detail"]["container"]["command"] = []

        # Using pre here so we can lookup via customer names in config
        expected = "backstagedemo"
        actual = self._notification_api._get_customer(test_config_event,
                                                      "eu-west-2", "pre")

        self.assertEqual(expected, actual, "Extracted Customer Name matches")

    def test_get_customer_name_from_recs_to_ddb(self):
        expected = "test_recs_customer"
        actual = self._notification_api._get_customer(test_recs_to_ddb_event,
                                                      "eu-west-2", "test")

        self.assertEqual(expected, actual, "Extracted Customer Name matches")

    def test_extract_job_name_from_definition(self):
        job_definition = "arn:aws:batch:eu-west-2:************:job-definition/test_customer_sql_chart_builder:92"

        actual = self._notification_api._extract_job_name_from_definition(job_definition)
        expected = "test_customer_sql_chart_builder"

        self.assertEqual(actual, expected, "Extracted Job Name matches")

        job_definition_no_version = "arn:aws:batch:eu-west-2:************:job-definition/test_customer_sql_chart_builder"

        actual = self._notification_api._extract_job_name_from_definition(job_definition_no_version)
        expected = "test_customer_sql_chart_builder"

        self.assertEqual(actual, expected, "Extracted Job Name matches")

