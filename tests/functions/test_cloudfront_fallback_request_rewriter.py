from unittest import TestCase

from thefilter.functions.cloudfront_fallback_request_rewriter.lambda_function import \
    CloudfrontFallbackRequestRewriter


class TestCloudfrontFallbackRequestRewriter(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._api = CloudfrontFallbackRequestRewriter()

    def test_handle_event_given_typical_request(self):
        # typical refers to a non MLT or Search slot.
        test_typical_event = {
            "Records": [
                {
                    "cf": {
                        "config": {
                            "distributionDomainName": "d3m8f6nhm463sy.cloudfront.net",
                            "distributionId": "E25EUCWDSQ1CL3",
                            "eventType": "viewer-request",
                            "requestId": "EhLsuylwQyLk261AjmBtdcUA2aLfUIJ_jUivVVjcfQKCOnDNGeqBHw=="
                        },
                        "request": {
                            "clientIp": "*************",
                            "headers": {
                                "host": [
                                    {
                                        "key": "Host",
                                        "value": "thefilter-feature-epix-fallback.s3.amazonaws.com"
                                    }
                                ],
                                "x-forwarded-for": [
                                    {
                                        "key": "X-Forwarded-For",
                                        "value": "*************"
                                    }
                                ],
                                "user-agent": [
                                    {
                                        "key": "User-Agent",
                                        "value": "Amazon CloudFront"
                                    }
                                ],
                                "via": [
                                    {
                                        "key": "Via",
                                        "value": "1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)"
                                    }
                                ],
                                "accept-encoding": [
                                    {
                                        "key": "Accept-Encoding",
                                        "value": "gzip, deflate, br"
                                    }
                                ]
                            },
                            "method": "GET",
                            "origin": {
                                "s3": {
                                    "authMethod": "origin-access-identity",
                                    "customHeaders": {},
                                    "domainName": "thefilter-feature-epix-fallback.s3.amazonaws.com",
                                    "path": "",
                                    "region": "us-east-1"
                                }
                            },
                            "querystring": "ignore=testtool&userId=1234&seedIds=c2VyaWVzOzEwNDE=",
                            "uri": "/v0/slots/typical_slot_id/items"
                        }
                    }
                }
            ]
        }

        result = self._api.handle_event(test_typical_event)
        expected = test_typical_event['Records'][0]['cf']['request']
        self.assertEqual(expected, result)

    def test_handle_event_given_mlt_request(self):
        mlt_slot_id = 'a0182e54-72ee-4180-8a02-8b4289a64a04'
        test_mlt_event = {
            "Records": [
                {
                    "cf": {
                        "config": {
                            "distributionDomainName": "d3m8f6nhm463sy.cloudfront.net",
                            "distributionId": "E25EUCWDSQ1CL3",
                            "eventType": "viewer-request",
                            "requestId": "EhLsuylwQyLk261AjmBtdcUA2aLfUIJ_jUivVVjcfQKCOnDNGeqBHw=="
                        },
                        "request": {
                            "clientIp": "*************",
                            "headers": {
                                "host": [
                                    {
                                        "key": "Host",
                                        "value": "thefilter-feature-epix-fallback.s3.amazonaws.com"
                                    }
                                ],
                                "x-forwarded-for": [
                                    {
                                        "key": "X-Forwarded-For",
                                        "value": "*************"
                                    }
                                ],
                                "user-agent": [
                                    {
                                        "key": "User-Agent",
                                        "value": "Amazon CloudFront"
                                    }
                                ],
                                "via": [
                                    {
                                        "key": "Via",
                                        "value": "1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)"
                                    }
                                ],
                                "accept-encoding": [
                                    {
                                        "key": "Accept-Encoding",
                                        "value": "gzip, deflate, br"
                                    }
                                ]
                            },
                            "method": "GET",
                            "origin": {
                                "s3": {
                                    "authMethod": "origin-access-identity",
                                    "customHeaders": {},
                                    "domainName": "thefilter-feature-epix-fallback.s3.amazonaws.com",
                                    "path": "",
                                    "region": "us-east-1"
                                }
                            },
                            "querystring": "ignore=testtool&userId=1234&seedIds=c2VyaWVzOzEwNDE=",
                            "uri": f"/v0/slots/{mlt_slot_id}/items"
                        }
                    }
                }
            ]
        }

        result = self._api.handle_event(test_mlt_event)
        expected = {
            'uri': '/v0/slots/a0182e54-72ee-4180-8a02-8b4289a64a04/c2VyaWVzOzEwNDE=',
            'method': 'GET',
            'querystring': '',
            'clientIp': '*************',
            'origin': {
                's3': {'authMethod': 'origin-access-identity', 'customHeaders': {},
                       'domainName': 'thefilter-feature-epix-fallback.s3.amazonaws.com',
                       'path': '', 'region': 'us-east-1'}},
            'headers': {
                'host': [
                    {'key': 'Host',
                     'value': 'thefilter-feature-epix-fallback.s3.amazonaws.com'}],
                'x-forwarded-for': [
                    {'key': 'X-Forwarded-For',
                     'value': '*************'}],
                'user-agent': [{'key': 'User-Agent',
                                'value': 'Amazon CloudFront'}],
                'via': [{'key': 'Via',
                         'value': '1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)'}],
                'accept-encoding': [
                    {'key': 'Accept-Encoding',
                     'value': 'gzip, deflate, br'}]
            }
        }
        self.assertEqual(expected, result)

    def test_handle_event_given_search_request(self):
        search_slot_id = '76dd5146-c45c-464d-92dd-2c9fbfb1ee5d'
        test_search_event = {
            "Records": [
                {
                    "cf": {
                        "config": {
                            "distributionDomainName": "d3m8f6nhm463sy.cloudfront.net",
                            "distributionId": "E25EUCWDSQ1CL3",
                            "eventType": "viewer-request",
                            "requestId": "EhLsuylwQyLk261AjmBtdcUA2aLfUIJ_jUivVVjcfQKCOnDNGeqBHw=="
                        },
                        "request": {
                            "clientIp": "*************",
                            "headers": {
                                "host": [
                                    {
                                        "key": "Host",
                                        "value": "thefilter-feature-epix-fallback.s3.amazonaws.com"
                                    }
                                ],
                                "x-forwarded-for": [
                                    {
                                        "key": "X-Forwarded-For",
                                        "value": "*************"
                                    }
                                ],
                                "user-agent": [
                                    {
                                        "key": "User-Agent",
                                        "value": "Amazon CloudFront"
                                    }
                                ],
                                "via": [
                                    {
                                        "key": "Via",
                                        "value": "1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)"
                                    }
                                ],
                                "accept-encoding": [
                                    {
                                        "key": "Accept-Encoding",
                                        "value": "gzip, deflate, br"
                                    }
                                ]
                            },
                            "method": "GET",
                            "origin": {
                                "s3": {
                                    "authMethod": "origin-access-identity",
                                    "customHeaders": {},
                                    "domainName": "thefilter-feature-epix-fallback.s3.amazonaws.com",
                                    "path": "",
                                    "region": "us-east-1"
                                }
                            },
                            "querystring": "q=godfather",
                            "uri": f"/v0/slots/{search_slot_id}/items"
                        }
                    }
                }
            ]
        }

        result = self._api.handle_event(test_search_event)
        expected = {
            'uri': '/v0/search/god',
            'method': 'GET',
            'querystring': '',
            'clientIp': '*************',
            'origin': {
                's3': {'authMethod': 'origin-access-identity', 'customHeaders': {},
                       'domainName': 'thefilter-feature-epix-fallback.s3.amazonaws.com',
                       'path': '', 'region': 'us-east-1'}},
            'headers': {
                'host': [
                    {'key': 'Host',
                     'value': 'thefilter-feature-epix-fallback.s3.amazonaws.com'}],
                'x-forwarded-for': [
                    {'key': 'X-Forwarded-For',
                     'value': '*************'}],
                'user-agent': [{'key': 'User-Agent',
                                'value': 'Amazon CloudFront'}],
                'via': [{'key': 'Via',
                         'value': '1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)'}],
                'accept-encoding': [
                    {'key': 'Accept-Encoding',
                     'value': 'gzip, deflate, br'}]
            }
        }
        self.assertEqual(expected, result)

    def test_handle_event_given_typical_uktv_request(self):
        # typical refers to a non MLT or Search slot.
        test_typical_uktv_event = {
            "Records": [
                {
                    "cf": {
                        "config": {
                            "distributionDomainName": "d3m8f6nhm463sy.cloudfront.net",
                            "distributionId": "E25EUCWDSQ1CL3",
                            "eventType": "viewer-request",
                            "requestId": "EhLsuylwQyLk261AjmBtdcUA2aLfUIJ_jUivVVjcfQKCOnDNGeqBHw=="
                        },
                        "request": {
                            "clientIp": "*************",
                            "headers": {
                                "host": [
                                    {
                                        "key": "Host",
                                        "value": "eeee7491a9c34dd9b2f2daa63dc09b96-rest.thefilter.com" # makes this a uktv request
                                    }
                                ],
                                "x-forwarded-for": [
                                    {
                                        "key": "X-Forwarded-For",
                                        "value": "*************"
                                    }
                                ],
                                "user-agent": [
                                    {
                                        "key": "User-Agent",
                                        "value": "Amazon CloudFront"
                                    }
                                ],
                                "via": [
                                    {
                                        "key": "Via",
                                        "value": "1.1 a74d31d7dc1caa00f81053fe7a0eb676.cloudfront.net (CloudFront)"
                                    }
                                ],
                                "accept-encoding": [
                                    {
                                        "key": "Accept-Encoding",
                                        "value": "gzip, deflate, br"
                                    }
                                ]
                            },
                            "method": "GET",
                            "origin": {
                                "s3": {
                                    "authMethod": "origin-access-identity",
                                    "customHeaders": {},
                                    "domainName": "thefilter-feature-uktv-fallback.s3.amazonaws.com",
                                    "path": "",
                                    "region": "us-east-1"
                                }
                            },
                            "querystring": "ignore=testtool&userId=1234&seedIds=c2VyaWVzOzEwNDE=",
                            "uri": "/slots/typical_slot_id/items"
                        }
                    }
                }
            ]
        }

        result = self._api.handle_event(test_typical_uktv_event)
        expected = test_typical_uktv_event['Records'][0]['cf']['request']
        self.assertEqual(expected, result)
