import json
import os
import unittest
from unittest.mock import patch

from thefilter.functions.consumer_catalogue_to_elasticsearch.consumer_catalogue_to_elasticsearch import \
    ConsumerCatalogueToElasticsearch
from thefilter.functions.consumer_catalogue_to_elasticsearch.lambda_function import \
    lambda_handler

test_data_dir = "tests/data/"

test_env_vars = {
    "ENVIRONMENT": "TEST_ENVIRONMENT",
    "REGION": "TEST_REGION"
}


class TestConsumerCatalogueToElasticSearch(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(TestConsumerCatalogueToElasticSearch, self).__init__(*args, **kwargs)

        with open(test_data_dir + "sqsSnsMessageLambdaEvent.json") as f:
            self.sqsSnsMessage = json.load(f)

        with open(
                test_data_dir + "consumer_catalogue_to_es_test_event.json"
        ) as f:
            self.consumer_catalogue_to_elasticsearch_test_event = json.load(f)

        with open(
                test_data_dir + "consumer_catalogue_to_es_uktv_test_event.json"
        ) as f:
            self.uktv_test_event = json.load(f)

        with patch.dict('os.environ', test_env_vars):
            self.consumer_catalogue_to_elasticsearch = ConsumerCatalogueToElasticsearch(
                os.environ.get("ENVIRONMENT"),
                os.environ.get("REGION")
            )

    def test_given_empty_event_unwrapper_raises_exception(self):
        with self.assertRaises(Exception):
            self.consumer_catalogue_to_elasticsearch.unwrap_event({})

    def test_given_sqsSnsMessage_can_unwrap(self):
        expected = [
            {
                'identifier': {'eventId': 'example_eventId',
                               'operationId': 'A43EEB9E-96E6-4235-AE3C-58E37C7A7A5C'},
                'customer': {'name': 'example_customer'},
                'user': {'accountId': 'example_accountId', 'userId': 'example_userId',
                         'anonId': 'example_anonId', 'primaryId': 'example_userId',
                         'clusterId': 'A'},
                'timestamp': {'received': '2016-04-06T10:10:09+00:00',
                              'initiated': '2016-04-06T10:10:09+00:00'},
                'thing': [
                    {'id': 'example_id',
                     'brandId': 'example_brandId',
                     'name': 'example_name',
                     'typeName': 'thing:creativeWork',
                     'genre': [{'id': 'example_genre_id', 'name': 'example_genre_name'}],
                     'contentRating': [
                         {'id': 'example_contentRating_id',
                          'name': 'example_content_name'}],
                     'actor': [{'id': 'example_actor_id', 'name': 'example_actor_name'}],
                     'director': [
                         {'id': 'example_director_id', 'name': 'example_director_name'}],
                     'producer': [],
                     'crew': [],
                     'duration': 'P0Y0M0DT2H30M5S',
                     'datePublished': '2016-04-06T10:10:09.123Z',
                     'image': {},
                     'custom': {
                         'active': {'state': True,
                                    'stateUpdateTimestamp': '2021-01-02T13:59:45+00:00'},
                         'subGenre': [{'id': 'example_id', 'name': 'example_name'},
                                      {'id': 'example_id2',
                                       'name': 'example_name2'}]}}
                ],
                'action': 'example_action',
                'context': {'application': 'example_application',
                            'server': 'example_server', 'site': 'example_server',
                            'environment': 'example_environment',
                            'code_version': 'example_version'}, 'custom': {
                'bt': {'mpx': 'example_mpx', 'serviceType': 'example_serviceType',
                       'slotType': 'example_slotType'}},
                'sourceOperationId': 'example_sourceOperationId',
                'messageAttributes': {'example_key': 'example_value'}}
        ]

        unwrapped_event = \
            self.consumer_catalogue_to_elasticsearch.unwrap_event(self.sqsSnsMessage)

        self.assertEqual(expected, unwrapped_event)

    @patch(
        'thefilter.functions.consumer_catalogue_to_elasticsearch.consumer_catalogue_to_elasticsearch.ConsumerCatalogueToElasticsearch._check_item_and_timestamp_added_exists')
    def test_given_catalogue_lambda_with_timestamp_added(
            self,
            mock_check_item_and_timestamp_added_exists
    ):
        expected_timestamp = {
            'initiated': '2023-10-02T19:17:34+00:00',
            'received': '2023-10-02T19:17:34+00:00'
        }
        unwrapped_event = self.consumer_catalogue_to_elasticsearch.unwrap_event(
            self.consumer_catalogue_to_elasticsearch_test_event
        )
        self.assertEqual(expected_timestamp, unwrapped_event[0]['timestamp'])

        mock_check_item_and_timestamp_added_exists.return_value = '2023-10-02T19:17:34+00:00'
        updated_event = self.consumer_catalogue_to_elasticsearch.handle_timestamp_added(
            unwrapped_event)

        expected_updated_timestamp = {
            'initiated': '2023-10-02T19:17:34+00:00',
            'received': '2023-10-02T19:17:34+00:00',
            'added': '2023-10-02T19:17:34+00:00'
        }
        self.assertEqual(expected_updated_timestamp, updated_event[0]['timestamp'])

    @patch(
        'thefilter.functions.consumer_catalogue_to_elasticsearch.consumer_catalogue_to_elasticsearch.ConsumerCatalogueToElasticsearch._check_item_and_timestamp_added_exists')
    def test_uktv_event(
            self,
            mock_check_item_and_timestamp_added_exists
    ):
        expected_timestamp = {
            'initiated': '2023-10-04T22:06:20+00:00',
            'received': '2023-10-04T22:06:20+00:00'
        }
        unwrapped_event = self.consumer_catalogue_to_elasticsearch.unwrap_event(
            self.uktv_test_event
        )

        self.assertEqual(expected_timestamp, unwrapped_event[0]['timestamp'])

        mock_check_item_and_timestamp_added_exists.return_value = '2023-10-04T22:06:20+00:00'
        updated_event = self.consumer_catalogue_to_elasticsearch.handle_timestamp_added(
            unwrapped_event
        )

        expected_updated_timestamp = {
            'initiated': '2023-10-04T22:06:20+00:00',
            'received': '2023-10-04T22:06:20+00:00',
            'added': '2023-10-04T22:06:20+00:00'
        }
        self.assertEqual(expected_updated_timestamp, updated_event[0]['timestamp'])

    @patch('thefilter.aws.elastic_search.ElasticBulkPublisher.__init__',
           return_value=None)
    @patch('thefilter.aws.elastic_search.ElasticBulkPublisher.publish')
    def test_runs_with_mock_publisher(self, mock_publisher_publish, mock_publisher_init):
        mock_publisher_publish.return_value = [200, ""]
        lambda_handler(self.sqsSnsMessage, "")

