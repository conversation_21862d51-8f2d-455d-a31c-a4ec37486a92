{"type": "event_processing_request", "id": "83fa3f70-8a4d-408b-8390-1ab33e984977", "timestamp_ms": *************, "firehose_version": "2.4.0", "source_id": "79afa073-bec2-4c22-ab1d-17cf9da2f89b", "account": {"account_id": 123456, "account_settings": {"Example String Setting": "Example Setting Value", "Example Boolean Setting": false, "Example Integer Setting": 123, "apiKey": "sample API Key"}}, "partner_identities": [{"type": "a_partner_id", "encoding": "raw", "value": "partnerId"}], "user_identities": [{"type": "customer", "encoding": "md5", "value": "test_EPIX_ID_value"}, {"type": "email", "encoding": "md5", "value": "e179e95c00e7718ab4a23840f992ea63"}, {"type": "other", "encoding": "raw", "value": "test_MPP_ID_value"}, {"type": "other2", "encoding": "raw", "value": "test_Misc_Device_ID"}, {"type": "other4", "encoding": "raw", "value": "test_Anon_ID"}], "user_attributes": {"$City": "Boca Raton", "$FirstName": "<PERSON>", "$Country": "USA", "$State": "FL", "$Zip": "33431", "$Gender": "M", "$LastName": "<PERSON><PERSON><PERSON>"}, "runtime_environment": {"type": "ios", "client_ip_address": "127.0.0.1", "sdk_version": "6.12.1", "http_header_user_agent": "user-agent", "identities": [{"type": "ios_advertising_id", "encoding": "raw", "value": "66b728c2-f9a4-4d87-82ef-ce07414fe3f7"}, {"type": "ios_vendor_id", "encoding": "raw", "value": "97b826c2-ab80-4876-a184-db36cc39b1ee"}], "build_id": "BuildId", "brand": "Apple", "product": "iPad Pro", "name": "My iPad", "manufacturer": "Apple", "os_version": "10.0", "model": "iPad Pro", "screen_height": 2436, "screen_width": 1125, "country": "US", "locale_language": "en", "locale_country": "en_US", "network_country": "US", "network_carrier": "at&t", "network_code": "MobileNetworkCode", "network_mobile_country_code": "MobileCountryCode", "timezone_offset": -8, "timezone_name": "UTC-8", "cpu_architecture": "x86", "is_tablet": true, "radio_access_technology": "RadioAccessTechnology", "application_name": "App Name", "application_version": "v2", "application_package": "Package", "is_sandboxed": true, "apple_search_ads_attribution": {"Version3.1": {"iad-org-name": "org", "iad-conversion-date": "date", "iad-campaign-id": "id", "iad-attribution": "true", "iad-clickdate": "date", "iad-adgroup-name": "name", "iad-campaign-name": "name", "iad-keyword": "keyword", "iad-adgroup-id": "id"}}}, "events": [{"type": "session_start", "id": "a506c966-4bd3-41ee-8e7d-184fc8823cf1", "timestamp_ms": "1594827409111", "source_id": "eb26d1b9-5636-43a0-86f6-4c19fcd5ba41", "session_id": 123456789, "location": {"latitude": 47.6062, "longitude": 122.3231, "accuracy": 0.0}}, {"type": "session_end", "id": "a7dfc023-9194-459d-ab24-01897a368d82", "timestamp_ms": "1594857409111", "source_id": "afae7c8b-0e5f-42a8-84dd-eb323c5bd44d", "session_id": 123456789, "location": {"latitude": 47.6062, "longitude": 122.3231, "accuracy": 0.0}}], "source_channel": "native", "device_application_stamp": "8e6cac63-7803-4d4f-ba07-8cb4f2b8f788", "consent_state": {"gdpr_consent_state": {"consentPurpose": {"consented": true, "document": "document", "timestamp_unixtime_ms": *************, "location": "location", "hardware_id": "hardware id"}}}, "system_notifications": [{"type": "gdpr_consent_state", "purpose": "consentPurpose", "old_gdpr_consent_state": {"consented": false, "document": "document", "timestamp_unixtime_ms": *************, "location": "location", "hardware_id": "hardware id"}, "new_gdpr_consent_state": {"consented": true, "document": "document", "timestamp_unixtime_ms": *************, "location": "location", "hardware_id": "hardware id"}}], "mpid": "12345"}