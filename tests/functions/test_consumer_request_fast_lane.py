import os
from unittest import TestCase
from unittest.mock import patch, Mock

from thefilter.aws.sqs import SqsMetadataUnwrapper
from thefilter.functions.consumer_request_fast_lane.consumer_request_fast_lane import \
    ConsumerRequestFastLane

test_event = {
    'Records': [{'messageId': 'a2d6183e-b03b-4335-90a3-b0c721a92d51',
                 'receiptHandle': 'AQEB/7guarEAZMAD+yUNMXgidrwUfcGJjlylwSOyiwUyg5BZ+NJIIZnJ21cfXj56xWO4KsukM76KfCZ+MGF69neS/v2PDJrjjLY+NAbDNSy/261/ZgR+aZOIv0IWJv3qvwuZxb5uarQiyklVgjqUfiRShLJtqA0KB+98M47OrpRBpNXEez3lAjnM0TvsULS7SgvHuvlZzHZ2We2xVFDgMebS1aaCOKwaY7GC4fUYWnDV0fdiJBCbejo3CQTDt/AeIfL6hXDy3EbwaxLY6yLzfzzGEbptsHcnbWm9M7n3oQ2536eJdL42Am/ZJbv1xPp9uQAK8qIDrr3DYfWGC/gkf+VSsYIRvlHM7DJ6g2DVzMTIsDgBjdo5/Bb0kKWrj+UlW7cxlZvD9OrlXr58xQd6HoExDA==',
                 'body': '{"customer": {"name": "uktv"}, "identifier": {"eventId": "10a33229-3f5e-48b2-a496-04fa3acbf50b", "operationId": "10a33229-3f5e-48b2-a496-04fa3acbf50b"}, "user": {"accountId": null, "userId": "********", "anonId": "35248ed8-ece6-4d31-a698-73314e4d8455", "primaryId": "********", "clusterId": null}, "timestamp": {"initiated": "2021-03-15T13:26:57.628907+00:00", "received": "2021-03-15T13:26:57.628907+00:00"}, "action": "personalisation", "context": {"application": "api_personalisation", "environment": "feature", "server": null, "site": null, "code_version": "candidate_20210303183401"}, "thing": [{"id": "4", "name": null, "typeName": "SubCategory", "genre": [], "contentRating": [], "actor": [], "director": [], "partOfSeason": {}, "partOfSeries": {}, "publication": [], "keywords": [], "episodeNumber": null, "seasonNumber": null, "image": {}, "description": null, "duration": null, "datePublished": null, "custom": {"subGenre": [], "brand": [], "channel": [], "category": [], "subCategory": []}}], "custom": {}, "sourceOperationId": null, "messageAttributes": null, "status_code": 200, "execution_time": 325.642, "parameters": {"rule_id": "c76c032d-b362-40da-9d04-5de1d20d4357", "size": 25, "sort": null, "sortBy": null, "seedIds": [], "chart_Id": null, "message": null, "userId": "********", "anonId": "35248ed8-ece6-4d31-a698-73314e4d8455", "accountId": null, "query_string": {"anonId": "35248ed8-ece6-4d31-a698-73314e4d8455", "brandcount": "1", "categoryId": "1", "categoryName": "comedy", "ignore": "site24x7", "platform": "web", "replayed": "**************", "replayed_from": "pre", "space": "subcategory", "userId": "********"}, "request_start": "2021-03-15T13:26:57.628785+00:00", "debug_metadata": null, "exclude": [], "headers": {"User-Agent": "python-requests/2.25.1"}, "editorials": null, "allowed_items": null, "stats": {"sagemakerModelGetResultMs": 9.634, "sagemakerModelEnforceAndDedupeMs": 74.098, "sagemakerModelDiscardedByEnforceAndDedupe": 2}}, "number_items_returned": 1, "experiment_info": {"slot_hash": "b82470d0b0941fb1dd66b1e81f9a2823", "experiment_id": "5619bcd1-bab6-4cf1-b3be-2958a2932c27", "title": "Subcategories and Collections", "notes": "", "base": true, "recipe_id": "ef85863f-aa98-4dc0-b746-c0a7077e4312"}, "invocation_count": 26, "api_created": "2021-03-15T13:22:17.953421+00:00"}',
                 'attributes': {'ApproximateReceiveCount': '1',
                                'SentTimestamp': '1615814817966',
                                'SenderId': 'AROASEZJTAYEACC46UP76:feature-UKTV-api_personalisation',
                                'ApproximateFirstReceiveTimestamp': '1615814817967'},
                 'messageAttributes': {
                     'replay': {'stringValue': 'false', 'stringListValues': [],
                                'binaryListValues': [], 'dataType': 'String'}},
                 'md5OfMessageAttributes': '63aaa657ff60eaa16d438c6d814cbd1b',
                 'md5OfBody': '39f22e234f7bee376092cc1e6869d721',
                 'eventSource': 'aws:sqs',
                 'eventSourceARN': 'arn:aws:sqs:eu-west-2:147726730760:feature-RequestFastLane',
                 'awsRegion': 'eu-west-2'}]
}


class TestConsumerRequestFastLane(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        unwrapper = SqsMetadataUnwrapper()

        with patch.dict('os.environ', {"SNS_TOPIC": "NO_LOG"}):
            self._request_fast_lane = \
                ConsumerRequestFastLane(unwrapper, region="eu-west-2")

    def test_unwrap_event(self):
        expected = [
            {'customer': {'name': 'uktv'},
             'identifier': {'eventId': '10a33229-3f5e-48b2-a496-04fa3acbf50b',
                            'operationId': '10a33229-3f5e-48b2-a496-04fa3acbf50b'},
             'user': {'accountId': None, 'userId': '********',
                      'anonId': '35248ed8-ece6-4d31-a698-73314e4d8455',
                      'primaryId': '********', 'clusterId': None},
             'timestamp': {'initiated': '2021-03-15T13:26:57.628907+00:00',
                           'received': '2021-03-15T13:26:57.628907+00:00'},
             'action': 'personalisation',
             'context': {'application': 'api_personalisation', 'environment': 'feature',
                         'server': None, 'site': None,
                         'code_version': 'candidate_20210303183401'}, 'thing': [
                {'id': '4', 'name': None, 'typeName': 'SubCategory', 'genre': [],
                 'contentRating': [], 'actor': [], 'director': [], 'partOfSeason': {},
                 'partOfSeries': {}, 'publication': [], 'keywords': [],
                 'episodeNumber': None, 'seasonNumber': None, "image": {},
                 'description': None, 'duration': None, 'datePublished': None,
                 'custom': {'subGenre': [], 'brand': [],
                            'channel': [], 'category': [], 'subCategory': []}}],
             'custom': {}, 'sourceOperationId': None,
             'messageAttributes': {
                 'replay': {'stringValue': 'false', 'stringListValues': [],
                            'binaryListValues': [], 'dataType': 'String'}},
             'status_code': 200, 'execution_time': 325.642,
             'parameters': {'rule_id': 'c76c032d-b362-40da-9d04-5de1d20d4357',
                            'size': 25, 'sort': None, 'sortBy': None, 'seedIds': [],
                            'chart_Id': None, 'message': None, 'userId': '********',
                            'anonId': '35248ed8-ece6-4d31-a698-73314e4d8455',
                            'accountId': None, 'query_string': {
                     'anonId': '35248ed8-ece6-4d31-a698-73314e4d8455', 'brandcount': '1',
                     'categoryId': '1', 'categoryName': 'comedy', 'ignore': 'site24x7',
                     'platform': 'web', 'replayed': '**************',
                     'replayed_from': 'pre', 'space': 'subcategory',
                     'userId': '********'},
                            'request_start': '2021-03-15T13:26:57.628785+00:00',
                            'debug_metadata': None, 'exclude': [],
                            'headers': {'User-Agent': 'python-requests/2.25.1'},
                            'editorials': None, 'allowed_items': None,
                            'stats': {'sagemakerModelGetResultMs': 9.634,
                                      'sagemakerModelEnforceAndDedupeMs': 74.098,
                                      'sagemakerModelDiscardedByEnforceAndDedupe': 2}},
             'number_items_returned': 1,
             'experiment_info': {'slot_hash': 'b82470d0b0941fb1dd66b1e81f9a2823',
                                 'experiment_id': '5619bcd1-bab6-4cf1-b3be-2958a2932c27',
                                 'title': 'Subcategories and Collections', 'notes': '',
                                 'base': True,
                                 'recipe_id': 'ef85863f-aa98-4dc0-b746-c0a7077e4312'},
             'invocation_count': 26, 'api_created': '2021-03-15T13:22:17.953421+00:00'}
        ]

        unwrap_event = self._request_fast_lane.unwrap_event(test_event)
        self.assertEqual(expected, unwrap_event)

    def test_failed_to_unwrap(self):
        with self.assertRaises(Exception) as e:
            self._request_fast_lane.unwrap_event({})


    def test_kinesis_trim_event(self):
        self.maxDiff = None

        starting_event = {
            "customer": {
                "name": "uktv"
            },
            "identifier": {
                "eventId": "4c9e87bc-b387-4fbe-a0eb-2f54f3e7d532",
                "operationId": "4c9e87bc-b387-4fbe-a0eb-2f54f3e7d532"
            },
            "user": {
                "userId": "0",
                "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                "primaryId": "0"
            },
            "timestamp": {
                "initiated": "2024-02-27T14:39:29.945223+00:00",
                "received": "2024-02-27T14:39:29.945223+00:00"
            },
            "action": "personalisation",
            "context": {
                "application": "kinesis",
                "environment": "production",
                "server": None,
                "site": None,
                "code_version": "master_20240223101223"
            },
            "thing": [
                {
                    "id": "3466",
                    "brandId": "3466_brand",
                    "name": "Not Going Out: Bloopers Special",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 0,
                            "positionOverall": 0
                        }
                    }
                },
                {
                    "id": "215",
                    "brandId": "215_brand",
                    "name": "Gavin & Stacey",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 1,
                            "positionOverall": 1
                        }
                    }
                },
                {
                    "id": "4083",
                    "brandId": "4083_brand",
                    "name": "Schitt's Creek",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 2,
                            "positionOverall": 2
                        }
                    }
                },
                {
                    "id": "2160",
                    "brandId": "2160_brand",
                    "name": "Celebrity Storage Hunters",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 3,
                            "positionOverall": 3
                        }
                    }
                },
                {
                    "id": "3876",
                    "brandId": "3876_brand",
                    "name": "Rick Stein's Cornwall",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 4,
                            "positionOverall": 4
                        }
                    }
                },
                {
                    "id": "3484",
                    "brandId": "3484_brand",
                    "name": "Rick Stein's Far Eastern Odyssey",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 5,
                            "positionOverall": 5
                        }
                    }
                },
                {
                    "id": "3694",
                    "brandId": "3694_brand",
                    "name": "Cops On The Rock",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 6,
                            "positionOverall": 6
                        }
                    }
                },
                {
                    "id": "4237",
                    "brandId": "4237_brand",
                    "name": "Release the Hounds",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 7,
                            "positionOverall": 7
                        }
                    }
                },
                {
                    "id": "1377",
                    "brandId": "1377_brand",
                    "name": "Extreme Fishing",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 8,
                            "positionOverall": 8
                        }
                    }
                },
                {
                    "id": "602",
                    "brandId": "602_brand",
                    "name": "Red Bull Soapbox Race",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 9,
                            "positionOverall": 9
                        }
                    }
                },
                {
                    "id": "319",
                    "brandId": "319_brand",
                    "name": "Top Gear",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 10,
                            "positionOverall": 10
                        }
                    }
                },
                {
                    "id": "3442",
                    "brandId": "3442_brand",
                    "name": "Rick Stein: From Venice to Istanbul",
                    "typeName": "Brand",
                    "partOfSeason": {

                    },
                    "partOfSeries": {

                    },
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases",
                            "positionInModel": 11,
                            "positionOverall": 11
                        }
                    }
                }
            ],
            "custom": {

            },
            "status_code": 200,
            "execution_time": 11.664000000000001,
            "parameters": {
                "rule_id": "6432d9b3-6614-4779-9919-aa41e5708c97",
                "size": 62,
                "userId": "0",
                "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                "query_string": {
                    "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                    "platform": "uktvplay",
                    "sourcePage": "dave",
                    "space": "brand",
                    "userId": "0",
                    "chartName": "latest-dave",
                    "forcedSpace": ""
                },
                "request_start": "2024-02-27T14:39:29.945100+00:00",
                "headers": {
                    "User-Agent": "python-requests/2.25.1"
                },
                "promotions": {

                }
            },
            "number_items_returned": 12,
            "experiment_info": {
                "slot_hash": "7ee07a0fafb26eb46b453a2657c477af",
                "experiment_id": "06b5c086-5da4-421a-a6dd-efcce3cdfd17",
                "title": "Latest Releases Dave",
                "notes": ""
            },
            "invocation_count": 5411,
            "api_created": "2024-02-27T14:01:02.262547+00:00",
            "messageAttributes": {
                "replay": {
                    "stringValue": "https://uktv.pre.thefilter.com|False",
                    "stringListValues": [

                    ],
                    "binaryListValues": [

                    ],
                    "dataType": "String"
                }
            }
        }

        expected_trimmed_event = {
            "customer": {
                "name": "uktv"
            },
            "identifier": {
                "eventId": "4c9e87bc-b387-4fbe-a0eb-2f54f3e7d532",
                "operationId": "4c9e87bc-b387-4fbe-a0eb-2f54f3e7d532"
            },
            "user": {
                "userId": "0",
                "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                "primaryId": "0"
            },
            "timestamp": {
                "initiated": "2024-02-27T14:39:29.945223+00:00",
                "received": "2024-02-27T14:39:29.945223+00:00"
            },
            "action": "personalisation",
            "context": {
                "application": "kinesis",
                "environment": "production"
            },
            "thing": [
                {
                    "id": "3466",
                    "name": "Not Going Out: Bloopers Special",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "215",
                    "name": "Gavin & Stacey",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "4083",
                    "name": "Schitt's Creek",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "2160",
                    "name": "Celebrity Storage Hunters",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "3876",
                    "name": "Rick Stein's Cornwall",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "3484",
                    "name": "Rick Stein's Far Eastern Odyssey",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "3694",
                    "name": "Cops On The Rock",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "4237",
                    "name": "Release the Hounds",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "1377",
                    "name": "Extreme Fishing",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                },
                {
                    "id": "602",
                    "name": "Red Bull Soapbox Race",
                    "custom": {
                        "modelInfo": {
                            "modelName": "uktv_latest_releases"
                        }
                    }
                }
            ],
            "execution_time": 11.664000000000001,
            "parameters": {
                "rule_id": "6432d9b3-6614-4779-9919-aa41e5708c97",
                "size": 62,
                "userId": "0",
                "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                "query_string": {
                    "anonId": "bbc3b7ff-b0ff-41a6-8dbb-5ce9a2989cae",
                    "platform": "uktvplay",
                    "sourcePage": "dave",
                    "space": "brand",
                    "userId": "0",
                    "chartName": "latest-dave",
                    "forcedSpace": ""
                },
                "request_start": "2024-02-27T14:39:29.945100+00:00",
                "headers": {
                    "User-Agent": "python-requests/2.25.1"
                }
            },
            "number_items_returned": 12,
            "experiment_info": {
                "experiment_id": "06b5c086-5da4-421a-a6dd-efcce3cdfd17",
                "title": "Latest Releases Dave",
                "notes": ""
            },
            "invocation_count": 5411
        }
        trimmed_event = self._request_fast_lane.trim_kinesis_event(starting_event)
        self.assertEqual(trimmed_event, expected_trimmed_event, "Trimmed event matches")
