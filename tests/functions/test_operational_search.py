from unittest import TestCase
from unittest.mock import patch

from thefilter.config.elasticsearch_config import ESConfig
from thefilter.functions.operational_search.operational_search import EntitySearcher
from thefilter.logs.tracer import NoOpTracerFactory

test_event = {
    'body-json': {},
    'customer': 'Operational',
    'params': {
        'path': {},
        'querystring': {'entityTypeWhitelist': '', 'q': 'action', 'size': '25'},
        'header': 'test_header'
    }
}

test_results = {
    'code': 200,
    'items': [
        {
            '_index': 'entities_20210219',
            '_type': '_doc',
            '_id': 'Z2VucmVfQWN0aW9u',
            '_score': 265796.16,
            '_source': {
                'createdBy': 'env-person',
                'lastUpdateBy': 'env-person',
                'dateCreated': '2021-03-05T13:45:02+00:00',
                'dateUpdated': '2021-03-05T13:45:02+00:00',
                'entity': {
                    'name': 'Action',
                    'typeName': 'genre',
                    'id': 'Z2VucmVfQWN0aW9u'
                }
            }
        }
    ]
}


class TestEntitySearcher(TestCase):
    def __init__(self, *args, **kwargs):
        super(TestEntitySearcher, self).__init__(*args, **kwargs)
        tracer_factory = NoOpTracerFactory()
        self._entity_searcher = EntitySearcher(
            ESConfig.get_metadata_url("test_environment", "test_region"),
            tracer_factory
        )

    @patch.object(EntitySearcher, "_run_query_against_elasticsearch")
    def test_search(self, mock_api_search):
        expected = test_results['items']
        mock_api_search.return_value = expected
        query = "action"
        entity_type_whitelist = []
        include_hidden_from_search = False
        size = 25
        search_result = self._entity_searcher.search(query, entity_type_whitelist,
                                                     include_hidden_from_search, size)
        self.assertEqual(expected, search_result)

    @patch.object(EntitySearcher, "_run_query_against_elasticsearch")
    def test_operational_search(self, mock_es_result):
        mock_es_result.return_value = test_results['items']
        search_result = self._entity_searcher.operational_search(test_event)
        self.assertEqual(test_results, search_result)
