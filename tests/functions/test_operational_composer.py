from unittest import TestCase

from thefilter.functions.operational_composer.operational_composer import \
    OperationalComposer
from thefilter.logs.logclient import <PERSON><PERSON><PERSON><PERSON>ogger
from thefilter.repositories import StubComposerPageRepository, ComposerPage


class TestOperationalComposer(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._operational_composer = \
            OperationalComposer(
                environment="test_environment",
                composer_page_repository=StubComposerPageRepository(),
                log_client=NoOpLogger()
            )

    def test_handle_composer_request_exception_given_empty_event(self):
        test_event = {}

        with self.assertRaises(Exception):
            self._operational_composer.handle_composer_request(test_event)

        with self.assertRaises(Exception) as e:
            self.assertTrue("EXCEPTION: handle_composer_request issue. 'method'"
                            in str(e.exception))

    def test_time_format_checker(self):
        good_time_format = self._operational_composer.time_format_checker(
            "2021-01-02T13:59:45+00:00")
        self.assertTrue(good_time_format)
        bad_time_format = self._operational_composer.time_format_checker(
            "2021-01-02T13:59:45")
        self.assertFalse(bad_time_format)
        bad_time_format = self._operational_composer.time_format_checker(
            "21-01-02T13:59:45+00:00")
        self.assertFalse(bad_time_format)

    def test_post_request_exception_given_empty_event(self):
        with self.assertRaises(Exception):
            self._operational_composer.post_request({})

    def test_post_request_exception_given_ComposerPage(self):
        test_event = {
            'body-json': {
                "pageId": "test_pageId",
                "pageUserId": "",
                "pageSeedId": "",
                "customer": "test_customer",
                "pageName": "test_pageName",
                "createdBy": "test_createdBy",
                "dateCreated": "2021-05-11T14:13:39+00:00",
                "lastUpdatedBy": "test_lastUpdatedBy",
                "dateUpdated": "2021-05-11T14:13:39+00:00",
                "pageDefinition": "[{}]"
            }
        }

        request = self._operational_composer.post_request(test_event)
        expected_composer_page = ComposerPage(**test_event['body-json'])
        expected = {'status': f"Written {expected_composer_page}."}
        self.assertEqual(expected, request)

    def test_post_request_exception_given_bad_body(self):
        test_event = {
            'body-json': {
                "pageId": "test_pageId",
            }
        }

        with self.assertRaises(TypeError):
            self._operational_composer.post_request(test_event)

    def test_get_all_customers_request(self):
        test_event = {
            'body-json': {},
            'method': 'GET',
            'params': {
                'path': {},
                'querystring': {}
            }
        }
        request = self._operational_composer.get_request(test_event)
        expected = {'status': 'Composer Pages scanned for customers.'}
        self.assertEqual(expected, request)

    def test_get_all_ComposerPages_for_customer(self):
        test_event = {
            'body-json': {},
            'method': 'GET',
            'params': {
                'querystring': {'customer': 'test_customer'},
                'path': {}
            }
        }
        request = self._operational_composer.get_request(test_event)
        expected = {'status': "test_customer's Composer Pages scanned."}
        self.assertEqual(expected, request)

    def test_get_ComposerPage_by_id(self):
        test_event = {
            'body-json': {},
            'method': 'GET',
            'params': {
                'path': {
                    'pageId': '33256b23-92b0-4f91-86cb-83e02f0eafde'
                },
            }
        }
        request = self._operational_composer.get_request(test_event)
        expected = {'status': 'Read 33256b23-92b0-4f91-86cb-83e02f0eafde.'}
        self.assertEqual(expected, request)

    def test_delete_ComposerPage_by_id(self):
        page_id = '33256b23-92b0-4f91-86cb-83e02f0eafde'
        test_event = {
            'body-json': {},
            'method': 'DELETE',
            'params': {
                'path': {
                    'pageId': page_id
                },
            }
        }
        request = self._operational_composer.delete_request(test_event)
        expected = {'status': f'Deleted Composer Page with id {page_id}.'}
        self.assertEqual(expected, request)
