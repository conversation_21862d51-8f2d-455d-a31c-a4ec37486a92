import re
from datetime import datetime, timezone
from unittest import TestCase
from unittest.mock import patch

from freezegun import freeze_time

from thefilter.functions.operational_add_entity.entities_handler import EntitiesHandler

NOW = datetime(2020, 11, 26, 16, 27, 39, tzinfo=timezone.utc)
TASK_NAME = 'Operational_Add_Entity_Lambda'


def is_entity_guid(guid_to_test: str) -> bool:
    pattern = "^[{]?[0-9a-fA-F]{8}" + "-([0-9a-fA-F]{4}-)" + "{3}[0-9a-fA-F]{12}[}]?$"
    if re.search(pattern, guid_to_test):
        return True
    else:
        return False


@patch.dict(
    "os.environ", {
        "ENVIRONMENT": "ENVIRONMENT",
        "ES_METADATA_URL": "TEST_ENDPOINT",
        "REGION": "TEST_REGION"
    })
class TestEntitiesHandler(TestCase):

    def setUp(self) -> None:
        self.entities_empty_array = []
        self.entities_empty_object = [{}]
        self.entities_no_id = [
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "testName",
                    "typeName": "testTypeName"
                }
            }
        ]
        self.entities_batch_updates = [
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "testName",
                    "typeName": "testTypeName"
                }
            },
            {
                "dateCreated": "2020-11-05T10:46:56+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "testName2",
                    "typeName": "testTypeName2"
                }
            },
        ]
        self.entity_source_update = [
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "UKTV_Entity_Creator",
                "dateUpdated": "2020-11-05T10:46:55+00:00",
                "lastUpdatedBy": "UKTV_Entity_Creator",
                "entity": {
                    "id": "dGFnX0FiZHVjdGlvbg==",
                    "name": "Abduction",
                    "typeName": "tag"
                }
            }
        ]
        self.entity_combo = [
            {},
            {
                "dateCreated": "2020-11-05T10:46:56+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "testName2",
                    "typeName": "testTypeName2"
                }
            },
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "UKTV_Entity_Creator",
                "dateUpdated": "2020-11-05T10:46:55+00:00",
                "lastUpdatedBy": "UKTV_Entity_Creator",
                "entity": {
                    "id": "dGFnX0FiZHVjdGlvbg==",
                    "name": "Abduction",
                    "typeName": "tag"
                }
            }
        ]

    @freeze_time(NOW)
    def test_empty_event_array(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entities_empty_array)
        self.assertEqual([], updated_entities)

    @freeze_time(NOW)
    def test_empty_event_objects(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entities_empty_object)
        self.assertEqual([], updated_entities)

    @freeze_time(NOW)
    def test_entities_single_update(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entities_no_id)

        entity_id = updated_entities[0]['entity'].pop('id')
        self.assertTrue(is_entity_guid(entity_id))

        self.assertEqual(
            [
                {'createdBy': 'Portal',
                 'dateCreated': '2020-11-05T10:46:55+00:00',
                 'dateUpdated': '2020-12-30T08:09:10+00:00',
                 'entity': {'name': 'testName',
                            'typeName': 'testTypeName'},
                 'lastUpdatedBy': 'portal_user'}
            ],
            updated_entities)

    @freeze_time(NOW)
    def test_entities_batch_updates(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entities_batch_updates)

        entity_id = updated_entities[0]['entity'].pop('id')
        self.assertTrue(is_entity_guid(entity_id))
        entity_id = updated_entities[1]['entity'].pop('id')
        self.assertTrue(is_entity_guid(entity_id))

        self.assertEqual(
            [
                {'createdBy': 'Portal',
                 'dateCreated': '2020-11-05T10:46:55+00:00',
                 'dateUpdated': '2020-12-30T08:09:10+00:00',
                 'entity': {'name': 'testName',
                            'typeName': 'testTypeName'},
                 'lastUpdatedBy': 'portal_user'},
                {'createdBy': 'Portal',
                 'dateCreated': '2020-11-05T10:46:56+00:00',
                 'dateUpdated': '2020-12-30T08:09:10+00:00',
                 'entity': {'name': 'testName2',
                            'typeName': 'testTypeName2'},
                 'lastUpdatedBy': 'portal_user'}
            ],
            updated_entities)

    @freeze_time(NOW)
    def test_entities_update(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entity_source_update)
        self.assertEqual(
            [
                {'createdBy': 'UKTV_Entity_Creator',
                 'dateCreated': '2020-11-05T10:46:55+00:00',
                 'dateUpdated': '2020-11-05T10:46:55+00:00',
                 'entity': {'id': 'dGFnX0FiZHVjdGlvbg==',
                            'name': 'Abduction',
                            'typeName': 'tag'},
                 'lastUpdatedBy': 'UKTV_Entity_Creator'}
            ],
            updated_entities)

    @freeze_time(NOW)
    def test_entities_combo(self):
        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(self.entity_combo)

        entity_id = updated_entities[0]['entity'].pop('id')
        self.assertTrue(is_entity_guid(entity_id))

        self.assertEqual(
            [
                {'createdBy': 'Portal',
                 'dateCreated': '2020-11-05T10:46:56+00:00',
                 'dateUpdated': '2020-12-30T08:09:10+00:00',
                 'entity': {'name': 'testName2',
                            'typeName': 'testTypeName2'},
                 'lastUpdatedBy': 'portal_user'},
                {'createdBy': 'UKTV_Entity_Creator',
                 'dateCreated': '2020-11-05T10:46:55+00:00',
                 'dateUpdated': '2020-11-05T10:46:55+00:00',
                 'entity': {'id': 'dGFnX0FiZHVjdGlvbg==',
                            'name': 'Abduction',
                            'typeName': 'tag'},
                 'lastUpdatedBy': 'UKTV_Entity_Creator'}
            ],
            updated_entities)

    @freeze_time(NOW)
    def test_time_format_check(self):
        dateCreated_good_style = '2020-01-23T12:34:56+00:00'

        dateCreated_bad_style_1 = '2020-11-26 12:34:56.123456+08:15'
        dateCreated_bad_style_2 = '2020-01-23 12:34:56+00:00'
        dateCreated_bad_style_3 = '2020-01-23T12:34:56+0000'

        entities_handler = EntitiesHandler(NOW)

        self.assertTrue(entities_handler.time_format_checker(dateCreated_good_style))
        self.assertFalse(entities_handler.time_format_checker(dateCreated_bad_style_1))
        self.assertFalse(entities_handler.time_format_checker(dateCreated_bad_style_2))
        self.assertFalse(entities_handler.time_format_checker(dateCreated_bad_style_3))

    @freeze_time(NOW)
    def test_duplicate_entities(self):
        """Test that duplicate entities are not created when the same entity is passed multiple times."""
        # Create a list with duplicate entities (same name and typeName, no ID)
        duplicate_entities = [
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "duplicateName",
                    "typeName": "duplicateType"
                }
            },
            {
                "dateCreated": "2020-11-05T10:46:55+00:00",
                "createdBy": "Portal",
                "dateUpdated": "2020-12-30T08:09:10+00:00",
                "lastUpdatedBy": "portal_user",
                "entity": {
                    "name": "duplicateName",
                    "typeName": "duplicateType"
                }
            }
        ]

        entities_handler = EntitiesHandler(NOW)
        updated_entities = entities_handler.update(duplicate_entities)

        # Extract entity names and types for easier comparison
        entity_identifiers = [(entity['entity']['name'], entity['entity']['typeName']) 
                             for entity in updated_entities]

        # Count occurrences of each name/type pair
        duplicate_count = entity_identifiers.count(("duplicateName", "duplicateType"))

        # Assert that each unique entity appears only once
        self.assertEqual(1, duplicate_count, 
                        "The same entity was created multiple times")
