import os
import unittest
from datetime import datetime, timezone
from decimal import Decimal
from unittest import mock

from freezegun import freeze_time

from thefilter.aws.aws_lambda import SqsSnsUnwrapper
from thefilter.aws.sns import SnsUnwrapper
from thefilter.aws.sqs import SqsUnwrapper
from thefilter.functions.consumer_user_history_to_dynamo.consumer_user_history_to_dynamo import \
    ConsumerUserHistoryToDynamo
from thefilter.functions.consumer_user_history_to_dynamo.user_history import \
    UserHistoryBuilder
from thefilter.logs.logclient import NoOpLogger
from thefilter.model.context import Context
from thefilter.model.identifier import Identifier
from thefilter.model.messages.message import Message
from thefilter.model.schemaorg import Thing
from thefilter.model.timestamp import Timestamp
from thefilter.model.user import User
from thefilter.model.user_history import UserHistoryDDB

test_event = {
    'Records': [{'messageId': '11ab1dd3-be57-4789-8aa4-d4cf98c90e55',
                 'receiptHandle': 'AQEBbi/Ry9W5YBQSz+WTlMdFoSWaawHJRzyQNhqEiIEYmHq8uVhWTH186frALLzJarxwuMXMz2Z0mbvmYqPLq/euZ0yZ0JY2HiTSiIS52an6VMEBQ/oijT42VHX7vONS43e6D3OX7HFYkw0MXNT2b+7xs4wQGtxXkSscYQ+Ivq3YJ4PvQmYhtwUG1cU9T0a90bqLDubAYkgqLFoOOKStXEpcEEll1tfn93RBV7HNtyNLgZnBVx9bUMS0lSnkwaAXWLbrGXMAjwSl24kjwRO5J/2olOs4kKNeBYmckOfdUXpIFBwJcQ9pGzTnFhdl7+c5PbSLJIcknxesR8+vXE6BJgJLqFH4Seg8FTJKFqCq9KAqb+eiJxxzmDtNAxvI8Qb/elZ/hblRQgLMI9EAmFxUVIfHlnxKPllIOgDnKBqvc+elzTo=',
                 'body': '{\n  "Type" : "Notification",\n  "MessageId" : "fa1879fa-a24e-5e8d-a138-21332d1b4189",\n  "TopicArn" : "arn:aws:sns:eu-west-2:************:feature-BeaconIngestion",\n  "Message" : "{\\"customer\\": {\\"name\\": \\"uktv\\"}, \\"identifier\\": {\\"eventId\\": \\"f23c0c19-26d0-4f06-8136-9d6dc1dc7fdd\\", \\"operationId\\": \\"7eb70474-1d01-4bcd-bdaf-4b1d2b910fe8\\"}, \\"user\\": {\\"accountId\\": null, \\"userId\\": \\"********\\", \\"anonId\\": \\"b19d4c536c0e3769c3322943783ea9aa\\", \\"primaryId\\": \\"********\\", \\"clusterId\\": null}, \\"timestamp\\": {\\"initiated\\": \\"2021-03-15T16:36:37.376511+00:00\\", \\"received\\": \\"2021-03-15T16:36:37.376511+00:00\\"}, \\"action\\": \\"play\\", \\"context\\": {\\"application\\": \\"api_beacon_lite\\", \\"environment\\": \\"feature\\", \\"server\\": null, \\"site\\": null, \\"code_version\\": null}, \\"thing\\": [{\\"id\\": \\"CTAU555K\\", \\"name\\": null, \\"typeName\\": null, \\"genre\\": [], \\"contentRating\\": [], \\"actor\\": [], \\"director\\": [], \\"partOfSeason\\": {}, \\"partOfSeries\\": {}, \\"publication\\": [], \\"keywords\\": [], \\"episodeNumber\\": null, \\"description\\": null, \\"duration\\": null, \\"datePublished\\": null, \\"custom\\": {\\"subGenre\\": [], \\"brand\\": [], \\"channel\\": [], \\"category\\": [], \\"subCategory\\": []}}], \\"custom\\": {\\"anonId\\": \\"b19d4c536c0e3769c3322943783ea9aa\\", \\"eventType\\": \\"play\\", \\"itemId\\": \\"CTAU555K\\", \\"platform\\": \\"youview\\", \\"replayed\\": \\"20210315163636\\", \\"replayedFrom\\": \\"pre\\", \\"replayed_from\\": \\"production\\", \\"sourcePage\\": \\"theBill\\", \\"userId\\": \\"********\\"}, \\"sourceOperationId\\": null, \\"messageAttributes\\": null, \\"headers\\": {\\"User-Agent\\": \\"python-requests/2.25.1\\"}}",\n  "Timestamp" : "2021-03-15T16:36:37.466Z",\n  "SignatureVersion" : "1",\n  "Signature" : "kumWtjh+DjXi3yB1DM3gBOzVkRgvj+M+pcv6W7uc7axsbl5xO5gT6azHw0nR0uh+PCBf9D5uyzb84fs87hwb1OS7nxuwwOISXjsaZj9Or3Pm5ep2xa235qJX+JIsFNKhMAAI1yyrhC/BW2+Rob6C8zQF4b5LIOPQzaUh0PGJ1+pS0uaeqvo7EufFHutt6c4zhkfRZuULlnq6G6dd2Ue5cbizDskn3Hzjumo3PV9Kbv+fXrsNdoP29Cp9QMKsTvwmkI3YnOrFFohlsggc4sx9o4DpwYQQiTP0zSxn1j938zaD+Z4/Gs3Yt4LluPlbPu43FgKKmi2N0+SLKYdyUkpenw==",\n  "SigningCertURL" : "https://sns.eu-west-2.amazonaws.com/SimpleNotificationService-010a507c1833636cd94bdb98bd93083a.pem",\n  "UnsubscribeURL" : "https://sns.eu-west-2.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-2:************:feature-BeaconIngestion:78993b9a-4395-4f8d-80cd-4ab1d9eed415",\n  "MessageAttributes" : {\n    "sendToUserHistory" : {"Type":"String","Value":"true"},\n    "action" : {"Type":"String","Value":"play"},\n    "replay" : {"Type":"String","Value":"false"}\n  }\n}',
                 'attributes': {'ApproximateReceiveCount': '1',
                                'SentTimestamp': '1615826197493',
                                'SenderId': 'AIDAIVEA3AGEU7NF6DRAG',
                                'ApproximateFirstReceiveTimestamp': '1615826197495'},
                 'messageAttributes': {},
                 'md5OfBody': '4635fb0e74975acb6c26930a6b129ec4',
                 'eventSource': 'aws:sqs',
                 'eventSourceARN': 'arn:aws:sqs:eu-west-2:************:feature-BeaconUserHistoryDynamo',
                 'awsRegion': 'eu-west-2'}]}

EXPECTED_USER_HISTORY_DDB = UserHistoryDDB(userId="test_userId",
                                           timestampInitiated="2019-01-02T03:04:00",
                                           timestampReceived="2019-01-02T03:04:00",
                                           action="test_action",
                                           beaconId="test_eventId",
                                           space=None,
                                           thingId="test_thing_id",
                                           brandId="test_brandId",
                                           thingTypeName="test_typeName",
                                           userAccountId=None,
                                           userClusterId=None,
                                           timestampKey=None,
                                           timeToLiveEpoch=**********,
                                           progressPct=Decimal("10.0"),
                                           duration=Decimal("30.5"))

TEST_TIMESTAMP = Timestamp(initiated=datetime(2019, 1, 2, 3, 4),
                           received=datetime(2019, 1, 2, 3, 4))
ES_METADATA_URL = "test_endpoint"
CUSTOMER = "customer_a"
EVENT_TYPES = "play,purchase"
IDENTIFIER = Identifier(eventId="test_eventId", operationId="test_opId")
TEST_USER = User(userId="test_userId")
CONTEXT = Context(application="app",
                  environment="env",
                  server="test_server",
                  site="test_site",
                  code_version="test_version")
THING = Thing(id="test_thing_id", typeName="test_typeName", brandId='test_brandId')
ACTUAL_BEACON = Message(identifier=IDENTIFIER,
                        customer={"name": "example_customer"},
                        user=TEST_USER,
                        timestamp=TEST_TIMESTAMP,
                        action="test_action",
                        context=CONTEXT,
                        thing=[THING],
                        custom={
                            "progress_pct": 10.0,
                            "duration": 30.5
                        })


class UserHistoryBuilderTests(unittest.TestCase):
    @freeze_time("2022-01-01T12:00:00+00:00")
    def test_create_user_history_ddb(self):
        expected_result = EXPECTED_USER_HISTORY_DDB
        user_history_builder = UserHistoryBuilder()
        actual_user_history_item = user_history_builder._create_user_historyDDB(
            ACTUAL_BEACON, "userId", "test")

        # Not worried about the timestampKey
        actual_user_history_item.timestampKey = None
        self.assertEqual(expected_result, actual_user_history_item)


@mock.patch.dict('os.environ', {"ENVIRONMENT": "TEST_ENVIRONMENT"},
                 {"REGION": "TEST_ES_METADATA_ENDPOINT"})
class TestConsumerUseHistoryToDynamo(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        ENVIRONMENT = os.environ.get('ENVIRONMENT')
        REGION = os.environ.get("REGION")
        user_history_builder = UserHistoryBuilder()
        unwrapper = SqsSnsUnwrapper(sns_unwrapper=SnsUnwrapper(),
                                    sqs_unwrapper=SqsUnwrapper())
        log_client = NoOpLogger()

        self.user_history_to_dynamo = ConsumerUserHistoryToDynamo(
            REGION,
            ENVIRONMENT,
            user_history_builder,
            unwrapper,
            log_client
        )

    def test_event_unwrapper(self):
        expected = [
            Message(
                customer={'name': 'uktv'},
                identifier=Identifier(eventId='f23c0c19-26d0-4f06-8136-9d6dc1dc7fdd',
                                      operationId='7eb70474-1d01-4bcd-bdaf-4b1d2b910fe8'),
                user=User(accountId=None, userId='********',
                          anonId='b19d4c536c0e3769c3322943783ea9aa',
                          primaryId='********', clusterId=None),
                timestamp=Timestamp(
                    initiated=datetime(2021, 3, 15, 16, 36, 37, tzinfo=timezone.utc),
                    received=datetime(2021, 3, 15, 16, 36, 37, tzinfo=timezone.utc)),
                action='play',
                context=Context(application='api_beacon_lite', environment='feature',
                                server=None, site=None, code_version=None),
                thing=[
                    Thing(
                        id='CTAU555K', name=None, typeName=None, genre=[],
                        contentRating=[], actor=[], director=[], partOfSeason={},
                        partOfSeries={}, publication=[], keywords=[],
                        episodeNumber=None, description=None, duration=None,
                        datePublished=None,
                        custom={
                            'subGenre': [], 'brand': [],
                            'channel': [], 'category': [], 'subCategory': []}
                    )
                ],
                custom={'anonId': 'b19d4c536c0e3769c3322943783ea9aa',
                        'eventType': 'play', 'itemId': 'CTAU555K',
                        'platform': 'youview', 'replayed': '20210315163636',
                        'replayedFrom': 'pre', 'replayed_from': 'production',
                        'sourcePage': 'theBill', 'userId': '********'},
                sourceOperationId=None, messageAttributes=None
            )
        ]
        event_unwrapper = self.user_history_to_dynamo.event_unwrapper(test_event)
        self.assertEqual(expected, event_unwrapper)

    def test_failed_to_unwrap(self):
        with self.assertRaises(Exception) as e:
            self.user_history_to_dynamo.event_unwrapper({})

        self.assertTrue('Failed to unwrap beacons' in str(e.exception))
