import datetime
from unittest import TestCase
from unittest.mock import patch
from urllib.parse import urlencode

import freezegun
from freezegun.api import FakeDatetime

from tests.functions.test_api_events.data import fast_lane_lambda_event, \
    event_forward_lambda_event_with_message_attrbutes
from thefilter.aws.elasticache import ElasticacheRepositoryStub
from thefilter.aws.kinesis import FirehoseStubClient
from thefilter.aws.sqs import SqsEventForwardingUnwrapper, StubSqsPublisher
from thefilter.behaviours.IpToCountry.ip_to_country_interface import \
    IpToCountryResolverStub
from thefilter.functions.api_event_forwarding.api_event_forwarding import EventForwarding
from thefilter.model.context import Context
from thefilter.model.identifier import Identifier
from thefilter.model.messages.message import Message
from thefilter.model.schemaorg import Thing
from thefilter.model.timestamp import Timestamp
from thefilter.model.user import User
from thefilter.publisher import NoOpPublisher
from thefilter.repositories import StubMetadataLiteRepository

metadata_items = {
    "test_thing_id": {
        "thing": {
            "id": "series_1_episode_1",
            "brandId": "test_thing_brandid",
            "typeName": "Episode",
            "partOfSeries": {
                "id": "series_1"
            },
            "age": 10,
            "popularity": 10
        }
    },
    "c2VyaWVzOzEwMTY=": {
        "thing": {
            "id": "series_1_episode_2",
            "brandId": "brandy_boi",
            "typeName": "Episode",
            "partOfSeries": {
                "id": "series_2"
            },
            "age": 10,
            "popularity": 10
        }
    }
}


class TestEventForwardingApi(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.api = EventForwarding(
            region='eu-west-2',
            environment='test_environment',
            customer='test_customer',
            metadata_lite_repo=StubMetadataLiteRepository(metadata_items),
            sqs_unwrapper=SqsEventForwardingUnwrapper(),
            sns_topic='test_sns_topic',
            sns_publisher=NoOpPublisher(),
            firehose_client=FirehoseStubClient(
                delivery_stream_name='test_delivery_stream_name',
                region_name='eu-west-2'
            ),
            rtve_cdp_firehose_client=FirehoseStubClient(
                delivery_stream_name='test_rtve_cdp_firehose_client',
                region_name='eu-west-2'
            ),
            elasticache_events_repository=ElasticacheRepositoryStub(),
            ip_to_country_resolver=IpToCountryResolverStub(),
            is_backstage_customer=False,
            google_conversion_sqs_publisher=StubSqsPublisher(),
            meta_conversion_sqs_publisher=StubSqsPublisher()
        )

    def test_handle_event(self):
        event = fast_lane_lambda_event
        response = self.api.handle_event(event)
        expected = {'statusCode': 200}
        self.assertEqual(expected, response)

    def test_handle_event_with_message_attributes(self):
        message_attributes = {
            "user_id": "test_user_123",
            "action": "play",
            "thing_id": "test_thing_id",
            "ignore": "postman",
            "headers_user_agent": "PostmanRuntime/7.43.0",
            "timestamp_received": "2025-02-27T16:54:18+00:00",
            "timestamp_initiated": "2025-02-27T16:54:18+00:00",
            "datepartition": "2025-02-27",
            "context_application": "api_events",
            "context_environment": "feature",
            "customer": "backstagedemo",
            "identifier_eventid": "70090c52-1952-487b-85f0-2f5b5c292b15",
            "source_ip": "*******",
            "messageAttributes": {
                "firehose_24iq": {
                    "StringValue": "true",
                    "stringListValues": [],
                    "binaryListValues": [],
                    "dataType": "String"
                },
                "sendToUserHistory": {
                    "StringValue": "true",
                    "stringListValues": [],
                    "binaryListValues": [],
                    "dataType": "String"
                },
                "action": {
                    "StringValue": "play",
                    "stringListValues": [],
                    "binaryListValues": [],
                    "dataType": "String"
                },
                "elasticache_24iq": {
                    "StringValue": "true",
                    "stringListValues": [],
                    "binaryListValues": [],
                    "dataType": "String"
                }
            }
        }
        event = fast_lane_lambda_event
        response = self.api.handle_event(event)
        expected = {'statusCode': 200}
        self.assertEqual(expected, response)

    def test_create_and_publish_ddb_message(self):
        # Can be used for debugging brand_id lookup and ddb persistence.
        body = {
            'user_id': 'test_user_rumpelstiltskin5', 'account_id': 'test_accountId',
            'action': 'play', 'thing_id': 'c2VyaWVzOzEwMTY=',
            'response_id': 'test_responseId', 'ignore': 'Postman',
            'user_agent': 'PostmanRuntime/7.31.3',
            'timestamp_received': '2023-04-17T16:38:10+00:00',
            'timestamp_initiated': '2023-04-17T16:38:10+00:00',
            'datepartition': '2023-04-17', 'context_application': 'api_events',
            'context_environment': 'feature', 'customer': 'epix',
            'identifier_eventid': '2ca1a5f6-3e28-4f3c-91f9-8b10aec6df94',
            'messageAttributes': {}
        }
        headers = {}
        self.api._create_and_publish_ddb_message(body, headers)

    def test_unwrap_events(self):
        event = fast_lane_lambda_event
        unwrapped_events = self.api._unwrap_events(event)
        expected_body = [
            {
                'user_id': 'test_user_rumpelstiltskin5',
                'account_id': 'test_accountId',
                'action': 'play',
                'thing_id': 'c2VyaWVzOzEwMTY=',
                'response_id': 'test_responseId',
                'ignore': 'Postman',
                'user_agent': 'PostmanRuntime/7.31.3',
                'timestamp_received': '2023-04-17T16:38:10+00:00',
                'timestamp_initiated': '2023-04-17T16:38:10+00:00',
                'datepartition': '2023-04-17',
                'context_application': 'api_events',
                'context_environment': 'feature',
                'customer': 'epix',
                'identifier_eventid': '2ca1a5f6-3e28-4f3c-91f9-8b10aec6df94',
                'messageAttributes': {}
            }
        ]

        self.assertEqual(expected_body, unwrapped_events)

    def test_unwrap_events_with_message_attributes(self):
        event = event_forward_lambda_event_with_message_attrbutes
        unwrapped_events = self.api._unwrap_events(event)
        expected_body = [
            {
                'user_id': 'test_user_rumpelstiltskin5',
                'account_id': 'test_accountId',
                'action': 'play',
                'thing_id': 'c2VyaWVzOzEwMTY=',
                'response_id': 'test_responseId',
                'ignore': 'Postman',
                'user_agent': 'PostmanRuntime/7.31.3',
                'timestamp_received': '2023-04-17T16:38:10+00:00',
                'timestamp_initiated': '2023-04-17T16:38:10+00:00',
                'datepartition': '2023-04-17',
                'context_application': 'api_events',
                'context_environment': 'feature',
                'customer': 'epix',
                'identifier_eventid': '2ca1a5f6-3e28-4f3c-91f9-8b10aec6df94',
                'messageAttributes': {
                    'firehose_24iq': {
                        'stringValue': 'true',
                        'stringListValues': [],
                        'binaryListValues': [],
                        'dataType': 'String'
                    },
                    'sendToUserHistory': {
                        'stringValue': 'true',
                        'stringListValues': [],
                        'binaryListValues': [],
                        'dataType': 'String'
                    },
                    'action': {
                        'stringValue': 'play',
                        'stringListValues': [],
                        'binaryListValues': [],
                        'dataType': 'String'
                    },
                    'elasticache_24iq': {
                        'stringValue': 'true',
                        'stringListValues': [],
                        'binaryListValues': [],
                        'dataType': 'String'
                    },
                    'firehose_rtve': {
                        'stringValue': 'true',
                        'stringListValues': [],
                        'binaryListValues': [],
                        'dataType': 'String'
                    },
                }
            }
        ]

        self.assertEqual(expected_body, unwrapped_events)

    def test_construct_message_attributes(self):
        pass

    def test_publish_events(self):
        pass

    @freezegun.freeze_time('2023-02-13T11:36:58+00:00')
    def test_ddb_uh(self):
        lambda_event = {
            'resource': '/v0/events', 'path': '/v0/events',
            'httpMethod': 'POST',
            'headers': {
                'User-Agent': 'PostmanRuntime/7.30.0',
                'Referer': 'This came from somewhere on the net'
            },
            'requestContext': {
                'stage': 'feature',
                'domainPrefix': 'uktv'
            },
            'queryStringParameters': None,
            'pathParameters': None, 'stageVariables': None,
            'body': '{\n    "user_id": "test_user_id",\n    '
                    '"user_anon_id": "test_anon_id",\n    "user_account_id": '
                    '"test_user_account_id",\n    "action": "play",\n    '
                    '"thing_id": "test_thing_id",\n    "response_id": '
                    '"test_response_id",\n    "ignore": "Postman"\n}',
            'isBase64Encoded': False
        }
        cleaned_lambda_body, headers = self.api._get_body_and_headers(lambda_event)
        ddb_uh_message = \
            self.api._create_ddb_user_history_message(cleaned_lambda_body, headers)
        expected_message = Message(
            customer={'name': 'test_customer'},
            identifier=Identifier(eventId='29084f5d-dd8f-470b-b495-00ab2fa0fcf7',
                                  operationId='61f6252d-4a5a-4701-bf5e-9bfd6bf68cd8'),
            user=User(accountId='test_user_account_id', userId='test_user_id',
                      anonId='test_anon_id', primaryId='test_user_id', clusterId=None),
            timestamp=Timestamp(initiated=FakeDatetime(2023, 2, 13, 11, 36, 58,
                                                       tzinfo=datetime.timezone.utc),
                                received=FakeDatetime(2023, 2, 13, 11, 36, 58,
                                                      tzinfo=datetime.timezone.utc)),
            action='play',
            context=Context(
                application='api_event_forwarding',
                environment='test_environment',
                server='', site='AWS', code_version=None
            ),
            thing=[
                Thing(id='test_thing_id', brandId=None, recommendationId=None, name=None,
                      alternateName=None, typeName=None, genre=[], subGenre=[],
                      contentRating=[], actor=[], director=[], producer=[], crew=[],
                      partOfSeason={}, partOfSeries={}, publication=[], keywords=[],
                      episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                      description=None, duration=None, datePublished=None,
                      image=None, inLanguage=None,
                      isLiveBroadcast=None, productionCompany=None, custom={},
                      space=None, isAdult=None, isKids=None)],
            custom={'ignore': 'Postman'}, sourceOperationId='test_response_id',
            messageAttributes=None
        )

        ddb_uh_message.identifier = Identifier(
            eventId='29084f5d-dd8f-470b-b495-00ab2fa0fcf7',
            operationId='61f6252d-4a5a-4701-bf5e-9bfd6bf68cd8'
        )
        self.assertEqual(expected_message, ddb_uh_message)

        message = self.api._update_brand_id(ddb_uh_message)
        brand_id = message.thing[0].brandId
        expected = 'test_thing_brandid'
        self.assertEqual(expected, brand_id)

    def test_update_brand_id(self):
        message = Message.get_default_message('test_customer')
        message.thing = [{"brandId": 'no_brand_id_lookup_required'}]
        actual = self.api._update_brand_id(message)
        self.assertEqual(message, actual)

    def test_update_brand_id_no_record(self):
        message = Message.get_default_message('test_customer')
        message.thing = [{"id": 'not_in_metadata'}]
        actual = self.api._update_brand_id(message)
        self.assertEqual(message, actual)

    @freezegun.freeze_time('2023-02-13T11:36:58+00:00')
    def test_event_with_brandid(self):
        lambda_event = {
            'resource': '/v0/events', 'path': '/v0/events',
            'httpMethod': 'POST',
            'headers': {
                'User-Agent': 'PostmanRuntime/7.30.0',
                'Referer': 'This came from somewhere on the net'
            },
            'requestContext': {
                'stage': 'feature',
                'domainPrefix': 'uktv'
            },
            'queryStringParameters': None,
            'pathParameters': None, 'stageVariables': None,
            'body': '{\n    "user_id": "test_user_id",\n    '
                    '"user_anon_id": "test_anon_id",\n    "user_account_id": '
                    '"test_user_account_id",\n    "action": "play",\n    '
                    '"thing_id": "test_thing_id",\n    "response_id": '
                    '"test_response_id",\n    "ignore": "Postman",\n'
                    '"brand_id": "test_seriesId"}',
            'isBase64Encoded': False
        }

        cleaned_lambda_body, headers = self.api._get_body_and_headers(lambda_event)
        ddb_uh_message = \
            self.api._create_ddb_user_history_message(cleaned_lambda_body, headers)

        expected_message = Message(
            customer={'name': 'test_customer'},
            identifier=Identifier(eventId='29084f5d-dd8f-470b-b495-00ab2fa0fcf7',
                                  operationId='61f6252d-4a5a-4701-bf5e-9bfd6bf68cd8'),
            user=User(accountId='test_user_account_id', userId='test_user_id',
                      anonId='test_anon_id', primaryId='test_user_id', clusterId=None),
            timestamp=Timestamp(initiated=FakeDatetime(2023, 2, 13, 11, 36, 58,
                                                       tzinfo=datetime.timezone.utc),
                                received=FakeDatetime(2023, 2, 13, 11, 36, 58,
                                                      tzinfo=datetime.timezone.utc)),
            action='play',
            context=Context(
                application='api_event_forwarding',
                environment='test_environment',
                server='', site='AWS', code_version=None
            ),
            thing=[
                Thing(id='test_thing_id', brandId='test_seriesId', recommendationId=None,
                      name=None,
                      alternateName=None, typeName=None, genre=[], subGenre=[],
                      contentRating=[], actor=[], director=[], producer=[], crew=[],
                      partOfSeason={}, partOfSeries={}, publication=[], keywords=[],
                      episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                      description=None, duration=None, datePublished=None,
                      image=None, inLanguage=None,
                      isLiveBroadcast=None, productionCompany=None, custom={},
                      space=None, isAdult=None, isKids=None)],
            custom={'ignore': 'Postman'}, sourceOperationId='test_response_id',
            messageAttributes=None
        )

        ddb_uh_message.identifier = Identifier(
            eventId='29084f5d-dd8f-470b-b495-00ab2fa0fcf7',
            operationId='61f6252d-4a5a-4701-bf5e-9bfd6bf68cd8'
        )
        self.assertEqual(expected_message, ddb_uh_message)

        message = self.api._update_brand_id(ddb_uh_message)
        brand_id = message.thing[0].brandId
        expected = 'test_seriesId'
        self.assertEqual(expected, brand_id)

    def test_filter_ddb(self):
        cleaned_lambda_body = {
            "action": "video_stop",
            "event_trigger": "playback_stop",
            "session_id": "test_session_id",
            "timestamp_initiated": "2023-03-21T09:05:30+00:00",
            "device_id": "",
            "device_type": "",
            "device_platform": "",
            "device_timezone": "",
            "service_id": "",
            "user_id": "test_user_id",
            "user_profile_id": "",
            "thing_id": "test_thing_id",
            "duration": "",
            "progress_pct": ""
        }
        ddb_uh_message = self.api._filter_body(cleaned_lambda_body)
        # Expect the cleaned_lambda_body to not be filtered out
        self.assertEqual(cleaned_lambda_body, ddb_uh_message)

    def test_appstage_raw_event(self):
        event = {
            "session_id": "c6f19087-e917-4105-adf4-5b83b9d3a54c",
            "timestamp_initiated": "2023-03-21T10:05:30+00:00",
            "device_id": "265FFA46-F362-4BA1-BA5C-6DD1BDDC50ED",
            "device_type": "iphone",
            "device_platform": "ios",
            "device_timezone": -1,
            "service_id": "5e0ad1b0-515e-11e9-a7ed-371ac744bd33",
            "user_id": "163178127",
            "user_profile_id": "AYAnu0bIqAa586QbXKL3",
            "action": "scroll",
            "event_trigger": "scroll_90",
            "source_page": "home",
            "thing_id": "",
            "percent_scrolled": "90",
            "user_agent": "RNVApp/301150044 CFNetwork/1327.0.4 Darwin/21.6.0",
            "timestamp_received": "2023-03-21T09:05:30+00:00",
            "datepartition": "2023-03-21",
            "context_application": "api_events",
            "context_environment": "pre",
            "customer": "test",
            "identifier_eventid": "9c9a612f-9652-4b65-9ecc-d24f430782dc"
        }
        ddb_uh_message = self.api._filter_body(event)
        # While it's a lovely appstage_raw_event,
        # it will be filtered out based on the action
        expected = {}
        self.assertEqual(expected, ddb_uh_message)

class TestEventForwardingApiHandleEvent(TestCase):
    def setUp(self):
        metadata_items = {
            "test_thing_id": {
                "thing": {
                    "id": "series_1_episode_1",
                    "brandId": "test_thing_brandid",
                    "typeName": "Episode",
                    "partOfSeries": {
                        "id": "series_1"
                    },
                    "age": 10,
                    "popularity": 10
                }
            }
        }
        self.api = EventForwarding(
            region='eu-west-2',
            environment='test_environment',
            customer='test_customer',
            metadata_lite_repo=StubMetadataLiteRepository(metadata_items),
            sqs_unwrapper=SqsEventForwardingUnwrapper(),
            sns_topic='test_sns_topic',
            sns_publisher=NoOpPublisher(),
            firehose_client=FirehoseStubClient(
                delivery_stream_name='test_delivery_stream_name',
                region_name='eu-west-2'
            ),
            rtve_cdp_firehose_client=FirehoseStubClient(
                delivery_stream_name='test_rtve_cdp_firehose_client',
                region_name='eu-west-2'
            ),
            elasticache_events_repository=ElasticacheRepositoryStub(),
            ip_to_country_resolver=IpToCountryResolverStub(),
            is_backstage_customer=False,
            google_conversion_sqs_publisher=StubSqsPublisher(),
            meta_conversion_sqs_publisher=StubSqsPublisher()
        )

    @patch(
        'thefilter.functions.api_event_forwarding.api_event_forwarding.EventForwarding.handle_event')
    def test_handle_event(self, mock_handle_event):
        event = fast_lane_lambda_event
        mock_handle_event.return_value = {'statusCode': 200}

        response = self.api.handle_event(event)
        expected = {'statusCode': 200}

        self.assertEqual(expected, response)
        mock_handle_event.assert_called_once_with(event)

class TestHandleUtmSourceIQ(TestCase):
    def setUp(self):
        self.event = {
            "custom_campaign_url": "https://www.example.com/product-page"
        }

    def test_utm_sourceiq_exists(self):
        # Test when utm_sourceIQ exists in the URL
        query_params = {
            "utm_source": "facebook",
            "utm_medium": "cpc",
            "utm_sourceIQ": "something"
        }
        self.event["custom_campaign_url"] += "?" + urlencode(query_params)
        result = EventForwarding._handle_utm_sourceiq(self.event)
        self.assertEqual(result["custom_utm_sourceiq"], "something")

    def test_utm_sourceiq_exists_different_casing(self):
        # Test when utm_sourceiq  exists in the URL
        query_params = {
            "utm_source": "facebook",
            "utm_medium": "cpc",
            "utm_sOuRcEiQ": "something"
        }
        self.event["custom_campaign_url"] += "?" + urlencode(query_params)
        result = EventForwarding._handle_utm_sourceiq(self.event)
        self.assertEqual(result["custom_utm_sourceiq"], "something")

    def test_utm_sourceiq_missing(self):
        # Test when utm_sourceIq is missing in the URL
        query_params = {
            "utm_source": "facebook",
            "utm_medium": "cpc"
        }
        self.event["custom_campaign_url"] += "?" + urlencode(query_params)
        result = EventForwarding._handle_utm_sourceiq(self.event)
        self.assertNotIn("custom_utm_sourceiq", result)

    def test_no_custom_campaign_url(self):
        # Test when custom_campaign_url is not present
        event_without_url = {}
        result = EventForwarding._handle_utm_sourceiq(event_without_url)
        self.assertNotIn("custom_utm_sourceiq", result)

    def test_empty_custom_campaign_url(self):
        # Test when custom_campaign_url is empty
        self.event["custom_campaign_url"] = ""
        result = EventForwarding._handle_utm_sourceiq(self.event)
        self.assertNotIn("custom_utm_sourceiq", result)
