import os
import unittest
from unittest import TestCase
from unittest.mock import patch

from thefilter.aws.sqs import SqsMetadataUnwrapper
from thefilter.functions.consumer_beacon_fast_lane.consumer_beacon_fast_lane import \
    ConsumerBeaconFastLane

test_event = {
    "Records": [
        {
            "messageId": "e4147298-82f7-40b6-a061-c64cad51dbd8",
            "receiptHandle": "AQEBgSiJbjy7VteoF+9uF31uOXPIoaeV5NJDjD0LjVYaMdeN+ae+ySp8/xLA3ObuEsDUOzrZHzhSS6LBnRW2uJ78cfQjqZad1xdNWNEUUitNSMT5aL4PYz+kUvocwHpaTAzQH0/MhA4lfitZpZrZvkomxGSqZqPYPQ2YzZXRmNUuB5vZNuLRSgiqU8SROnHwMWQNrMZcckoZL/4Yx4PwyKYucWa/yUjS3eFUICLbSlkxXXX5zL/jCIufJOoDo+wxkhZtYBz9E9QD5IIK9UWx3dd3q2pnHpFaX9/Ii2aHuW+PnMZ4C2SiG995IHGQ/7jOv0B4IS7NKI6Oy/FVRvXM/AkxByDDf7ToY147B80/6nW9uapEX0/Sq4bC1dNK6wb52jovuMba0eIzVRULC4Ne6YCY3Q==",
            "body": "{\"action\":\"view\",\"sourceOperationId\":null,\"context\":{\"application\":\"mParticleLambda/epix\",\"code_version\":null,\"environment\":\"production\",\"server\":\"AWS\",\"site\":null},\"timestamp\":{\"initiated\":\"2021-03-11T06:13:00.592+00:00\",\"received\":\"2021-03-11T06:13:02.537Z\"},\"identifier\":{\"eventId\":\"d3bdeac7-a427-47cc-b52f-f54d916c5bae\",\"operationId\":\"a8874e0f-6f0b-41d6-b210-dd207837132f\"},\"custom\":{\"epix\":{\"accountStatus\":null,\"appVersion\":null,\"attributionCount\":0,\"billingCycle\":null,\"billingProvider\":null,\"campaign\":null,\"campaignAbbr\":null,\"churnType\":null,\"city\":null,\"contentDurationSeconds\":0,\"country\":null,\"epixId\":0,\"freeTrialType\":null,\"is4k\":null,\"isdownload\":null,\"islive\":null,\"localTimezone\":null,\"otherId\":null,\"other2Id\":null,\"platform\":\"Apple Mobile\",\"platformAbbr\":null,\"platformModel\":null,\"productPrice\":0.0,\"productTrialPeriodDays\":0,\"reportingDate\":null,\"selection_type\":\"Collection\",\"selection_name\":\"science-fiction-and-fantasy\",\"selection_rail\":\"Home\",\"selection_position\":14,\"sessionDurationSeconds\":0,\"startingPlayheadSeconds\":0,\"state\":null,\"tenant\":null,\"isTestUser\":null,\"timestampUTC\":null,\"titleId\":null,\"uniqueId\":null,\"watchDurationSeconds\":0,\"userAgent\":\"mParticle Ruby client/1.0.10\",\"timezoneOffset\":\"-6\",\"appName\":\"Epix\",\"timeZoneName\":null,\"localTimestampInitiated\":\"2021-03-11T00:13:00.592-06:00\",\"debug\":false,\"tablet\":false}},\"customer\":{\"name\":\"epix\"},\"thing\":[{\"id\":\"bW92aWU7MTc1Mjk=\",\"name\":null,\"typeName\":null}],\"user\":{\"userId\":\"f327034531b48bd02a2c133593095e127ab0976e7b7f7d8c5e150688379b5c2c\"}}",
            "attributes": {
                "ApproximateReceiveCount": "1",
                "SentTimestamp": "1615443182572",
                "SenderId": "AROAWXMPEZE5D622T7JUK:production-mparticle_beacon",
                "ApproximateFirstReceiveTimestamp": "1615443182573"
            },
            "messageAttributes": {},
            "md5OfBody": "95fa3f811d98b2a2174bd963913d4d89",
            "eventSource": "aws:sqs",
            "eventSourceARN": "arn:aws:sqs:us-east-1:462546061626:production-BeaconFastLane",
            "awsRegion": "us-east-1"
        }
    ]
}


class TestConsumerBeaconFastLane(TestCase):
    def __init__(self, *args, **kwargs):
        super(TestConsumerBeaconFastLane, self).__init__(*args, **kwargs)

        self._unwrapper = SqsMetadataUnwrapper()
        with patch.dict('os.environ',
                        {"SNS_TOPIC": "TEST_SNS_TOPIC"},
                        {"REGION": "TEST_REGION"}):
            self._consumer_beacon_fast_lane = \
                ConsumerBeaconFastLane(os.environ.get("SNS_TOPIC"))

    def test_unwrap_beacons(self):
        expected_unwrapped_beacon = [
            {'action': 'view', 'sourceOperationId': None,
             'context': {'application': 'mParticleLambda/epix', 'code_version': None,
                         'environment': 'production', 'server': 'AWS', 'site': None},
             'timestamp': {'initiated': '2021-03-11T06:13:00.592+00:00',
                           'received': '2021-03-11T06:13:02.537Z'},
             'identifier': {'eventId': 'd3bdeac7-a427-47cc-b52f-f54d916c5bae',
                            'operationId': 'a8874e0f-6f0b-41d6-b210-dd207837132f'},
             'custom': {'epix': {'accountStatus': None, 'appVersion': None,
                                 'attributionCount': 0, 'billingCycle': None,
                                 'billingProvider': None, 'campaign': None,
                                 'campaignAbbr': None, 'churnType': None, 'city': None,
                                 'contentDurationSeconds': 0, 'country': None,
                                 'epixId': 0, 'freeTrialType': None, 'is4k': None,
                                 'isdownload': None, 'islive': None,
                                 'localTimezone': None, 'otherId': None,
                                 'other2Id': None, 'platform': 'Apple Mobile',
                                 'platformAbbr': None, 'platformModel': None,
                                 'productPrice': 0.0, 'productTrialPeriodDays': 0,
                                 'reportingDate': None, 'selection_type': 'Collection',
                                 'selection_name': 'science-fiction-and-fantasy',
                                 'selection_rail': 'Home', 'selection_position': 14,
                                 'sessionDurationSeconds': 0,
                                 'startingPlayheadSeconds': 0, 'state': None,
                                 'tenant': None, 'isTestUser': None,
                                 'timestampUTC': None, 'titleId': None, 'uniqueId': None,
                                 'watchDurationSeconds': 0,
                                 'userAgent': 'mParticle Ruby client/1.0.10',
                                 'timezoneOffset': '-6', 'appName': 'Epix',
                                 'timeZoneName': None,
                                 'localTimestampInitiated': '2021-03-11T00:13:00.592-06:00',
                                 'debug': False, 'tablet': False}},
             'customer': {'name': 'epix'},
             'thing': [{'id': 'bW92aWU7MTc1Mjk=', 'name': None, 'typeName': None}],
             'user': {
                 'userId': 'f327034531b48bd02a2c133593095e127ab0976e7b7f7d8c5e150688379b5c2c'},
             'messageAttributes': {}}
        ]

        unwrapped_beacons = \
            self._consumer_beacon_fast_lane.unwrap_beacons(test_event, self._unwrapper)

        self.assertTrue(expected_unwrapped_beacon, unwrapped_beacons)

    def test_failed_to_unwrap(self):
        with self.assertRaises(Exception) as e:
            self._consumer_beacon_fast_lane.unwrap_beacons({}, self._unwrapper)

        self.assertTrue(
            'consumer_beacon_fast_lane_lambda: Failed to unwrap beacons' in str(
                e.exception))

    def test_construct_message_attributes(self):
        expected_message_attributes = {
            'action': {'DataType': 'String', 'StringValue': 'view'},
            'sendToUserHistory': {'DataType': 'String', 'StringValue': 'true'}
        }

        unwrapped_beacons = \
            self._consumer_beacon_fast_lane.unwrap_beacons(test_event, self._unwrapper)

        beacon = unwrapped_beacons[0]
        message_attributes = \
            self._consumer_beacon_fast_lane._construct_message_attributes(beacon)

        self.assertTrue(expected_message_attributes, message_attributes)


if __name__ == "__main__":
    unittest.main()
