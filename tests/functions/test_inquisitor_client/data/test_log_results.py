test_log_results = {
    "pre-UKTV-api_beacon": [
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:02.300'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]77c3e7a06e2c484ca1ec2cc0cbe94001'},
         {'field': '@message',
          'value': '{\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}\n'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBxI5GhgCBlFOoyQAAAAC8XQAwQAGWUNa0AAAA0IgASjkgMfXzDEw/9zM18wxOIwHQJm2CUjh4gFQttcBGAAgARD5BhgB'}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:02.301'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]77c3e7a06e2c484ca1ec2cc0cbe94001'},
         {'field': '@message',
          'value': '[ERROR] Exception: {\'status_code\':403, \'log_entry\': {\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}}\nTraceback (most recent call last):\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/lambda_function.py", line 45, in lambda_handler\n\xa0\xa0\xa0\xa0api_beacon.publish_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 34, in publish_beacon\n\xa0\xa0\xa0\xa0self.publish_single_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 42, in publish_single_beacon\n\xa0\xa0\xa0\xa0return log_error_and_throw_exception("Failed to validate event as beacon", e)\n\xa0\xa0File "/var/task/thefilter/aws/aws_lambda.py", line 205, in log_error_and_throw_exception\n\xa0\xa0\xa0\xa0raise Exception({"status_code": status, "log_entry": log_entry})'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBxI5GhgCBlFOoyQAAAAC8XQAwQAGWUNa0AAAA0IgASjkgMfXzDEw/9zM18wxOIwHQJm2CUjh4gFQttcBGAAgARD6BhgB'}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:05.246'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]d2fbbe1565e249c7a5491bad8ea30813'},
         {'field': '@message',
          'value': '{\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}\n'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QABI5GhgCBlG6OLgAAAAAnW6tggAGWUNfsAAAAIIgASj3wszXzDEw4tnT18wxONsLQPitE0j+ywJQ0cACGAAgARA8GAE='}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:05.246'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]d2fbbe1565e249c7a5491bad8ea30813'},
         {'field': '@message',
          'value': '[ERROR] Exception: {\'status_code\': 500, \'log_entry\': {\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}}\nTraceback (most recent call last):\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/lambda_function.py", line 45, in lambda_handler\n\xa0\xa0\xa0\xa0api_beacon.publish_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 34, in publish_beacon\n\xa0\xa0\xa0\xa0self.publish_single_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 42, in publish_single_beacon\n\xa0\xa0\xa0\xa0return log_error_and_throw_exception("Failed to validate event as beacon", e)\n\xa0\xa0File "/var/task/thefilter/aws/aws_lambda.py", line 205, in log_error_and_throw_exception\n\xa0\xa0\xa0\xa0raise Exception({"status_code": status, "log_entry": log_entry})'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QABI5GhgCBlG6OLgAAAAAnW6tggAGWUNfsAAAAIIgASj3wszXzDEw4tnT18wxONsLQPitE0j+ywJQ0cACGAAgARA9GAE='}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:05.818'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]3a804100da464a0f948902cce870c946'},
         {'field': '@message',
          'value': '{\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}\n'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBBI5GhgCBlDtuycAAAAA6xZ93gAGWUNdYAAAB3IgASjZzcnXzDEwpObO18wxOLMJQMvjDEiRnAJQ5ZACGAAgARCEBhgB'}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:05.836'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]3a804100da464a0f948902cce870c946'},
         {'field': '@message',
          'value': '[ERROR] Exception: {\'status_code\': 500, \'log_entry\': {\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}}\nTraceback (most recent call last):\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/lambda_function.py", line 45, in lambda_handler\n\xa0\xa0\xa0\xa0api_beacon.publish_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 34, in publish_beacon\n\xa0\xa0\xa0\xa0self.publish_single_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 42, in publish_single_beacon\n\xa0\xa0\xa0\xa0return log_error_and_throw_exception("Failed to validate event as beacon", e)\n\xa0\xa0File "/var/task/thefilter/aws/aws_lambda.py", line 205, in log_error_and_throw_exception\n\xa0\xa0\xa0\xa0raise Exception({"status_code": status, "log_entry": log_entry})'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBBI5GhgCBlDtuycAAAAA6xZ93gAGWUNdYAAAB3IgASjZzcnXzDEwpObO18wxOLMJQMvjDEiRnAJQ5ZACGAAgARCFBhgB'}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:08.117'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]3a804100da464a0f948902cce870c946'},
         {'field': '@message',
          'value': '{\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}\n'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBBI5GhgCBlDtuycAAAAA6xZ93gAGWUNdYAAAB3IgASjZzcnXzDEwpObO18wxOLMJQMvjDEiRnAJQ5ZACGAAgARCkBhgB'}],
        [{'field': '@log', 'value': '294068739031:/aws/lambda/pre-UKTV-api_beacon'},
         {'field': '@timestamp', 'value': '2024-01-02 16:13:08.117'},
         {'field': '@logStream',
          'value': '2024/01/02/[$LATEST]3a804100da464a0f948902cce870c946'},
         {'field': '@message',
          'value': '[ERROR] Exception: {\'status_code\': 500, \'log_entry\': {\'message\': \'Failed to validate event as beacon\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 108, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}}\nTraceback (most recent call last):\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/lambda_function.py", line 45, in lambda_handler\n\xa0\xa0\xa0\xa0api_beacon.publish_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 34, in publish_beacon\n\xa0\xa0\xa0\xa0self.publish_single_beacon(event)\n\xa0\xa0File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 42, in publish_single_beacon\n\xa0\xa0\xa0\xa0return log_error_and_throw_exception("Failed to validate event as beacon", e)\n\xa0\xa0File "/var/task/thefilter/aws/aws_lambda.py", line 205, in log_error_and_throw_exception\n\xa0\xa0\xa0\xa0raise Exception({"status_code": status, "log_entry": log_entry})'},
         {'field': '@ptr',
          'value': 'CnEKMAosMjk0MDY4NzM5MDMxOi9hd3MvbGFtYmRhL3ByZS1VS1RWLWFwaV9iZWFjb24QBBI5GhgCBlDtuycAAAAA6xZ93gAGWUNdYAAAB3IgASjZzcnXzDEwpObO18wxOLMJQMvjDEiRnAJQ5ZACGAAgARClBhgB'}]
    ],
    "pre-customer-api_rec": [
        [
            {'field': '@log', 'value': '294068739031:/aws/lambda/function_b'},
            {'field': '@timestamp', 'value': '2024-01-02 13:55:16.411'},
            {'field': '@logStream',
             'value': '2024/01/02/[$LATEST]abc'},
            {'field': '@message',
             'value': 'messageboi'},
            {'field': '@ptr',
             'value': 'abcde'}
        ],
        [
            {'field': '@log', 'value': '294068739031:/aws/lambda/function_b'},
            {'field': '@timestamp', 'value': '2024-01-02 13:57:16.411'},
            {'field': '@logStream',
             'value': '2024/01/02/[$LATEST]abc'},
            {'field': '@message',
             'value': '{\'message\': \'api_rec message\', \'level\': \'ERROR\', \'exception\': "Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \'NoneType\'>, valid types: <class \'dict\'>", \'Traceback\': \'Traceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_rec/api_rec.py", line 1234, in validate_event\\n    beacon: Message = self._validator.validate(event)\\n                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 57, in validate\\n    return super(ExternalMessageValidator, self).validate_internal(message_candidate,\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/validation/message_validator.py", line 32, in validate_internal\\n    validate(instance=message_candidate, schema=schema)\\n  File "/opt/python/jsonschema/validators.py", line 899, in validate\\n    raise error\\njsonschema.exceptions.ValidationError: None is not of type \\\'string\\\'\\n\\nFailed validating \\\'type\\\' in schema[\\\'properties\\\'][\\\'thing\\\'][\\\'items\\\'][\\\'properties\\\'][\\\'id\\\']:\\n    {\\\'type\\\': \\\'string\\\'}\\n\\nOn instance[\\\'thing\\\'][0][\\\'id\\\']:\\n    None\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 40, in publish_single_beacon\\n    beacon = self.validate_event(event)\\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/functions/api_beacon/api_beacon.py", line 115, in validate_event\\n    message_id = dlq_publisher.publish_to_dead_letter_queue(event)\\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 206, in publish_to_dead_letter_queue\\n    response = self._publish_to_queue(self.DEAD_LETTER_QUEUE, message)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/var/task/thefilter/aws/sqs.py", line 188, in _publish_to_queue\\n    response = self._sqs_client.send_message(\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 553, in _api_call\\n    return self._make_api_call(operation_name, kwargs)\\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 962, in _make_api_call\\n    request_dict = self._convert_to_request_dict(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/client.py", line 1036, in _convert_to_request_dict\\n    request_dict = self._serializer.serialize_to_request(\\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\n  File "/opt/python/botocore/validate.py", line 381, in serialize_to_request\\n    raise ParamValidationError(report=report.generate_report())\\nbotocore.exceptions.ParamValidationError: Parameter validation failed:\\nInvalid type for parameter MessageAttributes, value: None, type: <class \\\'NoneType\\\'>, valid types: <class \\\'dict\\\'>\\n\'}\n'},
            {'field': '@ptr',
             'value': 'abcde'}
        ]
    ]
}
