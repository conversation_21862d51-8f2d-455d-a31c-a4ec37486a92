import json
from unittest import TestCase

from thefilter.functions.cloudfront_fallback_origin_response.lambda_function import \
    CloudfrontFallbackOriginResponse


class TestCloudfrontFallbackOriginResponse(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._api = CloudfrontFallbackOriginResponse()
        self._test_400_event = {
            'Records': [
                {'cf': {
                    'config': {'distributionDomainName': 'd3m8f6nhm463sy.cloudfront.net',
                               'distributionId': 'E25EUCWDSQ1CL3',
                               'eventType': 'origin-response',
                               'requestId': 'bCCRYYSHUxDkRQL4DpSgkddHMMpQYTCnZsIniTK_DT4TaWXN8EIz0w=='},
                    'request': {'clientIp': '*************', 'headers': {
                        'x-forwarded-for': [
                            {'key': 'X-Forwarded-For', 'value': '*************'}],
                        'user-agent': [
                            {'key': 'User-Agent', 'value': 'Amazon CloudFront'}],
                        'via': [{'key': 'Via',
                                 'value': '1.1 a1242769df6497af27605f96ae7bbd4e.cloudfront.net (CloudFront)'}],
                        'accept-encoding': [{'key': 'Accept-Encoding', 'value': 'gzip'}],
                        'host': [{'key': 'Host',
                                  'value': 'thefilter-feature-epix-fallback.s3.amazonaws.com'}],
                        'cache-control': [
                            {'key': 'Cache-Control', 'value': 'no-cache'}]},
                                'method': 'GET', 'origin': {
                            's3': {'authMethod': 'origin-access-identity',
                                   'customHeaders': {},
                                   'domainName': 'thefilter-feature-epix-fallback.s3.amazonaws.com',
                                   'path': '', 'region': 'us-east-1'}},
                                'querystring': '', 'uri': '/v0/search/zzz'},
                    'response': {'headers': {'x-amz-request-id': [
                        {'key': 'x-amz-request-id', 'value': 'KP5XCYQK2V6ATAQX'}],
                        'x-amz-id-2': [{'key': 'x-amz-id-2',
                                        'value': 'oh7FktZ+BQ0JW8tppqhh8IqT2pirmMymgUjPDdQRp95ERCBdC9In1d1GDFqVb/bY7pYnfSdjhbcCUVcOi/M8gA=='}],
                        'date': [{'key': 'Date',
                                  'value': 'Thu, 13 Jul 2023 14:05:08 GMT'}],
                        'server': [
                            {'key': 'Server', 'value': 'AmazonS3'}],
                        'content-type': [{'key': 'Content-Type',
                                          'value': 'application/xml'}],
                        'transfer-encoding': [
                            {'key': 'Transfer-Encoding',
                             'value': 'chunked'}]}, 'status': '403',
                        'statusDescription': 'Forbidden'}}
                }
            ]
        }

        self._test_200_event = {
            'Records': [
                {'cf':
                    {
                        'config': {
                            'distributionDomainName': 'd3m8f6nhm463sy.cloudfront.net',
                            'distributionId': 'E25EUCWDSQ1CL3',
                            'eventType': 'origin-response',
                            'requestId': 'ST7XffBvxPNDXmzDRLKhvp5hhcd5acuJxUmEWQk4rqpOMLGOnQhU8Q=='},
                        'request': {'clientIp': '*************',
                                    'headers': {'x-forwarded-for': [
                                        {'key': 'X-Forwarded-For',
                                         'value': '*************'}],
                                        'user-agent': [{
                                            'key': 'User-Agent',
                                            'value': 'Amazon CloudFront'}],
                                        'via': [
                                            {'key': 'Via',
                                             'value': '1.1 d87b5da71c300746940779825e266b64.cloudfront.net (CloudFront)'}],
                                        'accept-encoding': [
                                            {
                                                'key': 'Accept-Encoding',
                                                'value': 'gzip'}],
                                        'host': [
                                            {'key': 'Host',
                                             'value': 'thefilter-feature-epix-fallback.s3.amazonaws.com'}],
                                        'cache-control': [{
                                            'key': 'Cache-Control',
                                            'value': 'no-cache'}]},
                                    'method': 'GET', 'origin': {
                                's3': {'authMethod': 'origin-access-identity',
                                       'customHeaders': {},
                                       'domainName': 'thefilter-feature-epix-fallback.s3.amazonaws.com',
                                       'path': '', 'region': 'us-east-1'}},
                                    'querystring': '',
                                    'uri': '/v0/search/ch'}, 'response': {'headers': {
                        'x-amz-id-2': [{'key': 'x-amz-id-2',
                                        'value': 'gkCWrG/8GNhph/DWHNXywZhSZnZfECVypaUPxJDUTekGvNy42UaoTPuSLMTzCtkqketXbHRkf4M='}],
                        'x-amz-request-id': [
                            {'key': 'x-amz-request-id', 'value': 'N5M4TTJSQZR4HT4H'}],
                        'date': [
                            {'key': 'Date', 'value': 'Thu, 13 Jul 2023 14:05:19 GMT'}],
                        'last-modified': [{'key': 'Last-Modified',
                                           'value': 'Wed, 12 Jul 2023 16:25:04 GMT'}],
                        'etag': [
                            {'key': 'ETag',
                             'value': '"************c596aca95f6b0354c250"'}],
                        'x-amz-server-side-encryption': [
                            {'key': 'x-amz-server-side-encryption', 'value': 'AES256'}],
                        'accept-ranges': [{'key': 'Accept-Ranges', 'value': 'bytes'}],
                        'server': [{'key': 'Server', 'value': 'AmazonS3'}],
                        'content-type': [
                            {'key': 'Content-Type', 'value': 'application/json'}],
                        'content-length': [{'key': 'Content-Length', 'value': '3001'}]},
                        'status': '200',
                        'statusDescription': 'OK'}
                    }
                }
            ]
        }

    # Update these tests after the lambda is deployed and I can see the events
    def test_handle_400_event(self):
        result = self._api.handle_event(self._test_400_event)
        expected = {
            'headers': {'x-amz-request-id': [
                {'key': 'x-amz-request-id', 'value': 'KP5XCYQK2V6ATAQX'}],
                'x-amz-id-2': [{'key': 'x-amz-id-2',
                                'value': 'oh7FktZ+BQ0JW8tppqhh8IqT2pirmMymgUjPDdQRp95ERCBdC9In1d1GDFqVb/bY7pYnfSdjhbcCUVcOi/M8gA=='}],
                'date': [
                    {'key': 'Date', 'value': 'Thu, 13 Jul 2023 14:05:08 GMT'}],
                'server': [{'key': 'Server', 'value': 'AmazonS3'}],
                'content-type': [
                    {'key': 'Content-Type', 'value': 'application/json'}],
                'transfer-encoding': [
                    {'key': 'Transfer-Encoding', 'value': 'chunked'}]},
            'status': 200, 'statusDescription': 'OK',
            'body': '{"code": 200, "responseId": "b7c77587-1eda-4def-bf49-9ef0ea31b7ab", "title": "", "items": []}'}

        # because the result.body.responseId is a GUID, we need to spoof it here.
        body = json.loads(result['body'])
        body['responseId'] = 'b7c77587-1eda-4def-bf49-9ef0ea31b7ab'
        result['body'] = json.dumps(body)

        self.assertEqual(expected, result)

    def test_handle_200_event(self):
        result = self._api.handle_event(self._test_200_event)
        expected = {
            'headers': {
                'x-amz-id-2': [{'key': 'x-amz-id-2',
                                'value': 'gkCWrG/8GNhph/DWHNXywZhSZnZfECVypaUPxJDUTekGvNy42UaoTPuSLMTzCtkqketXbHRkf4M='}],
                'x-amz-request-id': [
                    {'key': 'x-amz-request-id', 'value': 'N5M4TTJSQZR4HT4H'}],
                'date': [
                    {'key': 'Date', 'value': 'Thu, 13 Jul 2023 14:05:19 GMT'}],
                'last-modified': [{'key': 'Last-Modified',
                                   'value': 'Wed, 12 Jul 2023 16:25:04 GMT'}],
                'etag': [{'key': 'ETag',
                          'value': '"************c596aca95f6b0354c250"'}],
                'x-amz-server-side-encryption': [
                    {'key': 'x-amz-server-side-encryption', 'value': 'AES256'}],
                'accept-ranges': [{'key': 'Accept-Ranges', 'value': 'bytes'}],
                'server': [{'key': 'Server', 'value': 'AmazonS3'}],
                'content-type': [
                    {'key': 'Content-Type', 'value': 'application/json'}],
                'content-length': [{'key': 'Content-Length', 'value': '3001'}]},
            'status': '200', 'statusDescription': 'OK'}
        self.assertEqual(expected, result)
