from random import Random
from unittest import TestCase

from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.domains.standard_domain import StandardDomain


class TestStandardDomain(TestCase):
    def setUp(self):
        self.random = Random(1234)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._standard_domain = StandardDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value={
                    'value': ['data_0', 'data_1'],
                    'topic': 'user',
                    'domain': 'some_domain'
                }
            )
        )

    def test_handle_request(self):
        customer = "test_customer"
        topic = None
        domain = "some_domain"
        result = self._standard_domain.handle_request(customer, topic, domain)
        expected = ['data_0', 'data_1']
        self.assertEqual(expected, result)
