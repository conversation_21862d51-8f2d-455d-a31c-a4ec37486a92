from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.domains.analytics import AnalyticsDomain
from unittest import TestCase
from unittest.mock import Mock, patch
from thefilter.jobs.analytics_job.tasks.backstage_analytics.backstage_analytics_task import BackstageAnalytics
from thefilter.jobs.analytics_job.tasks.analytics_task_config import AnalyticsTaskConfig
import freezegun

class TestAnalyticsDomain(TestCase):

    @patch('thefilter.jobs.analytics_job.tasks.backstage_analytics.backstage_analytics_task.AthenaQueryClient')
    @patch('thefilter.jobs.analytics_job.tasks.backstage_analytics.backstage_analytics_task.GlueClient')
    @patch('thefilter.jobs.analytics_job.tasks.backstage_analytics.backstage_analytics_task.BotoDDBClient')
    def setUp(self, mock_boto_ddb_client, mock_glue_client, mock_athena_query_client):

        self._analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value={}
            )
        )
        mock_log_client = Mock()


        mock_config = AnalyticsTaskConfig(
            customer="test-customer",
            environment="test-env",
            region="eu-west-2",
            log_client=mock_log_client,
            customers=["flowsports"]
        )

        self._backstage_analytics = BackstageAnalytics(mock_config)

    def test_handle_analytics_query_active_users(self):
        active_users_stub = {
            'value': {
                'data': {
                    "dateTo": "2024-02-02T12:21:28+00:00",
                    "difference": "-51",
                    "total": "1319",
                    "dateFromHistory": "2024-01-31T12:21:28+00:00",
                    "dateFrom": "2024-02-01T12:21:28+00:00"
                }
            },
            'topic': 'active-users-1d',
            'domain': 'analytics'
        }

        analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=active_users_stub
            )
        )

        expected_result = {
            'data': {
                "dateTo": "2024-02-02T12:21:28+00:00",
                "difference": "-51",
                "total": "1319",
                "dateFromHistory": "2024-01-31T12:21:28+00:00",
                "dateFrom": "2024-02-01T12:21:28+00:00"
            }
        }

        info_result = analytics_domain.handle_analytics_query(
            customer="test",
            topic="active-users",
            qsp={"days": "1"}
        )

        self.assertEqual(expected_result, info_result, "Analytics result matches for active-users")

    def test_handle_analytics_query_playbacks_size_1(self):
        playbacks_stub = {
            'value': {
                'data': {
                    "2024-09-26T03:00:00+00:00": "10"
                }
            },
            'topic': 'playbacks-1d',
            'domain': 'analytics'
        }

        analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=playbacks_stub
            )
        )

        expected_result = {
            'data': {
                "2024-09-26T03:00:00+00:00": "10"
            }
        }

        info_result = analytics_domain.handle_analytics_query(
            customer="test",
            topic="playbacks",
            qsp={"size": "1"}
        )

        self.assertEqual(expected_result, info_result, "Analytics result matches with size=1 for playbacks")

    def test_handle_analytics_query_playbacks_size_3(self):

        playbacks_stub = {
            'value': {
                'data': {
                    "2024-09-26T03:00:00+00:00": "10",
                    "2024-09-26T04:00:00+00:00": "15",
                    "2024-09-26T14:00:00+00:00": "133",
                }
            },
            'topic': 'playbacks-1d',
            'domain': 'analytics'
        }

        analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=playbacks_stub
            )
        )

        expected_result = {
            'data': {
                "2024-09-26T03:00:00+00:00": "10",
                "2024-09-26T04:00:00+00:00": "15",
                "2024-09-26T14:00:00+00:00": "133"
            }
        }

        info_result = analytics_domain.handle_analytics_query(
            customer="test",
            topic="playbacks",
            qsp={"size": "3"}
        )

        self.assertEqual(expected_result, info_result, "Analytics result matches with size=3 for playbacks")

    def test_handle_analytics_query_playbacks_without_size(self):

        playbacks_stub = {
            'value': {
                'data': {
                    "2024-09-26T03:00:00+00:00": "10",
                    "2024-09-26T04:00:00+00:00": "15",
                    "2024-09-26T14:00:00+00:00": "133",
                    "2024-09-26T05:00:00+00:00": "20"
                }
            },
            'topic': 'playbacks-1d',
            'domain': 'analytics'
        }

        analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=playbacks_stub
            )
        )

        expected_result = {
            'data': {
                "2024-09-26T03:00:00+00:00": "10",
                "2024-09-26T04:00:00+00:00": "15",
                "2024-09-26T14:00:00+00:00": "133",
                "2024-09-26T05:00:00+00:00": "20"
            }
        }

        info_result = analytics_domain.handle_analytics_query(
            customer="test",
            topic="playbacks",
            qsp={}
        )

        self.assertEqual(expected_result, info_result, "Analytics result matches without size for playbacks")

    @freezegun.freeze_time('2024-09-30T12:00:00+00:00')
    def test_convert_duration_to_days_3m(self):

        duration = '3m'
        expected_days = 92  # From June 30 to September 30, 2024 is 92 days

        delta_days = self._backstage_analytics._convert_duration_to_days(duration)

        self.assertEqual(
            delta_days,
            expected_days,
            f"Expected {expected_days} days for {duration} duration, got {delta_days}"
        )

    def test_handle_analytics_query_active_users_3m(self):
        active_users_stub = {
            'value': {
                'data': {
                    "dateTo": "2024-09-30T12:00:00+00:00",
                    "difference": "-100",
                    "total": "1500",
                    "dateFromHistory": "2024-06-30T12:00:00+00:00",
                    "dateFrom": "2024-07-01T12:00:00+00:00"
                }
            },
            'topic': 'active-users-3m',
            'domain': 'analytics'
        }

        analytics_domain = AnalyticsDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=active_users_stub
            )
        )

        expected_result = {
            'data': {
                "dateTo": "2024-09-30T12:00:00+00:00",
                "difference": "-100",
                "total": "1500",
                "dateFromHistory": "2024-06-30T12:00:00+00:00",
                "dateFrom": "2024-07-01T12:00:00+00:00"
            }
        }

        info_result = analytics_domain.handle_analytics_query(
            customer="test",
            topic="active-users",
            qsp={"months": "3"}
        )

        self.assertEqual(
            expected_result,
            info_result,
            "Analytics result matches for active-users with 3m qsp"
        )

    def test_generate_topic_key(self):
        expected = "top-users-30d"
        actual = self._analytics_domain._generate_topic_key("top-users", {"days": "30"})

        self.assertEqual(expected, actual, "Topic key matches")

    def test_generate_topic_key_no_qsp(self):
        expected = "top-users-1d"
        actual = self._analytics_domain._generate_topic_key("top-users", {})

        self.assertEqual(expected, actual, "Topic key matches with no qsp")


