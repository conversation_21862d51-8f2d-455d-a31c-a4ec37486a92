from random import Random
from unittest import TestCase
from unittest.mock import patch

from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.domains.random import RandomDomain


class TestRandomDomain(TestCase):

    def setUp(self):
        self.random = Random(1234)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._random_domain = RandomDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value={
                    'value': ['user_1', 'user_2', 'user_3', 'user_4', 'user_5'],
                    'topic': 'user',
                    'domain': 'random'
                }
            )
        )

    def test_handle_random_ids_count_all(self):
        expected_result = {
            'user': ['user_1', 'user_2', 'user_3', 'user_4', 'user_5']
        }

        info_result = self._random_domain.handle_random_ids(
            customer="test",
            info_result={},
            topic="user",
            qsp={"count": "all"}
        )

        self.assertEqual(expected_result, info_result, "Count all result matches")

    @patch('thefilter.domains.random.random')
    def test_handle_random_ids_no_count(self, random):
        random.sample._mock_side_effect = self.random.sample
        expected_result = {
            'user': ['user_4']
        }

        info_result = self._random_domain.handle_random_ids(
            customer="test",
            info_result={},
            topic="user",
            qsp={}
        )

        self.assertEqual(expected_result, info_result, "No count result matches")

    @patch('thefilter.domains.random.random')
    def test_handle_random_ids_count_2(self, random):
        random.sample._mock_side_effect = self.random.sample
        expected_result = {
            'user': ['user_4', 'user_1']
        }

        info_result = self._random_domain.handle_random_ids(
            customer="test",
            info_result={},
            topic="user",
            qsp={"count": 2}
        )

        self.assertEqual(expected_result, info_result, "Count 2 result matches")
