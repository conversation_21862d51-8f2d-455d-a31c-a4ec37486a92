from unittest import TestCase

from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.config.centralised_config import CentralisedConfig
from thefilter.functions.api_info.api_info import InfoApi
from thefilter.logs.logclient import NoOpLogger


class TestInfoApi(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        region = "eu-west-1"
        environment = "pre"
        log_client = NoOpLogger()
        self._api = InfoApi(
            region = region,
            environment = environment,
            customer="test_customer",
            log_client=log_client,
            customer_config=CentralisedConfig(
                region=region,
                environment=environment,
                logger=log_client,
                repo_type="local"
            ),
            ddb_client=BotoDDBClientStub(
                region=region,
                query_return_value={
                    'value': ['data_0', 'data_1'],
                    'topic': 'user',
                    'domain': 'some_domain'
                }
            )
        )

    def test_get_request(self):
        test_event = {
            "queryStringParameters": {},
            "pathParameters": {
                "domain": "something"
            }
        }
        expected = ['data_0', 'data_1']
        result = self._api.get_request(test_event)
        self.assertEqual(expected, result)

    def test_get_topic_with_encoded_topic(self):
        event = {
            "pathParameters": {
                "domain": "model",
                "topic": "Because%2520You%2520Watched"
            }
        }

        topic = self._api._get_topic(event)
        self.assertEqual("Because%2520You%2520Watched", topic)

    def test_get_topic_with_single_encoding(self):
        event = {
            "pathParameters": {
                "domain": "model",
                "topic": "Because%20You%20Watched"
            }
        }

        topic = self._api._get_topic(event)
        self.assertEqual("Because%20You%20Watched", topic)

    def test_get_topic_no_path_parameters(self):
        event = {
            "pathParameters": None
        }
        topic = self._api._get_topic(event)
        self.assertIsNone(topic)

    def test_get_topic_no_topic_key(self):
        event = {
            "pathParameters": {
                "domain": "model"
            }
        }
        topic = self._api._get_topic(event)
        self.assertIsNone(topic)

    def test_get_topic_with_spaces(self):
        event = {
            "pathParameters": {
                "domain": "model",
                "topic": "Because You Watched"
            }
        }
        topic = self._api._get_topic(event)
        self.assertEqual("Because You Watched", topic)


    def test_get_customer_with_valid_backstageServiceID(self):
        event = {
            'httpMethod': 'GET',
            'queryStringParameters': {
                'backstageServiceID': '3426aa19-7ebe-11e8-91c9-02b9ba38a7ac'
            },
            'pathParameters': {
                'topic': 'seed',
                'domain': 'random'
            }
        }

        customer = self._api._get_customer(event)
        self.assertEqual('alhurra', customer)

    def test_get_customer_with_customer_name(self):
        event = {
            'httpMethod': 'GET',
            'queryStringParameters': {
                'customer': 'epix'
            },
            'pathParameters': {
                'topic': 'seed',
                'domain': 'random'
            }
        }

        customer = self._api._get_customer(event)
        self.assertEqual('epix', customer)

    def test_get_customer_with_unknown_backstageServiceID(self):
        event = {
            'httpMethod': 'GET',
            'queryStringParameters': {
                'backstageServiceID': 'invalid_id'
            },
            'pathParameters': {
                'topic': 'seed',
                'domain': 'random'
            }
        }

        with self.assertRaises(Exception) as context:
            self._api._get_customer(event)
        self.assertEqual("500: get_request failed", str(context.exception))

    def test_get_customer_without_customer_name_or_backstageServiceID(self):
        event = {
            'httpMethod': 'GET',
            'pathParameters': {
                'topic': 'seed',
                'domain': 'random'
            }
        }

        customer = self._api._get_customer(event)
        self.assertEqual(None, customer)