from random import Random
from unittest import TestCase

import freezegun

from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.domains.model import ModelDomain


class TestModelDomain(TestCase):

    def setUp(self):
        self.random = Random(1234)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._model_domain = ModelDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=[
                    {
                        "domain": "model",
                        "topic": "Most Popular - Drama Movies",
                        "dateCreated": "2024-11-27T11:47:03+00:00",
                        "value": {
                            "audit_info": {
                                "created_by": "dev-test-user",
                                "date_created": "2024-11-27T11:47:03+00:00",
                                "date_updated": "2024-11-27",
                                "updated_by": "dev-test-user"
                            },
                            "model_type": "most_popular",
                            "overlay": "overlay=play",
                            "playlist_name": "Most Popular - Movies",
                            "slot_id": "882caad6-f7a0-4fda-aabc-d7a693980b6b",
                            "slot_url": "https://customer.feature.thefilter.com/v0/slots/882caad6-f7a0-4fda-aabc-d7a693980b6b/items?variant=7d&overlay=play&typename=Movie&userId={userId}",
                            "status": "Live",
                            "typename": [
                                {
                                    "label": "Movie",
                                    "value": "Movie"
                                }
                            ],
                            "variant": "variant=7d"
                        }
                    },
                    {
                        "domain": "model",
                        "topic": "More%20Like%20This",
                        "dateCreated": "2024-06-25T11:44:42+00:00",
                        "value": {
                            "audit_info": {
                                "created_by": "script",
                                "date_created": "2024-06-25T11:44:42+00:00",
                                "date_updated": "2024-06-25T11:44:42+00:00",
                                "updated_by": "script"
                            },
                            "model_type": "mlt",
                            "overlay": "overlay=standard",
                            "playlist_name": "More Like This",
                            "slot_id": "a0182e54-72ee-4180-8a02-8b4289a64a04",
                            "slot_url": "https://customer.feature.thefilter.com/v0/slots/a0182e54-72ee-4180-8a02-8b4289a64a04/items?userId={user_id}&seedIds={seed_id}",
                            "status": "Live",
                            "variant": "variant=standard"
                        }
                    }
                ]
            )
        )

        self._domain_expected = [
            {
                'audit_info': {
                    'created_by': 'dev-test-user',
                    'date_created': '2024-11-27T11:47:03+00:00',
                    'date_updated': '2024-11-27',
                    'updated_by': 'dev-test-user'
                },
                'model_type': 'most_popular',
                'overlay': 'overlay=play',
                'playlist_name': 'Most Popular - Movies',
                'slot_id': '882caad6-f7a0-4fda-aabc-d7a693980b6b',
                'slot_url': 'https://customer.feature.thefilter.com/v0/slots/882caad6-f7a0-4fda-aabc-d7a693980b6b/items?variant=7d&overlay=play&typename=Movie&userId={userId}',
                'status': 'Live',
                "typename": [
                    {
                        "label": "Movie",
                        "value": "Movie"
                    }
                ],
                'variant': 'variant=7d'
            },
            {
                'audit_info': {
                    'created_by': 'script',
                    'date_created': '2024-06-25T11:44:42+00:00',
                    'date_updated': '2024-06-25T11:44:42+00:00',
                    'updated_by': 'script'
                },
                'model_type': 'mlt',
                'overlay': 'overlay=standard',
                'playlist_name': 'More Like This',
                'slot_id': 'a0182e54-72ee-4180-8a02-8b4289a64a04',
                'slot_url': 'https://customer.feature.thefilter.com/v0/slots/a0182e54-72ee-4180-8a02-8b4289a64a04/items?userId={user_id}&seedIds={seed_id}',
                'status': 'Live',
                'variant': 'variant=standard'
            }
        ]

        self._model_domain_topic = ModelDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value={
                    "domain": "model",
                    "topic": "More%20Like%20This",
                    "dateCreated": "2024-06-25T11:44:42+00:00",
                    "value": {
                        "audit_info": {
                            "created_by": "script",
                            "date_created": "2024-06-25T11:44:42+00:00",
                            "date_updated": "2024-06-25T11:44:42+00:00",
                            "updated_by": "script"
                        },
                        "model_type": "mlt",
                        "overlay": "overlay=standard",
                        "playlist_name": "More Like This",
                        "slot_id": "a0182e54-72ee-4180-8a02-8b4289a64a04",
                        "slot_url": "https://customer.feature.thefilter.com/v0/slots/a0182e54-72ee-4180-8a02-8b4289a64a04/items?userId={user_id}&seedIds={seed_id}",
                        "status": "Live",
                        "variant": "variant=standard"
                    }
                }
            )
        )

    def test_get_model_domain(self):
        actual = self._model_domain.handle_get_domain_request("test")
        self.assertEqual(self._domain_expected, actual, "Domain request matches")

    def test_get_model_domain_topic(self):
        expected = {
            'audit_info': {
                'created_by': 'script',
                'date_created': '2024-06-25T11:44:42+00:00',
                'date_updated': '2024-06-25T11:44:42+00:00',
                'updated_by': 'script'
            },
            'model_type': 'mlt',
            'overlay': 'overlay=standard',
            'playlist_name': 'More Like This',
            'slot_id': 'a0182e54-72ee-4180-8a02-8b4289a64a04',
            'slot_url': 'https://customer.feature.thefilter.com/v0/slots/a0182e54-72ee-4180-8a02-8b4289a64a04/items?userId={user_id}&seedIds={seed_id}',
            'status': 'Live',
            'variant': 'variant=standard'
        }
        actual = self._model_domain_topic.handle_get_topic_request("test",
                                                                   "More%20Like%20This")

        self.assertEqual(expected, actual, "Domain request matches")

    @freezegun.freeze_time('2024-11-27T12:30:29+00:00')
    def test_handle_put_topic_request(self):

        with self.assertRaises(Exception):
            self._model_domain.handle_put_request("test","Empty Payload",{})

        request_payload = {
            "playlist_name": "Most Popular - Drama Movies",
            "model_type": "most_popular",
            "overlay": "overlay=play",
            "variant": "variant=7d",
            "slot_url": "https://customer.feature.thefilter.com/v0/slots/882caad6-f7a0-4fda-aabc-d7a693980b6b/items?variant=7d&overlay=play&excludeGenre=Romance&genre=Drama&typename=Movie&userId={userId}",
            "status": "Live",
            "slot_id": "882caad6-f7a0-4fda-aabc-d7a693980b6b",
            "audit_info": {
                "created_by": "dev-test-user",
                "updated_by": "dev-test-user",
                "date_created": "2024-11-27",
                "date_updated": "2024-11-27"
            },
            "excludeGenre": [{"value": "Romance", "label": "Romance"}],
            "genre": [{"value": "Drama", "label": "Drama"}],
            "typename": [{"value": "Movie", "label": "Movie"}]
        }

        response = self._model_domain.handle_put_request("test",
                                                         "Most Popular - Drama Movies",
                                                         request_payload)

        self.assertEqual(response, "200: Success", "Successful put request")

        # Add new record to expected domain
        expected = self._domain_expected
        expected.append({
            "playlist_name": "Most Popular - Drama Movies",
            "model_type": "most_popular",
            "overlay": "overlay=play",
            "variant": "variant=7d",
            "audit_info": {
                "created_by": "dev-test-user",
                "updated_by": "dev-test-user",
                "date_created": "2024-11-27T12:30:29+00:00",
                "date_updated": "2024-11-27"
            },
            "slot_url": "https://customer.feature.thefilter.com/v0/slots/882caad6-f7a0-4fda-aabc-d7a693980b6b/items?variant=7d&overlay=play&excludeGenre=Romance&genre=Drama&typename=Movie&userId={userId}",
            "status": "Live",
            "slot_id": "882caad6-f7a0-4fda-aabc-d7a693980b6b",
            "excludeGenre": [{"value": "Romance", "label": "Romance"}],
            "genre": [{"value": "Drama", "label": "Drama"}],
            "typename": [{"value": "Movie", "label": "Movie"}],
            "owner": "dev-test-user",
            "deletable": True
        })

        actual = self._model_domain.handle_get_domain_request("test")
        self.assertEqual(expected, actual, "Domain request matches")
