from random import Random
from unittest import TestCase

from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.domains.job_health import JobHealthDomain


class TestJobHealthDomain(TestCase):

    def setUp(self):
        self.random = Random(1234)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._job_health_domain = JobHealthDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value=[{
                    "domain": "job_health",
                    "topic": "standard_browse_indexer",
                    "dateCreated": "2024-09-02T10:04:49+00:00",
                    "value": {
                        "last_run": "2024-09-02T10:04:49+00:00",
                        "next_run": "2024-09-02T13:03:00+00:00",
                        "status": "Successful"
                    }
                },
                    {
                        "domain": "job_health",
                        "topic": "events_persistence",
                        "dateCreated": "2024-09-02T09:25:12+00:00",
                        "value": {
                            "last_run": "2024-09-02T09:25:12+00:00",
                            "next_run": "2024-09-02T11:20:00+00:00",
                            "status": "Successful"
                        }
                    }
                ]
            )
        )

        self._job_health_domain_topic = JobHealthDomain(
            environment="test",
            ddb_client=BotoDDBClientStub(
                region="eu-west-2",
                query_return_value={
                    "domain": "job_health",
                    "topic": "standard_browse_indexer",
                    "dateCreated": "2024-09-02T10:04:49+00:00",
                    "value": {
                        "last_run": "2024-09-02T10:04:49+00:00",
                        "next_run": "2024-09-02T13:03:00+00:00",
                        "status": "Successful"
                    }
                }
            )
        )

    def test_format_job_health_topic(self):
        expected = "Standard Browse Indexer"
        actual = self._job_health_domain._format_job_health_topic(
            "standard_browse_indexer")

        self.assertEqual(expected, actual, "Formatted topic name matches")

    def test_get_job_health_domain(self):
        expected = [
            {
                "topic": "Standard Browse Indexer",
                "value": {
                    "last_run": "2024-09-02T10:04:49+00:00",
                    "next_run": "2024-09-02T13:03:00+00:00",
                    "status": "Successful"
                }
            },
            {
                "topic": "Events Persistence",
                "value": {
                    "last_run": "2024-09-02T09:25:12+00:00",
                    "next_run": "2024-09-02T11:20:00+00:00",
                    "status": "Successful"
                }
            }
        ]
        actual = self._job_health_domain.handle_get_domain_request("test")

        self.assertEqual(expected, actual, "Domain request matches")

    def test_get_job_health_domain_topic(self):
        expected = {
            "topic": "Standard Browse Indexer",
            "value": {
                "last_run": "2024-09-02T10:04:49+00:00",
                "next_run": "2024-09-02T13:03:00+00:00",
                "status": "Successful"
            }
        }
        actual = self._job_health_domain_topic.handle_get_topic_request("test", "standard_browse_indexer")

        self.assertEqual(expected, actual, "Domain request matches")
