from datetime import datetime, timezone
from unittest import TestCase

from thefilter.aws.athena import AthenaQueryClientStub
from thefilter.aws.dynamoDB import BotoDDBClientStub
from thefilter.behaviours.recent_trending.recent_trending_builder import \
    RecentTrendingBuilder
from thefilter.logs.logclient import NoOpLogger


class TestRealTimeTrendingBuilder(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        REGION = 'eu-west-2'
        ENVIRONMENT = 'pre'
        CUSTOMER = 'test_customer'
        self._api = RecentTrendingBuilder(
            region=REGION,
            environment=ENVIRONMENT,
            customer=CUSTOMER,
            logger=NoOpLogger(),
            ddb_client=BotoDDBClientStub('eu-west-2', []),
            athena_client=AthenaQueryClientStub(
                stub_response=[]
            )
        )

    def test_get_formatted_results(self):
        items = {
            'item_0': {'0': 168, '1': 203, '2': 226},
            'item_1': {'0': 126, '1': 144, '2': 141},
            'item_2': {'0': 11, '1': 5},
            'item_3': {'0': 10, '2': 7},
            'item_4': {'0': 9, '1': 12, '2': 6},
            'item_5': {'0': 7},
            'item_6': {'1': 100, '2': 140},
            'item_7': {'1': 140, '2': 100},
            'item_8': {'1': 6, '2': 6},
            'item_9': {'1': 7}
        }
        keys = ['0', '1', '2']
        formatted_results = self._api.get_formatted_results(keys, items)
        expected = {
            'item_0': [168, 203, 226], 'item_1': [126, 144, 141], 'item_2': [11, 5, 2.5],
            'item_3': [10, 3.5, 7], 'item_4': [9, 12, 6], 'item_5': [7, 3.5, 3.5],
            'item_6': [50.0, 100, 140], 'item_7': [50.0, 140, 100],
            'item_8': [3.0, 6, 6], 'item_9': [3.5, 7, 3.5]}
        self.assertEqual(expected, formatted_results)

    def test_get_date_ranges(self):
        start = datetime.fromisoformat('2023-03-08T12:00:00+00:00')
        end = datetime.fromisoformat('2023-03-08T15:00:00+00:00')
        self._api._total_time = 3
        date_ranges = self._api._get_date_ranges(start, end)
        expected = [
            (
                datetime(2023, 3, 8, 11, 0, tzinfo=timezone.utc),
                datetime(2023, 3, 8, 12, 0, tzinfo=timezone.utc)
            ),
            (
                datetime(2023, 3, 8, 12, 0, tzinfo=timezone.utc),
                datetime(2023, 3, 8, 13, 0, tzinfo=timezone.utc)
            ),
            (
                datetime(2023, 3, 8, 13, 0, tzinfo=timezone.utc),
                datetime(2023, 3, 8, 14, 0, tzinfo=timezone.utc)
            ),
            (
                datetime(2023, 3, 8, 14, 0, tzinfo=timezone.utc),
                datetime(2023, 3, 8, 15, 0, tzinfo=timezone.utc)
            )
        ]
        self.assertEqual(expected, date_ranges)
