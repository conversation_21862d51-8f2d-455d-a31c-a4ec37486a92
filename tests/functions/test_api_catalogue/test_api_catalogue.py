import json
import os
import unittest
from dataclasses import fields
from unittest import TestCase, mock

import freezegun

from tests.functions.test_api_catalogue.data.test_api_catalogue_data import \
    catalogue_api_simple_event_single, \
    expected_result_catalogue_api_simple_event_single, \
    catalogue_api_event_batch, expected_formatted_catalogue_items, \
    actual_uktv_catalogue_event, expected_serialised_message, \
    expected_result_catalogue_api_simple_event_single_no_brand, \
    expected_result_catalogue_api_simple_event_single_missing_brand
from thefilter.aws.sns import DictionarySnsPublisher
from thefilter.functions.api_catalogue.activator import Activator
from thefilter.functions.api_catalogue.api_catalogue import ApiCatalogue
from thefilter.model.schemaorg import Thing, BaseThing
from thefilter.publisher import NoOpPublisher
from thefilter.validation.catalogue_thing_schema import catalogue_thing_schema

test_event = {
    'user': {'userId': '_tf-uktvcatalogueprocessor'},
    'identifier': {'operationId': '5999af2d-beff-4e2a-813b-b56c4bb60310',
                   'eventId': '511b8565-29b1-481c-9aa3-43be44499bdc'},
    'timestamp': {'initiated': '2021-03-18T11:57:06.087793+00:00',
                  'received': '2021-03-18T11:57:06.087817+00:00'}, 'action': 'catalogue',
    'thing': [{'space': 'subcategory', 'id': '20', 'name': 'Natural History',
               'typeName': 'SubCategory', 'genre': [], 'contentRating': [], 'actor': [],
               'director': [], 'partOfSeason': {}, 'partOfSeries': {}, 'publication': [],
               'keywords': [],
               'custom': {'slug': 'natural-history', 'brandCount': 5, 'subGenre': [],
                          'brand': [],
                          'channel': [{'id': '3844', 'name': 'Eden'},
                                      {'id': '3854', 'name': 'Dave'},
                                      {'id': '3866', 'name': 'Yesterday'},
                                      {'id': '3858', 'name': 'GOLD'}],
                          'category': [{'id': 4, 'name': 'Documentaries'}],
                          'subCategory': [{'id': 20, 'name': 'Natural History'}]}}],
    'context': {'application': 'awsbatch/uktv_promo_loader', 'environment': 'feature',
                'server': 'AWS_Batch', 'site': 'AWS', 'code_version': None}
}


@mock.patch.dict(
    os.environ, {
        "ENVIRONMENT": "test_environment",
        "SNS_TOPIC": "test_sns_topic"
    }
)
class TestApiCatalogue(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._api_catalogue = ApiCatalogue(
            environment="test_environment",
            sns_topic="test_sns_topic",
            publisher=NoOpPublisher()
        )

    def test_constructor_and_environment_variables(self):
        self.assertTrue(isinstance(self._api_catalogue, ApiCatalogue))

    def test_empty_event(self):
        actual = self._api_catalogue.publish_catalogue({})
        expected = {'code': 400, 'message': 'customer.name not provided on event.'}
        self.assertEqual(expected, actual)

    def test_event_only_with_customer_name(self):
        event = {
            "customer": {
                "name": "test_customer"
            }
        }
        actual = self._api_catalogue._validate_catalogue_items(event)
        expected = {'code': 400, 'message': 'No items provided on event.'}
        self.assertEqual(expected, actual)

    def test_basic_thing(self):
        event = {
            "customer": {
                "name": "test_customer"
            },
            "thing": [
                {
                    "id": "test_id",
                    "name": "test_name"
                }
            ]
        }
        expected = {
            'code': 200,
            'items': [
                Thing(
                    id='test_id', brandId=None, recommendationId=None, name='test_name',
                    alternateName=None, typeName=None, genre=[], subGenre=[],
                    contentRating=[], actor=[], director=[], producer=[], crew=[],
                    partOfSeason={}, partOfSeries={}, publication=[], keywords=[],
                    episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
                    description=None, duration=None, datePublished=None,
                    image=None, inLanguage=None,
                    isLiveBroadcast=None, productionCompany=None, custom={})]
        }
        actual = self._api_catalogue._validate_catalogue_items(event)
        self.assertEqual(expected, actual)

    def test_simple_event_single(self):
        event = catalogue_api_simple_event_single
        actual = self._api_catalogue._validate_catalogue_items(event)
        expected = {
            'code': 200,
            'items': [expected_result_catalogue_api_simple_event_single]
        }
        self.assertEqual(expected, actual)

    def test_catalogue_api_event_batch(self):
        event = catalogue_api_event_batch
        actual = self._api_catalogue._validate_catalogue_items(event)
        expected = {
            'code': 200,
            'items': [
                expected_result_catalogue_api_simple_event_single,
                expected_result_catalogue_api_simple_event_single
            ]
        }
        self.assertEqual(expected, actual)

    @freezegun.freeze_time("2022-12-15T13:59:45+00:00")
    def test_format_catalogue_items(self):
        self._api_catalogue._customer_name = 'test_customer'
        items = [expected_result_catalogue_api_simple_event_single]
        actual = self._api_catalogue._format_catalogue_items(items)
        actual[0]['identifier'] = {
            'eventId': '874f4eea-ddf3-41b0-aaf2-631e0532f6f6',
            'operationId': '8cde6df3-f008-4bb0-a30d-7363b22b29fe'
        }
        self.assertEqual(expected_formatted_catalogue_items, actual)

    @freezegun.freeze_time("2022-12-15T13:59:45+00:00")
    def test_format_catalogue_items_none_brandId(self):
        self._api_catalogue._customer_name = 'test_customer'
        items = [expected_result_catalogue_api_simple_event_single_no_brand]
        actual = self._api_catalogue._format_catalogue_items(items)
        actual[0]['identifier'] = {
            'eventId': '874f4eea-ddf3-41b0-aaf2-631e0532f6f6',
            'operationId': '8cde6df3-f008-4bb0-a30d-7363b22b29fe'
        }
        self.assertEqual(expected_formatted_catalogue_items, actual)

    @freezegun.freeze_time("2022-12-15T13:59:45+00:00")
    def test_format_catalogue_items_missing_brandId(self):
        self._api_catalogue._customer_name = 'test_customer'
        items = [expected_result_catalogue_api_simple_event_single_missing_brand]
        actual = self._api_catalogue._format_catalogue_items(items)
        actual[0]['identifier'] = {
            'eventId': '874f4eea-ddf3-41b0-aaf2-631e0532f6f6',
            'operationId': '8cde6df3-f008-4bb0-a30d-7363b22b29fe'
        }
        self.assertEqual(expected_formatted_catalogue_items, actual)

    def test_publisher_with_valid_event(self):
        self._api_catalogue._customer_name = 'test_customer'
        event = catalogue_api_simple_event_single

        response = self._api_catalogue.publish_catalogue(event)
        expected = {'code': 200}
        self.assertEqual(expected, response)

    def test_publisher_with_invalid_events(self):
        event = {
            "customer": {
                "name": "test_customer"
            },
            "thing": [
                {
                    'description': 'Why would you send an item with just a description!'
                }
            ]
        }
        response = self._api_catalogue.publish_catalogue(event)
        expected = {
            'code': 400,
            'message': 'Catalogue data missing required fields: id and name.'
        }
        self.assertEqual(expected, response)

        event = {
            "customer": {
                "name": "test_customer"
            },
            "thing": [
                {
                    'id': 'just_an_id'
                }
            ]
        }
        response = self._api_catalogue.publish_catalogue(event)
        expected = {
            'code': 400,
            'message': 'Catalogue data missing required fields: name.'
        }
        self.assertEqual(expected, response)

        event = {
            "customer": {
                "name": "test_customer"
            },
            "thing": [
                {
                    'name': 'just_a_name'
                }
            ]
        }
        response = self._api_catalogue.publish_catalogue(event)
        expected = {
            'code': 400,
            'message': 'Catalogue data missing required fields: id.'
        }
        self.assertEqual(expected, response)

    @freezegun.freeze_time('2022-12-19T14:38:15+00:00')
    @unittest.skipIf(os.environ.get('CI'), "Don't run this in GitLab")
    def test_publishing(self):
        event = actual_uktv_catalogue_event
        validated_response = self._api_catalogue._validate_catalogue_items(event)
        validated_items = validated_response['items']
        messages = self._api_catalogue._format_catalogue_items(validated_items)

        publisher = DictionarySnsPublisher(
            region="eu-west-2"
        )
        message = messages[0]
        message["identifier"] = {
            "eventId": "32c430a1-0312-4cfa-b912-4425ae836913",
            "operationId": "5c5767c9-97e7-4532-8f63-5213d62f18e7"
        }
        serialised_message = publisher._process_message(message)
        self.assertEqual(expected_serialised_message, serialised_message)

    def test_fields_not_in_thing(self):
        event = {
            "customer": {
                "name": "test_customer"
            },
            "items": [
                {
                    'id': 'example_id',
                    'name': 'just_a_name',
                    'unknown_field': 'pug',
                    'another_unknown_field': 'pug'
                }
            ]
        }
        validated_response = self._api_catalogue._validate_catalogue_items(event)
        validated_items = validated_response
        expected = {
            'code': 400,
            'message': 'Catalogue data incorrectly structured: unknown_field, '
                       'another_unknown_field fields are incorrectly named, or should be '
                       'added as custom fields.'
        }

        self.assertEqual(expected, validated_items)

    def test_thing_validator(self):
        event_with_bad_thing = {
            "customer": {
                "name": "test_customer"
            },
            "items": [
                {
                    'id': 'example_id',
                    'name': ['a_name_in_a_list_oopsie'],
                    'genre': ['drama', 'comedy']
                }
            ]
        }
        actual = self._api_catalogue._thingify_items(event_with_bad_thing, "items")
        expected = {
            'code': 400,
            'message': 'Catalogue data contains incorrect types: '
                       'type of name must be str; got list instead, '
                       'type of genre[0] must be dict; got str instead.'
        }
        self.assertEqual(expected, actual)

    def test_add_active_flag(self):
        clearly_a_bad_message = {}
        actual = self._api_catalogue._add_active_flag(clearly_a_bad_message)
        expected = {
            'code': 400,
            'message': 'Failed to add an active status flag to catalogue item: 0.'
        }
        self.assertEqual(expected, actual)


class ActivatorTests(TestCase):
    def __init__(self, *args, **kwargs):
        super(ActivatorTests, self).__init__(*args, **kwargs)
        with open('tests/data/test_thing.json', 'r') as f:
            self.test_thing = Thing(**json.load(f))
        with open('tests/data/test_message.json', 'r') as f:
            test_message = json.load(f)
            self.test_message = test_message
        self.timestamp = '2019-07-17T13:23:41+00:00'
        self.activator = Activator(self.timestamp)
        self._activated = self.activator.activate(test_message)

    def test_state_not_present(self):
        expected = self.test_message
        expected['thing'][0]['custom']['active'] = {
            'state': True, 'stateUpdateTimestamp': self.timestamp
        }

        self.assertEqual(expected, self._activated)

    def test_state_present_timestamp(self):
        expected = self.test_message
        expected['thing'][0]['custom']['active'] = {
            'state': False,
            'stateUpdateTimestamp': "2021-01-02T13:59:45+00:00"
        }

        test_message = self.test_message
        test_message['thing'][0]['custom']['active'] = {
            'state': False,
            'stateUpdateTimestamp': "2021-01-02T13:59:45+00:00"
        }

        self.assertEqual(expected, self._activated)

    def test_state_present_no_timestamp(self):
        expected = self.test_message
        expected['thing'][0]['custom']['active'] = {
            'state': False, 'stateUpdateTimestamp': "2021-01-02T13:59:45+00:00"
        }

        test_message = self.test_message
        test_message['thing'][0]['custom']['active'] = {'state': False}

        self.assertEqual(expected, self._activated)

    @freezegun.freeze_time('2022-12-19T14:38:15+00:00')
    def test_state_no_details(self):
        catalogue_item = {
            "thing": [{
                "custom": {}
            }]
        }
        expected = {
            'thing': [
                {
                    'custom': {
                        'active': {
                            'state': True,
                            'stateUpdateTimestamp': '2019-07-17T13:23:41+00:00'}
                    }
                }
            ]
        }
        activated_catalogue_item = self.activator.activate(catalogue_item)
        self.assertEqual(expected, activated_catalogue_item)

    def test_schema_validator_and_thing_overlap(self):
        """This test helps ensure that the validation schema overlaps with the
         same fields found in BaseThing, if you've updated one, and not the other
         this boi will pick it up."""
        catalogue_schema_fields = catalogue_thing_schema.keys()
        base_thing_fields = [f.name for f in fields(BaseThing)]

        try:
            self.assertTrue(set(catalogue_schema_fields) == set(base_thing_fields))
        except Exception as e:
            self.assertTrue(set(catalogue_schema_fields) == set(base_thing_fields))
            print("Error. Ensure the catalogue_schema_fields and BaseThing fields "
                  "have the same fields", e)


if __name__ == "__main__":
    unittest.main()
