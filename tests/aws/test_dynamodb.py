import json
import unittest
from decimal import Decimal

from thefilter.model.messages.request import Request
from thefilter.repositories import StubSlotsRepository, Slot, ComposerPage, \
    StubComposerPageRepository

try:
    from StringIO import StringIO
except ImportError:
    from io import String<PERSON>

from datetime import datetime, timezone
from unittest.mock import patch
from freezegun import freeze_time

from thefilter.aws.dynamoDB import Dynamo<PERSON><PERSON><PERSON>serRepository, DDBEditorialRepository, \
    UKTVDDBChartReadRepository, GenericDDBChartReadRepository, DDBComposerPageRepository, \
    DDBEntityAssociationRepository
from thefilter.logs.tracer import NoOpTracerFactory


@patch('botocore.client.BaseClient._make_api_call')
class TestDynamoDBUserRepository(unittest.TestCase):

    def test_get_history_in_range_action_filter(self, boto_mock):
        items_in_db = [
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-1",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "timestampAdded": "2019-11-24T17:47:25+00:00",
                "thingId": "i1",
                "brandId": "i1"
            },
            {
                "action": "A2",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-2",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "timestampAdded": "2019-11-24T17:47:25+00:00",
                "thingId": "i2",
                "brandId": "i2"
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t2",
                "userId": "1234_testId",
                "beaconId": "1234-3",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "timestampAdded": "2019-11-24T17:47:25+00:00",
                "thingId": "i3",
                "brandId": "i3"
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-4",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "timestampAdded": "2019-11-24T17:47:25+00:00",
                "thingId": "i4",
                "brandId": "i4"
            },
            {
                "action": "A3",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-5",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "timestampAdded": "2019-11-24T17:47:25+00:00",
                "thingId": "i5",
                "brandId": "i5"
            }
        ]

        expected_ddb_response = {'Items': items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="None",
            environment="test"
        )

        results = user_repo.get_history_in_range("1234", "testId",
                                                 datetime(2019, 11, 22,
                                                          tzinfo=timezone.utc),
                                                 datetime(2019, 12, 15,
                                                          tzinfo=timezone.utc),
                                                 actions=["A1", "A2"])

        self.assertEqual(
            [{'action': 'A1', 'id': '1234-1',
              'thing': [{'id': 'i1', 'brandId': 'i1', 'space': '', 'typeName': 't1'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A2', 'id': '1234-2',
              'thing': [{'id': 'i2', 'brandId': 'i2', 'space': '', 'typeName': 't1'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A1', 'id': '1234-3',
              'thing': [{'id': 'i3', 'brandId': 'i3', 'space': '', 'typeName': 't2'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A1', 'id': '1234-4',
              'thing': [{'id': 'i4', 'brandId': 'i4', 'space': '', 'typeName': 't3'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}}],
            results)

    def test_get_history_in_range(self, boto_mock):
        items_in_db = [
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-1",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i1",
                "brandId": "i1"
            },
            {
                "action": "A2",
                "timestampKey": 1574603491.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-2",
                "timestampInitiated": "2019-11-25T13:27:31+00:00",
                "timestampReceived": "2019-11-25T17:47:25+00:00",
                "thingId": "i2",
                "brandId": "i2"
            },
            {
                "action": "A3",
                "timestampKey": 1574604931.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-4",
                "timestampInitiated": "2019-11-26T13:27:31+00:00",
                "timestampReceived": "2019-11-26T17:47:25+00:00",
                "thingId": "i4",
                "brandId": "i4"
            }
        ]

        expected_ddb_response = {'Items': items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            environment="test",
            customer="None"
        )
        results = user_repo.get_history_in_range("1234", "testId",
                                                 datetime(2019, 11, 24, 13, 25,
                                                          tzinfo=timezone.utc),
                                                 datetime(2019, 11, 26, 13, 28,
                                                          tzinfo=timezone.utc), [])
        self.assertEqual(
            [{"id": "1234-1", "action": "A1",
              "timestamp": {"initiated": "2019-11-24T13:27:31+00:00",
                            "received": "2019-11-24T17:47:25+00:00"},
              "thing": [{"id": "i1", "brandId": "i1", "space": "", "typeName": "t1"}]},
             {"id": "1234-2", "action": "A2",
              "timestamp": {"initiated": "2019-11-25T13:27:31+00:00",
                            "received": "2019-11-25T17:47:25+00:00"},
              "thing": [{"id": "i2", "brandId": "i2", "space": "", "typeName": "t1"}]},
             {"id": "1234-4", "action": "A3",
              "timestamp": {"initiated": "2019-11-26T13:27:31+00:00",
                            "received": "2019-11-26T17:47:25+00:00"},
              "thing": [{"id": "i4", "brandId": "i4", "space": "", "typeName": "t3"}]}],
            results)

    def test_get_all_history_in_range(self, boto_mock):
        items_in_db = [
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-1",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i1",
                "brandId": "i1",
            },
            {
                "action": "A2",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-2",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i2",
                "brandId": "i2",
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t2",
                "userId": "1234_testId",
                "beaconId": "1234-3",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i3",
                "brandId": "i3",
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-4",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i4",
                "brandId": "i4",
            },
            {
                "action": "A3",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-5",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i5",
                "brandId": "i5",
            }
        ]

        expected_ddb_response = {'Items': items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            environment="test",
            customer="None"
        )

        results = user_repo.get_all_history("1234", "testId")
        self.assertEqual(
            [{'action': 'A1', 'id': '1234-1',
              'thing': [{'id': 'i1', "brandId": "i1", 'space': '', 'typeName': 't1'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A2', 'id': '1234-2',
              'thing': [{'id': 'i2', "brandId": "i2", 'space': '', 'typeName': 't1'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A1', 'id': '1234-3',
              'thing': [{'id': 'i3', "brandId": "i3", 'space': '', 'typeName': 't2'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A1', 'id': '1234-4',
              'thing': [{'id': 'i4', "brandId": "i4", 'space': '', 'typeName': 't3'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}},
             {'action': 'A3', 'id': '1234-5',
              'thing': [{'id': 'i5', "brandId": "i5", 'space': '', 'typeName': 't3'}],
              'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                            'received': '2019-11-24T17:47:25+00:00'}}],
            results)

    def test_get_all_history_limit(self, boto_mock):
        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            environment="test",
            customer="None"
        )
        user_repo.get_all_history("1234", "testId")
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 250)

        user_repo.get_all_history("1234", "testId", limit=200)
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 200)

        user_repo.get_all_history("1234", "testId", limit=300)
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 250)

    def test_get_all_history_action(self, boto_mock):
        items_in_db = [
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-1",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i1",
                "brandId": "i1"
            },
            {
                "action": "A2",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t1",
                "userId": "1234_testId",
                "beaconId": "1234-2",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i2",
                "brandId": "i2"
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t2",
                "userId": "1234_testId",
                "beaconId": "1234-3",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i3",
                "brandId": "i3"
            },
            {
                "action": "A1",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-4",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i4",
                "brandId": "i4"
            },
            {
                "action": "A3",
                "timestampKey": 1574602051.895407,
                "thingTypeName": "t3",
                "userId": "1234_testId",
                "beaconId": "1234-5",
                "timestampInitiated": "2019-11-24T13:27:31+00:00",
                "timestampReceived": "2019-11-24T17:47:25+00:00",
                "thingId": "i5",
                "brandId": "i5"
            }
        ]

        expected_ddb_response = {'Items': items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            environment="test",
            customer="None"
        )
        list_of_actions = ['A1']
        all_history = user_repo.get_all_history("1234", "testId",
                                                actions=tuple(list_of_actions))
        expected_filtered_hisory = [
            {'action': 'A1', 'id': '1234-1',
             'thing': [{'id': 'i1', "brandId": "i1", 'space': '', 'typeName': 't1'}],
             'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                           'received': '2019-11-24T17:47:25+00:00'}},
            {'action': 'A1', 'id': '1234-3',
             'thing': [{'id': 'i3', "brandId": "i3", 'space': '', 'typeName': 't2'}],
             'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                           'received': '2019-11-24T17:47:25+00:00'}},
            {'action': 'A1', 'id': '1234-4',
             'thing': [{'id': 'i4', "brandId": "i4", 'space': '', 'typeName': 't3'}],
             'timestamp': {'initiated': '2019-11-24T13:27:31+00:00',
                           'received': '2019-11-24T17:47:25+00:00'}}
        ]
        self.assertEqual(expected_filtered_hisory, all_history)

    def test_get_history_in_range_limit(self, boto_mock):
        user_repo = DynamoDBUserRepository(
            tracer_factory=NoOpTracerFactory(),
            environment="test",
            customer="None"
        )
        user_repo.get_history_in_range("1234", "testId",
                                       datetime(2019, 11, 24, 13, 25,
                                                tzinfo=timezone.utc),
                                       datetime(2019, 11, 26, 13, 28,
                                                tzinfo=timezone.utc), [])
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 250)

        user_repo.get_history_in_range("1234", "testId",
                                       datetime(2019, 11, 24, 13, 25,
                                                tzinfo=timezone.utc),
                                       datetime(2019, 11, 26, 13, 28,
                                                tzinfo=timezone.utc), [], limit=200)
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 200)

        user_repo.get_history_in_range("1234", "testId",
                                       datetime(2019, 11, 24, 13, 25,
                                                tzinfo=timezone.utc),
                                       datetime(2019, 11, 26, 13, 28,
                                                tzinfo=timezone.utc), [], limit=300)
        self.assertEqual(boto_mock.call_args[0][1]['Limit'], 250)

    @freeze_time("2020-08-18T14:00:00")
    def test_get_items_to_block(self, boto_mock):
        items_in_db = [
            {
                "slotId": "all",
                "thingId": "block-hard",
                "block": "true",
                "boostPercentage": 0,
                "description": "This should be blocked on any request",
                "startDate": "2020-08-01T00:00:00+00:00",
                "endDate": "2020-08-31T00:00:00+00:00"
            },
            {
                "slotId": "all",
                "thingId": "unblockable",
                "block": "true",
                "boostPercentage": 0,
                "description": "This should be blocked on any request",
                "startDate": "2020-07-01T00:00:00+00:00",
                "endDate": "2020-07-31T00:00:00+00:00"
            },
            {
                "slotId": "all",
                "thingId": "the-boost-sense",
                "block": "false",
                "boostPercentage": 0,
                "description": "This should be blocked on any request",
                "startDate": "2020-08-18T00:00:00+00:00",
                "endDate": "2020-08-24T00:00:00+00:00"
            },
            {
                "slotId": "A1",
                "thingId": "block-hard-2-block-harder",
                "block": "true",
                "boostPercentage": 0,
                "description": "Blocked only for slot A1",
                "startDate": "2020-08-18T00:00:00+00:00",
                "endDate": "2020-08-24T00:00:00+00:00"
            },
            {
                "slotId": "A1",
                "thingId": "the-block-element",
                "block": "true",
                "boostPercentage": 0,
                "description": "Always blocked for slot A1"
            },
            {
                "slotId": "B2",
                "thingId": "block-hard-with-a-vengeance",
                "block": "true",
                "boostPercentage": 0,
                "description": "Blocked only on slot B2",
                "startDate": "2020-08-18T00:00:00+00:00",
                "endDate": "2020-08-24T00:00:00+00:00"
            },
            {
                "slotId": "B2",
                "thingId": "live-free-or-block-hard",
                "block": "true",
                "boostPercentage": 0,
                "description": "Blocked only on slot B2",
                "startDate": "2020-08-18T00:00:00+00:00",
            },
            {
                "slotId": "B2",
                "thingId": "a-good-day-to-boost-hard",
                "block": "false",
                "boostPercentage": 10,
                "description": "Boosted, so excluded from blocking",
                "endDate": "2020-08-24T00:00:00+00:00"
            },
            {
                "slotId": "B2",
                "thingId": "The Exblockables",
                "block": "true",
                "boostPercentage": 0,
                "description": "Blocked only on slot B2",
                "startDate": "2020-09-01T00:00:00+00:00",
                "endDate": "2020-09-30T00:00:00+00:00"
            },
        ]

        expected_ddb_response = {'Items': items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        editorial_repo = DDBEditorialRepository(
            environment="test",
            customer="None"
        )

        # No slot id provided, so should only return all
        items_to_check = [items_in_db[0]["thingId"]]
        items_to_block = editorial_repo.get_items_to_block()
        self.assertEqual(1, len(items_to_block),
                         "Correct number of items with no specified slot")
        self.assertListEqual(items_to_check, items_to_block,
                             "All slot correctly returned")

        # Need to reset mock, as data is consumed otherwise
        boto_mock.side_effect = [expected_ddb_response]

        # Slot A1 should return both all and A1 items
        items_to_check = [items_in_db[0]["thingId"], items_in_db[3]["thingId"],
                          items_in_db[4]["thingId"]]
        items_to_block = editorial_repo.get_items_to_block(slot_id="A1")
        self.assertEqual(len(items_to_block), 3, "Correct number of items")
        self.assertListEqual(items_to_block, items_to_check,
                             "all and A1 block items correctly returned")

        boto_mock.side_effect = [expected_ddb_response]

        # Slot B2 should return both all and B2 blocked items
        items_to_check = [items_in_db[0]["thingId"], items_in_db[5]["thingId"],
                          items_in_db[6]["thingId"]]
        items_to_block = editorial_repo.get_items_to_block(slot_id="B2")
        self.assertEqual(len(items_to_block), 3, "Correct number of items")
        self.assertListEqual(items_to_block, items_to_check,
                             "all and B2 block items correctly returned")


@patch('botocore.client.BaseClient._make_api_call')
class TestUKTVDDBChartReadRepository(unittest.TestCase):

    def test_get_chart_by_name(self, boto_mock):
        items_in_db = [
            {
                "ddbId": "latest",
                "created": "2019-11-24T17:47:25+00:00",
                "timeToLive": 1611827634,
                "space": "brand",
                "chart": ["1941", "3513", "3512", "2261", "2011"]
            },
            {
                "ddbId": "most-popular",
                "created": "2019-11-24T17:47:25+00:00",
                "timeToLive": 1611827634,
                "space": "brand",
                "chart": [
                    ["2454", 9500, 1],
                    ["356", 9000, 2],
                    ["3981", 8500, 3],
                    ["789", 8000, 4],
                    ["2011", 7500, 5]
                ]
            }
        ]

        chart_name = "latest"
        expected_ddb_response = {
            'Items': [item for item in items_in_db if item['ddbId'] == chart_name]}
        boto_mock.side_effect = [expected_ddb_response]

        chart_repo = UKTVDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="None",
            environment="test"
        )

        request = Request(query_string={'chartName': chart_name})
        results = chart_repo.get_chart_ids(request)

        # LATEST
        self.assertEqual(
            ["1941_brand", "3513_brand", "3512_brand", "2261_brand", "2011_brand"],
            results)

        chart_name = "most-popular"
        expected_ddb_response = {
            'Items': [item for item in items_in_db if item['ddbId'] == chart_name]}
        boto_mock.side_effect = [expected_ddb_response]

        request = Request(query_string={'chartName': chart_name})
        results = chart_repo.get_chart_ids(request)

        # MOST POPULAR
        self.assertEqual(
            ["2454_brand", "356_brand", "3981_brand", "789_brand", "2011_brand"],
            results)

    def test_get_chart_by_name_missing(self, boto_mock):
        chart_name = "missing"
        expected_ddb_response = \
            {'Items': []}
        boto_mock.side_effect = [expected_ddb_response]

        chart_repo = UKTVDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="None",
            environment="test"
        )

        results = chart_repo.get_chart_by_name(chart_name)

        self.assertEqual([], results)


@patch('botocore.client.BaseClient._make_api_call')
class TestGenericDDBChartReadRepositoryWithEpix(unittest.TestCase):

    def test_get_chart_by_name(self, boto_mock):
        items_in_db = [
            {
                "ddbId": "Latest-All",
                "created": "2019-11-24T17:47:25+00:00",
                "timeToLive": 1611827634,
                "chart": ["1941", "3513", "3512", "2261", "2011"]
            }
        ]

        chart_name = "Latest-All"
        expected_ddb_response = \
            {'Items': [item for item in items_in_db if item['ddbId'] == chart_name]}
        boto_mock.side_effect = [expected_ddb_response]

        chart_repo = GenericDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="epix",
            environment="test"
        )

        request = Request(query_string={'chartName': chart_name})
        results = chart_repo.get_chart_ids(request)

        self.assertEqual(
            ["1941", "3513", "3512", "2261", "2011"],
            results)

    def test_get_list_chart_by_name(self, boto_mock):
        items_in_db = [
            {
                "ddbId": "most-popular",
                "created": "2019-11-24T17:47:25+00:00",
                "timeToLive": 1611827634,
                "chart": [
                    ["1941", 3000, 1],
                    ["3513", 2500, 2],
                    ["3512", 2000, 3],
                    ["2261", 1500, 4],
                    ["2011", 1000, 5]
                ]
            }
        ]

        chart_name = "most-popular"
        expected_ddb_response = \
            {'Items': [item for item in items_in_db if item['ddbId'] == chart_name]}
        boto_mock.side_effect = [expected_ddb_response]

        chart_repo = GenericDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="epix",
            environment="test"
        )

        request = Request(query_string={'chartName': chart_name})
        results = chart_repo.get_chart_ids(request)

        self.assertEqual(
            ["1941", "3513", "3512", "2261", "2011"],
            results)

    def test_get_chart_by_name_missing(self, boto_mock):
        chart_name = "missing"
        expected_ddb_response = \
            {'Items': []}
        boto_mock.side_effect = [expected_ddb_response]

        chart_repo = GenericDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="epix",
            environment="test"
        )

        request = Request(query_string={'chartName': chart_name})
        results = chart_repo.get_chart_ids(request)

        self.assertEqual([], results)

    def test_subscription_type_redirect_to_free_chart(self, boto_mock):
        chart_repo = GenericDDBChartReadRepository(
            tracer_factory=NoOpTracerFactory(),
            customer="epix",
            environment="test"
        )

        request = Request(query_string={"chartName": "latest", "subscriptionType": "free"})
        chart_name = chart_repo._try_get_chart_name(request)

        self.assertEqual(
            "latest_free",
            chart_name)


@patch('botocore.client.BaseClient._make_api_call')
class TestSlotsRepository(unittest.TestCase):

    def test_get_slot_by_id(self, boto_mock):
        slots_repo = StubSlotsRepository()
        result = slots_repo.read_slot("test_slotId")

        self.assertEqual({'status': 'Read test_slotId'}, result)

    def test_post_slot(self, boto_mock):
        slot_to_post = {
            "customer": "test_customer",
            "dateCreated": "2021-10-04T13:46:55+00:00",
            "createdBy": "auto_ES_to_DDB_slots",
            "dateUpdated": "2021-12-04T13:46:55+00:00",
            "lastUpdatedBy": "API Slots",
            "fallbackChart": "brand",
            "slotId": "test_slotId",
            "slotName": "test_slotName",
            "experiments": json.dumps([
                {
                    "id": "guid",
                    "userPercentage": 100,
                    "isBaseRecipe": True,
                    "size": 30,
                    "title": "",
                    "notes": "",
                    "modelDefinitions": [
                        {
                            "key": "test_model_key",
                            "version": 1,
                            "source": "test_invokable_model",
                            "fulfilment": {
                                "ranges": [
                                    {
                                        "start": 0,
                                        "end": 50
                                    }
                                ]
                            },
                            "parameters": {
                                "chartName": "test_parameter"
                            }
                        }
                    ]
                }
            ])
        }

        slots_repo = StubSlotsRepository()
        expected = {
            'status': "Written Slot(customer='test_customer', slotId='test_slotId', "
                      "slotName='test_slotName', fallbackChart='brand', "
                      "dateCreated='2021-10-04T13:46:55+00:00', "
                      "createdBy='auto_ES_to_DDB_slots', "
                      "dateUpdated='2021-12-04T13:46:55+00:00', lastUpdatedBy='API "
                      'Slots\', type=\'\', description=\'\', experiments=\'[{"id": '
                      '"guid", "userPercentage": 100, "isBaseRecipe": true, "size": 30, '
                      '"title": "", "notes": "", "modelDefinitions": [{"key": '
                      '"test_model_key", "version": 1, "source": "test_invokable_model", '
                      '"fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": '
                      '{"chartName": "test_parameter"}}]}]\', responseSize=0, '
                      "zeroResultsOk=False, isDefault=False, status='Live')"}

        self.assertEqual(expected, slots_repo.write_slot(Slot(**slot_to_post)))

    def test_slot_refactor_all_details(self, boto_mock):
        slot_to_refactor = {
            "customer": "test_customer",
            "dateCreated": "2021-10-04T13:46:55+00:00",
            "createdBy": "auto_ES_to_DDB_slots",
            "dateUpdated": "2021-12-04T13:46:55+00:00",
            "lastUpdatedBy": "API Slots",
            "fallbackChart": "most-popular",
            "slotId": "test_slotId",
            "slotName": "test_rfy",
            "experiments": json.dumps([
                {
                    "id": "guid",
                    "userPercentage": 100,
                    "isBaseRecipe": True,
                    "size": 30,
                    "title": "",
                    "notes": "",
                    "modelDefinitions": [
                        {
                            "key": "test_model_key",
                            "version": 1,
                            "source": "test_invokable_model",
                            "fulfilment": {
                                "ranges": [
                                    {
                                        "start": 0,
                                        "end": 50
                                    }
                                ]
                            },
                            "parameters": {
                                "chartName": "test_parameter"
                            }
                        }
                    ]
                }
            ])
        }

        slots_repo = StubSlotsRepository()
        expected = Slot(
            customer='test_customer',
            slotId='test_slotId',
            slotName='test_rfy',
            fallbackChart='most-popular',
            dateCreated='2021-10-04T13:46:55+00:00',
            createdBy='auto_ES_to_DDB_slots',
            dateUpdated='2021-12-04T13:46:55+00:00',
            lastUpdatedBy='API Slots',
            type='rfy',
            description='test_rfy',
            experiments='[{"id": "guid", "userPercentage": 100, "isBaseRecipe": true, '
                        '"size": 30, "title": "", "notes": "", "modelDefinitions": '
                        '[{"key": "test_model_key", "version": 1, "source": '
                        '"test_invokable_model", "fulfilment": {"ranges": [{"start": '
                        '0, "end": 50}]}, "parameters": {"chartName": '
                        '"test_parameter"}}]}]',
            responseSize=0,
            zeroResultsOk=False,
            isDefault=False
        )

        self.assertEqual(expected, slots_repo.refactor_slot(Slot(**slot_to_refactor)))


def test_slot_refactor_missing_details(self, boto_mock):
    slot_to_refactor = {
        "customer": "test_customer",
        "dateCreated": "2021-10-04T13:46:55+00:00",
        "createdBy": "auto_ES_to_DDB_slots",
        "dateUpdated": "2021-12-04T13:46:55+00:00",
        "lastUpdatedBy": "API Slots",
        "fallbackChart": "most-popular",
        "slotId": "test_slotId",
        "slotName": "test_slotName_rfy",
        "experiments": json.dumps([
            {
                "id": "guid",
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 30,
                "title": "",
                "notes": "",
                "modelDefinitions": [
                    {
                        "key": "test_model_key",
                        "version": 1,
                        "source": "test_invokable_model",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 50
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "test_parameter"
                        }
                    }
                ]
            }
        ]
        )
    }

    slots_repo = StubSlotsRepository()
    expected = Slot(
        customer="test_customer",
        slotId="test_slotId",
        slotName="test_slotName_rfy",
        fallbackChart="most-popular",
        dateCreated="2021-10-04T13:46:55+00:00",
        createdBy="auto_ES_to_DDB_slots",
        dateUpdated="2021-12-04T13:46:55+00:00",
        lastUpdatedBy="API Slots",
        type="rfy",
        description="test_slotName_rfy",
        experiments=json.dumps([
            {
                "id": "guid",
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 30,
                "title": "",
                "notes": "",
                "modelDefinitions": [
                    {
                        "key": "test_model_key",
                        "version": 1,
                        "source": "test_invokable_model",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 50
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "test_parameter"
                        }
                    }
                ]
            }
        ]),
        responseSize=30,
        zeroResultsOk=False
    )

    self.assertEqual(expected, slots_repo.refactor_slot(Slot(**slot_to_refactor)))


def test_slot_refactor_no_changes(self, boto_mock):
    slot_to_refactor = {
        "customer": "test_customer",
        "dateCreated": "2021-10-04T13:46:55+00:00",
        "createdBy": "auto_ES_to_DDB_slots",
        "dateUpdated": "2021-12-04T13:46:55+00:00",
        "lastUpdatedBy": "API Slots",
        "fallbackChart": "most-popular",
        "slotId": "test_slotId",
        "slotName": "test_slotName_rfy",
        "type": "rfy",
        "description": "Test Slot",
        "experiments": json.dumps([
            {
                "id": "guid",
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 30,
                "title": "",
                "notes": "",
                "modelDefinitions": [
                    {
                        "key": "test_model_key",
                        "version": 1,
                        "source": "test_invokable_model",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 50
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "test_parameter"
                        }
                    }
                ]
            }
        ]),
        "responseSize": 30,
        "zeroResultsOk": False
    }

    slots_repo = StubSlotsRepository()
    expected = Slot(
        customer="test_customer",
        slotId="test_slotId",
        slotName="test_slotName_rfy",
        fallbackChart="most-popular",
        dateCreated="2021-10-04T13:46:55+00:00",
        createdBy="auto_ES_to_DDB_slots",
        dateUpdated="2021-12-04T13:46:55+00:00",
        lastUpdatedBy="API Slots",
        type="rfy",
        description="Test Slot",
        experiments=json.dumps([
            {
                "id": "guid",
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 30,
                "title": "",
                "notes": "",
                "modelDefinitions": [
                    {
                        "key": "test_model_key",
                        "version": 1,
                        "source": "test_invokable_model",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 50
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "test_parameter"
                        }
                    }
                ]
            }
        ]),
        responseSize=30,
        zeroResultsOk=False
    )

    self.assertEqual(expected, slots_repo.refactor_slot(Slot(**slot_to_refactor)))


class TestStubComposerPageRepository(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._repository = StubComposerPageRepository()

    def test_scan_customers(self):
        self._repository.scan_customers()

    def test_read_by_customer(self):
        id = "test_id"
        self._repository.read_by_customer(id)

    def test_write_composer_page(self):
        composer_page = {
            "pageId": "test_pageId",
            "pageUserId": "",
            "pageSeedId": "",
            "customer": "test_customer",
            "pageName": "test_pageName",
            "createdBy": "test_createdBy",
            "dateCreated": "2021-05-11T14:13:39+00:00",
            "lastUpdatedBy": "test_lastUpdatedBy",
            "dateUpdated": "2021-05-11T14:13:39+00:00",
            "pageDefinition": "[{}]"
        }
        page = ComposerPage(**composer_page)

        self._repository.write_composer_page(page)

    def test_read_composer_page(self):
        id = "test_id"
        self._repository.read_composer_page(id)


@patch('botocore.client.BaseClient._make_api_call')
class TestDDBComposerPageRepository(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._repository = DDBComposerPageRepository("eu-west-2", "test_environment")

        self._items_in_db = [
            {
                "pageId": "test_pageId_1",
                "pageUserId": "1234",
                "customer": "Test_Customer_Z",
                "pageName": "test_pageName",
                "createdBy": "test_createdBy",
                "dateCreated": "2021-05-11T14:13:39+00:00",
                "lastUpdatedBy": "test_lastUpdatedBy",
                "dateUpdated": "2021-05-11T14:13:39+00:00",
                "pageDefinition": "[{}]"
            },
            {
                "pageId": "test_pageId_2",
                "pageUserId": "2345",
                "customer": "The Filter - Demo",
                "pageName": "test_pageName",
                "createdBy": "test_createdBy",
                "dateCreated": "2021-05-11T14:13:39+00:00",
                "lastUpdatedBy": "test_lastUpdatedBy",
                "dateUpdated": "2021-05-11T14:13:39+00:00",
                "pageDefinition": "[{}]"
            },
            {
                "pageId": "test_pageId_3",
                "pageUserId": "3456",
                "customer": "The Filter - Demo",
                "pageName": "test_pageName",
                "createdBy": "test_createdBy",
                "dateCreated": "2021-05-11T14:13:39+00:00",
                "lastUpdatedBy": "test_lastUpdatedBy",
                "dateUpdated": "2021-05-11T14:13:39+00:00",
                "pageDefinition": "[{}]"
            },
            {
                "pageId": "test_pageId_4",
                "pageUserId": "4567",
                "customer": "Test_Customer_A",
                "pageName": "test_pageName",
                "createdBy": "test_createdBy",
                "dateCreated": "2021-05-11T14:13:39+00:00",
                "lastUpdatedBy": "test_lastUpdatedBy",
                "dateUpdated": "2021-05-11T14:13:39+00:00",
                "pageDefinition": "[{}]"
            }
        ]

    def test_get_scan_customers(self, boto_mock):
        expected_ddb_response = {'Items': self._items_in_db}
        boto_mock.side_effect = [expected_ddb_response]

        scan_customers = self._repository.scan_customers()

        expected_customers = ['The Filter - Demo', 'Test_Customer_A', 'Test_Customer_Z']
        self.assertEqual(expected_customers, scan_customers)

    def test_get_page_by_customer(self, boto_mock):
        customer_name = "The Filter - Demo"

        ddb_response = {
            'Items':
                [item for item in self._items_in_db if item['customer'] == customer_name]
        }

        boto_mock.side_effect = [ddb_response]
        expected_items = ddb_response['Items']

        read_by_customer = self._repository.read_by_customer(customer_name)

        self.assertEqual(expected_items, read_by_customer)

    def test_get_page_by_page_id(self, boto_mock):
        page_id = "test_pageId_3"

        ddb_response = {
            'Items':
                [item for item in self._items_in_db if item['pageId'] == page_id]
        }

        boto_mock.side_effect = [ddb_response]
        expected_items = ddb_response['Items'][0]
        read_by_page_id = self._repository.read_composer_page(page_id)

        self.assertEqual(expected_items, read_by_page_id)

    def test_delete_page_by_page_id(self, boto_mock):
        page_id = "test_pageId_3"

        ddb_response = {
            'Items':
                [item for item in self._items_in_db if item['pageId'] != page_id]
        }
        boto_mock.side_effect = [ddb_response]
        expected_items = ddb_response
        delete_by_page_id = self._repository.delete_by_page_id(page_id)
        self.assertEqual(expected_items, delete_by_page_id)


@patch('botocore.client.BaseClient._make_api_call')
class TestDDBEntityAssociationRepository(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._repository = DDBEntityAssociationRepository("eu-west-2",
                                                          "test_environment")

    def test_check_existing(self, boto_mock):
        associations_to_add = [
            {
                'thingId': 'mission_impossible_7',
                'thingName': 'Mission: Impossible - Dead Reckoning',
                'thingType': 'Movie',
                'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
                'entityName': 'Tom Cruise',
                'entityType': 'people',
                'entityWeight': Decimal('1.0'),
                'id': 'c722a849-28d4-421c-a17e-2ba068bea45f'
            },
            {
                'thingId': 'mission_impossible_5',
                'thingName': 'Mission: Impossible - Rogue Nation',
                'thingType': 'Movie',
                'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
                'entityName': 'Tom Cruise',
                'entityType': 'people',
                'entityWeight': Decimal('1.0'),
                'id': 'e24fa945-1aa8-4f19-83f2-b7678cf52523'},
            {
                'thingId': 'war_of_the_worlds',
                'thingName': 'War of the Worlds',
                'thingType': 'Movie',
                'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
                'entityName': 'Tom Cruise',
                'entityType': 'people',
                'entityWeight': Decimal('1.0'),
                'id': '190c1934-21f0-4055-bc0f-c9449f72c6fc'
            }
        ]
        existing_associations = [
            {
                'entityType': 'people',
                'thingName': 'Mission: Impossible - Rogue Nation',
                'entityName': 'Tom Cruise',
                'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
                'thingType': 'Movie',
                'entityWeight': Decimal('1'),
                'id': '7b154ae1-f365-44ed-985e-0602c63b1646',
                'thingId': 'mission_impossible_5'},
            {
                'entityType': 'people',
                'thingName': 'War of the Worlds',
                'entityName': 'Tom Cruise',
                'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
                'thingType': 'Movie',
                'entityWeight': Decimal('1'),
                'id': '190c1934-21f0-4055-bc0f-c9449f72c6fc',
                'thingId': 'war_of_the_worlds'
            }
        ]

        expected_diff = [
            {'thingId': 'mission_impossible_7',
             'thingName': 'Mission: Impossible - Dead Reckoning',
             'thingType': 'Movie',
             'entityId': 'cGVvcGxlX3RvbSBjcnVpc2U=',
             'entityName': 'Tom Cruise',
             'entityType': 'people',
             'entityWeight': Decimal('1.0'),
             'id': 'c722a849-28d4-421c-a17e-2ba068bea45f'}
        ]

        actual_diff = self._repository._check_existing(associations_to_add,
                                                       existing_associations)
        self.assertEqual(expected_diff, actual_diff,
                         "Existing associations removed correctly")


if __name__ == "__main__":
    unittest.main()
