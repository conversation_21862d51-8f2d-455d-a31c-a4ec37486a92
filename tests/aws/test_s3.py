import unittest

from thefilter.aws.s3 import S3TransferStub, S3TransferClient
from thefilter.aws.sts import StsClientStub
from thefilter.utils.slackbot import SlackBotStub


class AWSS3Tests(unittest.TestCase):
    def test_s3transfer(self):
        source_env = 'feature'
        destination_env = 'feature'
        sts_client = StsClientStub(
            source_env,
            slackbot_client=SlackBotStub(),
            destination_environment=destination_env)
        role_name = 'TF-epix-EventsPersistence-feature-eu-west-2'
        api = S3TransferStub(sts_client, role_name)
        source_path_s3_uri = 's3://thefilter-feature-test_bucket/file.ndjson'
        destination_path_s3_uri = 's3://thefilter-feature-new_bucket/renamed.ndjson'
        result = api.copy_file(source_path_s3_uri, destination_path_s3_uri)
        expected = f"Copied s3://thefilter-feature-test_bucket/file.ndjson " \
                   f"-> s3://thefilter-feature-new_bucket/renamed.ndjson"
        self.assertEqual(expected, result)

    def test_role_name_updater(self):
        source_env = 'production'
        destination_env = 'pre'
        sts_client = StsClientStub(
            source_env,
            slackbot_client=SlackBotStub(),
            destination_environment=destination_env)
        role_name = 'TF-customer-EventsPersistence-production-us-east-1'
        api = S3TransferStub(sts_client, role_name)
        updated_role_name = api._update_role_name(role_name)
        expected = 'TF-customer-EventsPersistence-pre-eu-west-2'
        self.assertEqual(expected, updated_role_name)

    def test_split_bucket_and_path(self):
        test_path_s3 = "s3://thefilter-environment-region-customer/source/events/2024-08-16.csv.gz"

        output_bucket, output_key = S3TransferClient._split_bucket_and_path(test_path_s3)

        self.assertEqual("thefilter-environment-region-customer", output_bucket, "Bucket matches")
        self.assertEqual("source/events/2024-08-16.csv.gz", output_key,
                         "Key matches")

        test_path_tmp_1 = "thefilter-environment-region-customer//tmp/source/events/2024-08-16.csv.gz"

        output_bucket, output_key = S3TransferClient._split_bucket_and_path(test_path_tmp_1)

        self.assertEqual("thefilter-environment-region-customer", output_bucket, "Bucket matches")
        self.assertEqual("source/events/2024-08-16.csv.gz", output_key,
                         "Key matches")

        test_path_tmp_2 = "thefilter-environment-region-customer/tmp/source/events/2024-08-16.csv.gz"

        output_bucket, output_key = S3TransferClient._split_bucket_and_path(test_path_tmp_2)

        self.assertEqual("thefilter-environment-region-customer", output_bucket, "Bucket matches")
        self.assertEqual("source/events/2024-08-16.csv.gz", output_key,
                         "Key matches")

        test_path_no_tmp = "thefilter-environment-region-customer/source/events/2024-08-16.csv.gz"

        output_bucket, output_key = S3TransferClient._split_bucket_and_path(test_path_no_tmp)

        self.assertEqual("thefilter-environment-region-customer", output_bucket, "Bucket matches")
        self.assertEqual("source/events/2024-08-16.csv.gz", output_key,
                         "Key matches")

