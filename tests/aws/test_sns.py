import json
import unittest

from tests.aws.data.test_message import expected_sns_unwrapped_message
from tests.data.test_data import expected_message
from thefilter.aws.sns import SnsUnwrapper
from thefilter.model.messages.message import Message

test_data_dir1 = "tests/data/"
test_data_dir2 = "tests/aws/data/"


class SnsMessageUnwrapperTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(SnsMessageUnwrapperTests, self).__init__(*args, **kwargs)
        with open(test_data_dir2 + "snsMessage.json") as f:
            self.snsMessage = json.load(f)

    def test_unwrap_outputs_a_message(self):
        unwrapper = SnsUnwrapper()
        unwrapped_message = unwrapper.unwrap(self.snsMessage, Message)
        self.assertIsInstance(unwrapped_message, Message)

    def test_unwrap_outputs_a_dict(self):
        unwrapper = SnsUnwrapper()
        unwrapped_message = unwrapper.unwrap(self.snsMessage)
        self.assertIsInstance(unwrapped_message, dict)

    def test_unwrap_to_dict_correct(self):
        unwrapper = SnsUnwrapper()
        unwrapped_message = unwrapper.unwrap(self.snsMessage)
        self.assertDictEqual(expected_sns_unwrapped_message, unwrapped_message)

    def test_unwrap_to_message_correct(self):
        unwrapper = SnsUnwrapper()
        unwrapped_message = unwrapper.unwrap(self.snsMessage, Message)
        self.assertEqual(expected_message, unwrapped_message)


if __name__ == "__main__":
    unittest.main()
