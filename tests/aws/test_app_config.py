import io
import json
import unittest
from botocore.response import StreamingBody
from unittest.mock import patch

from thefilter.aws.app_config import AWSAppConfigRepository


@patch('botocore.client.BaseClient._make_api_call')
class TestAWSAppConfigRepository(unittest.TestCase):

    def test_get_app_config_by_name(self, boto_mock):
        app_config_items = {
            "name": "Test Config",
            "env": "test"
        }

        if isinstance(app_config_items, dict):
            app_config_items = json.dumps(app_config_items)
        encoded_content = app_config_items.encode()

        boto_mock.side_effect = [{"Content": StreamingBody(io.BytesIO(encoded_content), len(encoded_content))}]

        app_config_repo = AWSAppConfigRepository(environment="test", region="eu-west-2")

        results = app_config_repo.get_app_config(name="test-config", environment="test", version="")

        self.assertEqual({
            'name': 'Test Config',
            'env': 'test'
        }, results)

    def test_get_app_config_by_name_and_version(self, boto_mock):
        app_config_items = {
            "name": "Test Config",
            "env": "test",
            "version": "1"
        }

        if isinstance(app_config_items, dict):
            app_config_items = json.dumps(app_config_items)
        encoded_content = app_config_items.encode()

        boto_mock.side_effect = [{"Content": StreamingBody(io.BytesIO(encoded_content), len(encoded_content))}]

        app_config_repo = AWSAppConfigRepository(environment="test", region="eu-west-2")

        results = app_config_repo.get_app_config(name="test-config", environment="test",  version="1")

        self.assertEqual({
            'name': 'Test Config',
            'env': 'test',
            'version': '1'
        }, results)


if __name__ == "__main__":
    unittest.main()
