import json
import pickle
import unittest
from unittest.mock import patch, MagicMock

from tests.aws.data.test_message import expected_sqs_message
from thefilter.aws.sqs import SqsUnwrapper, SqsPublisher, SqsMetadataUnwrapper, remove_null
from thefilter.model.messages.message import Message

test_data_dir1 = "tests/data/"
test_data_dir2 = "tests/aws/data/"


class SqsUnwrapperTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(SqsUnwrapperTests, self).__init__(*args, **kwargs)
        with open(test_data_dir2 + "sqsMessageLambdaEvent.json") as f:
            self.sqsMessageLambdaEvent = json.load(f)
        with open(test_data_dir1 + "test_message.json") as f:
            self.test_message = json.load(f)
        with open(test_data_dir1 + "test_message.pickle", "rb") as f:
            self.test_message_pickle = pickle.load(f)

    def test_unwrap_outputs_a_message(self):
        unwrapper = SqsUnwrapper()
        unwrapped_messages = unwrapper.unwrap(self.sqsMessageLambdaEvent, True)
        unwrapped_message = unwrapped_messages[0]
        self.assertIsInstance(unwrapped_message, Message)

    def test_unwrap_outputs_a_dict(self):
        unwrapper = SqsUnwrapper()
        unwrapped_messages = unwrapper.unwrap(self.sqsMessageLambdaEvent)
        unwrapped_message = unwrapped_messages[0]
        self.assertIsInstance(unwrapped_message, dict)

    def test_unwrap_to_dict_correct(self):
        unwrapper = SqsUnwrapper()
        unwrapped_messages = unwrapper.unwrap(self.sqsMessageLambdaEvent)
        unwrapped_message = unwrapped_messages[0]
        self.maxDiff = None
        self.assertDictEqual(self.test_message, unwrapped_message)

    def test_unwrap_to_message_correct(self):
        unwrapper = SqsUnwrapper()
        unwrapped_messages = unwrapper.unwrap(self.sqsMessageLambdaEvent, True)
        unwrapped_message = unwrapped_messages[0]
        self.assertEqual(expected_sqs_message, unwrapped_message)

    def test_unwrap_with_message_attributes(self):
        """The SqsMetadataUnwrapper passes both body and messageAttributes"""
        self.maxDiff = None
        unwrapper = SqsMetadataUnwrapper()
        unwrapped_messages = unwrapper.unwrap(self.sqsMessageLambdaEvent)
        unwrapped_message = unwrapped_messages[0]
        # reusing the self.test_message and updating it with messageAttributes:
        test_message = dict(self.test_message, **{'messageAttributes': {}})
        self.assertDictEqual(test_message, unwrapped_message)
        self.assertTrue('messageAttributes' in unwrapped_message)


# mock object for boto3.client for use in TestSqsPublisher
mock = MagicMock()


@patch('boto3.client', return_value=mock)
class TestSQSPublisher(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        with open(test_data_dir1 + "test_message.pickle", "rb") as f:
            self.test_message_pickle = pickle.load(f)

    def test_turns_message_object_to_dict(self, mock_client):
        publisher = SqsPublisher("dead_letter_queue", "fast_lane_queue")
        publisher.publish_to_dead_letter_queue(self.test_message_pickle)
        json_test_message_pickle = json.loads(self.test_message_pickle.to_json())
        remove_null(json_test_message_pickle)
        json_test_message_pickle = json.dumps(json_test_message_pickle)
        mock.send_message.assert_called_with(QueueUrl="dead_letter_queue",
                                             MessageBody=json_test_message_pickle,
                                             MessageAttributes={})
        publisher.publish_to_fast_lane_queue(self.test_message_pickle)
        mock.send_message.assert_called_with(QueueUrl="fast_lane_queue",
                                             MessageBody=json_test_message_pickle,
                                             MessageAttributes={})

    def test_dict_published_as_string(self, mock_client):
        publisher = SqsPublisher("dead_letter_queue", "fast_lane_queue")
        publisher.publish_to_dead_letter_queue({"test": "dict"})
        mock.send_message.assert_called_with(QueueUrl="dead_letter_queue",
                                             MessageBody=str({"test": "dict"}),
                                             MessageAttributes=None)
        publisher.publish_to_fast_lane_queue({"test": "dict"})
        mock.send_message.assert_called_with(QueueUrl="fast_lane_queue",
                                             MessageBody=str({"test": "dict"}),
                                             MessageAttributes=None)

    def test_publish_with_message_attributes(self, mock_client):
        publisher = SqsPublisher("dead_letter_queue", "fast_lane_queue")
        publisher.publish_to_fast_lane_queue({"test": "dict"},
                                             {"attribute": "something"})
        mock.send_message.assert_called_with(QueueUrl="fast_lane_queue",
                                             MessageBody=str({"test": "dict"}),
                                             MessageAttributes={
                                                 "attribute": "something"})


if __name__ == "__main__":
    unittest.main()
