import json
import pickle
import unittest
from dataclasses import dataclass
from datetime import datetime
from unittest.mock import ANY, MagicMock
from unittest.mock import patch

import freezegun
from elasticsearch import Elasticsearch
from jsonschema import validate

from thefilter.aws.elastic_search import (
    CachingElasticMetadataRepository,
    ElasticBulkPublisher,
    ElasticCatalogueFormatter,
    DictionaryElasticCatalogueFormatter,
    ElasticMetadataRepository,
    ElasticSearchBulkSearcher,
    ElasticsearchMessageScroller,
    UKTVCatalogueElasticBulkPublisher,
    ElasticsearchIndexReader
)
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.message import Message
from thefilter.model.schemaorg import Thing
from thefilter.model.timestamp import TimestampWithAdded

test_data_dir1 = "tests/aws/data/elastic_search"
test_data_dir2 = "tests/data"


class ElasticSearchCatalogueFormatterTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(ElasticSearchCatalogueFormatterTests,
              self).__init__(*args, **kwargs)
        with open("thefilter/validation/internal_message_schema.json") as f:
            self.schema = json.load(f)
        with open(
                f"{test_data_dir1}/catalogueFormatterOutputSchema.json") as f:
            self.formatter_output_schema = json.load(f)
        with open(f"{test_data_dir2}/test_message.pickle", "rb") as f:
            self.test_message = pickle.load(f)

    def test_catalogue_update_is_valid(self, event=None):
        """Run as independent test for the base test event, and run in other tests to check
        that modifications do not break the schema"""
        if event is None:
            event = self.test_message
        validate(self.schema, json.loads(event.to_json()))

    def test_catalogue_output_matches_schema(self):
        test_catalogue_update = [self.test_message]
        formatter = ElasticCatalogueFormatter(customer="test")
        documents = formatter.format(test_catalogue_update)
        validate(documents, self.formatter_output_schema)


class DictionaryElasticCatalogueFormatterTests(unittest.TestCase):
    def test_format_datetime_string(self):
        datetime_string = "2023-09-20 12:39:00+00:00"
        output = DictionaryElasticCatalogueFormatter._format_datetime_string(
            datetime_string)
        self.assertEqual("2023-09-20T12:39:00.000Z", output, "Timestamp matches")

    def test_format_catalogue_event(self):
        formatter = DictionaryElasticCatalogueFormatter(customer="test")
        catalogue_events = [
            {
                "thing": [{
                    "id": "test_item_1",
                    "datePublished": "2023-09-20 12:39:00+00:00",
                }],
                "timestamp": {
                    "added": "2023-09-20 12:39:00+00:00",
                    "initiated": "2023-09-20 12:39:00+00:00",
                    "received": "2023-09-20 12:39:00+00:00"
                }
            }
        ]

        expected = [
            {
                "document": {
                    "thing": {
                        "id": "test_item_1",
                        "datePublished": "2023-09-20T12:39:00+00:00",
                    },
                    "timestamp": {
                        "added": "2023-09-20T12:39:00.000Z",
                        "initiated": "2023-09-20T12:39:00.000Z",
                        "received": "2023-09-20T12:39:00.000Z"
                    }
                },
                "index": "metadata_test",
                "type": "_doc",
                "id": "test_item_1"
            }
        ]

        output = formatter.format(catalogue_events)

        self.assertEqual(expected, output, "Formatted catalogue events match")


class UKTVDictionaryElasticCatalogueFormatterTests(unittest.TestCase):

    @freezegun.freeze_time('2023-09-20T11:58:03+0000')
    def test_format_catalogue_event(self):
        formatter = ElasticCatalogueFormatter(customer="test")
        catalogue_message = Message.get_default_message(customer="test")

        catalogue_message.thing = [Thing(
            id="test_item_1",
            datePublished="2023-09-20 12:39:00+00:00",
            space="brand"
        )]

        catalogue_message.timestamp = TimestampWithAdded(
            added="2023-09-20 11:58:03+00:00",
            initiated="2023-09-20 11:58:03+00:00",
            received="2023-09-20 11:58:03+00:00"
        )

        catalogue_events = [catalogue_message]

        expected = [{
            'document': {'action': None,
                         'context': {'application': None,
                                     'code_version': None,
                                     'environment': None,
                                     'server': None,
                                     'site': None},
                         'custom': {},
                         'customer': {'name': 'test'},
                         'messageAttributes': None,
                         'sourceOperationId': None,
                         'thing': {'actor': [],
                                   'alternateName': None,
                                   'brandId': None,
                                   'brandName': None,
                                   'contentRating': [],
                                   'crew': [],
                                   'custom': {},
                                   'datePublished': '2023-09-20T12:39:00+00:00',
                                   'description': None,
                                   'director': [],
                                   'duration': None,
                                   'episodeNumber': None,
                                   'genre': [],
                                   'id': 'test_item_1',
                                   'image': None,
                                   'inLanguage': None,
                                   'isAdult': None,
                                   'isKids': None,
                                   'isLiveBroadcast': None,
                                   'keywords': [],
                                   'name': None,
                                   'numberOfSeasons': None,
                                   'partOfSeason': {},
                                   'partOfSeries': {},
                                   'producer': [],
                                   'productionCompany': None,
                                   'publication': [],
                                   'recommendationId': None,
                                   'seasonNumber': None,
                                   'space': 'brand',
                                   'subGenre': [],
                                   'typeName': None},
                         'timestamp': {
                             'added': '2023-09-20T11:58:03.000Z',
                             'initiated': '2023-09-20T11:58:03.000Z',
                             'received': '2023-09-20T11:58:03.000Z'},
                         'user': {'accountId': None,
                                  'anonId': None,
                                  'clusterId': None,
                                  'primaryId': None,
                                  'userId': None}},
            'id': 'test_item_1_brand',
            'index': 'metadata_test',
            'type': '_doc'}]

        output = formatter.format(catalogue_events)

        del output[0]["document"]["identifier"]

        self.assertEqual(expected, output, "Formatted catalogue events match")


class ElasticSearchBulkPublisherTests(unittest.TestCase):
    @patch("json.loads", return_value={})
    @patch("requests.post")
    def test_url_add_https(self, mock_requests_post, mock_json_loads):
        mock_formatter = MagicMock()
        publisher = ElasticBulkPublisher(mock_formatter, "url.exists/here")
        publisher.publish(None)
        mock_requests_post.assert_called_with("https://url.exists/here/_bulk",
                                              data=ANY,
                                              headers=ANY)

    @patch("json.loads", return_value={})
    @patch("requests.post")
    def test_url_remove_end_slash(self, mock_requests_post, mock_json_loads):
        mock_formatter = MagicMock()
        publisher = ElasticBulkPublisher(mock_formatter,
                                         "https://url.exists/here/")
        publisher.publish(None)
        mock_requests_post.assert_called_with("https://url.exists/here/_bulk",
                                              data=ANY,
                                              headers=ANY)


class UktvCatalogueElasticSearchBulkPublisherTests(unittest.TestCase):
    def test_formatted_body(self):
        mock_formatter = MagicMock()
        publisher = UKTVCatalogueElasticBulkPublisher(mock_formatter, 'mock_endpoint')
        test_input = [{"index": "test_index", "type": "_doc", "id": "item_1",
                       "document": {"typeName": "test", "name": "test_name"}},
                      {"index": "test_index", "type": "_doc", "id": "item_2",
                       "document": {"name": "test_name",
                                    "genre": [{"name": "genre_name"}]}}]
        body = publisher._build_bulk_update_body(test_input)
        expected_body = '{"index": {"_index": "test_index", "_type": "_doc", "_id": "item_1"}}\n' \
                        '{"typeName": "test", "name": "test_name"}\n' \
                        '{"index": {"_index": "test_index", "_type": "_doc", "_id": "item_2"}}\n' \
                        '{"name": "test_name", "genre": [{"name": "genre_name"}]}\n'
        self.assertEqual(expected_body, body)


class ElasticSearchBulkSearcherTests(unittest.TestCase):
    def test_format_queries(self):
        searcher = ElasticSearchBulkSearcher()
        bulk_query = searcher._format_queries([{
            "query": "one"
        }, {
            "query": 2
        }], "example_index", "example_type")
        self.assertEqual(
            '{"index": "example_index", "type": "example_type"}\n'
            '{"query": "one"}\n'
            '{"index": "example_index", "type": "example_type"}\n'
            '{"query": 2}\n', bulk_query)


class ElasticSearchMetadataRepositoryTests(unittest.TestCase):
    def test_get_item_no_results(self):
        empty_response = {
            "took": 0,
            "timed_out": False,
            "_shards": {
                "total": 1,
                "successful": 1,
                "skipped": 0,
                "failed": 0
            },
            "hits": {
                "total": 0,
                "max_score": None,
                "hits": []
            }
        }
        elastic_metadata_repo = ElasticMetadataRepository(NoOpTracerFactory(), 'enpoint',
                                                          'index', "_doc")
        Elasticsearch.search = unittest.mock.MagicMock(
            return_value=empty_response)
        response = elastic_metadata_repo.get_item('test_id')
        self.assertDictEqual({}, response)

    def test_get_item_one_result(self):
        test_response = {
            "took": 0,
            "timed_out": False,
            "_shards": {
                "total": 1,
                "successful": 1,
                "skipped": 0,
                "failed": 0
            },
            "hits": {
                "total":
                    1,
                "max_score":
                    1,
                "hits": [{
                    "_index": "malwina_index_final",
                    "_type": "_doc",
                    "_id": "8017058",
                    "_score": 1,
                    "_source": {
                        "identifier": {
                            "eventId": "88b27dcb-4596-40fc-877b-6223977599f7",
                            "operationId":
                                "f1314179-d070-46f1-865e-cbbbac00d8cb"
                        },
                        "user": {
                            "accountId": None,
                            "userId": "_tf-testcatalogueprocessor",
                            "anonId": None,
                            "primaryId": "_tf-testcatalogueprocessor"
                        },
                        "timestamp": {
                            "initiated": "2019-06-12T14:27:41.000Z",
                            "received": "2019-06-12T14:27:41.000Z"
                        },
                        "action": "catalogue",
                        "context": {
                            "application": "TheFilter.Core.test.Loader.dll",
                            "environment": "rc",
                            "server": "LONDEV102.uklive.exabre.co.uk",
                            "site": "london",
                            "code_version": "test_version"
                        },
                        "thing": {
                            "id": "test_id",
                            "Name": ["Test Name"]
                        }
                    }
                }]
            }
        }
        elastic_metadata_repo = ElasticMetadataRepository(NoOpTracerFactory(),
                                                          'endpoint', 'index', "_doc")
        Elasticsearch.search = unittest.mock.MagicMock(return_value=test_response)
        response = elastic_metadata_repo.get_item('test_id')
        self.assertDictEqual({
            "id": "test_id",
            "Name": ["Test Name"]
        }, response.get('thing'))


class TestCachingElasticSearchMetadataRepository(unittest.TestCase):
    @patch("elasticsearch.Elasticsearch.mget", return_value={"docs": [
        {'_index': 'test_index', '_type': None, '_id': '1234', 'found': False}]})
    def test_inner_get_items_item_not_found(self, mock_mget):
        metadata_repository = CachingElasticMetadataRepository(
            es_endpoint_url="test",
            es_index="test_index",
            tracer_factory=NoOpTracerFactory())
        results = metadata_repository._inner_get_items(["1234"])
        self.assertEqual({}, results)


class MockFailResponse:
    def success(self):
        return False


class MockHit:
    def to_dict(self):
        return {"test_log": "value"}


@dataclass
class MockSuccessResponse:
    hits = [MockHit()]

    def success(self):
        return True


class MockResponse:
    def success(self):
        return True

    def to_dict(self):
        return {
            "hits": {
                "hits": [
                    {"_source": {"beacons": {"results": "here"}}}
                ]
            }
        }


class TestElasticsearchMessageScroller(unittest.TestCase):
    def test_get_index_cross_year(self):
        index = ElasticsearchMessageScroller._get_index(datetime(2018, 12, 31),
                                                        datetime(2019, 1, 1, 1),
                                                        "CustOMer")
        self.assertEqual("log_customer.2018-12,log_customer.2019-01", index)

    def test_get_index_single_month(self):
        index = ElasticsearchMessageScroller._get_index(datetime(2019, 2, 1, 12, 23),
                                                        datetime(2019, 2, 12, 1),
                                                        "CustOMer")
        self.assertEqual("log_customer.2019-02", index)


class TestElasticsearchIndexReader(unittest.TestCase):
    def test_generate_simple_filter(self):
        reader = ElasticsearchIndexReader("")

        filters = reader._get_filters(
            config={
                "filters": [
                    {
                        "description": "If active state is false, don't return it. Pretty clear cut.",
                        "query": {
                            "bool": {
                                "must_not": [
                                    {
                                        "term": {
                                            "thing.custom.active.state": True
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            request_parameters={}
        )

        self.assertEqual(1, len(filters))

        expectation = [
            {
                'bool': {
                    'must_not': [
                        {
                            'term': {
                                'thing.custom.active.state': True
                            }
                        }
                    ]
                }
            }
        ]

        for i in range(0, len(filters)):
            self.assertDictEqual(expectation[i], filters[i])

    def test_generate_filter_with_required_parameter(self):
        reader = ElasticsearchIndexReader("")

        filters = reader._get_filters(
            config={
                "filters": [
                    {
                        "description": "Filter category when category is provided on the query string.",
                        "parameters": {
                            "required": [
                                {
                                    "key": "category"
                                }
                            ]
                        },
                        "query": {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "filters.category.lowercase_keyword": "{category}"
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            request_parameters={
                "category": "howdy"
            }
        )

        self.assertEqual(1, len(filters))

        expectation = [
            {
                'bool': {
                    'must': [
                        {
                            'term': {
                                'filters.category.lowercase_keyword': 'howdy'
                            }
                        }
                    ]
                }
            }
        ]

        for i in range(0, len(filters)):
            self.assertDictEqual(expectation[i], filters[i])

    def test_do_not_generate_filter_without_required_parameter(self):
        reader = ElasticsearchIndexReader("")

        filters = reader._get_filters(
            config={
                "filters": [
                    {
                        "description": "Filter category when category is provided on the query string.",
                        "parameters": {
                            "required": [
                                {
                                    "key": "category"
                                }
                            ]
                        },
                        "query": {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "filters.category.lowercase_keyword": "{category}"
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            request_parameters={
                "not_category": "howdy"
            }
        )

        self.assertFalse(filters)

    def test_translates_filter_input(self):
        reader = ElasticsearchIndexReader("")

        filters = reader._get_filters(
            config={
                "settings": {
                    "translations": {
                        "filters.rating": {
                            "TV Y": 0,
                            "TV TV-Y7": 5,
                            "TV TV-Y7-FV": 5,
                            "MPAA G": 10,
                            "TV TV-G": 10,
                            "G": 10,
                            "MPAA PG": 20,
                            "TV TV-PG": 20,
                            "MPAA NR": 20,
                            "PG": 20,
                            "MPAA PG-13": 30,
                            "TV TV-14": 30,
                            "PG-13": 30,
                            "MPAA R": 40,
                            "TV TV-MA": 40,
                            "R": 40,
                            "MPAA NC-17": 50,
                            "NC-17": 50,
                            "MPAA UR": 60,
                            "NR": 60
                        }
                    }
                },
                "filters": [
                    {
                        "description": "Set lower bound for rating when ratingMin is provided on the query string.",
                        "parameters": {
                            "required": [
                                {
                                    "key": "ratingMin",
                                    "translation": "filters.rating"
                                }
                            ]
                        },
                        "query": {
                            "bool": {
                                "must": [
                                    {
                                        "range": {
                                            "filters.rating": {
                                                "gte": "{ratingMin}"
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            request_parameters={
                "ratingMin": "MPAA PG-13"
            }
        )

        self.assertEqual(1, len(filters))

        expectation = [
            {
                'bool': {
                    'must': [
                        {
                            'range': {
                                'filters.rating': {
                                    'gte': '30'
                                }
                            }
                        }
                    ]
                }
            }
        ]

        for i in range(0, len(filters)):
            self.assertDictEqual(expectation[i], filters[i])

    def test_generate_filter_with_multiple_values(self):
        reader = ElasticsearchIndexReader("")

        filters = reader._get_filters(
            config={
                "filters": [
                    {
                        "description": "Filter category when category is provided on the query string.",
                        "parameters": {
                            "required": [
                                {
                                    "key": "category",
                                    "divider": ","
                                }
                            ]
                        },
                        "query": {
                            "bool": {
                                "must": [
                                    {
                                        "terms": {
                                            "filters.category.lowercase_keyword": "{category}"
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            },
            request_parameters={
                "category": "howdy,doody"
            }
        )

        self.assertEqual(1, len(filters))

        expectation = [
            {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "filters.category.lowercase_keyword": [
                                    "howdy",
                                    "doody"
                                ]
                            }
                        }
                    ]
                }
            }
        ]

        for i in range(0, len(filters)):
            self.assertDictEqual(expectation[i], filters[i])


if __name__ == "__main__":
    unittest.main()
