import unittest

from thefilter.aws.elastic_search_chart import ESChartReadRepository
from thefilter.logs.logclient import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from thefilter.logs.tracer import TracerFactory


class MockTracerFactory(TracerFactory):
    def get(self, name):
        class MockTracer:
            def __enter__(self):
                return self

            def __exit__(self, exc_type, exc_val, exc_tb):
                pass

            def annotate(self, key, value):
                pass
        return MockTracer()


class TestESChartReadRepository(unittest.TestCase):
    """
    Tests for the ESChartReadRepository class, focusing on the _build_filter_query method.
    """

    def setUp(self):
        """Set up a mock ESChartReadRepository for testing."""
        self.repo = ESChartReadRepository(
            tracer_factory=MockTracerFactory(),
            environment="test",
            customer="test",
            elasticsearch_metadata_endpoint="http://localhost:9200",
            logger=NoOpLogger(),
        )

    def test_build_filter_query_single_value(self):
        """Test _build_filter_query with a single filter value."""
        filter_param = "genre"
        filter_values = ["action"]
        
        expected_query = {
            "term": {
                "filters.thing_genre_name": "action"
            }
        }
        
        result = self.repo._build_filter_query(filter_param, filter_values)
        self.assertEqual(expected_query, result)

    def test_build_filter_query_multiple_values(self):
        """Test _build_filter_query with multiple filter values (should use AND logic)."""
        filter_param = "genre"
        filter_values = ["action", "comedy"]
        
        expected_query = {
            "bool": {
                "must": [
                    {
                        "term": {
                            "filters.thing_genre_name": "action"
                        }
                    },
                    {
                        "term": {
                            "filters.thing_genre_name": "comedy"
                        }
                    }
                ]
            }
        }
        
        result = self.repo._build_filter_query(filter_param, filter_values)
        self.assertEqual(expected_query, result)

    def test_build_filter_query_exclude_genre(self):
        """Test _build_filter_query with excludeGenre (should use OR logic with must_not)."""
        filter_param = "excludeGenre"
        filter_values = ["horror", "thriller"]
        
        expected_query = {
            "bool": {
                "must_not": [
                    {
                        "terms": {
                            "filters.thing_genre_name": ["horror", "thriller"]
                        }
                    }
                ]
            }
        }
        
        result = self.repo._build_filter_query(filter_param, filter_values)
        self.assertEqual(expected_query, result)

    def test_build_filter_query_unknown_param(self):
        """Test _build_filter_query with an unknown filter parameter."""
        filter_param = "unknown"
        filter_values = ["value"]
        
        result = self.repo._build_filter_query(filter_param, filter_values)
        self.assertIsNone(result)

    def test_build_complete_query(self):
        """Test building a complete query with multiple filters."""
        # Create filter queries for multiple parameters
        genre_query = self.repo._build_filter_query("genre", ["action", "comedy"])
        age_rating_query = self.repo._build_filter_query("ageRating", ["PG", "12"])
        
        # Build the complete query
        query = {
            "query": {
                "bool": {
                    "filter": [genre_query, age_rating_query]
                }
            }
        }
        
        # Expected query structure
        expected_query = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "filters.thing_genre_name": "action"
                                        }
                                    },
                                    {
                                        "term": {
                                            "filters.thing_genre_name": "comedy"
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "bool": {
                                "must": [
                                    {
                                        "term": {
                                            "filters.thing_custom_agerangeuid": "PG"
                                        }
                                    },
                                    {
                                        "term": {
                                            "filters.thing_custom_agerangeuid": "12"
                                        }
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        }
        
        self.assertEqual(expected_query, query)


if __name__ == "__main__":
    unittest.main()