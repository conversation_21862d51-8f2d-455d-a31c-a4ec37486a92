import unittest
from unittest.mock import patch

import freezegun

from thefilter.aws.eventbridge import EventBridgeRuleClient, StubEventbridgeBoto3Client
from thefilter.utils.datetime import DateTimeUtil

stub_events_response = {
    "Rules": [
        {
            "Name": "test-mlt",
            "Arn": "arn:aws:events:eu-west-2:147726730760:rule/test-mlt",
            "State": "ENABLED",
            "ScheduleExpression": "cron(10 11 ? * 3,5 *)",
            "EventBusName": "default"
        },
        {
            "Name": "test-rfy",
            "Arn": "arn:aws:events:eu-west-2:147726730760:rule/test-rfy",
            "State": "ENABLED",
            "Description": "DDB RFY (rfy_20221005)",
            "ScheduleExpression": "cron(40 15 ? * 1 *)",
            "EventBusName": "default"
        },
        {
            "Name": "feature-Backend-JobFailNotificationCloudWatchTrigg-K887LQ1MZGW6",
            "Arn": "arn:aws:events:eu-west-2:147726730760:rule/feature-Backend-JobFailNotificationCloudWatchTrigg-K887LQ1MZGW6",
            "EventPattern": "{\"source\":[\"aws.batch\"],\"detail\":{\"status\":[\"FAILED\"]}}",
            "Description": "Customer:operational|Job:job_fail_notification",
            "State": "ENABLED",
            "EventBusName": "default"
        },
        {
            "Name": "feature-test-operational-TestJob-QWGXNELHYPQZ",
            "Arn": "arn:aws:events:eu-west-2:147726730760:rule/feature-test-operational-TestJob-QWGXNELHYPQZ",
            "State": "ENABLED",
            "Description": "Customer:test|Job:test_job",
            "ScheduleExpression": "cron(0 4,16 ? * * *)",
            "EventBusName": "default"
        }
    ],
    "ResponseMetadata": {
        "RequestId": "d48bdf45-b2b9-4e73-bb7a-408afe896a28",
        "HTTPStatusCode": 200,
        "HTTPHeaders": {
            "x-amzn-requestid": "d48bdf45-b2b9-4e73-bb7a-408afe896a28",
            "content-type": "application/x-amz-json-1.1",
            "content-length": "12982",
            "date": "Thu, 29 Aug 2024 15:34:26 GMT"
        },
        "RetryAttempts": 0
    }
}

eb_boto3_stub_client = StubEventbridgeBoto3Client(mock_response=stub_events_response)

class TestEventbridgeClient(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._eventbridge_rules_client = EventBridgeRuleClient(
            region="eu-west-2"
        )

    @freezegun.freeze_time("2024-08-29T12:23:14+00:00")
    def test_calculate_next_run_time(self):
        # Basic cron
        job_cron = "cron(23 12 * * ? *)"
        expected_next_run = "2024-08-30T12:23:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(formatted_next_run, expected_next_run,
                         "Converted cron time correct")

        # 2 hourly repeating cron
        job_cron = "cron(0 */2 * * ? *)"
        expected_next_run = "2024-08-29T14:00:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(formatted_next_run, expected_next_run,
                         "Converted cron time correct")

        # 2 hourly repeating cron between certain hours on a Monday
        job_cron = "cron(4 0-12/2 * * 2 *)"
        expected_next_run = "2024-09-02T00:04:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(formatted_next_run, expected_next_run,
                         "Converted cron time correct")

        # Run at 2am on the third of each month
        job_cron = "cron(0 2 3 * * *)"
        expected_next_run = "2024-09-03T02:00:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(formatted_next_run, expected_next_run,
                         "Converted cron time correct")

        # Complex cron
        job_cron = "cron(34 12,15-16 * * SUN *)"
        expected_next_run = "2024-09-01T12:34:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(formatted_next_run, expected_next_run,
                         "Converted cron time correct")

        # Multi-day cron
        job_cron = "cron(20 12 * * 1,3,5 *)"
        expected_next_run = "2024-09-01T12:20:00+00:00"
        actual_next_run = self._eventbridge_rules_client._calculate_next_trigger_time(
            job_cron)
        formatted_next_run = DateTimeUtil.date_time_to_str(actual_next_run)
        self.assertEqual(expected_next_run, formatted_next_run,"Converted cron time correct")

        # Invalid AWS cron
        with self.assertRaises(ValueError):
            job_cron = "cron(34 12,15-16 * * *)"
            self._eventbridge_rules_client._calculate_next_trigger_time(job_cron)

    def test_split_description(self):
        testing_rule = {
            "Name": "Testing Rule",
            "Description": "Customer:test|Job:standard_catalogue_loader"
        }

        expected = {
            "Customer": "test",
            "Job": "standard_catalogue_loader"
        }

        actual = self._eventbridge_rules_client._get_customer_and_job_name(testing_rule)
        self.assertEqual(actual, expected, "Split description correct")

    def test_split_description_missing(self):
        testing_rule = {
            "Name": "Testing Rule"
        }

        expected = {
            "Customer": "unknown",
            "Job": "unknown"
        }

        actual = self._eventbridge_rules_client._get_customer_and_job_name(testing_rule)
        self.assertEqual(actual, expected, "Split description (missing) correct")

    def test_split_description_incorrect(self):
        testing_rule = {
            "Name": "Testing Rule",
            "Description": "This is an | incorrect description"
        }

        expected = {
            "Customer": "unknown",
            "Job": "unknown"
        }

        actual = self._eventbridge_rules_client._get_customer_and_job_name(testing_rule)
        self.assertEqual(actual, expected, "Split description (missing) correct")

    @freezegun.freeze_time("2024-08-29T00:00:00+00:00")
    @patch("boto3.client", return_value=eb_boto3_stub_client)
    def test_get_next_run_time(self, boto3_mock):
        eb_rules_client = EventBridgeRuleClient(region="eu-west-2")
        expected_next_run_time = "2024-08-29T04:00:00+00:00"
        actual_next_run_time = eb_rules_client.get_next_trigger_time(
            job_name="test_job", customer="test"
        )
        formatted_actual = DateTimeUtil.date_time_to_str(actual_next_run_time)

        self.assertEqual(formatted_actual, expected_next_run_time,
                         "Next run time expected")

    @freezegun.freeze_time("2024-08-29T00:00:00+00:00")
    @patch("boto3.client", return_value=eb_boto3_stub_client)
    def test_get_next_run_time_unscheduled(self, boto3_mock):
        eb_rules_client = EventBridgeRuleClient(region="eu-west-2")
        expected_next_run_time = "Not a scheduled rule"
        actual_next_run_time = eb_rules_client.get_next_trigger_time(
            job_name="job_fail_notification", customer="operational"
        )

        self.assertEqual(actual_next_run_time, expected_next_run_time,
                         "Unscheduled rule run time expected")
