import unittest

from thefilter.aws.athena import AthenaDatePartition, AthenaQueryClientStub
from thefilter.logs.logclient import NoOpLogger


class AWSAthenaTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._athena_date_partition = AthenaDatePartition(
            environment="test",
            region="test",
            customer="test",
            log_client=NoOpLogger(),
            athena_client=AthenaQueryClientStub([])
        )

    def test_date_partition(self):
        expected_dates = ['2024-02-03', '2024-02-04', '2024-02-05']
        input = ['20240203', '20240204', '20240205']

        date_partitions = self._athena_date_partition._convert_to_datepartitions(
            input
        )

        self.assertEqual(expected_dates, date_partitions, "Dates match YYYYMMDD")

    def test_date_partition_hyphenated(self):
        expected_dates = ['2024-02-03', '2024-02-04', '2024-02-05']
        input = ['2024-02-03', '2024-02-04', '2024-02-05']

        date_partitions = self._athena_date_partition._convert_to_datepartitions(
            input
        )

        self.assertEqual(expected_dates, date_partitions, "Dates match YYYY-MM-DD")
