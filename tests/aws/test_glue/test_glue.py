from unittest import TestCase

from thefilter.aws.glue import Glue<PERSON>lient<PERSON><PERSON>
from thefilter.logs.logclient import <PERSON><PERSON><PERSON><PERSON>og<PERSON>
from thefilter.utils.slackbot import SlackBotStub


class TestGlueClientStub(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        region = 'eu-west-2'
        logger = NoOpLogger()
        environment = 'test_environment'
        customer = 'test_customer'
        slackbot = SlackBotStub()
        self._glue_client = GlueClientStub(
            region, environment, customer, logger, slackbot
        )

    def test_get_glue_table_definition(self):
        file_path = 'tests/aws/test_glue/data/test_metadata.json'
        table_columns = self._glue_client.get_glue_table_definition_from_file(file_path)

        expected = [
            {'Name': 'field_0', 'Type': 'string', 'Comment': ''},
            {'Name': 'field_1', 'Type': 'int', 'Comment': ''},
            {'Name': 'field_2', 'Type': 'array<string>', 'Comment': ''},
            {'Name': 'field_3', 'Type': 'boolean', 'Comment': ''},
            {'Name': 'field_4', 'Type': 'string', 'Comment': ''},
            {'Name': 'field_5', 'Type': 'double', 'Comment': ''}
        ]
        self.assertEqual(expected, table_columns)

    def test_create_glue_table_input(self):
        table_name = 'test_table_name'
        s3_path = 'test_s3_path'
        table_definition = [
            {'Name': 'field_0', 'Type': 'string', 'Comment': ''},
            {'Name': 'field_1', 'Type': 'int', 'Comment': ''}
        ]
        table_input = self._glue_client.create_glue_table_input(
            table_name, table_definition, s3_path
        )
        expected = {
            'Name': 'test_table_name',
            'StorageDescriptor': {
                'Columns': [{'Name': 'field_0', 'Type': 'string', 'Comment': ''},
                            {'Name': 'field_1', 'Type': 'int', 'Comment': ''}],
                'Location': 'test_s3_path',
                'InputFormat': 'org.apache.hadoop.mapred.TextInputFormat',
                'OutputFormat':
                    'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat',
                'SerdeInfo': {
                    'SerializationLibrary': 'org.openx.data.jsonserde.JsonSerDe',
                    'Parameters': {'paths': 'field_0,field_1'}}
            }
        }
        self.assertEqual(expected, table_input)
