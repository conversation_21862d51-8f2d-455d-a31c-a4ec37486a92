{"Records": [{"messageId": "4204b702-f2a9-47c0-8177-8ed5402dfb14", "receiptHandle": "AQEBNEBhJLelqE6dHGQcECaEvdUivEXl9uFjDdZc1Iwvt8Zmb6tMDkWwq43zIbR21YWFFDLg8XoQWsSAHzTHrvgJ+5ffhuUMRVYx97upN9YSuPAidzqUT4mcaT1SIOTUiIgCBR0ZaDnZ9s73XNU2nhmcpMnbrj1RImEx+ie/oNJoBrFEFHlLv4xb9pYyA+DnNkprUet0c7YG06hru47MvJhPc42YLl4MPN0sBnb0LySeyjD6w4frDNepQ8t19klsKI8tGMmjxxPA5xqqs+2pMptXfs0QrxdgRy2MvpAVcZ+XsmWJXi430HgK/krzevfAdGpb2yS3Y2K08mDoxQMlFH2LsZlTuleBr9noJN5ZfWUP5o3Ls3O6d88X6NMBFzSg2iEp", "body": "{\"identifier\": {\"eventId\": \"example_eventId\", \"operationId\": \"A43EEB9E-96E6-4235-AE3C-58E37C7A7A5C\"}, \"customer\": { \"name\": \"example_customer\"}, \"user\": {\"accountId\": \"example_accountId\", \"userId\": \"example_userId\", \"anonId\": \"example_anonId\", \"primaryId\": \"example_userId\", \"clusterId\": \"A\"}, \"timestamp\": {\"received\": \"2016-04-06T10:10:09+00:00\", \"initiated\": \"2016-04-06T10:10:09+00:00\"}, \"thing\": [{\"id\": \"example_id\", \"brandId\": \"example_brandId\", \"brandName\": \"example_brandName\", \"name\": \"example_name\", \"typeName\": \"thing:creativeWork\", \"description\": null, \"genre\": [{\"id\": \"example_genre_id\", \"name\": \"example_genre_name\"} ], \"subGenre\": [], \"contentRating\": [{\"id\": \"example_contentRating_id\", \"name\": \"example_content_name\"} ], \"actor\": [{\"id\": \"example_actor_id\", \"name\": \"example_actor_name\"} ], \"director\": [{\"id\": \"example_director_id\", \"name\": \"example_director_name\"} ], \"producer\": [], \"crew\": [], \"duration\": \"P0Y0M0DT2H30M5S\", \"datePublished\": \"2016-04-06T10:10:09.123Z\", \"publication\": [], \"partOfSeason\": {}, \"partOfSeries\": {}, \"keywords\": [], \"productionCompany\": null, \"episodeNumber\": null, \"seasonNumber\": null, \"image\": {}, \"numberOfSeasons\": null, \"space\": null, \"isKids\": null, \"isAdult\": null, \"custom\": {\"active\": {\"state\": true, \"stateUpdateTimestamp\": \"2021-01-02T13:59:45+00:00\"}, \"brand\": [], \"category\": [], \"subCategory\": [], \"channel\": [], \"subGenre\": [{\"id\": \"example_id\", \"name\": \"example_name\"}, {\"id\": \"example_id2\", \"name\": \"example_name2\"} ] } } ], \"action\": \"example_action\", \"context\": {\"application\": \"example_application\", \"server\": \"example_server\", \"site\": \"example_server\", \"environment\": \"example_environment\", \"code_version\": \"example_version\"}, \"custom\": {\"bt\": {\"mpx\": \"example_mpx\", \"serviceType\": \"example_serviceType\", \"slotType\": \"example_slotType\"} }, \"sourceOperationId\": \"example_sourceOperationId\", \"messageAttributes\": {\"example_key\": \"example_value\"}}", "attributes": {"ApproximateReceiveCount": "13", "SentTimestamp": "1549461337610", "SenderId": "AIDAIK3Q2FDQ7LDFLHB7Y", "ApproximateFirstReceiveTimestamp": "1549461337610"}, "messageAttributes": {}, "md5OfBody": "bb0b4ed02d20ab1279e41a4903e83b20", "eventSource": "aws:sqs", "eventSourceARN": "arn:aws:sqs:eu-west-1:147726730760:Catalogue", "awsRegion": "eu-west-1"}]}