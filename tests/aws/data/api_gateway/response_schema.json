{"type": "object", "required": ["code", "responseId", "start", "end", "itemCount", "errorCount", "title", "items"], "properties": {"code": {"type": "integer"}, "responseId": {"type": "string"}, "datacentreId": {"type": "string"}, "start": {"type": "integer"}, "end": {"type": "integer"}, "itemCount": {"type": "integer"}, "errorCount": {"type": "integer"}, "title": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/definitions/item"}}}, "definitions": {"item": {"type": "object", "required": ["id", "typeName"], "properties": {"id": {"type": "string"}, "typeName": {"type": "string"}}}}}