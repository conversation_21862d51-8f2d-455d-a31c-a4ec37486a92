{"$schema": "http://json-schema.org/draft-04/schema#", "type": "array", "items": {"$ref": "#/definitions/document"}, "definitions": {"document": {"type": "object", "required": ["index", "type", "id", "document"], "properties": {"index": {"type": "string", "pattern": "^log_[a-z0-9]{4}.[0-9]{4}-[0-9]{2}$"}, "type": {"type": "string", "enum": ["_doc"]}, "id": {"type": "string"}, "document": {"type": "object"}}}}}