{"Type": "Notification", "MessageId": "b448f4fe-b1a5-5329-babe-a1ec199e6f55", "TopicArn": "arn:aws:sns:eu-west-1:************:Beacon-Ingestion", "Message": "{\"identifier\": {\"eventId\": \"example_eventId\", \"operationId\": \"A43EEB9E-96E6-4235-AE3C-58E37C7A7A5C\"}, \"customer\": { \"name\": \"example_customer\"}, \"user\": {\"accountId\": \"example_accountId\", \"userId\": \"example_userId\", \"anonId\": \"example_anonId\", \"primaryId\": \"example_userId\", \"clusterId\": \"A\"}, \"timestamp\": {\"received\": \"2016-04-06T10:10:09+00:00\", \"initiated\": \"2016-04-06T10:10:09+00:00\"}, \"thing\": [{\"id\": \"example_id\", \"brandId\": \"example_brandId\", \"name\": \"example_name\", \"alternateName\": null, \"typeName\": \"thing:creativeWork\", \"description\": null, \"genre\": [{\"id\": \"example_genre_id\", \"name\": \"example_genre_name\"} ], \"subGenre\": [], \"contentRating\": [{\"id\": \"example_contentRating_id\", \"name\": \"example_content_name\"} ], \"actor\": [{\"id\": \"example_actor_id\", \"name\": \"example_actor_name\"} ], \"director\": [{\"id\": \"example_director_id\", \"name\": \"example_director_name\"} ], \"producer\": [], \"crew\": [], \"duration\": \"P0Y0M0DT2H30M5S\", \"datePublished\": \"2016-04-06T10:10:09.123Z\", \"publication\": [], \"partOfSeason\": {}, \"partOfSeries\": {}, \"keywords\": [], \"productionCompany\": null, \"episodeNumber\": null, \"seasonNumber\": null,  \"image\": {}, \"inLanguage\": null, \"numberOfSeasons\": null, \"isKids\": null, \"isAdult\": null, \"custom\": {\"active\": {\"state\": true, \"stateUpdateTimestamp\": \"2021-01-02T13:59:45+00:00\"}, \"subGenre\": [{\"id\": \"example_id\", \"name\": \"example_name\"}, {\"id\": \"example_id2\", \"name\": \"example_name2\"} ] } } ], \"action\": \"example_action\", \"context\": {\"application\": \"example_application\", \"server\": \"example_server\", \"site\": \"example_server\", \"environment\": \"example_environment\", \"code_version\": \"example_version\"}, \"custom\": {\"bt\": {\"mpx\": \"example_mpx\", \"serviceType\": \"example_serviceType\", \"slotType\": \"example_slotType\"} }, \"sourceOperationId\": \"example_sourceOperationId\", \"messageAttributes\": {\"example_key\": \"example_value\"}}", "Timestamp": "2019-03-25T09:57:15.694Z", "SignatureVersion": "1", "Signature": "Hf2ZqNBSNQPwozfdYEBSK4G0xEDTPh6pHVqIwSK0Bq3hLEX/SWihywMQ4S5CytBFbGe5ifE24gJGqJHvWuoTSuVDamRGPZtonUXGNZVtxxKk4G14bdpbP4BAb3oaiYG5d2fWlbqCbOZeipp0NjqbbIv1/GmyHWEVdJbC7HMlZAMpDCEkFaiAhe4CDvpinPMKWrZwgHJWWizywKCPU1HtKw/0CJ2oxIEAlivbfY4iQ0O4qg5OvZDPulllxpSEAHmXyiDYtuOgv7zfmuJbDhQ5VhmzgmMvN1gY0P3/lDNXQaCGMoWyRzuHtjjmBo7zgHDBHelsQ6sW1WRdYKmdRyBS4Q==", "SigningCertURL": "https://sns.eu-west-1.amazonaws.com/SimpleNotificationService-6aad65c2f9911b05cd53efda11f913f9.pem", "UnsubscribeURL": "https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:************:Beacon-Ingestion:c642b047-f000-4f20-8971-66103c09791b", "MessageAttributes": {"event_type": {"Type": "String", "Value": "event_type"}}}