import datetime

from thefilter.model.context import Context
from thefilter.model.identifier import Identifier
from thefilter.model.messages.message import Message
from thefilter.model.schemaorg import Thing
from thefilter.model.timestamp import Timestamp
from thefilter.model.user import User

expected_sqs_message = Message(
    customer={'name': 'example_customer'},
    identifier=Identifier(eventId='example_eventId',
                          operationId='A43EEB9E-96E6-4235-AE3C-58E37C7A7A5C'),
    user=User(accountId='example_accountId', userId='example_userId',
              anonId='example_anonId', primaryId='example_userId', clusterId='A'),
    timestamp=Timestamp(
        initiated=datetime.datetime(2016, 4, 6, 10, 10, 9, tzinfo=datetime.timezone.utc),
        received=datetime.datetime(2016, 4, 6, 10, 10, 9, tzinfo=datetime.timezone.utc)
    ),
    action='example_action',
    context=Context(application='example_application', environment='example_environment',
                    server='example_server', site='example_server',
                    code_version='example_version'), thing=[
        Thing(id='example_id', brandId='example_brandId', brandName='example_brandName',
              recommendationId=None,
              name='example_name', alternateName=None, typeName='thing:creativeWork',
              genre=[{'id': 'example_genre_id', 'name': 'example_genre_name'}],
              subGenre=[], contentRating=[
                {'id': 'example_contentRating_id', 'name': 'example_content_name'}],
              actor=[{'id': 'example_actor_id', 'name': 'example_actor_name'}],
              director=[{'id': 'example_director_id', 'name': 'example_director_name'}],
              producer=[], crew=[], partOfSeason={}, partOfSeries={}, publication=[],
              keywords=[], episodeNumber=None, seasonNumber=None, numberOfSeasons=None,
              description=None, duration='P0Y0M0DT2H30M5S',
              datePublished='2016-04-06T10:10:09.123Z', image={}, inLanguage=None,
              isLiveBroadcast=None, productionCompany=None, isKids=None, isAdult=None,
              custom={
                  'active': {'state': True,
                             'stateUpdateTimestamp': '2021-01-02T13:59:45+00:00'},
                  'brand': [], 'category': [], 'subCategory': [], 'channel': [],
                  'subGenre': [{'id': 'example_id', 'name': 'example_name'},
                               {'id': 'example_id2', 'name': 'example_name2'}]})],
    custom={
        'bt': {'mpx': 'example_mpx', 'serviceType': 'example_serviceType',
               'slotType': 'example_slotType'}},
    sourceOperationId='example_sourceOperationId',
    messageAttributes={'example_key': 'example_value'}
)

expected_sns_unwrapped_message = {
    'identifier': {'eventId': 'example_eventId',
                   'operationId': 'A43EEB9E-96E6-4235-AE3C-58E37C7A7A5C'},
    'customer': {'name': 'example_customer'},
    'user': {'accountId': 'example_accountId', 'userId': 'example_userId',
             'anonId': 'example_anonId', 'primaryId': 'example_userId',
             'clusterId': 'A'},
    'timestamp': {'received': '2016-04-06T10:10:09+00:00',
                  'initiated': '2016-04-06T10:10:09+00:00'},
    'thing': [{'id': 'example_id', 'brandId': 'example_brandId', 'name': 'example_name',
               'alternateName': None, 'typeName': 'thing:creativeWork',
               'description': None,
               'genre': [{'id': 'example_genre_id', 'name': 'example_genre_name'}],
               'subGenre': [], 'contentRating': [
            {'id': 'example_contentRating_id', 'name': 'example_content_name'}],
               'actor': [{'id': 'example_actor_id', 'name': 'example_actor_name'}],
               'director': [
                   {'id': 'example_director_id', 'name': 'example_director_name'}],
               'producer': [], 'crew': [], 'duration': 'P0Y0M0DT2H30M5S',
               'datePublished': '2016-04-06T10:10:09.123Z', 'publication': [],
               'partOfSeason': {}, 'partOfSeries': {}, 'keywords': [],
               'productionCompany': None, 'episodeNumber': None, 'seasonNumber': None,
               'image': {}, 'inLanguage': None, 'numberOfSeasons': None,
               'isAdult': None, 'isKids': None,
               'custom': {
                   'active': {'state': True,
                              'stateUpdateTimestamp': '2021-01-02T13:59:45+00:00'},
                   'subGenre': [{'id': 'example_id', 'name': 'example_name'},
                                {'id': 'example_id2', 'name': 'example_name2'}]}}],
    'action': 'example_action',
    'context': {'application': 'example_application', 'server': 'example_server',
                'site': 'example_server', 'environment': 'example_environment',
                'code_version': 'example_version'}, 'custom': {
        'bt': {'mpx': 'example_mpx', 'serviceType': 'example_serviceType',
               'slotType': 'example_slotType'}},
    'sourceOperationId': 'example_sourceOperationId',
    'messageAttributes': {'example_key': 'example_value'}
}
