from unittest import TestCase
from thefilter.aws.aws_lambda import RequestHeaderFilter


class TestHeaderFilter(TestCase):
    def test_referer(self):
        header_filter = RequestHeaderFilter
        request_headers = test_request["headers"]
        filtered_headers = header_filter.filter_headers(request_headers)
        self.assertEqual(expected, filtered_headers)


test_request = {
    "resource": "/events/{event_type}",
    "path": "/events/Play",
    "httpMethod": "GET",
    "headers": {
        "referer": "https://uktvplay.uktv.co.uk/shows/hypothetical/watch-online",
        "User-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) "
                      "AppleWebKit/537.36 (KHTML, like Gecko) "
                      "Chrome/78.0.3904.97 Safari/537.36",
        "BoGuS-hEaDeR": "should_NOT_be_validated"
    },
    "space": "brand",
    "https://eeee7491a9c34dd9b2f2daa63dc09b96-rest.thefilter.com/events/view?itemId":
        "3323"
}

expected = {
    "Referer": "https://uktvplay.uktv.co.uk/shows/hypothetical/watch-online",
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 "
                  "(KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36",
}
