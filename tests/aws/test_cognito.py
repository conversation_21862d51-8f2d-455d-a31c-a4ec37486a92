import uuid
from datetime import datetime
from unittest import TestCase
from unittest.mock import patch

import requests.exceptions

from thefilter.aws.cognito import CognitoClient


class TestCognito(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cognito_client = CognitoClient("test", "eu-west-2")

    def mock_make_api_call(self, operation_name: str, kwargs) -> dict:
        if operation_name == "ListUserPools":
            return {
                "ResponseMetadata": {
                    "HTTPStatusCode": 200,
                    "HTTPHeaders": {},
                    "RequestId": uuid.uuid4(),
                    "RetryAttempts": 0
                },
                "UserPools": [
                    {
                        "Id": "eu-west-2_testPoolID",
                        "Name": "test-userpool",
                        "Status": "Enabled",
                        "LastModifiedDate": datetime(2023, 9, 29),
                        "CreationDate": datetime(2023, 9, 29)
                    }
                ]
            }
        elif operation_name == "ListUserPoolClients":
            return {
                "ResponseMetadata": {
                    "HTTPStatusCode": 200,
                    "HTTPHeaders": {},
                    "RequestId": uuid.uuid4(),
                    "RetryAttempts": 0
                },
                "UserPoolClients": [
                    {
                        "ClientId": "123456789abcdefghi",
                        "ClientName": "test-test-AppClient",
                        "UserPoolId": "eu-west-2_testPoolID"
                    },
                    {
                        "ClientId": "padding_1",
                        "ClientName": "test-padding_1-AppClient",
                        "UserPoolId": "eu-west-2_testPoolID"
                    },
                    {
                        "ClientId": "padding_2",
                        "ClientName": "test-padding_2-AppClient",
                        "UserPoolId": "eu-west-2_testPoolID"
                    },
                    {
                        "ClientId": "http_error",
                        "ClientName": "test-http_error-AppClient",
                        "UserPoolId": "eu-west-2_testPoolID"
                    }
                ]
            }
        elif operation_name == "DescribeUserPoolClient":
            return {
                "ResponseMetadata": {
                    "HTTPStatusCode": 200,
                    "HTTPHeaders": {},
                    "RequestId": uuid.uuid4(),
                    "RetryAttempts": 0
                },
                "UserPoolClient": {
                    "UserPoolId": "eu-west-2_testPoolID",
                    "ClientName": "pre-test-AppClient",
                    "ClientId": "123456789abcdefghi",
                    "ClientSecret": "test_client_secret",
                    "LastModifiedDate": datetime(2023, 9, 29),
                    "CreationDate": datetime(2023, 9, 29),
                    "RefreshTokenValidity": 30,
                    "TokenValidityUnits": {},
                    "ReadAttributes": [
                        "email",
                        "email_verified",
                        "name",
                        "phone_number",
                        "phone_number_verified",
                        "preferred_username",
                        "profile",
                        "updated_at"
                    ],
                    "WriteAttributes": [
                        "email",
                        "name",
                        "phone_number",
                        "preferred_username",
                        "profile",
                        "updated_at"
                    ],
                    "ExplicitAuthFlows": [
                        "ALLOW_CUSTOM_AUTH",
                        "ALLOW_REFRESH_TOKEN_AUTH",
                        "ALLOW_USER_SRP_AUTH"
                    ],
                    "SupportedIdentityProviders": [
                        "COGNITO"
                    ],
                    "AllowedOAuthFlows": [
                        "client_credentials"
                    ],
                    "AllowedOAuthScopes": [
                        "TF-test-API/read",
                        "TF-test-API/write"
                    ],
                    "AllowedOAuthFlowsUserPoolClient": True,
                    "PreventUserExistenceErrors": "ENABLED",
                    "EnableTokenRevocation": False,
                    "EnablePropagateAdditionalUserContextData": False,
                    "AuthSessionValidity": 3
                }
            }
        else:
            return {}

    def mocked_requests_post(*args, **kwargs):
        class MockResponse:
            def __init__(self, json_data, status_code):
                self.json_data = json_data
                self.status_code = status_code

            def json(self):
                return self.json_data

        if kwargs["url"] == "https://users.test.thefilter.com/oauth2/token":
            if kwargs["auth"] == ("123456789abcdefghi", "test_client_secret"):
                return MockResponse({
                    "access_token": "testToken",
                    "expires_in": 3600,
                    "token_type": "Bearer"
                }, 200)
            elif kwargs["auth"][0] == "http_error":
                raise requests.exceptions.HTTPError("This client Id is meant to error")
            else:
                return MockResponse(None, 401)
        else:
            return MockResponse(None, 404)

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    @patch("requests.post", side_effect=mocked_requests_post)
    def test_cognito_get_token(self, mock_requests_post):
        token = self._cognito_client.get_cognito_token("test")
        self.assertEqual(token, "testToken", "Retrieved token matches")

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    @patch("requests.post", side_effect=mocked_requests_post)
    def test_cognito_get_raise_auth_exception(self, mock_requests_post):
        with self.assertRaises(Exception):
            self._cognito_client.get_cognito_token("missing")

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    @patch("requests.post", side_effect=mocked_requests_post)
    def test_cognito_get_raise_HTTPError(self, mock_requests_post):
        with self.assertRaises(requests.exceptions.HTTPError):
            self._cognito_client.get_cognito_token("http_error")