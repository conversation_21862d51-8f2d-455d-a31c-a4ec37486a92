from datetime import datetime
from unittest import TestCase
from unittest.mock import patch

from thefilter.aws.cloudfront import CloudFrontClient


class TestCloudfront(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._cloudfront_client = CloudFrontClient()

    def mock_make_api_call(self, operation_name: str, kwargs) -> dict:
        if operation_name == "ListDistributions":
            return {
                "ResponseMetadata": {
                    "RequestId": "b3cdce8d-2354-4010-83f2-99bc85e261da",
                    "HTTPStatusCode": 200,
                    "HTTPHeaders": {
                        "x-amzn-requestid": "b3cdce8d-2354-4010-83f2-99bc85e261da",
                        "content-type": "text/xml",
                        "content-length": "13472",
                        "date": "Mon, 02 Oct 2023 14:42:49 GMT"
                    },
                    "RetryAttempts": 0
                },
                "DistributionList": {
                    "Marker": "",
                    "MaxItems": 100,
                    "IsTruncated": False,
                    "Quantity": 3,
                    "Items": [
                        {
                            "Id": "E2BVKRWMTPE5TI",
                            "ARN": "arn:aws:cloudfront::294068739031:distribution/E2BVKRWMTPE5TI",
                            "Status": "Deployed",
                            "LastModifiedTime": datetime(2023, 8, 23),
                            "DomainName": "deocfx9c08hfq.cloudfront.net",
                            "Aliases": {
                                "Quantity": 1,
                                "Items": [
                                    "portal.pre.thefilter.com"
                                ]
                            },
                            "Origins": {
                                "Quantity": 2,
                                "Items": [
                                    {
                                        "Id": "S3-visualise.pre.thefilter.com"

                                    },
                                    {
                                        "Id": "S3-thefilter-pre-media/images"
                                        # Truncated for brevity
                                    }
                                ]
                            }
                            # Truncated for brevity
                        },
                        {
                            "Id": "E11IYE2HWRCUJA",
                            "ARN": "arn:aws:cloudfront::294068739031:distribution/E11IYE2HWRCUJA",
                            "Status": "Deployed",
                            "LastModifiedTime": datetime(2023, 9, 28),
                            "DomainName": "d1cm7gx1e0t9ft.cloudfront.net",
                            "Aliases": {
                                "Quantity": 1,
                                "Items": [
                                    "epix.pre.thefilter.com"
                                ]
                            },
                            "Origins": {
                                "Quantity": 1,
                                "Items": [
                                    {
                                        "Id": "S3-thefilter-pre-epix-fallback",
                                        "DomainName": "thefilter-pre-epix-fallback.s3.amazonaws.com"
                                        # Truncated for brevity
                                    }
                                ]
                            }
                            # Truncated for brevity
                        },
                        {
                            "Id": "E2FZKV7044FEJ4",
                            "ARN": "arn:aws:cloudfront::294068739031:distribution/E2FZKV7044FEJ4",
                            "Status": "Deployed",
                            "LastModifiedTime": datetime(2023, 9, 28),
                            "DomainName": "d1130bp2rpbecc.cloudfront.net",
                            "Aliases": {
                                "Quantity": 1,
                                "Items": [
                                    "uktv.pre.thefilter.com"
                                ]
                            },
                            "Origins": {
                                "Quantity": 1,
                                "Items": [
                                    {
                                        "Id": "S3-thefilter-pre-uktv-fallback"
                                        # Truncated for brevity
                                    }
                                ]
                            }
                            # Truncated for brevity
                        }
                    ]
                }
            }

        elif operation_name == "CreateInvalidation":
            return {
                "ResponseMetadata": {
                    "RequestId": "959b74d6-1e81-4b4b-8589-843ccff55958",
                    "HTTPStatusCode": 201,
                    "HTTPHeaders": {
                        "x-amzn-requestid": "959b74d6-1e81-4b4b-8589-843ccff55958",
                        "location": "https://cloudfront.amazonaws.com/2020-05-31/distribution/test_distributions/invalidation/IDQH3UQSBWYQ7KUH0TJNE9XIG3",
                        "content-type": "text/xml",
                        "content-length": "418",
                        "date": "Mon, 02 Oct 2023 14:56:17 GMT"
                    },
                    "RetryAttempts": 0
                },
                "Location": "https://cloudfront.amazonaws.com/2020-05-31/distribution/test_distribution/invalidation/IDQH3UQSBWYQ7KUH0TJNE9XIG3",
                "Invalidation": {
                    "Id": "IDQH3UQSBWYQ7KUH0TJNE9XIG3",
                    "Status": "InProgress",
                    "CreateTime": datetime(2023,
                                           10,
                                           2),
                    "InvalidationBatch": {
                        "Paths": {
                            "Quantity": 1,
                            "Items": [
                                "/*"
                            ]
                        },
                        "CallerReference": "Python CloudFront Invalidation - 2023-10-02T15:56:17.457899"
                    }
                }
            }
        else:
            return {}

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    def test_cloudfront_get_distributions(self):
        distributions = self._cloudfront_client.get_cloudfront_distributions()
        self.assertEqual(len(distributions), 3, "Expected distribution count")

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    def test_cloudfront_get_cloudfront_fallback_distributions(self):
        distributions = self._cloudfront_client.get_cloudfront_fallback_distributions()
        self.assertEqual(len(distributions), 2, "Expected fallback distribution count")

    @patch("botocore.client.BaseClient._make_api_call", new=mock_make_api_call)
    def test_cloudfront_get_cloudfront_invalidate_distribution(self):
        invalidation_response = self._cloudfront_client.invalidate_distribution(
            "test_distribution")
        self.assertIn("test_distribution", invalidation_response["Location"],
                      "Invalidation ID matches")
        invalidation_paths = invalidation_response["Invalidation"]["InvalidationBatch"][
            "Paths"]
        self.assertEqual(len(invalidation_paths["Items"]), 1,
                         "Invalidation count matches")
        self.assertEqual(invalidation_paths["Items"][0], "/*",
                         "Invalidation path matches")
