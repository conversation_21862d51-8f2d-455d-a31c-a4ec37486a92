import json
import unittest
from copy import deepcopy
from datetime import datetime

import freezegun
from jsonschema import validate

from thefilter.aws.api_gateway import ResponseFormatter, \
    UserHistoryResponseFormatter

test_data_dir = "tests/aws/data/api_gateway"


class ResponseFormatterTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(ResponseFormatterTests, self).__init__(*args, **kwargs)

        self._formatter = ResponseFormatter()

        list_of_ids = [
            '117357125226', '244717637094', '239917637086', '239917637085',
            '129381445236', '4996165213', '105184325479', '244715589454',
            '239914565494', '142907973141', '219547717472', '24930885330',
            '10823749092', '160907333036', '105186373034'
        ]
        example_input = []
        for item in list_of_ids:
            thing = {}
            thing['id'] = item
            thing['typeName'] = " "
            example_input.append(thing)

        self.example_formatter_input = example_input
        with open(f'{test_data_dir}/example_formatter_output.json') as f:
            self.example_formatter_output = json.load(f)
        with open(f'{test_data_dir}/response_schema.json') as f:
            self.response_schema = json.load(f)

        self.formatted_es_metadata_response = {'code': 200,
                                               'end': 2,
                                               'errorCount': 0,
                                               'itemCount': 1000,
                                               'items': [{
                                                   'description': 'Test_Description',
                                                   'id': '12',
                                                   'name': 'Test_Brand',
                                                   'space': 'brand',
                                                   'typeName': 'Brand',
                                                   'url': None},
                                                   {'description': None,
                                                    'id': '13',
                                                    'name': 'Test_Brand',
                                                    'space': None,
                                                    'typeName': 'Brand',
                                                    'url': 'https://picture.png'}],
                                               'responseId': 'test_uuid',
                                               'start': 1,
                                               'title': ''}

        self.formatted_with_model_info = {'code': 200,
                                          'end': 2,
                                          'errorCount': 0,
                                          'itemCount': 1000,
                                          'items': [{
                                              'id': '12',
                                              'name': 'Test_Name_1',
                                              'modelName': 'test_model_name_1',
                                              'positionInModel': 'test_position_in_model_1',
                                              'positionOverall': 'test_pos_overall_1'},
                                              {'id': '13',
                                               'name': 'Test_Name_2',
                                               'modelName': 'test_model_name_2',
                                               'positionInModel': 'test_position_in_model_2',
                                               'positionOverall': 'test_pos_overall_2'}],
                                          'responseId': 'test_uuid',
                                          'start': 1,
                                          'title': ''}

    def test_output_correct(self):
        """ Checks an example input to expected output
        """
        response = self._formatter.format(
            status_code=200,
            thing_list=self.example_formatter_input,
            operation_id="example-operation-id",
            customer="test",
            title="[Next gen] New - generic"
        )
        assert response == self.example_formatter_output, \
            "Output does not match expected output.\n Got {} \n Expected {}".format(
                response,
                self.example_formatter_output)

    def test_output_against_schema(self):
        """ Checks output against schema """
        response = self._formatter.format(
            status_code=200,
            thing_list=self.example_formatter_input,
            operation_id="example-operation-id",
            customer='test'
        )
        validate(response, self.response_schema)

    def test_output_with_format_thing_with_metadata(self):
        things = [{"thing": {"typeName": "Brand",
                             "name": "Test_Brand",
                             "description": "Test_Description",
                             "space": "brand",
                             "id": "12"}},
                  {"thing": {"typeName": "Brand",
                             "name": "Test_Brand",
                             "id": "13",
                             "image": {"url": "https://picture.png"}}}]
        response = self._formatter.format(
            status_code=200,
            operation_id="test_uuid",
            customer="uktv",
            thing_list=things,
            format_with_es_metadata=['thing.id',
                                     'thing.description',
                                     'thing.image.url',
                                     'thing.name',
                                     'thing.space',
                                     'thing.typeName']
        )
        self.assertDictEqual(self.formatted_es_metadata_response, response)

    def test_output_with_model_info(self):
        things = [{"thing": {
            "name": "Test_Name_1",
            "id": "12",
            "custom": {
                "modelInfo": {
                    "positionOverall": "test_pos_overall_1",
                    "modelName": "test_model_name_1",
                    "positionInModel": "test_position_in_model_1"
                }
            }
        }},
            {"thing": {
                "typeName": "Brand",
                "name": "Test_Name_2",
                "id": "13",
                "custom": {
                    "modelInfo": {
                        "positionOverall": "test_pos_overall_2",
                        "modelName": "test_model_name_2",
                        "positionInModel": "test_position_in_model_2"
                    }
                }
            }}]
        response = self._formatter.format(
            status_code=200,
            operation_id="test_uuid",
            customer="uktv",
            thing_list=things,
            format_with_es_metadata=['thing.id',
                                     'thing.name',
                                     'thing.custom.modelInfo.modelName',
                                     'thing.custom.modelInfo.positionOverall',
                                     'thing.custom.modelInfo.positionInModel']
        )
        self.assertDictEqual(self.formatted_with_model_info, response)

    def test_format_thing_no_metadata(self):
        expected = {
            "id": "1234",
            "typeName": "TVSeries"
        }

        input_to_format = deepcopy(expected)
        input_to_format.update({
            "name": "Test Series",
            "genre": "Comedy"
        })

        result = self._formatter.format_thing(input_to_format, False, "test", "")

        self.assertDictEqual(expected, result, "No metadata thing matches")

    def test_format_thing_no_metadata_view(self):
        expected = {
            "id": "1234",
            "typeName": "TVSeries",
            "view": "Viewed"
        }

        input_to_format = deepcopy(expected)
        input_to_format.update({
            "name": "Test Series",
            "genre": "Comedy"
        })

        result = self._formatter.format_thing(input_to_format, False, "test", "")

        self.assertDictEqual(expected, result, "No metadata thing matches")

    def test_format_thing_debug_metadata(self):
        expected = {
            "id": "1234",
            "typeName": "TVSeries",
            "name": "Test Series",
            "genre": "Comedy"
        }

        input_to_format = deepcopy(expected)
        input_to_format["id"] = 1234

        result = self._formatter.format_thing(input_to_format, True, "test", "")

        self.assertDictEqual(expected, result, "Metadata thing matches")

    def test_format_thing_no_metadata_uktv(self):
        expected = {
            "id": "1234",
            "typeName": "Brand"
        }

        input_to_format = deepcopy(expected)
        input_to_format["id"] = "1234_brand"

        result = self._formatter.format_thing(input_to_format, False, "uktv", "")

        self.assertDictEqual(expected, result, "No metadata uktv thing matches")

    def test_format_thing_collection(self):
        expected = {
            "id": "123",
            "typeName": "Collection",
            "title": "Collection 123"
        }

        thing_input = deepcopy(expected)
        thing_input["name"] = thing_input.pop("title")

        result = self._formatter.format_thing(thing_input, False, "test", "")

        self.assertDictEqual(expected, result, "Collection thing matches")

    @freezegun.freeze_time("2024-01-19T12:43:00+00:00")
    def test_format_values(self):
        values = ["value1", 2.0, "value 3", 4, datetime.now(), None]
        expected = ["value1", "2.0", "value 3", "4", "2024-01-19T12:43:00", None]
        result = self._formatter.format_values(values)

        self.assertEqual(expected, result, "Formatted values match")

    def test_format_values_no_list(self):
        values = "value1"
        expected = ["value1"]
        result = self._formatter.format_values(values)

        self.assertEqual(expected, result, "Formatted values match")


class UserHistoryResponseFormatterTests(unittest.TestCase):

    def test_response_formatter(self):
        formatter = UserHistoryResponseFormatter()
        event_list = [{'action': 'Test_Play',
                       'user': {'userId': 'Test_User_Id'},
                       'thing': [{'typeName': 'Test_Brand', 'id': 'Test_Id'}],
                       'timestamp': {
                           'initiated': 'Test_Timestamp'}},
                      {'action': 'Test_Play_2',
                       'user': {'userId': 'Test_User_Id_2'},
                       'thing': [{'typeName': 'Test_Brand', 'id': 'Test_Id'}],
                       'timestamp': {
                           'initiated': 'Test_Timestamp'}}
                      ]
        formatted_user_event_history = {
            'code': 200,
            "responseId": "test_uuid",
            "start": 1,
            "end": 2,
            "itemCount": 2,
            "errorCount": 0,
            "title": "",
            'items': [
                {
                    'action': 'Test_Play',
                    'user': {
                        'userId': 'Test_User_Id'},
                    'thing': [{
                        'typeName': 'Test_Brand',
                        'id': 'Test_Id'}],
                    'timestamp': {
                        'initiated': 'Test_Timestamp'}
                },
                {
                    'action': 'Test_Play_2',
                    'user': {
                        'userId': 'Test_User_Id_2'},
                    'thing': [{
                        'typeName': 'Test_Brand',
                        'id': 'Test_Id'}],
                    'timestamp': {
                        'initiated': 'Test_Timestamp'}
                }
            ]
        }
        response = formatter.format(
            status_code=200,
            operation_id="test_uuid",
            events=event_list)
        self.assertDictEqual(formatted_user_event_history, response)
