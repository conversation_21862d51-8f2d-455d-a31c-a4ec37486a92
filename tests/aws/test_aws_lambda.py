import json
import unittest
from datetime import datetime, timezone
from unittest import TestCase

import freezegun

from tests.data.test_data import expected_message
from thefilter.aws.aws_lambda import SqsSnsUnwrapper, log_error_and_throw_exception, \
    LambdaEventInterpreter, generate_personalisation_request_and_slot_override
from thefilter.aws.sns import SnsUnwrapper
from thefilter.aws.sqs import SqsUnwrapper
from thefilter.model.messages.message import Message
from thefilter.model.messages.request import Request
from thefilter.model.messages.response import RequestLog

test_data_dir = "tests/data/"


class SqsSnsUnwrapperTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        with open(test_data_dir + "sqsSnsMessageLambdaEvent.json") as f:
            self.sqsSnsMessageLambdaEvent = json.load(f)
        with open(test_data_dir + "sqsSnsRequestLogLambdaEvent.json") as f:
            self.sqsSnsRequestLogLambdaEvent = json.load(f)
        with open(test_data_dir + "test_message.json") as f:
            self.test_message = json.load(f)
        with open(test_data_dir + "test_request_log.json") as f:
            req_log_json = json.load(f)
            self.test_request_log = RequestLog(**req_log_json)

    def test_unwrap_outputs_a_message(self):
        unwrapper = SqsSnsUnwrapper(sns_unwrapper=SnsUnwrapper(),
                                    sqs_unwrapper=SqsUnwrapper())
        unwrapped_messages = unwrapper.unwrap(self.sqsSnsMessageLambdaEvent,
                                              return_object_type=Message)
        unwrapped_message = unwrapped_messages[0]
        self.assertIsInstance(unwrapped_message, Message)

    def test_unwrap_outputs_a_request_log(self):
        unwrapper = SqsSnsUnwrapper(sns_unwrapper=SnsUnwrapper(),
                                    sqs_unwrapper=SqsUnwrapper())
        unwrapped_messages = unwrapper.unwrap(self.sqsSnsRequestLogLambdaEvent,
                                              RequestLog)
        unwrapped_message = unwrapped_messages[0]
        self.assertIsInstance(unwrapped_message, RequestLog)

    def test_unwrap_to_message_correct(self):
        unwrapper = SqsSnsUnwrapper(sqs_unwrapper=SqsUnwrapper(),
                                    sns_unwrapper=SnsUnwrapper())
        unwrapped_messages = unwrapper.unwrap(self.sqsSnsMessageLambdaEvent,
                                              Message)
        unwrapped_message = unwrapped_messages[0]
        self.assertEqual(expected_message, unwrapped_message)

    def test_unwrap_to_request_log_correct(self):
        unwrapper = SqsSnsUnwrapper(sqs_unwrapper=SqsUnwrapper(),
                                    sns_unwrapper=SnsUnwrapper())
        unwrapped_messages = unwrapper.unwrap(self.sqsSnsRequestLogLambdaEvent,
                                              RequestLog)
        unwrapped_message = unwrapped_messages[0]
        self.assertEqual(self.test_request_log, unwrapped_message)


class CloudWatchLoggerTests(unittest.TestCase):
    def test_throws_exception(self):
        with self.assertRaises(Exception):
            log_error_and_throw_exception("Test message", Exception())


class LambdaEventInterpreterTests(unittest.TestCase):
    def test_interpret_items_request_with_space(self):
        test_event = {
            "body-json": [
                "12",
                "13_brand"
            ],
            "params": {
                "path": {},
                "querystring": {
                    "space": "brand"
                },
                "header": {"Authorization": "Basic tokentoken"}
            }
        }
        expected_ids = ["12_brand", "13_brand"]
        expected_space = "brand"
        expected_token = "tokentoken"

        resulting_ids, resulting_space, resulting_token = \
            LambdaEventInterpreter.interpret_uktv_delete_request_event(test_event)

        self.assertEqual(expected_token, resulting_token)
        self.assertEqual(expected_space, resulting_space)
        self.assertListEqual(expected_ids, resulting_ids)

    def test_interpret_items_request_with_episode_space(self):
        test_event = {
            "body-json": [
                "12",
                "13"
            ],
            "params": {
                "path": {},
                "querystring": {
                    "space": "episode"
                },
                "header": {"Authorization": "Basic tokentoken"}
            }
        }
        expected_ids = ["12", "13"]
        expected_space = "episode"
        expected_token = "tokentoken"

        resulting_ids, resulting_space, resulting_token = \
            LambdaEventInterpreter.interpret_uktv_delete_request_event(test_event)

        self.assertEqual(expected_token, resulting_token)
        self.assertEqual(expected_space, resulting_space)
        self.assertListEqual(expected_ids, resulting_ids)

    def test_interpret_items_request_no_space(self):
        test_event = {
            "body-json": [
                "12",
                "13_brand"
            ],
            "params": {
                "path": {},
                "querystring": {},
                "header": {"Authorization": "Basic tokentoken"}
            }
        }
        expected_ids = ["12", "13_brand"]
        expected_space = ""
        expected_token = "tokentoken"

        resulting_ids, resulting_space, resulting_token = \
            LambdaEventInterpreter.interpret_uktv_delete_request_event(test_event)

        self.assertEqual(expected_token, resulting_token)
        self.assertEqual(expected_space, resulting_space)
        self.assertListEqual(expected_ids, resulting_ids)

    def test_interpret_item_request_with_space(self):
        test_event = {
            "body-json": {},
            "params": {
                "path": {
                    "id": "12"
                },
                "querystring": {
                    "space": "brand"
                },
                "header": {"Authorization": "Basic tokentoken"}
            }
        }
        expected_ids = ["12_brand"]
        expected_space = "brand"
        expected_token = "tokentoken"

        resulting_ids, resulting_space, resulting_token = \
            LambdaEventInterpreter.interpret_uktv_delete_request_event(test_event)

        self.assertEqual(expected_token, resulting_token)
        self.assertEqual(expected_space, resulting_space)
        self.assertListEqual(expected_ids, resulting_ids)

    def test_interpret_item_request_no_space(self):
        test_event = {
            "body-json": {},
            "params": {
                "path": {
                    "id": "12"
                },
                "querystring": {},
                "header": {"Authorization": "Basic tokentoken"}
            }
        }
        expected_ids = ["12"]
        expected_space = ""
        expected_token = "tokentoken"

        resulting_ids, resulting_space, resulting_token = \
            LambdaEventInterpreter.interpret_uktv_delete_request_event(test_event)

        self.assertEqual(expected_token, resulting_token)
        self.assertEqual(expected_space, resulting_space)
        self.assertListEqual(expected_ids, resulting_ids)

    def test_user_history_request(self):
        test_event = {
            'body': {},
            'pathParameters': {'user_id': 'test_user'},
            'queryStringParameters': {'top': '100', 'skip': '10'},
            'headers': {'Authorization': 'Bearer tokentoken'}
        }
        expected_user_id = 'test_user'
        expected_top = 100
        expected_skip = 10

        resulting_user_id, resulting_top, resulting_skip = \
            LambdaEventInterpreter.interpret_user_history_request(test_event)
        self.assertEqual(expected_user_id, resulting_user_id)
        self.assertEqual(expected_top, resulting_top)
        self.assertEqual(expected_skip, resulting_skip)

    def test_user_history_request_no_top(self):
        test_event = {
            'body': {},
            'pathParameters': {'user_id': 'test_user'},
            'queryStringParameters': {'skip': '10'},
            'header': {'Authorization': 'Bearer tokentoken'}
        }
        expected_user_id = 'test_user'
        expected_top = 50
        expected_skip = 10

        resulting_user_id, resulting_top, resulting_skip = \
            LambdaEventInterpreter.interpret_user_history_request(test_event)
        self.assertEqual(expected_user_id, resulting_user_id)
        self.assertEqual(expected_top, resulting_top)
        self.assertEqual(expected_skip, resulting_skip)


if __name__ == "__main__":
    unittest.main()


@freezegun.freeze_time('2022-08-05T16:14:24+0000')
class Test(TestCase):
    def test_generate_personalisation_request_and_slot_override(self):
        event = {
            'body': {}, 'customer': 'UKTV',
            'pathParameters': {'slotId': '108d571f-8c52-4e9e-82ff-0fdc40e03ae6'},
            'queryStringParameters': {'ignore': 'r53', 'userId': 'health-check'}
        }
        now = datetime.now(timezone.utc)

        request, override_slot = \
            generate_personalisation_request_and_slot_override(event, now)

        expected_request = \
            Request(
                rule_id='108d571f-8c52-4e9e-82ff-0fdc40e03ae6', size=None, sort=None,
                sortBy=None, seedIds=[], chart_Id=None, subscription_type=None,
                message=None, userId='health-check', anonId=None, accountId=None,
                query_string={'ignore': 'r53', 'userId': 'health-check'},
                request_start=datetime(2022, 8, 5, 16, 14, 24, tzinfo=timezone.utc),
                debug_metadata=None, exclude=[], headers={}, editorials=None,
                allowed_items=None, stats=None, promotions=None
            )
        expected_override_slot = {}

        self.assertEqual(expected_request, request)
        self.assertEqual(expected_override_slot, override_slot)
