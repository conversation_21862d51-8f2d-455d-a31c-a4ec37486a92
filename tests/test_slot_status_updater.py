from _decimal import Decimal
from unittest import TestCase

from thefilter.behaviours.slot_status.slot_status_updater import SlotStatusUpdater
from thefilter.logs.logclient import NoOpLogger
from thefilter.model.chart import ChartDDB
from thefilter.repositories import StubSlotsRepository


class TestSlotStatusUpdater(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def test_update_slot_status_with_model_key(self):
        stub_slots = [{
            "customer": "test_customer",
            "slotId": "rfy_slot_id",
            "createdBy": "test",
            "dateCreated": "2022-05-31T13:25:18+01:00",
            "dateUpdated": "2022-05-31T13:25:18+01:00",
            "fallbackChart": "",
            "lastUpdatedBy": "test",
            "experiments": "[{\"id\": \"experiment_one\",\"userPercentage\": 100, \"isBaseRecipe\": true,\"size\": 30,\"title\": \"\",\"notes\": \"\",\"modelDefinitions\": [{\"key\": \"rfy_model\",\"version\": 1,\"source\": \"PrebuiltModel\",\"fulfilment\": {\"ranges\": [{\"start\": 0,\"end\": 30}]},\"parameters\": {}}]}]",
            "slotName": "Recommended For You",
            "type": "rfy"
        }]
        environment = "test_environment"
        region = "eu-west-2"
        customer = "test_customer"
        log_client = NoOpLogger()
        slots_repo = StubSlotsRepository(scan_results=stub_slots)
        slot_status_api = SlotStatusUpdater(
            environment, region, customer, slots_repo, log_client
        )

        model_key = "rfy_model"
        message = slot_status_api.update_slot_status(model_key=model_key)
        expected_message = """Model: rfy_model has new recs, and the associated slots: 
rfy_slot_id: Recommended For You with status None 
have been updated to a 'live' status"""
        self.assertEqual(expected_message, message)

    def test_update_slot_status_with_charts(self):
        charts = [
            ChartDDB(ddbId='latest-standard-standard',
                     created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['244943f0-4e47-11ee-b411-a3a5d460d7d1',
                            '246647d0-4e47-11ee-a6f6-1b61bdf25669',
                            '2473f850-4e47-11ee-9956-1991b3c1b3c5',
                            '246bbd40-4e47-11ee-b3d6-fb38e303a123',
                            '24416d50-4e47-11ee-a6cd-099dd1c67755',
                            '242258c0-4e47-11ee-91df-2f32ae69000b',
                            '2469c1b0-4e47-11ee-8b7b-55a56e97c329',
                            '4jBvA4ABEaM1lpDmADuk',
                            '24234970-4e47-11ee-9762-c79b692e1c52',
                            'ff6962f0-b99a-11ec-b65a-476cc689fe2b',
                            '16f79e00-b99b-11ec-86a4-215c93d0f893',
                            '244520e0-4e47-11ee-84c3-4feb91b0934a',
                            '24562360-4e47-11ee-9df5-f5cbe33eea8b',
                            '24445240-4e47-11ee-a8ba-077c3e663ef8',
                            '24472e90-4e47-11ee-b474-c3645563c7cf',
                            '24507a60-4e47-11ee-a8d3-15a96907bcd3',
                            'xjBvA4ABEaM1lpDmADuk',
                            '24274140-4e47-11ee-af9b-87ef1afe86d6',
                            '242d5620-4e47-11ee-aabf-efa70ab3dda9',
                            '2462c890-4e47-11ee-94f2-450dfbcc3dfb',
                            '2477f0b0-4e47-11ee-aa28-51f97d4ccc29',
                            '24289a20-4e47-11ee-a4c0-1dc95b2a267b',
                            '24504490-4e47-11ee-84ef-3f2ce3afd171',
                            '24653790-4e47-11ee-99da-17740f75e2cc',
                            '24349310-4e47-11ee-b707-0311d1f3f98c',
                            '242423a0-4e47-11ee-9cf8-afd7cae6eb2c',
                            '2480b640-4e47-11ee-bbb6-0d52794041eb',
                            '247ce050-4e47-11ee-ba32-c7395d5629bc',
                            '24263f20-4e47-11ee-b3ef-95dcbfe52e2b',
                            '24813b60-4e47-11ee-9869-75feefc7b961']),
            ChartDDB(ddbId='most-popular-7d-play', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['zzBvA4ABEaM1lpDmADuk',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d',
                            '-jDcA4ABEaM1lpDmQzvs']),
            ChartDDB(ddbId='most-popular-7d-user', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '-jDcA4ABEaM1lpDmQzvs',
                            'zzBvA4ABEaM1lpDmADuk',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d']),
            ChartDDB(ddbId='most-popular-14d-play', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['-jDcA4ABEaM1lpDmQzvs',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '242eca60-4e47-11ee-beba-1f3b356a944d',
                            'zzBvA4ABEaM1lpDmADuk',
                            '244291c0-4e47-11ee-a287-8146a971ec3f',
                            '2489a0f0-4e47-11ee-9e61-d78e60eec816',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '243c8dc0-4e47-11ee-941a-b1bdf1b6c387',
                            '6bf57d40-02af-11ed-b379-810cf0da27d3',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '2428d680-4e47-11ee-8a19-fbd68388fafa',
                            '242459e0-4e47-11ee-8e4c-5193d8894290']),
            ChartDDB(ddbId='most-popular-14d-user', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '242459e0-4e47-11ee-8e4c-5193d8894290',
                            '-jDcA4ABEaM1lpDmQzvs',
                            '2489a0f0-4e47-11ee-9e61-d78e60eec816',
                            'zzBvA4ABEaM1lpDmADuk',
                            '242eca60-4e47-11ee-beba-1f3b356a944d',
                            '244291c0-4e47-11ee-a287-8146a971ec3f',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '6bf57d40-02af-11ed-b379-810cf0da27d3',
                            '2428d680-4e47-11ee-8a19-fbd68388fafa',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '243c8dc0-4e47-11ee-941a-b1bdf1b6c387',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d']),
            ChartDDB(ddbId='most-popular-30d-play', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['244291c0-4e47-11ee-a287-8146a971ec3f',
                            '24331410-4e47-11ee-b4d0-f1a04e77452c',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d',
                            '24566920-4e47-11ee-a866-6d31ae7332cc',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '24356f40-4e47-11ee-82cc-d547e1b7fdb4',
                            '6bf57d40-02af-11ed-b379-810cf0da27d3',
                            '2428d680-4e47-11ee-8a19-fbd68388fafa',
                            '24462770-4e47-11ee-a79e-6f100213dce7',
                            '242024e0-4e47-11ee-ac8d-03208722f1a8',
                            '2447fef0-4e47-11ee-8c1d-cf090da5f346',
                            '243c8dc0-4e47-11ee-941a-b1bdf1b6c387',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '245e8090-4e47-11ee-9c63-c5e9fd87b69d',
                            '242423a0-4e47-11ee-9cf8-afd7cae6eb2c',
                            '24271d80-4e47-11ee-9a4d-2de5e4187651',
                            '244b7380-4e47-11ee-a719-675b73d74c71',
                            '246dafd0-4e47-11ee-9e13-7d36bc0a533f',
                            '24954110-4e47-11ee-afc7-cd5fe53a7f9a',
                            '-jDcA4ABEaM1lpDmQzvs',
                            '242459e0-4e47-11ee-8e4c-5193d8894290',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '247b9f50-4e47-11ee-b535-398d2125683c',
                            '24504490-4e47-11ee-84ef-3f2ce3afd171',
                            '2489a0f0-4e47-11ee-9e61-d78e60eec816',
                            'zzBvA4ABEaM1lpDmADuk',
                            '242eca60-4e47-11ee-beba-1f3b356a944d',
                            '24755ad0-4e47-11ee-b330-0b7fee2e6fd0']),
            ChartDDB(ddbId='most-popular-30d-user', created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['242423a0-4e47-11ee-9cf8-afd7cae6eb2c',
                            '24504490-4e47-11ee-84ef-3f2ce3afd171',
                            '24755ad0-4e47-11ee-b330-0b7fee2e6fd0',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '247b9f50-4e47-11ee-b535-398d2125683c',
                            '244291c0-4e47-11ee-a287-8146a971ec3f',
                            '2489a0f0-4e47-11ee-9e61-d78e60eec816',
                            'zzBvA4ABEaM1lpDmADuk',
                            '242eca60-4e47-11ee-beba-1f3b356a944d',
                            '-jDcA4ABEaM1lpDmQzvs',
                            '242459e0-4e47-11ee-8e4c-5193d8894290',
                            '24954110-4e47-11ee-afc7-cd5fe53a7f9a',
                            '24566920-4e47-11ee-a866-6d31ae7332cc',
                            '24356f40-4e47-11ee-82cc-d547e1b7fdb4',
                            '6bf57d40-02af-11ed-b379-810cf0da27d3',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            '2428d680-4e47-11ee-8a19-fbd68388fafa',
                            '2430a3f0-4e47-11ee-be8d-6d61bd554c1d',
                            '246dafd0-4e47-11ee-9e13-7d36bc0a533f',
                            '24271d80-4e47-11ee-9a4d-2de5e4187651',
                            '244b7380-4e47-11ee-a719-675b73d74c71',
                            '24462770-4e47-11ee-a79e-6f100213dce7',
                            '243c8dc0-4e47-11ee-941a-b1bdf1b6c387',
                            '245e8090-4e47-11ee-9c63-c5e9fd87b69d',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '2447fef0-4e47-11ee-8c1d-cf090da5f346',
                            '242024e0-4e47-11ee-ac8d-03208722f1a8',
                            '24331410-4e47-11ee-b4d0-f1a04e77452c']),
            ChartDDB(ddbId='latest-standard-popular',
                     created='2024-06-12T12:30:39+00:00',
                     timeToLiveEpoch=1719491439,
                     chart=['2430a3f0-4e47-11ee-be8d-6d61bd554c1d',
                            '244f29a0-4e47-11ee-9d33-e162ff228ec4',
                            'zzBvA4ABEaM1lpDmADuk',
                            '244ebea0-4e47-11ee-96e4-e19a6c192d01',
                            '242379e0-4e47-11ee-a2a1-e72b7d35b02c',
                            '-jDcA4ABEaM1lpDmQzvs']),
            ChartDDB(ddbId='trending-daily-standard-0',
                     created='2024-06-12T12:32:09+00:00',
                     timeToLiveEpoch=1719491501,
                     chart=['UDr8CoUB0C7rOzdg_lzv',
                            '24717da0-4e47-11ee-8daa-1da58d3bc712',
                            '24263f20-4e47-11ee-b3ef-95dcbfe52e2b',
                            '24685cc0-4e47-11ee-9776-1961f6d555a9',
                            '242c9ad0-4e47-11ee-882d-258faab7db31',
                            '24123c20-4e47-11ee-9f91-e13cd059b513',
                            '2479ca90-4e47-11ee-b12f-a7bdcd9f6adb',
                            '2471f6a0-4e47-11ee-b1e0-0350e6cf2a99',
                            '2433b2e0-4e47-11ee-b26b-a1ecacdf26a4',
                            '24811a70-4e47-11ee-8b11-43aad7d8fdad',
                            '24813b60-4e47-11ee-9869-75feefc7b961',
                            'b50d0a00-b98a-11ec-9ea3-69128a920574']),
            ChartDDB(ddbId='trending-daily-standard-1',
                     created='2024-06-12T12:32:37+00:00',
                     timeToLiveEpoch=1719491501,
                     chart=['24123c20-4e47-11ee-9f91-e13cd059b513',
                            '2479ca90-4e47-11ee-b12f-a7bdcd9f6adb',
                            '2471f6a0-4e47-11ee-b1e0-0350e6cf2a99',
                            '2433b2e0-4e47-11ee-b26b-a1ecacdf26a4',
                            '24811a70-4e47-11ee-8b11-43aad7d8fdad',
                            '24813b60-4e47-11ee-9869-75feefc7b961',
                            'b50d0a00-b98a-11ec-9ea3-69128a920574',
                            '2468b910-4e47-11ee-bed2-c7464e199fb4',
                            '24816180-4e47-11ee-860f-01c39fde29fd',
                            'e41aea30-bbe7-11ec-a3ce-579ff8c97e29',
                            '9831e600-b9ad-11ec-aa0c-7f1265797b19',
                            '247c72b0-4e47-11ee-9b82-cb90d51b74a5'])
        ]
        stub_slots = [
            {'lastUpdatedBy': 'pre-tcwp-cognito',
             'dateCreated': '2024-03-25T11:35:39+00:00', 'fallbackChart': '',
             'responseSize': Decimal('25'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': '064bbfeb-d46d-4731-8569-aff8c1981293',
             'slotName': 'Trending Movies',
             'slotDefinition': '{"id": "064bbfeb-d46d-4731-8569-aff8c1981293", "name": "Trending Movies", "type": "trending", "description": "Trending Movies", "supportsLowLatency": false, "experiments": [{"id": "f78ee9ec-00be-417c-b678-c3c8400dbe2c", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending", "notes": "Data-Ops RealTime Trending Movies", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "movies", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}], "zeroResultsOk": false}',
             'experiments': '[{"id": "f78ee9ec-00be-417c-b678-c3c8400dbe2c", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending", "notes": "Data-Ops RealTime Trending Movies", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "movies", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}]',
             'isDefault': False, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Trending Movies',
             'dateUpdated': '2024-03-25T11:35:39+00:00', 'type': 'trending'},
            {'lastUpdatedBy': 'slot_refactor_script',
             'dateCreated': '2021-07-02T11:04:24+00:00', 'fallbackChart': 'all',
             'responseSize': Decimal('6'), 'createdBy': 'auto_ES_to_DDB',
             'slotId': '108d571f-8c52-4e9e-82ff-0fdc40e03ae6', 'slotName': 'Fallback',
             'slotDefinition': '{"id": "108d571f-8c52-4e9e-82ff-0fdc40e03ae6", "name": "Fallback", "description": "Fallback", "experiments": [{"id": "3fb98e4a-524b-4c3d-884c-bd3f0b35b793", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Fallback", "notes": "", "modelDefinitions": [{"key": "StubModel", "version": 1, "source": "StubModel", "fulfilment": {"ranges": [{"start": 0, "end": 5}]}}]}]}',
             'experiments': '[{"id": "3fb98e4a-524b-4c3d-884c-bd3f0b35b793", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Fallback", "notes": "", "modelDefinitions": [{"key": "StubModel", "version": 1, "source": "StubModel", "fulfilment": {"ranges": [{"start": 0, "end": 5}]}}]}]',
             'customer': 'epix', 'zeroResultsOk': False, 'description': 'Fallback',
             'dateUpdated': '2023-11-23T11:30:19+00:00', 'type': 'fallback'},
            {'lastUpdatedBy': 'pre-tcwp-cognito',
             'dateCreated': '2023-11-23T11:43:48+00:00', 'fallbackChart': '',
             'responseSize': Decimal('1'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': '185a3bc9-55cb-4b88-a7b4-9643c2a64f26',
             'slotName': 'AutoPlay MLT',
             'slotDefinition': '{"id": "185a3bc9-55cb-4b88-a7b4-9643c2a64f26", "name": "AutoPlay MLT", "type": "mlt", "description": "AutoPlay MLT (Not currently used on Epix)", "supportsLowLatency": false, "experiments": [{"id": "37f69ff7-c80e-472e-8870-d496fea6f2f0", "userPercentage": 100, "isBaseRecipe": true, "size": 1, "title": "AutoPlay MLT Model", "notes": "PrebuiltModel mlt_20230306", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "AutoPlayMLTModel", "fulfilment": {"ranges": [{"start": 0, "end": 1}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "returnType": "brand"}}]}]}',
             'experiments': '[{"id": "37f69ff7-c80e-472e-8870-d496fea6f2f0", "userPercentage": 100, "isBaseRecipe": true, "size": 1, "title": "AutoPlay MLT Model", "notes": "PrebuiltModel mlt_20230306", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "AutoPlayMLTModel", "fulfilment": {"ranges": [{"start": 0, "end": 1}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "returnType": "brand"}}]}]',
             'customer': 'epix', 'zeroResultsOk': False,
             'description': 'AutoPlay MLT (Not currently used on Epix)', 'type': 'mlt',
             'dateUpdated': '2023-11-23T11:43:48+00:00'},
            {'lastUpdatedBy': 'slot_refactor_script',
             'dateCreated': '2022-09-16T16:51:17+01:00', 'fallbackChart': '',
             'responseSize': Decimal('30'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': '1a002ee2-4aba-411a-9a76-b605353b6747',
             'slotName': 'Editorial Lane',
             'slotDefinition': '{"id": "1a002ee2-4aba-411a-9a76-b605353b6747", "name": "Editorial Lane", "type": "editorial", "description": "", "supportsLowLatency": false, "experiments": [{"id": "fce93681-3056-4ade-9ac6-ca2ea384cd19", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "EntityModel", "version": 1, "source": "EntityModel", "fulfilment": {"ranges": [{"start": 0, "end": 30}]}, "parameters": {"userHistoryDays": 90, "entityType": "group", "entityName": "EditorialLaneEpix", "sortMethod": "recencyPopularity", "watchedAtEnd": true}}]}]}',
             'experiments': '[{"id": "fce93681-3056-4ade-9ac6-ca2ea384cd19", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "EntityModel", "version": 1, "source": "EntityModel", "fulfilment": {"ranges": [{"start": 0, "end": 30}]}, "parameters": {"userHistoryDays": 90, "entityType": "group", "entityName": "EditorialLaneEpix", "sortMethod": "recencyPopularity", "watchedAtEnd": true}}]}]',
             'customer': 'epix', 'zeroResultsOk': False, 'description': 'Editorial Lane',
             'dateUpdated': '2023-11-23T11:30:19+00:00', 'type': 'entity'},
            {'lastUpdatedBy': 'pre-adamp-cognito',
             'dateCreated': '2024-03-26T16:15:45+00:00', 'fallbackChart': '',
             'responseSize': Decimal('6'), 'createdBy': 'pre-adamp-cognito',
             'slotId': '2c0d0849-e1ca-4737-a565-f58b240e2z1a',
             'slotName': 'Similar To X',
             'slotDefinition': '{"id": "2c0d0849-e1ca-4737-a565-f58b240e2z1a", "name": "Similar To X", "type": "similar", "description": "Titles that are similar to a designated piece of content", "supportsLowLatency": false, "experiments": [{"id": "2c0d0849-e1ca-4737-a565-f58b240e2z1af", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Similar To", "notes": "Autogenerated Similar To", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "PrebuiltModel", "fulfilment": {"ranges": [{"start": 0, "end": 6}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "returnType": "brand", "defaultVariant": "standard", "defaultOverlay": "standard", "titlePlaceholder": "Similar To {seed_name}"}}]}], "zeroResultsOk": false}',
             'experiments': '[{"id": "2c0d0849-e1ca-4737-a565-f58b240e2z1af", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Similar To", "notes": "Autogenerated Similar To", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "PrebuiltModel", "fulfilment": {"ranges": [{"start": 0, "end": 6}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "returnType": "brand", "defaultVariant": "standard", "defaultOverlay": "standard", "titlePlaceholder": "Similar To {seed_name}"}}]}]',
             'isDefault': True, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Titles that are similar to a designated piece of content',
             'type': 'similar', 'dateUpdated': '2024-03-26T16:15:45+00:00'},
            {'lastUpdatedBy': 'pre-tcwp-cognito',
             'dateCreated': '2024-03-25T11:35:55+00:00', 'fallbackChart': '',
             'responseSize': Decimal('25'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': '40659eb1-6a2c-43c0-8b9e-25fbd1a55317',
             'slotName': 'Trending TV Series',
             'slotDefinition': '{"id": "40659eb1-6a2c-43c0-8b9e-25fbd1a55317", "name": "Trending TV Series", "type": "trending", "description": "Trending TV Series", "supportsLowLatency": false, "experiments": [{"id": "dcbbb8b0-e520-41f8-a65d-1064048262a8", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending", "notes": "Data-Ops RealTime Trending Series", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "series", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}], "zeroResultsOk": false}',
             'experiments': '[{"id": "dcbbb8b0-e520-41f8-a65d-1064048262a8", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending", "notes": "Data-Ops RealTime Trending Series", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "series", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}]',
             'isDefault': False, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Trending TV Series',
             'dateUpdated': '2024-03-25T11:35:55+00:00', 'type': 'trending'},
            {'lastUpdatedBy': 'pre-adamp-cognito',
             'dateCreated': '2024-01-31T17:12:54+00:00', 'fallbackChart': '',
             'responseSize': Decimal('6'), 'createdBy': 'pre-adamp-cognito',
             'slotId': '63740562-701f-4391-87da-baf604c28a31',
             'slotName': 'Because You Watched',
             'slotDefinition': '{"id": "63740562-701f-4391-87da-baf604c28a31", "name": "Because You Watched", "type": "byw", "description": "Uses a user\'s history to provide an MLT from a recently watched item", "supportsLowLatency": false, "experiments": [{"id": "31cea70e-11f7-43ca-83d1-8aeae50a63d2", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Because You Watched", "notes": "BYW", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "PrebuiltBYWModel", "fulfilment": {"ranges": [{"start": 0, "end": 5}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "excludeGenre": "erotic", "titlePlaceholder": "Because You Watched {seed_name}", "bywMethod": "seedSelection", "skipZeroResult": true}}]}], "zeroResultsOk": false}',
             'experiments': '[{"id": "31cea70e-11f7-43ca-83d1-8aeae50a63d2", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Because You Watched", "notes": "BYW", "modelDefinitions": [{"key": "mlt_20230306", "version": 1, "source": "PrebuiltBYWModel", "fulfilment": {"ranges": [{"start": 0, "end": 5}]}, "parameters": {"useSeedId": true, "deduplicate_path": "", "excludeGenre": "erotic", "titlePlaceholder": "Because You Watched {seed_name}", "bywMethod": "seedSelection", "skipZeroResult": true}}]}]',
             'isDefault': False, 'customer': 'epix', 'zeroResultsOk': False,
             'description': "Uses a user's history to provide an MLT from a recently watched item",
             'dateUpdated': '2024-01-31T17:12:54+00:00', 'type': 'byw'},
            {'lastUpdatedBy': 'slot_refactor_script',
             'dateCreated': '2023-10-12T13:07:20+01:00', 'fallbackChart': '',
             'responseSize': Decimal('6'), 'createdBy': 'pre-adamp-cognito',
             'slotId': '6e0a51f3-5651-4c1c-a2c3-8b6479e64a24',
             'slotDefinition': '{"id": "6e0a51f3-5651-4c1c-a2c3-8b6479e64a24", "name": "Email Recommended For You", "type": "rfy", "description": "", "supportsLowLatency": true, "experiments": [{"id": "04ba5229-f2f1-42b5-b839-e81a22e6f942", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Email Recommended For You", "notes": "", "modelDefinitions": [{"key": "rfy_email_20220105", "source": "PrebuiltModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 5}]}, "parameters": {"deduplicate_path": ""}}, {"key": "fallback", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular-fallback"}}]}]}',
             'slotName': 'Email Recommended For You',
             'experiments': '[{"id": "04ba5229-f2f1-42b5-b839-e81a22e6f942", "userPercentage": 100, "isBaseRecipe": true, "size": 6, "title": "Email Recommended For You", "notes": "", "modelDefinitions": [{"key": "rfy_email_20220105", "source": "PrebuiltModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 5}]}, "parameters": {"deduplicate_path": ""}}, {"key": "fallback", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular-fallback"}}]}]',
             'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Email Recommended For You',
             'dateUpdated': '2023-11-23T11:30:19+00:00', 'type': 'rfy'},
            {'lastUpdatedBy': 'slot_refactor_script',
             'dateCreated': '2022-10-05T17:31:50+01:00', 'fallbackChart': '',
             'responseSize': Decimal('50'), 'createdBy': 'pre-adamp-cognito',
             'slotId': '76dd5146-c45c-464d-92dd-2c9fbfb1ee5d', 'slotName': 'Search',
             'slotDefinition': '{"id": "76dd5146-c45c-464d-92dd-2c9fbfb1ee5d", "name": "Search", "type": "search", "description": "Epix search configuration", "supportsLowLatency": false, "experiments": [{"id": "cf6c1941-ccc8-4dea-88dc-0674e9124b59", "userPercentage": 100, "isBaseRecipe": true, "size": 50, "title": "Search", "notes": "Search With Entities", "modelDefinitions": [{"key": "search", "version": 1, "source": "SearchModel", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"search": {"settings": {"translations": {"filters.rating": {"MPAA G": 10, "TV TV-G": 10, "G": 10, "MPAA PG": 20, "TV TV-PG": 20, "MPAA NR": 20, "PG": 20, "MPAA PG-13": 30, "TV TV-14": 30, "PG-13": 30, "MPAA R": 40, "TV TV-MA": 40, "R": 40, "MPAA NC-17": 50, "NC-17": 50, "MPAA UR": 60, "NR": 60}}}, "mlt": {"userHistoryDepth": 10, "userHistoryMaxBoost": 1, "fields": ["genre.searchable", "enrichedGenre.searchable"], "includeFromUserHistory": true}, "perFieldQueries": [{"description": "100,000 : Full, 100% field value matches! If you literally type in the exact name of something, who are we to argue?", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 100000, "queries": [{"constant_score": {"filter": {"term": {"name.lowercase_keyword": {"value": "{q}"}}}, "boost": 1.2}}, {"constant_score": {"filter": {"term": {"alternateName.lowercase_keyword": {"value": "{q}"}}}, "boost": 0.8}}, {"constant_score": {"filter": {"term": {"actor.lowercase_keyword": "{q}"}}, "boost": 0.7}}, {"constant_score": {"filter": {"term": {"director.lowercase_keyword": "{q}"}}, "boost": 0.5}}, {"constant_score": {"filter": {"term": {"people.lowercase_keyword": "{q}"}}, "boost": 0.5}}, {"constant_score": {"filter": {"term": {"franchise.lowercase_keyword": "{q}"}}, "boost": 1}}, {"constant_score": {"filter": {"term": {"tags.lowercase_keyword": "{q}"}}, "boost": 0.2}}, {"constant_score": {"filter": {"term": {"moods.lowercase_keyword": "{q}"}}, "boost": 0.1}}]}}}, {"description": "10,000 : Full, 100% field value match for Crew", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10000, "queries": [{"constant_score": {"filter": {"term": {"crew.lowercase_keyword": {"value": "{q}"}}}, "boost": 1}}]}}}, {"description": "10,000 : Full term matches! If any of the terms in your query match ", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10000, "queries": [{"constant_score": {"filter": {"match": {"name.searchable": {"query": "{q}"}}}, "boost": 3}}, {"constant_score": {"filter": {"match": {"alternateName.searchable": {"query": "{q}"}}}, "boost": 0.95}}, {"constant_score": {"filter": {"match": {"genre.searchable": {"query": "{q}"}}}, "boost": 1.2}}, {"constant_score": {"filter": {"match": {"franchise.searchable": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match": {"tags.searchable": {"query": "{q}"}}}, "boost": 0.01}}, {"constant_score": {"filter": {"match": {"moods.searchable": {"query": "{q}"}}}, "boost": 0.001}}, {"constant_score": {"filter": {"match": {"people.searchable": {"query": "{q}"}}}, "boost": 0.4}}]}}}, {"description": "1000: Full term matches based on the number of matching terms. Reorders items so that multi-term matches get a little extra somethin", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 1000, "queries": [{"match": {"name.searchable": {"query": "{q}"}}}, {"match": {"alternateName.searchable": {"query": "{q}"}}}, {"match": {"franchise.searchable": {"query": "{q}"}}}, {"match": {"tags.searchable": {"query": "{q}", "boost": 0.01}}}, {"match": {"moods.searchable": {"query": "{q}"}}}]}}}, {"description": "100 : Partial full matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 500, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"constant_score": {"filter": {"match_phrase_prefix": {"name.lowercase_keyword": {"query": "{q}"}}}, "boost": 1.5}}, {"constant_score": {"filter": {"match_phrase_prefix": {"alternateName.lowercase_keyword": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match_phrase_prefix": {"franchise.lowercase_keyword": {"query": "{q}"}}}, "boost": 1.05}}, {"constant_score": {"filter": {"match_phrase_prefix": {"moods.lowercase_keyword": {"query": "{q}"}}}, "boost": 0.01}}]}}]}}}, {"description": "10 : Partial full matches, lessened score but with a possibility to match multiple times.", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"match_phrase_prefix": {"name.lowercase_keyword": {"query": "{q}", "boost": 2}}}, {"match_phrase_prefix": {"alternateName.lowercase_keyword": {"query": "{q}"}}}, {"match_phrase_prefix": {"franchise.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"tags.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"moods.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"people.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}]}}]}}}, {"description": "25 : Fuzzy full matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 25, "queries": [{"constant_score": {"filter": {"match": {"name.lowercase_keyword": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 5}}, {"constant_score": {"filter": {"match": {"franchise.lowercase_keyword": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 1.05}}]}}}, {"description": "50 : Partial term matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 100, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"constant_score": {"filter": {"match_phrase_prefix": {"name.searchable": {"query": "{q}"}}}, "boost": 1000}}, {"constant_score": {"filter": {"match_phrase_prefix": {"alternateName.searchable": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match_phrase_prefix": {"franchise.searchable": {"query": "{q}"}}}, "boost": 1.05}}, {"constant_score": {"filter": {"match_phrase_prefix": {"tags.searchable": {"query": "{q}"}}}, "boost": 0.01}}, {"constant_score": {"filter": {"match_phrase_prefix": {"moods.searchable": {"query": "{q}"}}}, "boost": 0.01}}]}}]}}}, {"description": "10 : Partial term matches based on closeness of match", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"match_phrase_prefix": {"name.searchable": {"query": "{q}", "boost": 100}}}, {"match_phrase_prefix": {"alternateName.searchable": {"query": "{q}"}}}, {"match_phrase_prefix": {"franchise.searchable": {"query": "{q}"}}}, {"match_phrase_prefix": {"tags.searchable": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"moods.searchable": {"query": "{q}"}}}]}}]}}}, {"description": "20 : Fuzzy term matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 25, "queries": [{"constant_score": {"filter": {"match": {"name.searchable": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 100}}}, "boost": 1000}}, {"constant_score": {"filter": {"match": {"franchise.searchable": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 1.05}}]}}}], "rescorers": [{"description": "Boost items with a box office score", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.boxOffice": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.boxOffice", "factor": 10}}]}}}, "window_size": 500}}, {"description": "Boost items with a newness score", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.newness": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.newness", "factor": 30000}}]}}}, "window_size": 2000}}, {"description": "100 : Boost items by normalised popularity score, but don\'t let that override term matches!", "rescore": {"query": {"query_weight": 1, "rescore_query_weight": 1, "rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.popularity": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.popularity", "factor": 300}}]}}}, "window_size": 1000}}, {"description": "Push search results in the vault genre down to the bottom of the result set.", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"term": {"genre.lowercase_keyword": {"value": "vault"}}}]}}, "functions": [{"weight": -500000}]}}}, "window_size": 5000}}], "filters": [{"description": "If active state is false, don\'t return it. Pretty clear cut.", "query": {"bool": {"must_not": [{"term": {"thing.custom.active.state": false}}]}}}, {"description": "Don\'t return Episodes", "query": {"bool": {"must_not": [{"term": {"typeName": "episode"}}]}}}, {"description": "Filter category when category is provided on the query string.", "parameters": {"required": [{"key": "category"}]}, "query": {"bool": {"must": [{"term": {"filters.category.lowercase_keyword": "{category}"}}]}}}, {"description": "Set lower bound for releaseYear when releaseYearMin is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMin"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"gte": "{releaseYearMin}"}}}]}}}, {"description": "Set upper bound for releaseYear when releaseYearMax is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMax"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"lte": "{releaseYearMax}"}}}]}}}, {"description": "Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMax", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"lte": "{ratingMax}"}}}]}}}, {"description": "Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMin", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"gte": "{ratingMin}"}}}]}}}]}}}]}]}',
             'experiments': '[{"id": "cf6c1941-ccc8-4dea-88dc-0674e9124b59", "userPercentage": 100, "isBaseRecipe": true, "size": 50, "title": "Search", "notes": "Search With Entities", "modelDefinitions": [{"key": "search", "version": 1, "source": "SearchModel", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"search": {"settings": {"translations": {"filters.rating": {"MPAA G": 10, "TV TV-G": 10, "G": 10, "MPAA PG": 20, "TV TV-PG": 20, "MPAA NR": 20, "PG": 20, "MPAA PG-13": 30, "TV TV-14": 30, "PG-13": 30, "MPAA R": 40, "TV TV-MA": 40, "R": 40, "MPAA NC-17": 50, "NC-17": 50, "MPAA UR": 60, "NR": 60}}}, "mlt": {"userHistoryDepth": 10, "userHistoryMaxBoost": 1, "fields": ["genre.searchable", "enrichedGenre.searchable"], "includeFromUserHistory": true}, "perFieldQueries": [{"description": "100,000 : Full, 100% field value matches! If you literally type in the exact name of something, who are we to argue?", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 100000, "queries": [{"constant_score": {"filter": {"term": {"name.lowercase_keyword": {"value": "{q}"}}}, "boost": 1.2}}, {"constant_score": {"filter": {"term": {"alternateName.lowercase_keyword": {"value": "{q}"}}}, "boost": 0.8}}, {"constant_score": {"filter": {"term": {"actor.lowercase_keyword": "{q}"}}, "boost": 0.7}}, {"constant_score": {"filter": {"term": {"director.lowercase_keyword": "{q}"}}, "boost": 0.5}}, {"constant_score": {"filter": {"term": {"people.lowercase_keyword": "{q}"}}, "boost": 0.5}}, {"constant_score": {"filter": {"term": {"franchise.lowercase_keyword": "{q}"}}, "boost": 1}}, {"constant_score": {"filter": {"term": {"tags.lowercase_keyword": "{q}"}}, "boost": 0.2}}, {"constant_score": {"filter": {"term": {"moods.lowercase_keyword": "{q}"}}, "boost": 0.1}}]}}}, {"description": "10,000 : Full, 100% field value match for Crew", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10000, "queries": [{"constant_score": {"filter": {"term": {"crew.lowercase_keyword": {"value": "{q}"}}}, "boost": 1}}]}}}, {"description": "10,000 : Full term matches! If any of the terms in your query match ", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10000, "queries": [{"constant_score": {"filter": {"match": {"name.searchable": {"query": "{q}"}}}, "boost": 3}}, {"constant_score": {"filter": {"match": {"alternateName.searchable": {"query": "{q}"}}}, "boost": 0.95}}, {"constant_score": {"filter": {"match": {"genre.searchable": {"query": "{q}"}}}, "boost": 1.2}}, {"constant_score": {"filter": {"match": {"franchise.searchable": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match": {"tags.searchable": {"query": "{q}"}}}, "boost": 0.01}}, {"constant_score": {"filter": {"match": {"moods.searchable": {"query": "{q}"}}}, "boost": 0.001}}, {"constant_score": {"filter": {"match": {"people.searchable": {"query": "{q}"}}}, "boost": 0.4}}]}}}, {"description": "1000: Full term matches based on the number of matching terms. Reorders items so that multi-term matches get a little extra somethin", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 1000, "queries": [{"match": {"name.searchable": {"query": "{q}"}}}, {"match": {"alternateName.searchable": {"query": "{q}"}}}, {"match": {"franchise.searchable": {"query": "{q}"}}}, {"match": {"tags.searchable": {"query": "{q}", "boost": 0.01}}}, {"match": {"moods.searchable": {"query": "{q}"}}}]}}}, {"description": "100 : Partial full matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 500, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"constant_score": {"filter": {"match_phrase_prefix": {"name.lowercase_keyword": {"query": "{q}"}}}, "boost": 1.5}}, {"constant_score": {"filter": {"match_phrase_prefix": {"alternateName.lowercase_keyword": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match_phrase_prefix": {"franchise.lowercase_keyword": {"query": "{q}"}}}, "boost": 1.05}}, {"constant_score": {"filter": {"match_phrase_prefix": {"moods.lowercase_keyword": {"query": "{q}"}}}, "boost": 0.01}}]}}]}}}, {"description": "10 : Partial full matches, lessened score but with a possibility to match multiple times.", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"match_phrase_prefix": {"name.lowercase_keyword": {"query": "{q}", "boost": 2}}}, {"match_phrase_prefix": {"alternateName.lowercase_keyword": {"query": "{q}"}}}, {"match_phrase_prefix": {"franchise.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"tags.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"moods.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"people.lowercase_keyword": {"query": "{q}", "boost": 0.1}}}]}}]}}}, {"description": "25 : Fuzzy full matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 25, "queries": [{"constant_score": {"filter": {"match": {"name.lowercase_keyword": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 5}}, {"constant_score": {"filter": {"match": {"franchise.lowercase_keyword": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 1.05}}]}}}, {"description": "50 : Partial term matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 100, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"constant_score": {"filter": {"match_phrase_prefix": {"name.searchable": {"query": "{q}"}}}, "boost": 1000}}, {"constant_score": {"filter": {"match_phrase_prefix": {"alternateName.searchable": {"query": "{q}"}}}, "boost": 1}}, {"constant_score": {"filter": {"match_phrase_prefix": {"franchise.searchable": {"query": "{q}"}}}, "boost": 1.05}}, {"constant_score": {"filter": {"match_phrase_prefix": {"tags.searchable": {"query": "{q}"}}}, "boost": 0.01}}, {"constant_score": {"filter": {"match_phrase_prefix": {"moods.searchable": {"query": "{q}"}}}, "boost": 0.01}}]}}]}}}, {"description": "10 : Partial term matches based on closeness of match", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 10, "queries": [{"dis_max": {"tie_breaker": 0.7, "boost": 1, "queries": [{"match_phrase_prefix": {"name.searchable": {"query": "{q}", "boost": 100}}}, {"match_phrase_prefix": {"alternateName.searchable": {"query": "{q}"}}}, {"match_phrase_prefix": {"franchise.searchable": {"query": "{q}"}}}, {"match_phrase_prefix": {"tags.searchable": {"query": "{q}", "boost": 0.1}}}, {"match_phrase_prefix": {"moods.searchable": {"query": "{q}"}}}]}}]}}}, {"description": "20 : Fuzzy term matches!", "query": {"dis_max": {"tie_breaker": 0.7, "boost": 25, "queries": [{"constant_score": {"filter": {"match": {"name.searchable": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 100}}}, "boost": 1000}}, {"constant_score": {"filter": {"match": {"franchise.searchable": {"query": "{q}", "fuzziness": 1, "fuzzy_transpositions": "true", "boost": 1}}}, "boost": 1.05}}]}}}], "rescorers": [{"description": "Boost items with a box office score", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.boxOffice": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.boxOffice", "factor": 10}}]}}}, "window_size": 500}}, {"description": "Boost items with a newness score", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.newness": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.newness", "factor": 30000}}]}}}, "window_size": 2000}}, {"description": "100 : Boost items by normalised popularity score, but don\'t let that override term matches!", "rescore": {"query": {"query_weight": 1, "rescore_query_weight": 1, "rescore_query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.popularity": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.popularity", "factor": 300}}]}}}, "window_size": 1000}}, {"description": "Push search results in the vault genre down to the bottom of the result set.", "rescore": {"query": {"rescore_query": {"function_score": {"query": {"bool": {"must": [{"term": {"genre.lowercase_keyword": {"value": "vault"}}}]}}, "functions": [{"weight": -500000}]}}}, "window_size": 5000}}], "filters": [{"description": "If active state is false, don\'t return it. Pretty clear cut.", "query": {"bool": {"must_not": [{"term": {"thing.custom.active.state": false}}]}}}, {"description": "Don\'t return Episodes", "query": {"bool": {"must_not": [{"term": {"typeName": "episode"}}]}}}, {"description": "Filter category when category is provided on the query string.", "parameters": {"required": [{"key": "category"}]}, "query": {"bool": {"must": [{"term": {"filters.category.lowercase_keyword": "{category}"}}]}}}, {"description": "Set lower bound for releaseYear when releaseYearMin is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMin"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"gte": "{releaseYearMin}"}}}]}}}, {"description": "Set upper bound for releaseYear when releaseYearMax is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMax"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"lte": "{releaseYearMax}"}}}]}}}, {"description": "Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMax", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"lte": "{ratingMax}"}}}]}}}, {"description": "Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMin", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"gte": "{ratingMin}"}}}]}}}]}}}]}]',
             'customer': 'epix', 'zeroResultsOk': True,
             'description': 'Epix search configuration',
             'dateUpdated': '2023-11-23T11:30:19+00:00', 'type': 'search'},
            {'lastUpdatedBy': 'dev-thomas-perryman',
             'dateCreated': '2024-05-10T11:39:13+01:00', 'fallbackChart': '',
             'responseSize': Decimal('6'), 'createdBy': 'dev-thomas-perryman',
             'slotId': '85d5d30d-6eb3-4898-a372-572e8a9114c4',
             'slotName': 'Continue Watching',
             'experiments': '[\n  {\n    "id": "231bdd35-8636-49c4-a151-7f766a8527ce",\n    "userPercentage": 100,\n    "isBaseRecipe": true,\n    "size": 25,\n    "title": "Continue Watching",\n    "notes": "Continue Watching",\n    "modelDefinitions": [\n      {\n        "key": "continue_watching-{variant}",\n        "source": "ContinueWatchingModel",\n        "version": 1,\n        "fulfilment": {\n          "ranges": [\n            {\n              "start": 0,\n              "end": 25\n            }\n          ]\n        },\n        "parameters": {\n          "defaultVariant": "standard",\n          "defaultOverlay": "standard"\n        }\n      }\n    ]\n  }\n]',
             'isDefault': False, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Continue Watching', 'type': 'cw',
             'dateUpdated': '2024-05-10T11:39:13+01:00'},
            {'lastUpdatedBy': 'pre-tcwp-cognito',
             'dateCreated': '2023-11-23T11:42:13+00:00', 'fallbackChart': '',
             'responseSize': Decimal('30'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': '882caad6-f7a0-4fda-aabc-d7a693980b6b',
             'slotName': 'Most Popular',
             'slotDefinition': '{"id": "882caad6-f7a0-4fda-aabc-d7a693980b6b", "name": "Most Popular", "type": "most_popular", "description": "Most Popular (not actually used by Epix)", "supportsLowLatency": false, "experiments": [{"id": "f154414c-5b35-4f05-a195-b186835ec794", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "epix_latest_all", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular"}}]}]}',
             'experiments': '[{"id": "f154414c-5b35-4f05-a195-b186835ec794", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "epix_latest_all", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular"}}]}]',
             'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Most Popular (not actually used by Epix)',
             'dateUpdated': '2023-11-23T11:42:13+00:00', 'type': 'most_popular'},
            {'lastUpdatedBy': 'slot_refactor_script',
             'dateCreated': '2023-11-01T16:53:38+00:00', 'fallbackChart': '',
             'responseSize': Decimal('50'), 'createdBy': 'dev-adam-poolman',
             'slotId': '9a684a8e-1942-4485-b36c-27917e0760d5', 'slotName': 'HERO',
             'slotDefinition': '{"id": "9a684a8e-1942-4485-b36c-27917e0760d5", "name": "HERO", "type": "hero", "description": "For testing hero banner model.", "supportsLowLatency": false, "experiments": [{"id": "16efcb0f-2486-40c7-845f-19d5f38511eb", "userPercentage": 100, "isBaseRecipe": true, "size": 50, "title": "HERO BANNER", "notes": "Hero banner", "modelDefinitions": [{"key": "hero-banner-model", "version": 1, "source": "HeroStubModel", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}}]}]}',
             'experiments': '[{"id": "16efcb0f-2486-40c7-845f-19d5f38511eb", "userPercentage": 100, "isBaseRecipe": true, "size": 50, "title": "HERO BANNER", "notes": "Hero banner", "modelDefinitions": [{"key": "hero-banner-model", "version": 1, "source": "HeroStubModel", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}}]}]',
             'customer': 'epix', 'zeroResultsOk': False,
             'description': 'For testing hero banner model.',
             'dateUpdated': '2023-11-09T16:46:08+00:00', 'type': 'hero'},
            {'lastUpdatedBy': 'dev-adam-poolman',
             'dateCreated': '2024-04-22T12:45:07+01:00', 'fallbackChart': '',
             'responseSize': Decimal('6'), 'createdBy': 'dev-adam-poolman',
             'slotId': 'a0182e54-72ee-4180-8a02-8b4289a64a04',
             'slotName': 'More Like This',
             'experiments': '[\n  {\n    "id": "a0182e54-72ee-4180-8a02-8b4289a64a04",\n    "userPercentage": 100,\n    "isBaseRecipe": true,\n    "size": 12,\n    "title": "More Like This",\n    "notes": "",\n    "modelDefinitions": [\n      {\n        "key": "mlt_20230306",\n        "version": 1,\n        "source": "PrebuiltModel",\n        "fulfilment": {\n          "ranges": [\n            {\n              "start": 0,\n              "end": 5\n            }\n          ]\n        },\n        "parameters": {\n          "useSeedId": true,\n          "deduplicate_path": ""\n        }\n      }\n    ]\n  }\n]',
             'isDefault': True, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'More Like This', 'dateUpdated': '2024-04-22T12:45:07+01:00',
             'type': 'mlt'}, {'lastUpdatedBy': 'pre-tcwp-cognito',
                              'dateCreated': '2024-03-25T09:44:43+00:00',
                              'fallbackChart': '', 'responseSize': Decimal('250'),
                              'createdBy': 'pre-tcwp-cognito',
                              'slotId': 'bdf396f0-f9b5-42f1-bacc-8aa1cb292537',
                              'slotName': 'Browse',
                              'slotDefinition': '{"id": "bdf396f0-f9b5-42f1-bacc-8aa1cb292537", "name": "Browse", "type": "browse", "description": "Epix browse configuration", "supportsLowLatency": false, "experiments": [{"id": "f3f72c8e-54db-4d0f-88d1-974ced85a672", "userPercentage": 100, "isBaseRecipe": true, "size": 250, "title": "Browse", "notes": "Initial Browse MVP", "modelDefinitions": [{"key": "browse", "version": 1, "source": "BrowseModel", "fulfilment": {"ranges": [{"start": 0, "end": 250}]}, "parameters": {"browse": {"settings": {"translations": {"filters.rating": {"MPAA G": 10, "TV TV-G": 10, "G": 10, "MPAA PG": 20, "TV TV-PG": 20, "MPAA NR": 20, "PG": 20, "MPAA PG-13": 30, "TV TV-14": 30, "PG-13": 30, "MPAA R": 40, "TV TV-MA": 40, "R": 40, "MPAA NC-17": 50, "NC-17": 50, "Unknown": 50, "MPAA UR": 60, "NR": 60}}}, "filters": [{"description": "If active state is false, don\'t return it. Pretty clear cut.", "query": {"bool": {"must_not": [{"term": {"thing.custom.active.state": false}}]}}}, {"description": "Don\'t return Episodes", "query": {"bool": {"must_not": [{"term": {"typeName": "episode"}}]}}}, {"description": "Don\'t return TVSeries", "query": {"bool": {"must_not": [{"term": {"typeName": "tvseries"}}]}}}, {"description": "Filter category when category is provided on the query string.", "parameters": {"required": [{"key": "category", "divider": ","}]}, "query": {"bool": {"must": [{"terms": {"filters.category.lowercase_keyword": "{category}"}}]}}}, {"description": "Exclude the provided category when excludeCategory is provided on the query string.", "parameters": {"required": [{"key": "excludeCategory", "divider": ","}]}, "query": {"bool": {"must_not": [{"terms": {"filters.category.lowercase_keyword": "{excludeCategory}"}}]}}}, {"description": "Set lower bound for releaseYear when releaseYearMin is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMin"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"gte": "{releaseYearMin}"}}}]}}}, {"description": "Set upper bound for releaseYear when releaseYearMax is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMax"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"lte": "{releaseYearMax}"}}}]}}}, {"description": "Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMax", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"lte": "{ratingMax}"}}}]}}}, {"description": "Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMin", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"gte": "{ratingMin}"}}}]}}}, {"description": "Filter first letter of title when alpha is provided on the query string.", "parameters": {"required": [{"key": "alpha"}]}, "query": {"bool": {"must": [{"term": {"filters.alpha.keyword": "{alpha}"}}]}}}], "perFieldQueries": [{"description": "Score based on recency.", "query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.newness": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.newness"}}]}}}, {"description": "Score based on popularity.", "query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.popularity": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.popularity"}}]}}}]}}}]}], "zeroResultsOk": false}',
                              'experiments': '[{"id": "f3f72c8e-54db-4d0f-88d1-974ced85a672", "userPercentage": 100, "isBaseRecipe": true, "size": 250, "title": "Browse", "notes": "Initial Browse MVP", "modelDefinitions": [{"key": "browse", "version": 1, "source": "BrowseModel", "fulfilment": {"ranges": [{"start": 0, "end": 250}]}, "parameters": {"browse": {"settings": {"translations": {"filters.rating": {"MPAA G": 10, "TV TV-G": 10, "G": 10, "MPAA PG": 20, "TV TV-PG": 20, "MPAA NR": 20, "PG": 20, "MPAA PG-13": 30, "TV TV-14": 30, "PG-13": 30, "MPAA R": 40, "TV TV-MA": 40, "R": 40, "MPAA NC-17": 50, "NC-17": 50, "Unknown": 50, "MPAA UR": 60, "NR": 60}}}, "filters": [{"description": "If active state is false, don\'t return it. Pretty clear cut.", "query": {"bool": {"must_not": [{"term": {"thing.custom.active.state": false}}]}}}, {"description": "Don\'t return Episodes", "query": {"bool": {"must_not": [{"term": {"typeName": "episode"}}]}}}, {"description": "Don\'t return TVSeries", "query": {"bool": {"must_not": [{"term": {"typeName": "tvseries"}}]}}}, {"description": "Filter category when category is provided on the query string.", "parameters": {"required": [{"key": "category", "divider": ","}]}, "query": {"bool": {"must": [{"terms": {"filters.category.lowercase_keyword": "{category}"}}]}}}, {"description": "Exclude the provided category when excludeCategory is provided on the query string.", "parameters": {"required": [{"key": "excludeCategory", "divider": ","}]}, "query": {"bool": {"must_not": [{"terms": {"filters.category.lowercase_keyword": "{excludeCategory}"}}]}}}, {"description": "Set lower bound for releaseYear when releaseYearMin is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMin"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"gte": "{releaseYearMin}"}}}]}}}, {"description": "Set upper bound for releaseYear when releaseYearMax is provided on the query string.", "parameters": {"required": [{"key": "releaseYearMax"}]}, "query": {"bool": {"must": [{"range": {"filters.releaseYear": {"lte": "{releaseYearMax}"}}}]}}}, {"description": "Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMax", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"lte": "{ratingMax}"}}}]}}}, {"description": "Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration", "parameters": {"required": [{"key": "ratingMin", "translation": "filters.rating"}]}, "query": {"bool": {"must": [{"range": {"filters.rating": {"gte": "{ratingMin}"}}}]}}}, {"description": "Filter first letter of title when alpha is provided on the query string.", "parameters": {"required": [{"key": "alpha"}]}, "query": {"bool": {"must": [{"term": {"filters.alpha.keyword": "{alpha}"}}]}}}], "perFieldQueries": [{"description": "Score based on recency.", "query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.newness": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.newness"}}]}}}, {"description": "Score based on popularity.", "query": {"function_score": {"query": {"bool": {"must": [{"range": {"scores.popularity": {"gt": 0}}}]}}, "functions": [{"field_value_factor": {"field": "scores.popularity"}}]}}}]}}}]}]',
                              'isDefault': False, 'customer': 'epix',
                              'zeroResultsOk': True,
                              'description': 'Epix browse configuration',
                              'dateUpdated': '2024-03-25T09:44:43+00:00',
                              'type': 'browse'}, {'lastUpdatedBy': 'pre-tcwp-cognito',
                                                  'dateCreated': '2023-11-23T11:41:02+00:00',
                                                  'fallbackChart': '',
                                                  'responseSize': Decimal('30'),
                                                  'createdBy': 'pre-tcwp-cognito',
                                                  'slotId': 'e85f8b98-8175-43d7-847a-7b1140e1343e',
                                                  'slotName': 'Latest Releases',
                                                  'slotDefinition': '{"id": "e85f8b98-8175-43d7-847a-7b1140e1343e", "name": "Latest Releases", "type": "latest", "description": "Latest Releases", "supportsLowLatency": false, "experiments": [{"id": "b60cb260-ec5d-4f28-b6e8-1524d29feb33", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "epix_latest_all", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "latest-all"}}]}]}',
                                                  'experiments': '[{"id": "b60cb260-ec5d-4f28-b6e8-1524d29feb33", "userPercentage": 100, "isBaseRecipe": true, "size": 30, "title": "", "notes": "", "modelDefinitions": [{"key": "epix_latest_all", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "latest-all"}}]}]',
                                                  'customer': 'epix',
                                                  'zeroResultsOk': False,
                                                  'description': 'Latest Releases',
                                                  'dateUpdated': '2023-11-23T11:41:02+00:00',
                                                  'type': 'latest'},
            {'lastUpdatedBy': 'pre-tcwp-cognito',
             'dateCreated': '2024-03-25T11:35:26+00:00', 'fallbackChart': '',
             'responseSize': Decimal('25'), 'createdBy': 'pre-tcwp-cognito',
             'slotId': 'ef834577-2685-48ed-88cd-c77040f5bec6',
             'slotName': 'Trending All',
             'slotDefinition': '{"id": "ef834577-2685-48ed-88cd-c77040f5bec6", "name": "Trending All", "type": "trending", "description": "Trending All", "supportsLowLatency": false, "experiments": [{"id": "215c6a28-5782-4cef-8752-a97b22d2799b", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending All", "notes": "Data-Ops RealTime Trending All", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "standard", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}], "zeroResultsOk": false}',
             'experiments': '[{"id": "215c6a28-5782-4cef-8752-a97b22d2799b", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Trending All", "notes": "Data-Ops RealTime Trending All", "modelDefinitions": [{"key": "trending-{variant}", "source": "TrendingModel", "version": 1, "fulfilment": {"ranges": [{"start": 0, "end": 11}]}, "parameters": {"defaultVariant": "recent", "defaultOverlay": "standard", "chartName": "trending-{variant}-{overlay}", "use_recommendation_id": true}}]}]',
             'isDefault': False, 'customer': 'epix', 'zeroResultsOk': False,
             'description': 'Trending All', 'dateUpdated': '2024-03-25T11:35:26+00:00',
             'type': 'trending'}, {'lastUpdatedBy': 'pre-tcwp-cognito',
                                   'dateCreated': '2024-03-19T12:34:38+00:00',
                                   'fallbackChart': '', 'responseSize': Decimal('25'),
                                   'createdBy': 'pre-tcwp-cognito',
                                   'slotId': 'f7cb342f-4dc0-4180-bf47-8e0413a49c8f',
                                   'slotName': 'Recommended For You',
                                   'slotDefinition': '{"id": "f7cb342f-4dc0-4180-bf47-8e0413a49c8f", "name": "Recommended For You", "type": "rfy", "description": "Recommended For You", "supportsLowLatency": false, "experiments": [{"id": "e2018b50-998e-49e9-a81f-13fd25ae9a11", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Recommended For You", "notes": "Group A hitting Epix-RFY-rfy_20230310", "modelDefinitions": [{"key": "rfy_20240219", "version": 1, "source": "PrebuiltModel", "fulfilment": {"ranges": [{"start": 0, "end": 24}]}, "parameters": {"deduplicate_path": ""}}, {"key": "fallback", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular-fallback"}}]}], "zeroResultsOk": false}',
                                   'experiments': '[{"id": "e2018b50-998e-49e9-a81f-13fd25ae9a11", "userPercentage": 100, "isBaseRecipe": true, "size": 25, "title": "Recommended For You", "notes": "Group A hitting Epix-RFY-rfy_20230310", "modelDefinitions": [{"key": "rfy_20240219", "version": 1, "source": "PrebuiltModel", "fulfilment": {"ranges": [{"start": 0, "end": 24}]}, "parameters": {"deduplicate_path": ""}}, {"key": "fallback", "version": 1, "source": "NamedChart", "fulfilment": {"ranges": [{"start": 0, "end": 50}]}, "parameters": {"chartName": "most-popular-fallback"}}]}]',
                                   'isDefault': True, 'customer': 'epix',
                                   'zeroResultsOk': False,
                                   'description': 'Recommended For You',
                                   'dateUpdated': '2024-03-19T12:34:38+00:00',
                                   'type': 'rfy'}]
        environment = "test_environment"
        region = "eu-west-2"
        customer = "test_customer"
        log_client = NoOpLogger()
        slots_repo = StubSlotsRepository(scan_results=stub_slots)
        slot_status_api = SlotStatusUpdater(
            environment, region, customer, slots_repo, log_client
        )

        message = slot_status_api.update_slot_status(charts=charts)
        expected_message = """Charts have new recs, and the associated slots: 
e85f8b98-8175-43d7-847a-7b1140e1343e: Latest Releases with status None
882caad6-f7a0-4fda-aabc-d7a693980b6b: Most Popular with status None
064bbfeb-d46d-4731-8569-aff8c1981293: Trending Movies with status None
40659eb1-6a2c-43c0-8b9e-25fbd1a55317: Trending TV Series with status None
ef834577-2685-48ed-88cd-c77040f5bec6: Trending All with status None 
have been updated to a 'live' status"""
        self.assertEqual(expected_message, message)
