import unittest
from types import GeneratorType
from unittest.mock import patch

from elasticsearch_dsl import Q, Search

from thefilter.data_sources.elasticsearch_data_source import BaseElasticSearchDataSource, \
    ElasticSearchMetadataSource


class TestElasticSearchMetadataSource(unittest.TestCase):
    @patch(
        "thefilter.data_sources.elasticsearch_data_source.ElasticSearchMetadataSource._get_results_generator"
    )
    def test_get_generator_passes_params_to_get_results_generator(
            self, mock_get_results_generator):
        endpoint = "endpoint"
        customer = "customer"
        filters = []
        values = ["thing.id", "thing.name"]
        generator = ElasticSearchMetadataSource.get_generator(
            endpoint=endpoint,
            customer=customer,
            filters=filters,
            values=values)
        mock_get_results_generator.assert_called_with(
            endpoint=endpoint,
            indices=["metadata_customer"],
            filters=filters,
            values=values)

    @patch(
        "thefilter.data_sources.elasticsearch_data_source.ElasticSearchMetadataSource.get_generator"
    )
    def test_get_df_passess_params_to_get_generator(self, mock_get_generator):
        endpoint = "endpoint"
        customer = "customer"
        values = ["thing.id", "thing.name"]
        filters = []
        df = ElasticSearchMetadataSource.get_df(endpoint=endpoint,
                                                customer=customer,
                                                filters=filters,
                                                values=values)
        mock_get_generator.assert_called_with(endpoint=endpoint,
                                              customer=customer,
                                              filters=filters,
                                              values=values)


class TestBaseElasticSearchDataSource(unittest.TestCase):
    def test_flatten_dict(self):
        input_ = {
            "dict1": {
                "key": "value"
            },
            "dict2": {
                "dict3": {
                    "another_key": "another_value",
                    "yet another_key": "yet another_value"
                }
            }
        }
        output = BaseElasticSearchDataSource._flatten_dict(input_)
        expected_output = {
            "dict1.key": "value",
            "dict2.dict3.another_key": "another_value",
            "dict2.dict3.yet another_key": "yet another_value"
        }
        self.assertDictEqual(expected_output, output)

    def test_source_adds_return_values(self):
        search_obj = Search().query("match", something="value")
        search_obj = BaseElasticSearchDataSource._source(
            search_obj, ["attr1", "attr2"])
        self.assertDictEqual(
            {
                "query": {
                    "match": {
                        "something": "value"
                    }
                },
                "_source": {
                    "include": ["attr1", "attr2"]
                }
            }, search_obj.to_dict())

    def test_add_filters_adds_filters(self):
        search_obj = Search().query("match", something="value")
        filter_obj = Q("terms", this="that")
        search_obj = BaseElasticSearchDataSource._add_filters(
            search_obj, [("thing.genre", "action"),
                         ("thing.test", "something"), filter_obj])
        self.assertEqual(
            {
                "query": {
                    "bool": {
                        "filter": [{
                            "terms": {
                                "thing.genre": ["action"]
                            }
                        }, {
                            "terms": {
                                "thing.test": ["something"]
                            }
                        }, {
                            "terms": {
                                "this": "that"
                            }
                        }],
                        "must": [{
                            "match": {
                                "something": "value"
                            }
                        }]
                    }
                }
            }, search_obj.to_dict())

    def test_add_filters_log_persist(self):
        search_obj = Search().query()
        search_obj = BaseElasticSearchDataSource._add_filters(
            search_obj, [("action", "personalisation"),
                         ("!parameters.query_string.ignore", "persist_ignore")])
        self.assertEqual(
            {
                "query": {
                    "bool": {
                        "filter": [
                            {
                                "terms": {
                                    "action": ["personalisation"]
                                }
                            },
                            {
                                "bool": {
                                    "must_not": [
                                        {
                                            "terms": {
                                                "parameters.query_string.ignore": [
                                                    "persist_ignore"]
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }
            },
            search_obj.to_dict())

    @patch(
        "thefilter.data_sources.elasticsearch_data_source.BaseElasticSearchDataSource._source"
    )
    @patch(
        "thefilter.data_sources.elasticsearch_data_source.BaseElasticSearchDataSource._add_filters"
    )
    @patch(
        "thefilter.data_sources.elasticsearch_data_source.BaseElasticSearchDataSource.get_search_object"
    )
    def test_construct_query_calls_source_add_filters_get_search_object(
            self, mock_get_search, mock_add_filters, mock_source):
        endpoint = "endpoint"
        indices = ["log_customer.2019-01"]
        filters = [("attr", "val")]
        values = ["attr2"]
        BaseElasticSearchDataSource._construct_query(endpoint=endpoint,
                                                     indices=indices,
                                                     filters=filters,
                                                     values=values)
        mock_get_search.assert_called_with(endpoint=endpoint, indices=indices)
        mock_add_filters.assert_called_with(mock_get_search(), filters=filters)
        mock_source.assert_called_with(mock_add_filters(), values=values)

    @patch('elasticsearch_dsl.Search', return_value=iter([1, 2, 3]))
    @patch(
        "thefilter.data_sources.elasticsearch_data_source.BaseElasticSearchDataSource._construct_query"
    )
    def test_get_results_generator_returns_generator(self,
                                                     mock_construct_query,
                                                     mock_search_obj):
        results_generator = BaseElasticSearchDataSource._get_results_generator(
            "endpoint", ["index"], [("attr", "val")], ["attr2"])
        self.assertIsInstance(results_generator, GeneratorType)

    def test_get_search_object_returns_search_object(self):
        search_obj = BaseElasticSearchDataSource.get_search_object(
            "endpoint", ["index"])
        self.assertIsInstance(search_obj, Search)
        self.assertEqual(["index"], search_obj._index)
