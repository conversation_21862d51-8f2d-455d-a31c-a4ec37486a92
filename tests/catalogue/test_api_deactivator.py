import unittest
from datetime import datetime, timezone
from thefilter.catalogue.api_deactivator import ApiElasticSearchItemDeactivator


class ApiDeactivatorTests(unittest.TestCase):
    now = datetime(2019, 9, 4, 9, 28, 23, tzinfo=timezone.utc)

    def test_construct_deactivation_query(self):
        deactivator = ApiElasticSearchItemDeactivator('endpoint', 'index', self.now)
        items = ['1_brand', '42_channel']
        query_obj = deactivator._construct_deactivation_query(items)
        expected_query_obj = {'query': {'ids': {'values': ['1_brand', '42_channel']}},
         'script': {'lang': 'painless',
                    'params': {'now': self.now},
                    'source': '\n'
                              '                                                    '
                              'ctx._source.thing.custom.active.stateUpdateTimestamp = params.now;\n'
                              '                                                    '
                              'ctx._source.thing.custom.active.state = false'}}
        self.maxDiff = None
        self.assertEqual(expected_query_obj, query_obj.to_dict())