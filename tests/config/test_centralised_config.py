import json
import os
from unittest import TestCase
from unittest.mock import patch, MagicMock

from thefilter.config.centralised_config import CentralisedConfig
from thefilter.logs.logclient import NoOpLogger


class TestCentralConfig(TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logger = NoOpLogger()
        region = "eu-west-2"
        environment = "pre"
        self._config_api = CentralisedConfig(
            region, environment, logger, repo_type="local"
        )

    def test_get_customer_names(self):
        all_customer_names = self._config_api.get_customer_names()
        expected = [
            'alhurra',
            'backstagedemo',
            'broadwayhd',
            'covington',
            'epix',
            'externaldemo',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'rtve',
            'slovaktelekom',
            'tastemade',
            'telegram',
            'uktv',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, all_customer_names)

    def test_get_backstage_customer_names(self):
        backstage_customers = self._config_api.get_backstage_customer_names()
        expected = [
            'alhurra',
            'backstagedemo',
            'broadwayhd',
            'covington',
            'externaldemo',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'tastemade',
            'telegram',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, backstage_customers)

    def test_get_demo_customers(self):
        backstage_customers = self._config_api.get_demo_customer_names()
        expected = [
            'backstagedemo',
            'externaldemo',
            'slovaktelekom'
        ]
        self.assertEqual(expected, backstage_customers)

    def test_get_fallback_customer_names(self):
        # Pre should include all lowest level values
        region = 'eu-west-2'
        environment = 'pre'
        fallback_customer_names = \
            self._config_api.get_fallback_customer_names(region, environment)
        expected = [
            'broadwayhd',
            'covington',
            'epix',
            'tastemade',
            'uktv'
        ]
        self.assertEqual(expected, fallback_customer_names)

        # Production needs to have the region specified
        region = 'eu-west-2'
        environment = 'production'
        fallback_customer_names = \
            self._config_api.get_fallback_customer_names(region, environment)
        expected = [
            'uktv'
        ]
        self.assertEqual(expected, fallback_customer_names)

        region = 'us-east-1'
        environment = 'production'
        fallback_customer_names = \
            self._config_api.get_fallback_customer_names(region, environment)
        expected = [
            'broadwayhd',
            'covington',
            'epix',
            'tastemade'
        ]
        self.assertEqual(expected, fallback_customer_names)

    def test_is_backstage_customer(self):
        customer = 'test_customer'
        actual = self._config_api.is_backstage_customer(customer)
        self.assertFalse(actual)

        customer = 'backstagedemo'
        actual = self._config_api.is_backstage_customer(customer)
        self.assertTrue(actual)

    def test_is_demo_customer(self):
        customer = 'test_customer'
        actual = self._config_api.is_demo_customer(customer)
        self.assertFalse(actual)

        customer = 'backstagedemo'
        actual = self._config_api.is_demo_customer(customer)
        self.assertTrue(actual)

    def test_backstage_service_id_to_customer(self):
        service_id = "c7d51a60-e32b-11ec-a22c-bdf5da079f79"
        self._config_api._environment = "pre"
        result = self._config_api.backstage_service_id_to_customer(service_id)
        expected = "backstagedemo"
        self.assertEqual(expected, result)

        service_id = "unknown"
        self._config_api._environment = "pre"
        result = self._config_api.backstage_service_id_to_customer(service_id)
        expected = None
        self.assertEqual(expected, result)

        service_id = "unknown"
        self._config_api._environment = "unknown"
        result = self._config_api.backstage_service_id_to_customer(service_id)
        expected = None
        self.assertEqual(expected, result)

    def test_get_customers_by_region_feature(self):
        config_api = CentralisedConfig(
            "eu-west-2", "feature", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region()
        expected = [
            'backstagedemo',
            'broadwayhd',
            'epix',
            'uktv'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_pre_eu_london(self):
        config_api = CentralisedConfig(
            "eu-west-2", "pre", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region("eu-west-2")
        expected = [
            'alhurra',
            'backstagedemo',
            'broadwayhd',
            'covington',
            'epix',
            'externaldemo',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'slovaktelekom',
            'tastemade',
            'telegram',
            'uktv',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_pre_eu_frankfurt(self):
        config_api = CentralisedConfig(
            "eu-west-2", "pre", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region("eu-central-1")
        expected = [
            'rtve'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_pre_no_region(self):
        config_api = CentralisedConfig(
            "eu-west-2", "pre", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region()
        expected = [
            'alhurra',
            'backstagedemo',
            'broadwayhd',
            'covington',
            'epix',
            'externaldemo',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'slovaktelekom',
            'tastemade',
            'telegram',
            'uktv',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_production_eu_london(self):
        config_api = CentralisedConfig(
            "eu-west-2", "production", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region()
        expected = [
            'uktv',
            'virginmedia'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_production_eu_frankfurt(self):
        config_api = CentralisedConfig(
            "eu-central-1", "production", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region()
        expected = [
            'rtve'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customers_by_region_production_us(self):
        config_api = CentralisedConfig(
            "eu-west-2", "production", NoOpLogger(), repo_type="local"
        )
        customer_names = config_api.get_customer_names_by_region("us-east-1")
        expected = [
            'alhurra',
            'broadwayhd',
            'covington',
            'epix',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'tastemade',
            'telegram',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, customer_names)

    def test_get_customer_names_by_environment(self):
        config_api = CentralisedConfig(
            "eu-west-2", "feature", NoOpLogger(), repo_type="local"
        )
        feature_customers = config_api.get_customer_names_by_environment()
        expected = [
            'backstagedemo',
            'broadwayhd',
            'epix',
            'uktv'
        ]
        self.assertEqual(expected, feature_customers)

        config_api = CentralisedConfig(
            "eu-west-2", "pre", NoOpLogger(), repo_type="local"
        )
        pre_customers = config_api.get_customer_names_by_environment()
        expected = [
            'alhurra',
            'backstagedemo',
            'broadwayhd',
            'covington',
            'epix',
            'externaldemo',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'rtve',
            'slovaktelekom',
            'tastemade',
            'telegram',
            'uktv',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, pre_customers)

        config_api = CentralisedConfig(
            "eu-west-2", "production", NoOpLogger(), repo_type="local"
        )
        production_customers = config_api.get_customer_names_by_environment()
        expected = [
            'alhurra',
            'broadwayhd',
            'covington',
            'epix',
            'flowsports',
            'kan',
            'marti',
            'newsmax',
            'revry',
            'rfa',
            'rtve',
            'tastemade',
            'telegram',
            'uktv',
            'virginmedia',
            'voa',
            'votvot'
        ]
        self.assertEqual(expected, production_customers)

    def test_get_rec_customers_names(self):
        # feature
        config_api = CentralisedConfig(
            "eu-west-2", "feature", NoOpLogger(), repo_type="local"
        )
        feature_rec_customers_names = config_api.get_rec_customers_names()
        expected = [
            'backstagedemo',
            'broadwayhd',
            'epix',
            'uktv'
        ]
        self.assertEqual(expected, feature_rec_customers_names)

        # pre london
        rec_customers = self._config_api.get_rec_customers_names()
        expected = [
            'backstagedemo',
            'broadwayhd',
            'covington',
            'epix',
            'revry',
            'tastemade',
            'uktv',
        ]
        self.assertEqual(expected, rec_customers)

        # pre frankfurt
        config_api = CentralisedConfig(
            "eu-central-1", "pre", NoOpLogger(), repo_type="local"
        )
        rec_customers = config_api.get_rec_customers_names()
        expected = [
            'rtve'
        ]
        self.assertEqual(expected, rec_customers)

        # prod london
        config_api = CentralisedConfig(
            "eu-west-2", "production", NoOpLogger(), repo_type="local"
        )
        feature_rec_customers_names = config_api.get_rec_customers_names()
        expected = [
            'uktv'
        ]
        self.assertEqual(expected, feature_rec_customers_names)

        # prod virginia
        config_api = CentralisedConfig(
            "us-east-1", "production", NoOpLogger(), repo_type="local"
        )
        feature_rec_customers_names = config_api.get_rec_customers_names()
        expected = [
            'broadwayhd',
            'covington',
            'epix',
            'revry',
            'tastemade'
        ]
        self.assertEqual(expected, feature_rec_customers_names)

        # prod frankfurt
        config_api = CentralisedConfig(
            "eu-central-1", "production", NoOpLogger(), repo_type="local"
        )
        feature_rec_customers_names = config_api.get_rec_customers_names()
        expected = [
            'rtve'
        ]
        self.assertEqual(expected, feature_rec_customers_names)

    def test_is_non_rec_customer(self):
        # feature
        config_api = CentralisedConfig(
            "eu-west-2", "feature", NoOpLogger(), repo_type="local"
        )
        feature_non_rec_customers_names = config_api.get_non_rec_customers_names()
        expected = []
        self.assertEqual(expected, feature_non_rec_customers_names)

        # pre
        is_non_rec_customer = self._config_api.is_non_rec_customer("epix")
        expected = False
        self.assertEqual(expected, is_non_rec_customer)

        is_non_rec_customer = self._config_api.is_non_rec_customer("flowsports")
        expected = True
        self.assertEqual(expected, is_non_rec_customer)

    def test_get_suppressed_metadata_fields(self):
        suppressed_metadata_fields = \
            self._config_api.get_suppressed_metadata_fields("epix")
        expected = []
        self.assertEqual(expected, suppressed_metadata_fields)

        suppressed_metadata_fields = \
            self._config_api.get_suppressed_metadata_fields("tastemade")
        expected = ['thing_actor_id', 'thing_actor_name']
        self.assertEqual(expected, suppressed_metadata_fields)

    def test_add_customer_name_to_config(self):
        test_customer = "test_customer"
        self._config_api.add_customer_to_config(
            customer=test_customer,
            production_region=None,
            is_demo=True,
            is_non_rec=True,
            is_backstage=False,
            backstage_service_id=None,
            incremental=False
        )
        customer_names = self._config_api.get_customer_names()
        self.assertTrue(test_customer in customer_names)

        with self.assertRaises(SystemExit):
            self._config_api.add_customer_to_config(
                customer=test_customer,
                production_region=None,
                is_demo=True,
                is_non_rec=True,
                is_backstage=False,
                backstage_service_id=None,
                incremental=False
            )

        # clean up
        self._remove_test_customer_from_config(test_customer)

    @staticmethod
    def _remove_test_customer_from_config(customer_name: str):
        """We delete the test_customer from the config here rather than in the client
        as we don't want to expose this method!"""
        config_path = '../../thefilter/config/centralised_config.json'
        config_file_path = os.path.join(os.path.dirname(__file__), config_path)
        with open(config_file_path, 'r') as json_file:
            config_json: dict = json.load(json_file)
            config_json.pop(customer_name)

        with open(config_file_path, "w") as json_file:
            json.dump(config_json, json_file, indent=2)

    def test_read_file(self):
        # useless test. Just for coverage :)
        config_json_using_api = self._config_api.read_file()
        config_path = "../../thefilter/config/centralised_config.json"
        config_file_path = os.path.join(os.path.dirname(__file__), config_path)
        with open(config_file_path, 'r') as json_file:
            config_json_from_file = json.load(json_file)
        self.assertEqual(config_json_from_file, config_json_using_api)


class TestGetSearchableTypenames(TestCase):
    def setUp(self):
        self.region = "eu-west-2"
        self.environment = "pre"
        self.logger = MagicMock()
        self.config = CentralisedConfig(
            self.region, self.environment, self.logger, repo_type="local"
        )

    @patch("thefilter.config.centralised_config.CentralisedConfig.get_config_as_dataclass")
    def test_customer_with_searchable_typenames(self, mock_get_config):
        # Mock customer configuration with specific searchable_typenames
        mock_get_config.return_value = {
            "test_customer": MagicMock(
                search_config=MagicMock(
                    searchable_typenames=["CustomType1", "CustomType2"]
                )
            )
        }
        result = self.config.get_searchable_typenames("test_customer")
        self.assertEqual(result, ["CustomType1", "CustomType2"])

    @patch("thefilter.config.centralised_config.CentralisedConfig.get_config_as_dataclass")
    def test_customer_with_default_searchable_typenames(self, mock_get_config):
        # Mock customer configuration with no searchable_typenames
        mock_get_config.return_value = {
            "test_customer": MagicMock(
                search_config=MagicMock(searchable_typenames=None)
            )
        }
        result = self.config.get_searchable_typenames("test_customer")
        self.assertEqual(result, [
            "TVSeries", "Movie", "Brand", "CreativeWork",
            "AudioSeries", "Article", "NewsSeries"
        ])
