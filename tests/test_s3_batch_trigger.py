from unittest import TestCase

import freezegun

from thefilter.aws.batch import Batch<PERSON><PERSON><PERSON>tub, BatchClient
from thefilter.aws.s3 import S3TriggerStub
from thefilter.functions.s3_batch_trigger.s3_batch_trigger import S3BatchTrigger
from thefilter.logs.logclient import <PERSON><PERSON><PERSON><PERSON>ogger
from thefilter.utils.slackbot import SlackBotStub


class TestS3BatchTrigger(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        region = "eu-west-2"
        environment = "feature"
        customer = "test"

        last_modified_time = "2023-04-19T12:00:00+00:00"
        s3_trigger_client = S3TriggerStub(
            set_last_modified_time=last_modified_time,
            set_list_s3_objects=""
        )
        batch_client = BatchClientStub(
            region=region,
            batch_job_name="testCustomerEventLoaderJob",
            batch_job_queue="arn:aws:batch:eu-west-2:147726730760:job-queue/HighPriorityBatchJobqueue",
            is_batch_job_running_return_value=False
        )
        log_client = NoOpLogger()
        slackbot_client = SlackBotStub()

        self._api = S3BatchTrigger(
            region,
            environment,
            customer,
            s3_trigger_client,
            batch_client,
            log_client,
            slackbot_client
        )

    def test_run(self):
        s3_put_event = {
            "Records": [
                {
                    "eventName": "ObjectCreated:Put",
                    "s3": {
                        "bucket": {
                            "name": "thefilter-feature-eu-west-2-test",
                            "arn": "arn:aws:s3:::thefilter-pre-eu-west-2-test"
                        },
                        "object": {
                            "key": "source/events/day_utc=1681862400/test_file.json",
                        }
                    }
                }
            ]
        }
        response = self._api.run(s3_put_event)
        expected = "dennis: Test Customer_events Loader Job triggered from " \
                   "thefilter-feature-eu-west-2-test/source/events/" \
                   "day_utc=1681862400 [2023-04-19]."
        self.assertEqual(expected, response)

    @freezegun.freeze_time('2023-04-19T12:00:00+00:00')
    def test_exit_code(self):
        self._api._s3_trigger_client._set_last_modified_time = None

        with self.assertRaises(SystemExit):
            self._api._check_files_uploaded()

    def test_handle_notification(self):
        self._api._s3_bucket = 'thefilter-feature-eu-west-2-test'
        self._api._s3_folder = "source/views/day_utc=1681862400"
        self._api._job_type = 'customer_events'
        batch_response = {"statusCode": 400}
        message = self._api._handle_notification(batch_response)
        expected = "dennis: Error with: " \
                   "Test Customer_events Loader Job triggered from " \
                   "thefilter-feature-eu-west-2-test/" \
                   "source/views/day_utc=1681862400 [2023-04-19]."
        self.assertEqual(expected, message)
