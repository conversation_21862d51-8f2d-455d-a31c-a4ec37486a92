import json
import unittest

import freezegun

from deployment.scripts.new_customer.default_slot_creator import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from deployment.scripts.new_customer.post_deploy_new_customer import \
    PostDeployNewCustomer
from tests.scripts.data.expected_postman_collection import \
    get_expected_postman_collection
from tests.scripts.data.generated_test_slots import expected_generated_test_slots
from thefilter.aws.ssm import SSMClientStub
from thefilter.testing.postman.postman_examples.postman_collection_api import \
    PostmanCollectionAPI


class TestPostDeployNewCustomer(unittest.TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.post_deploy_new_customer = PostDeployNewCustomer(test_mode=True)
        self.maxDiff = None

    @freezegun.freeze_time("2023-11-06T17:00:00+00:00")
    def test_generate_slots(self):
        generated_slots = \
            self.post_deploy_new_customer.generate_slots()

        self.assertEqual(13, len(generated_slots),
                         "Slot count matches - if failed and new ones added this needs adding to DDB")

        for slot in generated_slots:
            del slot["slotId"]
            del slot["experiments"][0]["id"]

        # If failure, the ids will be tagged as false positives, these can be ignored
        self.assertEqual(expected_generated_test_slots, generated_slots,
                         "Slot output matches")

    def test_create_postman_collection(self):
        # just check that it works.
        is_backstage_customer = False
        postman_collection_id = "test_postman_collection_id"
        customer_name = "test_customer"
        region = "eu-west-2"
        ssm_client = SSMClientStub()
        slots = self.post_deploy_new_customer.generate_slots()
        postman_collection_api = PostmanCollectionAPI(region, ssm_client)
        collection = postman_collection_api.create_collection(
            postman_collection_id, customer_name, slots
        )
        expected = get_expected_postman_collection()

    def test_descriptions(self):
        default_slot_creator = DefaultSlotCreator()
        slot_name = "Because You Watched"
        slot_id = "eca7c3c4-de91-44f6-b6e6-8730544eae5d"
        experiment = "test_experiment"
        result = \
            default_slot_creator._build_slot_definition(slot_id, slot_name, experiment)
        expected = {
            "id": "eca7c3c4-de91-44f6-b6e6-8730544eae5d",
            "name": "Because You Watched",
            "description": "Content suggestions based on similarity of items from a users watch history",
            "experiments": "test_experiment"
        }
        self.assertEqual(json.dumps(expected), result)

