expected_generated_test_slots = [
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Fallback Healthcheck",
        'status': 'Initial',
        "type": "fallback",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Fallback Healthcheck",
                "notes": "Autogenerated Fallback Healthcheck slot.",
                "modelDefinitions": [
                    {
                        "key": "StubModel",
                        "version": 1,
                        "source": "StubModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "fallback-rfy",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Recommended For You",
        'status': 'Initial',
        "type": "rfy",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Recommended For You",
                "notes": "Autogenerated Recommended For You",
                "modelDefinitions": [
                    {
                        "key": "rfy_autogenerated",
                        "version": 1,
                        "source": "PrebuiltModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "deduplicate_path": ""
                        }
                    },
                    {
                        "version": 1,
                        "source": "NamedChart",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "most-popular-1d-play",
                            "use_recommendation_id": True
                        }
                    }
                ]
            }
        ]
    },
    {
        'customer': 'test',
        'createdBy': 'scripted_setup',
        'dateCreated': '2023-11-06T17:00:00+00:00',
        'dateUpdated': '2023-11-06T17:00:00+00:00',
        'experiments': [{'isBaseRecipe': True,
                         'modelDefinitions': [{'fulfilment': {'ranges': [{'end': 11,
                                                                          'start': 0}]},
                                               'key': 'StubModel',
                                               'source': 'StubModel',
                                               'version': 1}],
                         'notes': 'Autogenerated Recommended For You Audio slot.',
                         'size': 12,
                         'title': 'Recommended For You Audio',
                         'userPercentage': 100}],
        'fallbackChart': 'fallback-rfy-audioseries',
        'isDefault': True,
        'lastUpdatedBy': 'scripted_setup',
        'responseSize': 12,
        'slotName': 'Recommended For You Audio',
        'status': 'Initial',
        'type': 'rfy_audio',
        'zeroResultsOk': False},
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "handled_in_model",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "More Like This",
        'status': 'Initial',
        "type": "mlt",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "More Like This",
                "notes": "Autogenerated More Like This",
                "modelDefinitions": [
                    {
                        "key": "mlt_autogenerated",
                        "version": 1,
                        "source": "PrebuiltModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "useSeedId": True,
                            "titlePlaceholder": "More Like {seed_name}"
                        }
                    }
                ]
            }
        ]
    },
    {
        'createdBy': 'scripted_setup',
        'customer': 'test',
        'dateCreated': '2023-11-06T17:00:00+00:00',
        'dateUpdated': '2023-11-06T17:00:00+00:00',
        'experiments': [{'isBaseRecipe': True,
                         'modelDefinitions': [{
                        "key": "mlt_autogenerated",
                        "version": 1,
                        "source": "PrebuiltModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "useSeedId": True,
                            "titlePlaceholder": "More Like {seed_name}"
                        }
                    }],
                         'notes': 'Autogenerated More Like This Audio',
                         'size': 12,
                         'title': 'More Like This Audio',
                         'userPercentage': 100}],
        'fallbackChart': 'handled_in_model',
        'isDefault': True,
        'lastUpdatedBy': 'scripted_setup',
        'responseSize': 12,
        'slotName': 'More Like This Audio',
        'status': 'Initial',
        'type': 'mlt_audio',
        'zeroResultsOk': False
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "handled_in_model",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Because You Watched",
        'status': 'Initial',
        "type": "byw",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Because You Watched",
                "notes": "Autogenerated Because You Watched",
                "modelDefinitions": [
                    {
                        "key": "mlt_autogenerated",
                        "version": 1,
                        "source": "PrebuiltBYWModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "useSeedId": True,
                            "titlePlaceholder": "Because You Watched {seed_name}",
                            "bywMethod": "seedSelection",
                            "defaultOverlay": "seedSelection"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "handled_in_model",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Because You Watched Audio",
        'status': 'Initial',
        "type": "byw_audio",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Because You Watched Audio",
                "notes": "Autogenerated Because You Watched Audio",
                "modelDefinitions": [
                    {
                        "key": "mlt_autogenerated",
                        "version": 1,
                        "source": "PrebuiltBYWModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "useSeedId": True,
                            "titlePlaceholder": "Because You Watched {seed_name}",
                            "bywMethod": "seedSelection",
                            "defaultOverlay": "seedSelection"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "handled_in_model",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Similar To",
        'status': 'Initial',
        "type": "mlt",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Similar To",
                "notes": "Autogenerated Similar To",
                "modelDefinitions": [
                    {
                        "key": "mlt_autogenerated",
                        "version": 1,
                        "source": "SimilarToModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "useSeedId": True,
                            "deduplicate_path": "",
                            "historyMethod": "moveToEnd",
                            "defaultVariant": "standard",
                            "defaultOverlay": "standard",
                            "titlePlaceholder": "Similar To {seed_name}"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Latest",
        'status': 'Initial',
        "type": "latest",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Latest",
                "notes": "Autogenerated Latest slot.",
                "modelDefinitions": [
                    {
                        "key": "latest-{variant}",
                        "version": 1,
                        "source": "NamedChart",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 12
                                }
                            ]
                        },
                        "parameters": {
                            "defaultVariant": "standard",
                            "defaultOverlay": "standard",
                            "chartName": "latest-{variant}-{overlay}"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Most Popular",
        'status': 'Initial',
        "type": "most_popular",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Most Popular",
                "notes": "Most Popular",
                "modelDefinitions": [
                    {
                        "key": "most-popular-{variant}",
                        "version": 1,
                        "source": "NamedChart",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 11
                                }
                            ]
                        },
                        "parameters": {
                            "chartName": "most-popular-{variant}-{overlay}",
                            "defaultOverlay": "play",
                            "defaultVariant": "7d"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Trending",
        'status': 'Initial',
        "type": "trending",
        "zeroResultsOk": False,
        "responseSize": 12,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 12,
                "title": "Trending",
                "notes": "Autogenerated Trending slot.",
                "modelDefinitions": [
                    {
                        "key": "trending-{variant}",
                        "source": "TrendingModel",
                        "version": 1,
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 12
                                }
                            ]
                        },
                        "parameters": {
                            "defaultVariant": "daily",
                            "defaultOverlay": "standard",
                            "chartName": "trending-{variant}-{overlay}"
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "search",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Search",
        'status': 'Initial',
        "type": "search",
        "zeroResultsOk": True,
        "responseSize": 100,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 100,
                "title": "Search",
                "notes": "Autogenerated Initial Search - supports name, genre, actor and entities",
                "modelDefinitions": [
                    {
                        "key": "search",
                        "version": 1,
                        "source": "SearchModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 99
                                }
                            ]
                        },
                        "parameters": {
                            "search": {
                                "mlt": {
                                    "userHistoryDepth": 10,
                                    "userHistoryMaxBoost": 40,
                                    "fields": [
                                        "keywords.searchable"
                                    ],
                                    "includeFromUserHistory": True
                                },
                                "perFieldQueries": [
                                    {
                                        "description": "100,000 : Full, 100% field value matches! If you literally type in the exact name of something, who are we to argue? Targets the title of the content and prominent actors.",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 100000,
                                                "queries": [
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "name.lowercase_keyword": {
                                                                        "value": "{q}"
                                                                    }
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "franchise.lowercase_keyword": {
                                                                        "value": "{q}"
                                                                    }
                                                                }
                                                            },
                                                            "boost": 0.9
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "prominentActor.lowercase_keyword": "{q}"
                                                                }
                                                            },
                                                            "boost": 0.9
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "genre.lowercase_keyword": "{q}"
                                                                }
                                                            },
                                                            "boost": 0.7
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "keyword.lowercase_keyword": "{q}"
                                                                }
                                                            },
                                                            "boost": 0.7
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "10,000 : Full term matches! If any of the terms in your query match ",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 10000,
                                                "queries": [
                                                    {
                                                        "match": {
                                                            "name.searchable": {
                                                                "query": "{q}"
                                                            }
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "100 : Partial full matches!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 100,
                                                "queries": [
                                                    {
                                                        "dis_max": {
                                                            "tie_breaker": 0.7,
                                                            "boost": 1,
                                                            "queries": [
                                                                {
                                                                    "constant_score": {
                                                                        "filter": {
                                                                            "match_phrase_prefix": {
                                                                                "name.lowercase_keyword": {
                                                                                    "query": "{q}"
                                                                                }
                                                                            }
                                                                        },
                                                                        "boost": 1
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "10 : Fuzzy full matches!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 20,
                                                "queries": [
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "match": {
                                                                    "name.lowercase_keyword": {
                                                                        "query": "{q}",
                                                                        "fuzziness": 1,
                                                                        "fuzzy_transpositions": "true",
                                                                        "boost": 1
                                                                    }
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "10 : Partial term matches!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 50,
                                                "queries": [
                                                    {
                                                        "dis_max": {
                                                            "tie_breaker": 0.7,
                                                            "boost": 1,
                                                            "queries": [
                                                                {
                                                                    "constant_score": {
                                                                        "filter": {
                                                                            "match_phrase_prefix": {
                                                                                "name.searchable": {
                                                                                    "query": "{q}"
                                                                                }
                                                                            }
                                                                        },
                                                                        "boost": 1
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "10 : Fuzzy term matches!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 20,
                                                "queries": [
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "match": {
                                                                    "name.searchable": {
                                                                        "query": "{q}",
                                                                        "fuzziness": 1,
                                                                        "fuzzy_transpositions": "true",
                                                                        "boost": 1
                                                                    }
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "1 : Collection inclusion matches. We don't want to score much basted on this, just include items in the set to support browse-like behaviours. A rescore will reorder matches to make them a little bit tastier!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 1,
                                                "queries": [
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "genre.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "keyword.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 0.7
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "0.1 : Collection inclusion matches. We don't want to score much basted on this, just include items in the set to support browse-like behaviours. A rescore will reorder matches to make them a little bit tastier!",
                                        "query": {
                                            "dis_max": {
                                                "tie_breaker": 0.7,
                                                "boost": 0.1,
                                                "queries": [
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "subGenre.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "keyword.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "channel.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "constant_score": {
                                                            "filter": {
                                                                "term": {
                                                                    "enrichedGenre.searchable": "{q}"
                                                                }
                                                            },
                                                            "boost": 1
                                                        }
                                                    },
                                                    {
                                                        "match": {
                                                            "editorialTags": {
                                                                "query": "{q}",
                                                                "fuzziness": 0,
                                                                "fuzzy_transpositions": "false",
                                                                "boost": 1
                                                            }
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                "rescorers": [
                                    {
                                        "description": "Boost items with a newness score",
                                        "rescore": {
                                            "query": {
                                                "rescore_query": {
                                                    "function_score": {
                                                        "query": {
                                                            "bool": {
                                                                "must": [
                                                                    {
                                                                        "range": {
                                                                            "scores.newness": {
                                                                                "gt": 0
                                                                            }
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "functions": [
                                                            {
                                                                "field_value_factor": {
                                                                    "field": "scores.newness",
                                                                    "factor": 1000
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            },
                                            "window_size": 2000
                                        }
                                    },
                                    {
                                        "description": "100 : Boost items by normalised popularity score, but don't let that override term matches!",
                                        "rescore": {
                                            "query": {
                                                "query_weight": 1,
                                                "rescore_query_weight": 1,
                                                "rescore_query": {
                                                    "function_score": {
                                                        "query": {
                                                            "bool": {
                                                                "must": [
                                                                    {
                                                                        "range": {
                                                                            "scores.popularity": {
                                                                                "gt": 0
                                                                            }
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "functions": [
                                                            {
                                                                "field_value_factor": {
                                                                    "field": "scores.popularity",
                                                                    "factor": 1000
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            },
                                            "window_size": 2000
                                        }
                                    },
                                    {
                                        "description": "10 : Boost items recently made available, but don't let that override term matches!",
                                        "rescore": {
                                            "query": {
                                                "query_weight": 1,
                                                "rescore_query_weight": 1,
                                                "rescore_query": {
                                                    "function_score": {
                                                        "query": {
                                                            "bool": {
                                                                "must": [
                                                                    {
                                                                        "range": {
                                                                            "scores.recency": {
                                                                                "gt": 0
                                                                            }
                                                                        }
                                                                    }
                                                                ]
                                                            }
                                                        },
                                                        "functions": [
                                                            {
                                                                "field_value_factor": {
                                                                    "field": "scores.recency",
                                                                    "factor": 10
                                                                }
                                                            }
                                                        ]
                                                    }
                                                }
                                            },
                                            "window_size": 100
                                        }
                                    }
                                ],
                                "filters": [
                                    {
                                        "description": "If active state is false, don't return it. Pretty clear cut.",
                                        "query": {
                                            "bool": {
                                                "must_not": [
                                                    {
                                                        "term": {
                                                            "thing.custom.active.state": False
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Don't return Episodes",
                                        "query": {
                                            "bool": {
                                                "must_not": [
                                                    {
                                                        "term": {
                                                            "typeName": "Episode"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Filter category when category is provided on the query string.",
                                        "parameters": {
                                            "required": [
                                                {
                                                    "key": "category"
                                                }
                                            ]
                                        },
                                        "query": {
                                            "bool": {
                                                "must": [
                                                    {
                                                        "term": {
                                                            "filters.category.lowercase_keyword": "{category}"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        ]
    },
    {
        "customer": "test",
        "createdBy": "scripted_setup",
        "dateCreated": "2023-11-06T17:00:00+00:00",
        "dateUpdated": "2023-11-06T17:00:00+00:00",
        "fallbackChart": "most-popular-1d-play",
        "lastUpdatedBy": "scripted_setup",
        "slotName": "Browse",
        'status': 'Initial',
        "type": "browse",
        "zeroResultsOk": True,
        "responseSize": 250,
        "isDefault": True,
        "experiments": [
            {
                "userPercentage": 100,
                "isBaseRecipe": True,
                "size": 250,
                "title": "Browse",
                "notes": "Initial Browse",
                "modelDefinitions": [
                    {
                        "key": "browse",
                        "version": 1,
                        "source": "BrowseModel",
                        "fulfilment": {
                            "ranges": [
                                {
                                    "start": 0,
                                    "end": 250
                                }
                            ]
                        },
                        "parameters": {
                            "browse": {
                                "settings": {
                                    "translations": {}
                                },
                                "filters": [
                                    {
                                        "description": "If active state is false, don't return it. Pretty clear cut.",
                                        "query": {
                                            "bool": {
                                                "must_not": [
                                                    {
                                                        "term": {
                                                            "thing.custom.active.state": False
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Don't return Episodes",
                                        "query": {
                                            "bool": {
                                                "must_not": [
                                                    {
                                                        "term": {
                                                            "typeName": "episode"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Filter category when category is provided on the query string.",
                                        "parameters": {
                                            "required": [
                                                {
                                                    "key": "category",
                                                    "divider": ","
                                                }
                                            ]
                                        },
                                        "query": {
                                            "bool": {
                                                "must": [
                                                    {
                                                        "terms": {
                                                            "filters.category.lowercase_keyword": "{category}"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Exclude the provided category when excludeCategory is provided on the query string.",
                                        "parameters": {
                                            "required": [
                                                {
                                                    "key": "excludeCategory",
                                                    "divider": ","
                                                }
                                            ]
                                        },
                                        "query": {
                                            "bool": {
                                                "must_not": [
                                                    {
                                                        "terms": {
                                                            "filters.category.lowercase_keyword": "{excludeCategory}"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    },
                                    {
                                        "description": "Filter first letter of title when alpha is provided on the query string.",
                                        "parameters": {
                                            "required": [
                                                {
                                                    "key": "alpha"
                                                }
                                            ]
                                        },
                                        "query": {
                                            "bool": {
                                                "must": [
                                                    {
                                                        "term": {
                                                            "filters.alpha.keyword": "{alpha}"
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ],
                                "perFieldQueries": []
                            }
                        }
                    }
                ]
            }
        ]
    }
]
