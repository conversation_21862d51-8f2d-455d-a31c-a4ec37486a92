import unittest.mock as mock


def get_expected_postman_collection() -> dict:
    return {
        "info": {"_postman_id": "test_postman_collection_id", "name": "test_customer",
                 "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"},
        "auth": {"type": "apikey",
                 "apikey": [{"key": "value", "value": "{{API_KEY}}", "type": "string"},
                            {"key": "key", "value": "x-api-key", "type": "string"}]},
        "event": [{"listen": "prerequest",
                   "script": {"type": "text/javascript", "exec": [""]}},
                  {"listen": "test",
                   "script": {"type": "text/javascript", "exec": [""]}}], "item": [
            {"name": "Auth", "item": [{"name": "Get Auth Token", "event": [
                {"listen": "test", "script": {
                    "exec": ["pm.test(\"Status Code is 200 - OK\", function () {\r",
                             "    pm.response.to.be.success;\r", "})\r", "\r",
                             "pm.test(\"Set PERSONALISATION_ACCESS_TOKEN\", function () {\r",
                             "    var jsonData = pm.response.json();\r", " \r",
                             "    pm.environment.set(\"PERSONALISATION_ACCESS_TOKEN\", jso nData.access_token);\r",
                             "});\r", ""], "type": "text/javascript"}}],
                                       "protocolProfileBehavior": {
                                           "disableBodyPruning": True}, "request": {
                    "auth": {"type": "basic", "basic": [
                        {"key": "password", "value": "{{CLIENT_SECRET}}",
                         "type": "string"}, {"key": "username", "value": "{{CLIENT_ID}}",
                                             "type": "string"}]}, "method": "POST",
                    "header": [], "body": {"mode": "urlencoded", "urlencoded": [
                        {"key": "grant_type", "value": "client_credentials",
                         "type": "text"}]}, "url": {
                        "raw": "https://users.{{ENVIRONMENT}}.thefilter.com/oauth2/token",
                        "protocol": "https",
                        "host": ["users", "{{ENVIRONMENT}}", "thefilter", "com"],
                        "path": ["oauth2", "token"]}}, "response": []}]},
            {"name": "Endpoints", "item": [{"name": "Events", "item": [
                {"name": "POST event", "event": [{"listen": "test", "script": {
                    "exec": ["// Check if the response status code is 200 (OK)",
                             "pm.test(\"Status code is 200\", function () {",
                             "    pm.response.to.have.status(200);", "});"],
                    "type": "text/javascript"}}],
                 "protocolProfileBehavior": {"disableBodyPruning": True},
                 "request": {"method": "POST", "header": [], "body": {"mode": "raw",
                                                                      "raw": "{\n    \"user_id\": \"test_userId\",\n    \"action\": \"play\",\n    \"thing_id\": \"test_thing_id\"\n, \"ignore\": \"postman\"}",
                                                                      "options": {
                                                                          "raw": {
                                                                              "language": "json"}}},
                             "url": {
                                 "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/events",
                                 "protocol": "https",
                                 "host": ["{{CUSTOMER}}", "{{ENVIRONMENT}}", "thefilter",
                                          "com"], "path": ["v0", "events"]}},
                 "response": []}, {"name": "OPTIONS event", "event": [{"listen": "test",
                                                                       "script": {
                                                                           "exec": [
                                                                               "// Check if the response status code is 200 (OK)",
                                                                               "pm.test(\"Status code is 200\", function () {",
                                                                               "    pm.response.to.have.status(200);",
                                                                               "});"],
                                                                           "type": "text/javascript"}}],
                                   "protocolProfileBehavior": {
                                       "disableBodyPruning": True},
                                   "request": {"method": "OPTIONS", "header": [],
                                               "url": {
                                                   "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/events",
                                                   "protocol": "https",
                                                   "host": ["{{CUSTOMER}}",
                                                            "{{ENVIRONMENT}}",
                                                            "thefilter", "com"],
                                                   "path": ["v0", "events"]}},
                                   "response": []}, {"name": "User", "event": [
                    {"listen": "test", "script": {
                        "exec": ["pm.test(\"Status Code is success\", function () {",
                                 "    pm.response.to.be.success;", "})", "",
                                 "pm.test(\"Check response schema\", function () { ",
                                 "    pm.response.to.have.jsonBody;",
                                 "    pm.response.to.have.jsonSchema(", "        {",
                                 "            \"user\": {",
                                 "                \"id\": \"String\",",
                                 "                \"type\": \"String\"",
                                 "            },", "            \"history\": [",
                                 "                {",
                                 "                    \"id\": \"String\",",
                                 "                    \"action\": \"String\",",
                                 "                    \"timestamp\": {",
                                 "                        \"received\": \"String\",",
                                 "                        \"initiated\": \"String\"",
                                 "                    },",
                                 "                    \"thing\": \"Object\"",
                                 "                }", "            ]", "        }",
                                 "    );", "});", "",
                                 "pm.test(\"Expect response to have multiple items\", function () { ",
                                 "    var jsonData = pm.response.json();",
                                 "    pm.expect(jsonData.history.length).to.be.greaterThan(0);",
                                 "});"], "type": "text/javascript"}}],
                                                     "protocolProfileBehavior": {
                                                         "disableBodyPruning": True},
                                                     "request": {"method": "GET",
                                                                 "header": [], "url": {
                                                             "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/user/test_userId",
                                                             "protocol": "https",
                                                             "host": ["{{CUSTOMER}}",
                                                                      "{{ENVIRONMENT}}",
                                                                      "thefilter",
                                                                      "com"],
                                                             "path": ["v0", "user",
                                                                      "test_userId"]}},
                                                     "response": []}]}]},
            {"name": "Slots", "item": [{"name": "Fallback Healthcheck", "item": [
                {"name": "Fallback - Unauthorised", "event": [{"listen": "test",
                                                               "script": {"exec": [
                                                                   "pm.test(\"Status Code is success\", function () {",
                                                                   "    pm.response.to.be.success;",
                                                                   "})", "",
                                                                   "pm.test(\"Check response schema\", function () { ",
                                                                   "    pm.response.to.have.jsonBody;",
                                                                   "    pm.response.to.have.jsonSchema(",
                                                                   "        {",
                                                                   "            \"code\": \"Number\",",
                                                                   "            \"responseId\": \"String\",",
                                                                   "            \"title\": \"String\",",
                                                                   "            \"items\": [",
                                                                   "                {",
                                                                   "                    \"id\": \"String\",",
                                                                   "                    \"typeName\": \"String\"",
                                                                   "                }",
                                                                   "            ]",
                                                                   "        }", "    );",
                                                                   "});", "",
                                                                   "pm.test(\"Expect response to have six items\", function () { ",
                                                                   "    var jsonData = pm.response.json();",
                                                                   "    pm.expect(jsonData.items.length).to.be.equal(6);",
                                                                   "});"],
                                                                          "type": "text/javascript"}}],
                 "protocolProfileBehavior": {"disableBodyPruning": True},
                 "request": {"auth": {"type": "noauth"}, "method": "GET", "header": [],
                             "url": {
                                 "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items?ignore=postman",
                                 "protocol": "https",
                                 "host": ["{{CUSTOMER}}", "{{ENVIRONMENT}}", "thefilter",
                                          "com"], "path": ["v0", "slots",
                                                           "108d571f-8c52-4e9e-82ff-0fdc40e03ae6",
                                                           "items"],
                                 "query": [{"key": "ignore", "value": "postman"}]}},
                 "response": []}, {"name": "Fallback", "event": [{"listen": "test",
                                                                  "script": {"exec": [
                                                                      "pm.test(\"Status Code is success\", function () {",
                                                                      "    pm.response.to.be.success;",
                                                                      "})", "",
                                                                      "pm.test(\"Check response schema\", function () { ",
                                                                      "    pm.response.to.have.jsonBody;",
                                                                      "    pm.response.to.have.jsonSchema(",
                                                                      "        {",
                                                                      "            \"code\": \"Number\",",
                                                                      "            \"responseId\": \"String\",",
                                                                      "            \"title\": \"String\",",
                                                                      "            \"items\": [",
                                                                      "                {",
                                                                      "                    \"id\": \"String\",",
                                                                      "                    \"typeName\": \"String\"",
                                                                      "                }",
                                                                      "            ]",
                                                                      "        }",
                                                                      "    );", "});",
                                                                      "",
                                                                      "pm.test(\"Expect response to have six items\", function () { ",
                                                                      "    var jsonData = pm.response.json();",
                                                                      "    pm.expect(jsonData.items.length).to.be.equal(6);",
                                                                      "});"],
                                                                             "type": "text/javascript"}}],
                                   "protocolProfileBehavior": {
                                       "disableBodyPruning": True},
                                   "request": {"auth": {"type": "noauth"},
                                               "method": "GET", "header": [], "url": {
                                           "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items?ignore=postman",
                                           "protocol": "https",
                                           "host": ["{{CUSTOMER}}", "{{ENVIRONMENT}}",
                                                    "thefilter", "com"],
                                           "path": ["v0", "slots",
                                                    "108d571f-8c52-4e9e-82ff-0fdc40e03ae6",
                                                    "items"], "query": [
                                               {"key": "ignore", "value": "postman"}]}},
                                   "response": []}]}, {"name": "Recommended For You",
                                                       "event": [{"listen": "test",
                                                                  "script": {"exec": [
                                                                      "pm.test(\"Status Code is success\", function () {",
                                                                      "    pm.response.to.be.success;",
                                                                      "})", "",
                                                                      "pm.test(\"Check response schema\", function () { ",
                                                                      "    pm.response.to.have.jsonBody;",
                                                                      "    pm.response.to.have.jsonSchema(",
                                                                      "        {",
                                                                      "            \"code\": \"Number\",",
                                                                      "            \"responseId\": \"String\",",
                                                                      "            \"title\": \"String\",",
                                                                      "            \"items\": [",
                                                                      "                {",
                                                                      "                    \"id\": \"String\",",
                                                                      "                    \"typeName\": \"String\"",
                                                                      "                }",
                                                                      "            ]",
                                                                      "        }",
                                                                      "    );", "});",
                                                                      "",
                                                                      "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                      "    var jsonData = pm.response.json();",
                                                                      "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                      "});"],
                                                                             "type": "text/javascript"}}],
                                                       "protocolProfileBehavior": {
                                                           "disableBodyPruning": True},
                                                       "request": {"method": "GET",
                                                                   "header": [], "url": {
                                                               "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                               "protocol": "https",
                                                               "host": ["{{CUSTOMER}}",
                                                                        "{{ENVIRONMENT}}",
                                                                        "thefilter",
                                                                        "com"],
                                                               "path": ["v0", "slots",
                                                                        "38846955-c411-4d1a-9de5-8331feabf512",
                                                                        "items"]}},
                                                       "response": []},
                                       {"name": "More Like This", "event": [
                                           {"listen": "test", "script": {"exec": [
                                               "pm.test(\"Status Code is success\", function () {",
                                               "    pm.response.to.be.success;", "})",
                                               "",
                                               "pm.test(\"Check response schema\", function () { ",
                                               "    pm.response.to.have.jsonBody;",
                                               "    pm.response.to.have.jsonSchema(",
                                               "        {",
                                               "            \"code\": \"Number\",",
                                               "            \"responseId\": \"String\",",
                                               "            \"title\": \"String\",",
                                               "            \"items\": [",
                                               "                {",
                                               "                    \"id\": \"String\",",
                                               "                    \"typeName\": \"String\"",
                                               "                }", "            ]",
                                               "        }", "    );", "});", "",
                                               "pm.test(\"Expect response to have multiple items\", function () { ",
                                               "    var jsonData = pm.response.json();",
                                               "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                               "});"], "type": "text/javascript"}}],
                                        "protocolProfileBehavior": {
                                            "disableBodyPruning": True},
                                        "request": {"method": "GET", "header": [],
                                                    "url": {
                                                        "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                        "protocol": "https",
                                                        "host": ["{{CUSTOMER}}",
                                                                 "{{ENVIRONMENT}}",
                                                                 "thefilter", "com"],
                                                        "path": ["v0", "slots",
                                                                 "40c54b9e-bf21-42fb-9b3e-369408687a00",
                                                                 "items"]}},
                                        "response": []}, {"name": "Because You Watched",
                                                          "event": [{"listen": "test",
                                                                     "script": {"exec": [
                                                                         "pm.test(\"Status Code is success\", function () {",
                                                                         "    pm.response.to.be.success;",
                                                                         "})", "",
                                                                         "pm.test(\"Check response schema\", function () { ",
                                                                         "    pm.response.to.have.jsonBody;",
                                                                         "    pm.response.to.have.jsonSchema(",
                                                                         "        {",
                                                                         "            \"code\": \"Number\",",
                                                                         "            \"responseId\": \"String\",",
                                                                         "            \"title\": \"String\",",
                                                                         "            \"items\": [",
                                                                         "                {",
                                                                         "                    \"id\": \"String\",",
                                                                         "                    \"typeName\": \"String\"",
                                                                         "                }",
                                                                         "            ]",
                                                                         "        }",
                                                                         "    );", "});",
                                                                         "",
                                                                         "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                         "    var jsonData = pm.response.json();",
                                                                         "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                         "});"],
                                                                                "type": "text/javascript"}}],
                                                          "protocolProfileBehavior": {
                                                              "disableBodyPruning": True},
                                                          "request": {"method": "GET",
                                                                      "header": [],
                                                                      "url": {
                                                                          "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                                          "protocol": "https",
                                                                          "host": [
                                                                              "{{CUSTOMER}}",
                                                                              "{{ENVIRONMENT}}",
                                                                              "thefilter",
                                                                              "com"],
                                                                          "path": ["v0",
                                                                                   "slots",
                                                                                   "2fd16464-72ff-4a1f-9a91-34be1f1d8708",
                                                                                   "items"]}},
                                                          "response": []},
                                       {"name": "Latest", "event": [{"listen": "test",
                                                                     "script": {"exec": [
                                                                         "pm.test(\"Status Code is success\", function () {",
                                                                         "    pm.response.to.be.success;",
                                                                         "})", "",
                                                                         "pm.test(\"Check response schema\", function () { ",
                                                                         "    pm.response.to.have.jsonBody;",
                                                                         "    pm.response.to.have.jsonSchema(",
                                                                         "        {",
                                                                         "            \"code\": \"Number\",",
                                                                         "            \"responseId\": \"String\",",
                                                                         "            \"title\": \"String\",",
                                                                         "            \"items\": [",
                                                                         "                {",
                                                                         "                    \"id\": \"String\",",
                                                                         "                    \"typeName\": \"String\"",
                                                                         "                }",
                                                                         "            ]",
                                                                         "        }",
                                                                         "    );", "});",
                                                                         "",
                                                                         "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                         "    var jsonData = pm.response.json();",
                                                                         "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                         "});"],
                                                                                "type": "text/javascript"}}],
                                        "protocolProfileBehavior": {
                                            "disableBodyPruning": True},
                                        "request": {"method": "GET", "header": [],
                                                    "url": {
                                                        "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                        "protocol": "https",
                                                        "host": ["{{CUSTOMER}}",
                                                                 "{{ENVIRONMENT}}",
                                                                 "thefilter", "com"],
                                                        "path": ["v0", "slots",
                                                                 "3918c337-0edb-4261-a798-8089acf093b0",
                                                                 "items"]}},
                                        "response": []}, {"name": "Most Popular 1d",
                                                          "event": [{"listen": "test",
                                                                     "script": {"exec": [
                                                                         "pm.test(\"Status Code is success\", function () {",
                                                                         "    pm.response.to.be.success;",
                                                                         "})", "",
                                                                         "pm.test(\"Check response schema\", function () { ",
                                                                         "    pm.response.to.have.jsonBody;",
                                                                         "    pm.response.to.have.jsonSchema(",
                                                                         "        {",
                                                                         "            \"code\": \"Number\",",
                                                                         "            \"responseId\": \"String\",",
                                                                         "            \"title\": \"String\",",
                                                                         "            \"items\": [",
                                                                         "                {",
                                                                         "                    \"id\": \"String\",",
                                                                         "                    \"typeName\": \"String\"",
                                                                         "                }",
                                                                         "            ]",
                                                                         "        }",
                                                                         "    );", "});",
                                                                         "",
                                                                         "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                         "    var jsonData = pm.response.json();",
                                                                         "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                         "});"],
                                                                                "type": "text/javascript"}}],
                                                          "protocolProfileBehavior": {
                                                              "disableBodyPruning": True},
                                                          "request": {"method": "GET",
                                                                      "header": [],
                                                                      "url": {
                                                                          "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                                          "protocol": "https",
                                                                          "host": [
                                                                              "{{CUSTOMER}}",
                                                                              "{{ENVIRONMENT}}",
                                                                              "thefilter",
                                                                              "com"],
                                                                          "path": ["v0",
                                                                                   "slots",
                                                                                   "ddb2bb09-e9d8-490e-9cf5-1c45d0c1529d",
                                                                                   "items"]}},
                                                          "response": []},
                                       {"name": "Most Popular 7d", "event": [
                                           {"listen": "test", "script": {"exec": [
                                               "pm.test(\"Status Code is success\", function () {",
                                               "    pm.response.to.be.success;", "})",
                                               "",
                                               "pm.test(\"Check response schema\", function () { ",
                                               "    pm.response.to.have.jsonBody;",
                                               "    pm.response.to.have.jsonSchema(",
                                               "        {",
                                               "            \"code\": \"Number\",",
                                               "            \"responseId\": \"String\",",
                                               "            \"title\": \"String\",",
                                               "            \"items\": [",
                                               "                {",
                                               "                    \"id\": \"String\",",
                                               "                    \"typeName\": \"String\"",
                                               "                }", "            ]",
                                               "        }", "    );", "});", "",
                                               "pm.test(\"Expect response to have multiple items\", function () { ",
                                               "    var jsonData = pm.response.json();",
                                               "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                               "});"], "type": "text/javascript"}}],
                                        "protocolProfileBehavior": {
                                            "disableBodyPruning": True},
                                        "request": {"method": "GET", "header": [],
                                                    "url": {
                                                        "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                        "protocol": "https",
                                                        "host": ["{{CUSTOMER}}",
                                                                 "{{ENVIRONMENT}}",
                                                                 "thefilter", "com"],
                                                        "path": ["v0", "slots",
                                                                 "fd7f7338-e2b4-456e-ba80-7962cdd50f4c",
                                                                 "items"]}},
                                        "response": []}, {"name": "Most Popular 14d",
                                                          "event": [{"listen": "test",
                                                                     "script": {"exec": [
                                                                         "pm.test(\"Status Code is success\", function () {",
                                                                         "    pm.response.to.be.success;",
                                                                         "})", "",
                                                                         "pm.test(\"Check response schema\", function () { ",
                                                                         "    pm.response.to.have.jsonBody;",
                                                                         "    pm.response.to.have.jsonSchema(",
                                                                         "        {",
                                                                         "            \"code\": \"Number\",",
                                                                         "            \"responseId\": \"String\",",
                                                                         "            \"title\": \"String\",",
                                                                         "            \"items\": [",
                                                                         "                {",
                                                                         "                    \"id\": \"String\",",
                                                                         "                    \"typeName\": \"String\"",
                                                                         "                }",
                                                                         "            ]",
                                                                         "        }",
                                                                         "    );", "});",
                                                                         "",
                                                                         "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                         "    var jsonData = pm.response.json();",
                                                                         "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                         "});"],
                                                                                "type": "text/javascript"}}],
                                                          "protocolProfileBehavior": {
                                                              "disableBodyPruning": True},
                                                          "request": {"method": "GET",
                                                                      "header": [],
                                                                      "url": {
                                                                          "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                                          "protocol": "https",
                                                                          "host": [
                                                                              "{{CUSTOMER}}",
                                                                              "{{ENVIRONMENT}}",
                                                                              "thefilter",
                                                                              "com"],
                                                                          "path": ["v0",
                                                                                   "slots",
                                                                                   "33d5cbe7-1c9a-47fb-88b8-75f502f96fa6",
                                                                                   "items"]}},
                                                          "response": []},
                                       {"name": "Most Popular 30d", "event": [
                                           {"listen": "test", "script": {"exec": [
                                               "pm.test(\"Status Code is success\", function () {",
                                               "    pm.response.to.be.success;", "})",
                                               "",
                                               "pm.test(\"Check response schema\", function () { ",
                                               "    pm.response.to.have.jsonBody;",
                                               "    pm.response.to.have.jsonSchema(",
                                               "        {",
                                               "            \"code\": \"Number\",",
                                               "            \"responseId\": \"String\",",
                                               "            \"title\": \"String\",",
                                               "            \"items\": [",
                                               "                {",
                                               "                    \"id\": \"String\",",
                                               "                    \"typeName\": \"String\"",
                                               "                }", "            ]",
                                               "        }", "    );", "});", "",
                                               "pm.test(\"Expect response to have multiple items\", function () { ",
                                               "    var jsonData = pm.response.json();",
                                               "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                               "});"], "type": "text/javascript"}}],
                                        "protocolProfileBehavior": {
                                            "disableBodyPruning": True},
                                        "request": {"method": "GET", "header": [],
                                                    "url": {
                                                        "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                        "protocol": "https",
                                                        "host": ["{{CUSTOMER}}",
                                                                 "{{ENVIRONMENT}}",
                                                                 "thefilter", "com"],
                                                        "path": ["v0", "slots",
                                                                 "9191495e-3e39-4759-9266-43dd6cca2db2",
                                                                 "items"]}},
                                        "response": []}, {"name": "Trending", "event": [
                    {"listen": "test", "script": {
                        "exec": ["pm.test(\"Status Code is success\", function () {",
                                 "    pm.response.to.be.success;", "})", "",
                                 "pm.test(\"Check response schema\", function () { ",
                                 "    pm.response.to.have.jsonBody;",
                                 "    pm.response.to.have.jsonSchema(", "        {",
                                 "            \"code\": \"Number\",",
                                 "            \"responseId\": \"String\",",
                                 "            \"title\": \"String\",",
                                 "            \"items\": [", "                {",
                                 "                    \"id\": \"String\",",
                                 "                    \"typeName\": \"String\"",
                                 "                }", "            ]", "        }",
                                 "    );", "});", "",
                                 "pm.test(\"Expect response to have multiple items\", function () { ",
                                 "    var jsonData = pm.response.json();",
                                 "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                 "});"], "type": "text/javascript"}}],
                                                          "protocolProfileBehavior": {
                                                              "disableBodyPruning": True},
                                                          "request": {"method": "GET",
                                                                      "header": [],
                                                                      "url": {
                                                                          "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                                          "protocol": "https",
                                                                          "host": [
                                                                              "{{CUSTOMER}}",
                                                                              "{{ENVIRONMENT}}",
                                                                              "thefilter",
                                                                              "com"],
                                                                          "path": ["v0",
                                                                                   "slots",
                                                                                   "0ae3c401-f812-4ed8-bbce-ccc69ef36b96",
                                                                                   "items"]}},
                                                          "response": []},
                                       {"name": "Search", "event": [{"listen": "test",
                                                                     "script": {"exec": [
                                                                         "pm.test(\"Status Code is success\", function () {",
                                                                         "    pm.response.to.be.success;",
                                                                         "})", "",
                                                                         "pm.test(\"Check response schema\", function () { ",
                                                                         "    pm.response.to.have.jsonBody;",
                                                                         "    pm.response.to.have.jsonSchema(",
                                                                         "        {",
                                                                         "            \"code\": \"Number\",",
                                                                         "            \"responseId\": \"String\",",
                                                                         "            \"title\": \"String\",",
                                                                         "            \"items\": [",
                                                                         "                {",
                                                                         "                    \"id\": \"String\",",
                                                                         "                    \"typeName\": \"String\"",
                                                                         "                }",
                                                                         "            ]",
                                                                         "        }",
                                                                         "    );", "});",
                                                                         "",
                                                                         "pm.test(\"Expect response to have multiple items\", function () { ",
                                                                         "    var jsonData = pm.response.json();",
                                                                         "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                                                         "});"],
                                                                                "type": "text/javascript"}}],
                                        "protocolProfileBehavior": {
                                            "disableBodyPruning": True},
                                        "request": {"method": "GET", "header": [],
                                                    "url": {
                                                        "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                        "protocol": "https",
                                                        "host": ["{{CUSTOMER}}",
                                                                 "{{ENVIRONMENT}}",
                                                                 "thefilter", "com"],
                                                        "path": ["v0", "slots",
                                                                 "6805a013-a931-4f8f-8fc7-9171183bf783",
                                                                 "items"]}},
                                        "response": []}, {"name": "Browse", "event": [
                    {"listen": "test", "script": {
                        "exec": ["pm.test(\"Status Code is success\", function () {",
                                 "    pm.response.to.be.success;", "})", "",
                                 "pm.test(\"Check response schema\", function () { ",
                                 "    pm.response.to.have.jsonBody;",
                                 "    pm.response.to.have.jsonSchema(", "        {",
                                 "            \"code\": \"Number\",",
                                 "            \"responseId\": \"String\",",
                                 "            \"title\": \"String\",",
                                 "            \"items\": [", "                {",
                                 "                    \"id\": \"String\",",
                                 "                    \"typeName\": \"String\"",
                                 "                }", "            ]", "        }",
                                 "    );", "});", "",
                                 "pm.test(\"Expect response to have multiple items\", function () { ",
                                 "    var jsonData = pm.response.json();",
                                 "    pm.expect(jsonData.items.length).to.be.greaterThan(0);",
                                 "});"], "type": "text/javascript"}}],
                                                          "protocolProfileBehavior": {
                                                              "disableBodyPruning": True},
                                                          "request": {"method": "GET",
                                                                      "header": [],
                                                                      "url": {
                                                                          "raw": "https://{{CUSTOMER}}.{{ENVIRONMENT}}.thefilter.com/v0/slots/" + f"{mock.ANY}" + "/items",
                                                                          "protocol": "https",
                                                                          "host": [
                                                                              "{{CUSTOMER}}",
                                                                              "{{ENVIRONMENT}}",
                                                                              "thefilter",
                                                                              "com"],
                                                                          "path": ["v0",
                                                                                   "slots",
                                                                                   "7bcf4a65-5b8f-4a9f-b550-f3457b3ba359",
                                                                                   "items"]}},
                                                          "response": []}]}]}
