from thefilter.inference.logic.configuration import InvocationItem

test_data_model_results_adult_content = [
    InvocationItem(
        thing={
            'id': 'b7a167e3-b028-4689-8fda-312ad6dbbf4a',
            'brandId': 'b7a167e3-b028-4689-8fda-312ad6dbbf4a', 'recommendationId': None,
            'name': '<PERSON> Boobs', 'alternateName': 'Big Boobs', 'typeName': 'Movie',
            'genre': [{'name': 'Stars', 'id': None}, {'name': 'Anal', 'id': None},
                      {'name': '<PERSON> Boobs', 'id': None}], 'subGenre': [],
            'contentRating': [{'name': '18', 'id': None}], 'actor': [
                {'name': '<PERSON><PERSON><PERSON>', 'id': '93274e72-7c5b-3fca-b500-2dfb8f701aa4'},
                {'name': '<PERSON><PERSON>', 'id': '3c53bfc0-3a9a-3ad8-a126-87405ffb7160'},
                {'name': '<PERSON>', 'id': 'c7ae720f-4ae2-31fe-bac7-f06515dbd9f3'},
                {'name': '<PERSON> Love', 'id': 'ca191776-7809-3f6f-93d5-f94e7280812a'},
                {'name': 'Stella Cox', 'id': 'ca2000b6-7eb8-3f5b-b46f-1d9d1ee09baf'},
                {'name': 'Alberto Blanco', 'id': 'fadfb4b8-9044-3b79-b4ce-f30b6ad24115'},
                {'name': 'Juan Lucho', 'id': '2352f4ee-1db8-3a2d-ab4d-d72c1c438bab'},
                {'name': 'Joel Tomas', 'id': '1e5c0fb2-8f29-340c-9b39-d5bbbd4035e7'},
                {'name': 'Emilio Ardana', 'id': 'ec96e61e-34d7-349f-8711-fa1fd0427ad9'},
                {'name': 'Marc Rose', 'id': 'c009c12a-884c-3638-9acc-c3cbc681eb69'}],
            'director': [
                {'name': 'Xavi Rocka', 'id': 'bea68dd3-3dd2-3dad-bca0-7e7eb1040614'}],
            'producer': [], 'crew': [], 'partOfSeason': {}, 'partOfSeries': {},
            'publication': [
                {'notes': None, 'endDate': '2024-02-29T23:00:00+00:00', 'custom': [],
                 'name': 'VOD', 'publishedOn': [], 'location': None, 'id': None,
                 'startDate': '2020-02-29T23:00:00+00:00'}],
            'keywords': ['stars', 'anal', 'big boobs'], 'episodeNumber': '',
            'seasonNumber': '', 'numberOfSeasons': None,
            'description': "Is there anything better than a big set of tits? Well buckle up and enjoy because they are provided in full in Private's all new movie Big Boobs! Of course the likes of Stella Cox, Lucia Love & Yasmin Scott got the call up, alongside busty newcummers Courtney Blue & Rachelle Richeley. These sluts put their huge cannons to work with full jiggling action during some wild pussy rides, but their Big Boobs aren't their only gift. No... These girls are blessed with the tightest of asses and the deepest of throats which makes anal and oral a truly wild experience! Not to spoil the ending, but there are a whole lot of cum covered tits to come your way. This boob filled bonanza is a must see for any hardcore Tit-Aholic!",
            'duration': 'PT2H29M', 'datePublished': '1970-01-01T01:01:01+00:00',
            'custom': {'fokusOnType': 'XOD_MOVIE', 'isAdult': True,
                       'activationState': 'PRODUCTION', 'contentOwner': 'Privatenew',
                       'subGenre': [], 'brand': [],
                       'channel': [], 'category': [], 'subCategory': [],
                       'active': {'state': True,
                                  'stateUpdateTimestamp': '2022-09-02T12:33:00+00:00'},
                       'modelInfo': {'modelName': 'search', 'positionInModel': 0}},
            'image': {
                'url': 'https://waoo.tv/client-portal/res/f4272899-85d7-47e5-953d-2cf056dde8d9.jpg'},
            'inLanguage': 'da', 'isLiveBroadcast': None}, position_in_model_result=0),
    InvocationItem(
        thing={
            'id': 'f1bd9a90-0e54-4166-923a-59d84020f4ab',
            'brandId': 'f1bd9a90-0e54-4166-923a-59d84020f4ab', 'recommendationId': None,
            'name': 'Step-Sis Tiny Boobs', 'alternateName': 'Step-Sis Tiny Boobs',
            'typeName': 'CreativeWork', 'genre': [{'name': 'Movie/Drama', 'id': '0x1'}],
            'subGenre': [{'name': 'adult movie/drama', 'id': '0x10x8'}],
            'contentRating': [], 'actor': [], 'director': [], 'producer': [], 'crew': [],
            'partOfSeason': {}, 'partOfSeries': {}, 'publication': [
                {'notes': None, 'endDate': '2022-08-28T01:18:00+00:00', 'custom': [],
                 'name': 'Vivid Red HD', 'publishedOn': [], 'location': None,
                 'id': '7742', 'startDate': '2022-08-28T00:44:00+00:00'},
                {'notes': None, 'endDate': '2022-09-04T01:18:00+00:00', 'custom': [],
                 'name': 'catchup_Vivid Red HD', 'publishedOn': [], 'location': None,
                 'id': 'catchup_7742', 'startDate': '2022-08-28T01:18:00+00:00'}],
            'keywords': ['movie'], 'episodeNumber': '', 'seasonNumber': '',
            'numberOfSeasons': None,
            'description': "The attraction is there but they've fought the desire long enough. An accidental touch or knowing glance gives way to a moment of weakness and these hussies can no longer deny their taboo fantasies.",
            'duration': 'PT34M', 'datePublished': '1970-01-01T01:01:01+00:00',
            'custom': {'fokusOnType': 'EPG_PROGRAM',
                       'technicalInfo': {'lastUpdated': '2022-08-14T08:53:46.668',
                                         'stationId': '20252'},
                       'activationState': 'PRODUCTION', 'isCatchup': True,
                       'isAdult': True, 'subGenre': [],
                       'brand': [], 'channel': [], 'category': [], 'subCategory': [],
                       'active': {'state': True,
                                  'stateUpdateTimestamp': '2022-09-02T12:33:00+00:00'},
                       'modelInfo': {'modelName': 'search', 'positionInModel': 1}},
            'image': {
                'url': 'https://waoo.tv/client-portal/epg/image/.img?programId=736428351&programUuid=f1bd9a90-0e54-4166-923a-59d84020f4ab'},
            'inLanguage': 'en', 'isLiveBroadcast': True}, position_in_model_result=1),
    InvocationItem(
        thing={
            'id': 'dHZzZXJpZXNfQm9vYmE=', 'brandId': 'dHZzZXJpZXNfQm9vYmE=',
            'recommendationId': '1e1ab543-b6ba-4173-af00-115fcdf5ea4a', 'name': 'Booba',
            'alternateName': 'Booba', 'typeName': 'TVSeries',
            'genre': [{'name': 'Booba', 'id': None}], 'subGenre': [],
            'contentRating': [], 'actor': [], 'director': [], 'producer': [], 'crew': [],
            'partOfSeason': {},
            'partOfSeries': {'name': 'Booba', 'id': 'dHZzZXJpZXNfQm9vYmE='},
            'publication': [
                {'notes': None, 'endDate': '2023-11-30T21:55:00+00:00', 'custom': [],
                 'name': 'VOD', 'publishedOn': [], 'location': None, 'id': None,
                 'startDate': '2020-12-01T03:55:00+00:00'}], 'keywords': ['booba'],
            'episodeNumber': '', 'seasonNumber': '', 'numberOfSeasons': None,
            'description': 'Booba har en fest sammen med sin bamseven i et rumskib, når de trodser tyngdeloven.',
            'duration': 'PT4M', 'datePublished': '2021-01-01T00:00:00+00:00',
            'custom': {'fokusOnType': 'XOD_MOVIE', 'isAdult': False,
                       'activationState': 'PRODUCTION', 'contentOwner': 'TV2NEWOD',
                       'subGenre': [], 'brand': [],
                       'channel': [], 'category': [], 'subCategory': [],
                       'active': {'state': True,
                                  'stateUpdateTimestamp': '2022-09-02T12:33:00+00:00'},
                       'modelInfo': {'modelName': 'search', 'positionInModel': 2}},
            'image': {
                'url': 'https://waoo.tv/client-portal/res/4dc0e87f-8bc4-4c30-a8cc-00bd208eb266.jpg'},
            'inLanguage': 'da', 'isLiveBroadcast': None}, position_in_model_result=2),
    InvocationItem(
        thing={
            'id': 'ad0f61a6-19ab-4aaa-a746-1cbdff450067',
            'brandId': 'ad0f61a6-19ab-4aaa-a746-1cbdff450067', 'recommendationId': None,
            'name': "Expedition Amelia: Bob Ballard's Search",
            'alternateName': "Expedition Amelia: Bob Ballard's Search",
            'typeName': 'CreativeWork',
            'genre': [{'name': 'News/Current affairs', 'id': '0x2'}],
            'subGenre': [{'name': 'documentary', 'id': '0x20x3'}], 'contentRating': [],
            'actor': [], 'director': [], 'producer': [], 'crew': [], 'partOfSeason': {},
            'partOfSeries': {}, 'publication': [
                {'notes': None, 'endDate': '2022-08-27T20:00:00+00:00', 'custom': [],
                 'name': 'National Geographic', 'publishedOn': [], 'location': None,
                 'id': '3851', 'startDate': '2022-08-27T19:00:00+00:00'},
                {'notes': None, 'endDate': '2022-09-03T20:00:00+00:00', 'custom': [],
                 'name': 'catchup_National Geographic', 'publishedOn': [],
                 'location': None, 'id': 'catchup_3851',
                 'startDate': '2022-08-27T20:00:00+00:00'}], 'keywords': ['documentary'],
            'episodeNumber': '', 'seasonNumber': '', 'numberOfSeasons': None,
            'description': 'National Geographics Bob Ballard opdagede Titanic. Nu er han på vej til en afsidesliggende Stillehavsø for at opklare endnu et varigt mysterium, Amelia Earharts forsvinden. Den banebrydende kvindelige pilot slog rekorder i luften og brød barrierer for kvinder, da hun blev en af de mest berømte kvinder i historien. Men i 1937 forsvandt hun. Kan Ballard finde hendes fly?',
            'duration': 'PT1H', 'datePublished': '1970-01-01T01:01:01+00:00',
            'custom': {'fokusOnType': 'EPG_PROGRAM',
                       'technicalInfo': {'lastUpdated': '2022-08-14T08:40:48.323',
                                         'stationId': '3591'},
                       'activationState': 'PRODUCTION', 'isCatchup': True,
                       'isAdult': False, 'subGenre': [],
                       'brand': [], 'channel': [], 'category': [], 'subCategory': [],
                       'active': {'state': True,
                                  'stateUpdateTimestamp': '2022-09-02T12:33:00+00:00'},
                       'modelInfo': {'modelName': 'search', 'positionInModel': 4}},
            'image': {
                'url': 'https://waoo.tv/client-portal/epg/image/.img?programId=736406931&programUuid=ad0f61a6-19ab-4aaa-a746-1cbdff450067'},
            'inLanguage': 'da', 'isLiveBroadcast': True}, position_in_model_result=4),
    InvocationItem(
        thing={
            'id': '8e2b5d49-e598-11ea-bda2-3868dd1c8378',
            'brandId': '8e2b5d49-e598-11ea-bda2-3868dd1c8378', 'recommendationId': None,
            'name': 'American Pie 7: The Book of Love',
            'alternateName': 'American Pie 7: The Book of Love', 'typeName': 'Movie',
            'genre': [{'name': 'Komedie', 'id': None}], 'subGenre': [],
            'contentRating': [{'name': '15', 'id': None}], 'actor': [
                {'name': 'Eugene Levy', 'id': '2bcfff84-8a17-393b-91b6-ae969c186896'},
                {'name': 'Bug Hall', 'id': 'e4434f63-bdf1-3885-b665-502fe2d248b0'},
                {'name': 'Brandon Hardesty',
                 'id': 'a32f4839-336d-3d1a-b56e-f6750d83e364'},
                {'name': 'Kevin M Norton',
                 'id': '7baf008e-8c85-3024-9b95-200ae6f13c60'}], 'director': [
                {'name': 'John Putch', 'id': '80b90879-511d-3855-b6d7-f4701391c3c5'}],
            'producer': [], 'crew': [], 'partOfSeason': {}, 'partOfSeries': {},
            'publication': [
                {'notes': None, 'endDate': '2037-12-31T23:00:00+00:00', 'custom': [],
                 'name': 'VOD', 'publishedOn': [], 'location': None, 'id': None,
                 'startDate': '2015-03-29T22:00:00+00:00'}], 'keywords': ['komedie'],
            'episodeNumber': '', 'seasonNumber': '', 'numberOfSeasons': None,
            'description': 'High school-eleverne Rob, Nathan og Lube vil gerne have deres drømmepiger med i seng. En dag finder de på biblioteket en legendarisk håndbog, som måske kan hjælpe dem. Bogen, som er skrevet af nogle tidligere elever, mangler nogle sider, hvilket fører til en hel del skøre overraskelser for alle de involverede parter!',
            'duration': 'PT1H34M', 'datePublished': '2012-01-01T00:00:00+00:00',
            'custom': {'fokusOnType': 'XOD_MOVIE', 'rating': '4.7', 'isAdult': False,
                       'activationState': 'PRODUCTION', 'contentOwner': 'SFAnytimeNy',
                       'subGenre': [], 'brand': [],
                       'channel': [], 'category': [], 'subCategory': [],
                       'active': {'state': True,
                                  'stateUpdateTimestamp': '2022-09-02T12:33:00+00:00'},
                       'modelInfo': {'modelName': 'search', 'positionInModel': 5}},
            'image': {
                'url': 'https://waoo.tv/client-portal/res/2f742c47-b0f4-425e-88a7-5f746a676e53.jpg'},
            'inLanguage': 'da', 'isLiveBroadcast': None}, position_in_model_result=5)
]
