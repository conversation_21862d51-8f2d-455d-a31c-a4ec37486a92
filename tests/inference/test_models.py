import json
from _decimal import Decima<PERSON>
from copy import deepcopy
from typing import List, <PERSON><PERSON>, Optional
from unittest import TestCase

from freezegun import freeze_time

from tests.inference.data.danish_model_results_adult_content import \
    test_data_model_results_adult_content
from tests.inference.data.danish_model_results_deduplication import \
    test_data_model_results_duplicate_name
from thefilter.inference.logic.configuration import InvocationResult, \
    ModelDefinition, \
    Fulfilment, RangeFulfilment, InvocationItem, StubModelDefinition
from thefilter.inference.logic.invokable_models import Model, StubModelFactory, \
    ids_in_list_checker, StubModel
from thefilter.inference.models import RichModelInvoker
from thefilter.logs.tracer import NoOpTracerFactory, TracerFactory
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository
from thefilter.model.schemaorg import Thing
from thefilter.repositories import StubMetadataRepository, UserRepository, \
    MetadataRepository, StorageUserRepository, StubMetadataLiteRepository, \
    MetadataLiteRepository
from thefilter.rules.ruleset import Ruleset
from thefilter.utils import datetime


class ExplicitMetadataRepository(MetadataRepository):

    def get_metadata_hierarchy(self, path: Tuple[str] = ()):
        pass

    def __init__(
            self,
            metadata_responses: dict):
        self.metadata_responses = metadata_responses

    def get_item(self, item_id: str) -> dict:
        pass

    def get_metadata_types(self, path: Tuple[str] = ()) -> dict:
        pass

    def get_items(self, item_ids: List[str]) -> dict:
        return self.metadata_responses

    def get_series_item(self, series_id: str) -> dict:
        pass

    def get_metadata_items(self) -> dict:
        pass


class TestModel(Model):
    def __init__(self,
                 things_to_return: List[dict],
                 customer: str,
                 tracer_factory: TracerFactory,
                 metadata_repository: MetadataRepository,
                 user_repository: UserRepository,
                 metadata_lite_repository: Optional[MetadataLiteRepository] = None,
                 **kwargs
                 ):
        super().__init__(customer=customer, tracer_factory=tracer_factory,
                         metadata_repository=metadata_repository,
                         metadata_lite_repository=metadata_lite_repository,
                         user_repository=user_repository,
                         **kwargs)
        self.things_to_return = things_to_return

    def invoke(
            self,
            model_definition: ModelDefinition,
            request: Request,
            ruleset: Ruleset) -> List[dict]:
        return self.things_to_return


class TestRichModelInvoker(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._tracer_factory = NoOpTracerFactory()
        self._customer = 'test_customer'
        self._metadata_results = {}
        self._invoker_api = self._set_up_invoker_api()

    def _set_up_invoker_api(self, metadata_results: dict = None):
        if metadata_results is None:
            self._metadata_results = {}
        else:
            self._metadata_results = metadata_results

        self._metadata_repo = StubMetadataRepository(self._metadata_results)
        self._metadata_lite_repo = StubMetadataLiteRepository(self._metadata_results)

        test_model = [
            json.loads(Thing(id="test_id_0", typeName="Movie", name='test_name_0',
                             alternateName="alternateName_a").to_json()),
            json.loads(Thing(id="test_id_1", typeName="Movie", name='test_name_1',
                             alternateName="alternateName_b").to_json())
        ]

        test_model_override = [
            json.loads(Thing(id="test_id_2", typeName="Movie", name='test_name_2',
                             alternateName="alternateName_c").to_json()),
            json.loads(Thing(id="test_id_3", typeName="Movie", name='test_name_3',
                             alternateName="alternateName_d").to_json())
        ]

        invoker = RichModelInvoker(
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=self._metadata_repo,
            metadata_lite_repository=self._metadata_lite_repo,
            model_factory=StubModelFactory(
                {
                    "test_model": TestModel(
                        things_to_return=test_model,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=self._metadata_repo,
                        user_repository=StorageUserRepository([])
                    ),
                    "test_model_override": TestModel(
                        things_to_return=test_model_override,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=self._metadata_repo,
                        user_repository=StorageUserRepository([])
                    )
                }
            ),
            chart_read_repository=StubChartReadRepository()
        )
        return invoker

    def test_combine_invocation_results(self):
        """
        Testing the influence of the request.size & the model.fulfilment.range.end
        on the overall size and the ability for the _combine_invocation_results
        method to make a single InvocationResult out of a list of multiple.

        """
        request = Request(
            size=5
        )

        model_result_0 = InvocationResult(
            model=ModelDefinition(
                key='rfy_version_20210930_hero',
                version=1,
                source='PrebuiltModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=3)]),
                parameters={'useSeedId': True, 'deduplicate_path': ''},
                block=None, key_strategy={}
            ),
            model_results=[
                InvocationItem(
                    thing={
                        'id': 'id_0',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 0,
                            }
                        }
                    },
                    position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'id_1',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 1,
                            }
                        }
                    },
                    position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'id_2',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 2,
                            }
                        }
                    },
                    position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'id_3',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 3,
                            }
                        }
                    },
                    position_in_model_result=0),
            ],
            model_info=None
        )

        model_result_1 = InvocationResult(
            model=ModelDefinition(key='otherkey', version=1,
                                  source='PrebuiltModel', fulfilment=Fulfilment(
                    ranges=[RangeFulfilment(start=0, end=2)]),
                                  parameters={'useSeedId': True, 'deduplicate_path': ''},
                                  block=None, key_strategy={}),
            model_results=[InvocationItem(
                thing={'id': 'a', 'typeName': 'TVSeries', 'alg': 'p', 'custom': {
                    'modelInfo': {'modelName': 'rfy_version_20210930_hero',
                                  'positionInModel': 0,
                                  'positionOverall': 2}},
                       'promotion': {
                           'promotionId': '788d041b-3035-421e-af6d-7c5304128dd2',
                           'promotionName': 'Test'}}, position_in_model_result=0),
                InvocationItem(
                    thing={'id': 'b', 'typeName': 'TVSeries', 'alg': 'p',
                           'custom': {
                               'modelInfo': {'modelName': 'rfy_version_20210930_hero',
                                             'positionInModel': 1,
                                             'positionOverall': 3}}},
                    position_in_model_result=1), InvocationItem(
                    thing={'id': 'c', 'typeName': 'TVSeries', 'alg': 'p', 'custom': {
                        'modelInfo': {'modelName': 'rfy_version_20210930_hero',
                                      'positionInModel': 2, 'positionOverall': 4}}},
                    position_in_model_result=2)], model_info=None
        )

        all_model_invocation_results = [model_result_1] + [model_result_0]

        invoker = RichModelInvoker(
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=StubMetadataRepository(),
            metadata_lite_repository=StubMetadataLiteRepository(),
            model_factory=StubModelFactory({
                "test_for_you": TestModel(
                    things_to_return=[],
                    customer=self._customer,
                    tracer_factory=self._tracer_factory,
                    metadata_repository=StubMetadataRepository(),
                    user_repository=StorageUserRepository([])
                ),
            }),
            chart_read_repository=StubChartReadRepository()
        )
        results = \
            invoker._combine_invocation_results(request, all_model_invocation_results)

        expected_result_size = 5
        self.assertEqual(expected_result_size, len(results.model_results))

        expected_invocation_result = InvocationResult(
            model=ModelDefinition(key='otherkey', version=1, source='PrebuiltModel',
                                  fulfilment=Fulfilment(
                                      ranges=[RangeFulfilment(start=0, end=2)]),
                                  parameters={'useSeedId': True,
                                              'deduplicate_path': ''}, block=None,
                                  key_strategy={}), model_results=[InvocationItem(
                thing={'id': 'a', 'typeName': 'TVSeries', 'alg': 'p', 'custom': {
                    'modelInfo': {'modelName': 'rfy_version_20210930_hero',
                                  'positionInModel': 0, 'positionOverall': 2}},
                       'promotion': {
                           'promotionId': '788d041b-3035-421e-af6d-7c5304128dd2',
                           'promotionName': 'Test'}}, position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'b',
                        'typeName': 'TVSeries',
                        'alg': 'p',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 1,
                                'positionOverall': 3}}},
                    position_in_model_result=1),
                InvocationItem(
                    thing={
                        'id': 'id_0',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 0}}},
                    position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'id_1',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 1}}},
                    position_in_model_result=0),
                InvocationItem(
                    thing={
                        'id': 'id_2',
                        'custom': {
                            'modelInfo': {
                                'modelName': 'rfy_version_20210930_hero',
                                'positionInModel': 2}}},
                    position_in_model_result=0)],
            model_info={}, is_empty=False)

        self.assertEqual(expected_invocation_result, results)

    def assert_id_at_position_with_attribution(
            self,
            actual_result: InvocationResult,
            actual_item: InvocationItem,
            expected_item: dict,
            expected_attribution: ModelDefinition):
        expected_id = expected_item["id"]
        actual_id = actual_item.thing["id"]
        actual_attribution = actual_item.thing['custom']['modelInfo']['modelName']
        self.assertEqual(
            expected_id,
            actual_item.thing["id"],
            f"The wrong item was found. We wanted "
            f"{expected_id} but got {actual_id}."
        )

        self.assertEqual(
            expected_attribution.key,
            actual_attribution,
            f"We got the right item, but the model it came from was "
            f"{actual_attribution} instead of "
            f"{expected_attribution.key}."
        )

        self.assertEqual(
            expected_attribution.version,
            actual_result.model.version,
            f"We got the right item from the right model, but we expected "
            f"it to come from version {expected_attribution.version}. It "
            f"actually came from version "
            f"{actual_result.model.version}."
        )

    # def test_invoker_full_mixed_response(self):
    #     for_you_model = ModelDefinition(
    #         "foryou_haxxed",
    #         1,
    #         "test_for_you",
    #         Fulfilment(
    #             [
    #                 RangeFulfilment(0, 1)
    #             ]
    #         ),
    #         parameters={},
    #         block=None,
    #         key_strategy={}
    #     )
    #
    #     for_you_items = [
    #         # Since item a is the first item to be returned from both this
    #         # model and the popular model, the higher priority for you will
    #         # get to return it, preventing the popular model from doing the
    #         # same.
    #         json.loads(Thing(id="a", typeName="episode").to_json()),
    #         json.loads(Thing(id="b", typeName="episode").to_json()),
    #         # Since only 2 items are being included from for you, item c being
    #         # here should not prevent it being returned in the popular model.
    #         json.loads(Thing(id="c", typeName="episode").to_json()),
    #         json.loads(Thing(id="d", typeName="episode").to_json())
    #     ]
    #
    #     popular_model = ModelDefinition(
    #         "popular_haxxed",
    #         1,
    #         "test_popular",
    #         Fulfilment(
    #             [
    #                 RangeFulfilment(3, 7)
    #             ]
    #         ),
    #         parameters={},
    #         block=None,
    #         key_strategy={}
    #     )
    #
    #     popular_items = [
    #         json.loads(Thing(id="a", typeName="episode").to_json()),
    #         json.loads(Thing(id="c", typeName="episode").to_json()),
    #         json.loads(Thing(id="e", typeName="episode").to_json()),
    #         json.loads(Thing(id="g", typeName="episode").to_json()),
    #     ]
    #
    #     backfill_model = ModelDefinition(
    #         "backfill_haxxed",
    #         1,
    #         "test_backfill",
    #         Fulfilment(
    #             [
    #                 RangeFulfilment(0, 100)
    #             ]
    #         ),
    #         parameters={},
    #         block=None,
    #         key_strategy={}
    #     )
    #
    #     backfill_items = [
    #         json.loads(Thing(id=f"{i}_backfill", typeName="episode").to_json())
    #         for i in range(0, 50)
    #     ]
    #
    #     invoker = RichModelInvoker(
    #         tracer_factory=NoOpTracerFactory(),
    #         metadata_repository=ExplicitMetadataRepository({}),
    #         model_factory=StubModelFactory(
    #             {
    #                 "test_for_you":
    #                     TestModel(for_you_items, self._customer, self._tracer_factory),
    #                 "test_popular":
    #                     TestModel(popular_items, self._customer, self._tracer_factory),
    #                 "test_backfill":
    #                     TestModel(backfill_items, self._customer, self._tracer_factory)
    #             }
    #         )
    #     )
    #
    #     invocation_result = invoker.invoke(
    #         [
    #             for_you_model,
    #             popular_model,
    #             backfill_model
    #         ],
    #         Request(size=51),
    #         Ruleset("test", 50, [])
    #     )
    #
    #     self.assertEqual(50, len(invocation_result.test_data_model_results_duplicate_name))
    #     self.assertTrue(all(invocation_result.test_data_model_results_duplicate_name))
    #
    #     for_you_results = [
    #         invocation_result.test_data_model_results_duplicate_name[0],
    #         invocation_result.test_data_model_results_duplicate_name[1]
    #     ]
    #
    #     popular_results = [
    #         invocation_result.test_data_model_results_duplicate_name[3],
    #         invocation_result.test_data_model_results_duplicate_name[4],
    #         invocation_result.test_data_model_results_duplicate_name[5],
    #     ]
    #
    #     # The backfill model has a gap to fill at position 2, then from
    #     # position 6 onwards.
    #     backfill_results = [invocation_result.test_data_model_results_duplicate_name[2]] + [
    #         result for result in invocation_result.test_data_model_results_duplicate_name[6:]
    #     ]
    #
    #     for i in range(0, len(for_you_results)):
    #         self.assert_id_at_position_with_attribution(
    #             actual_item=for_you_results[i],
    #             actual_result=invocation_result,
    #             expected_item=for_you_items[i],
    #             expected_attribution=for_you_model
    #         )
    #
    #     for i in range(0, len(popular_results)):
    #         self.assert_id_at_position_with_attribution(
    #             actual_item=popular_results[i],
    #             actual_result=invocation_result,
    #             # Skip the first possible popular item, since the higher
    #             # priority for you model will have already claimed it.
    #             expected_item=popular_items[1:][i],
    #             expected_attribution=popular_model
    #         )
    #
    #     for i in range(0, len(backfill_results)):
    #         self.assert_id_at_position_with_attribution(
    #             actual_item=backfill_results[i],
    #             actual_result=invocation_result,
    #             expected_item=backfill_items[i],
    #             expected_attribution=backfill_model
    #         )

    def test_invoker_partial_response(self):
        backfill_model = ModelDefinition(
            "backfill_haxxed",
            1,
            "test_backfill",
            Fulfilment(
                [
                    RangeFulfilment(0, 100)
                ]
            ),
            parameters={},
            block=None,
            key_strategy={}
        )

        backfill_items = [
            json.loads(Thing(id=f"{i}_backfill", typeName="episode").to_json())
            # Our only model will only return 20 results, despite 50 being
            # asked for.
            for i in range(0, 20)
        ]

        backfill_things = {}

        for item in backfill_items:
            backfill_things[item["id"]] = {
                "thing": {
                    "id": item["id"],
                    "typeName": item["typeName"],
                    "custom": {"active": {"state": True}}
                }
            }

        invoker = RichModelInvoker(
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=ExplicitMetadataRepository({}),
            metadata_lite_repository=StubMetadataLiteRepository(backfill_things),
            model_factory=StubModelFactory(
                {
                    "test_backfill": TestModel(
                        things_to_return=backfill_items,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=ExplicitMetadataRepository({}),
                        user_repository=StorageUserRepository([])
                    )
                }
            ),
            chart_read_repository=StubChartReadRepository()
        )

        invocation_result = invoker.invoke(
            [
                backfill_model
            ],
            Request(size=50),
            Ruleset("test", 50, [])
        )

        self.assertEqual(20, len(invocation_result.model_results))

        self.assertTrue(all(invocation_result.model_results))

        for i in range(0, len(invocation_result.model_results)):
            self.assert_id_at_position_with_attribution(
                actual_result=invocation_result,
                actual_item=invocation_result.model_results[i],
                expected_item=backfill_items[i],
                expected_attribution=backfill_model
            )

    def test_invoker_empty_response(self):
        backfill_model = ModelDefinition(
            "backfill_haxxed",
            1,
            "test_backfill",
            Fulfilment(
                [
                    RangeFulfilment(0, 100)
                ]
            ),
            parameters={},
            block=None,
            key_strategy={}
        )

        backfill_items = [
            # Our only model will return nothing, the swine.
        ]

        invoker = RichModelInvoker(
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=ExplicitMetadataRepository({}),
            metadata_lite_repository=StubMetadataLiteRepository(),
            model_factory=StubModelFactory(
                {
                    "test_backfill": TestModel(
                        things_to_return=backfill_items,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=ExplicitMetadataRepository({}),
                        user_repository=StorageUserRepository([])
                    )
                }
            ),
            chart_read_repository=StubChartReadRepository()
        )

        invocation_result = invoker.invoke(
            [
                backfill_model
            ],
            Request(size=50),
            Ruleset("test", 50, [])
        )

        self.assertEqual(0, len(invocation_result.model_results))

    def test_invoker_deduplicate_on_alternate_name(self):
        popular_model = ModelDefinition(
            "popular_haxxed",
            1,
            "test_popular",
            Fulfilment(
                [
                    RangeFulfilment(0, 1)
                ]
            ),
            parameters={},
            block=None,
            key_strategy={}
        )

        popular_items = [
            json.loads(Thing(id="a1", typeName="episode",
                             alternateName="a").to_json()),
            json.loads(Thing(id="b1", typeName="episode",
                             alternateName="b").to_json())
        ]

        backfill_model = ModelDefinition(
            "backfill_haxxed",
            1,
            "test_backfill",
            Fulfilment(
                [
                    RangeFulfilment(0, 100)
                ]
            ),
            parameters={},
            block=None,
            key_strategy={}
        )

        backfill_items = [
            json.loads(Thing(id="a2", typeName="episode",
                             alternateName="a").to_json()),
            json.loads(Thing(id="b2", typeName="episode",
                             alternateName="b").to_json()),
            json.loads(
                Thing(id="c", typeName="episode", alternateName="c").to_json())
        ]

        metadata_lite_items = {}

        for item in popular_items:
            metadata_lite_items[item["id"]] = {
                "thing": {
                    "id": item["id"],
                    "typeName": item["typeName"],
                    "custom": {"active": {"state": True}}
                }
            }

        for item in backfill_items:
            metadata_lite_items[item["id"]] = {
                "thing": {
                    "id": item["id"],
                    "typeName": item["typeName"],
                    "custom": {"active": {"state": True}}
                }
            }

        invoker = RichModelInvoker(
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=ExplicitMetadataRepository({}),
            metadata_lite_repository=StubMetadataLiteRepository(metadata_lite_items),
            model_factory=StubModelFactory(
                {
                    "test_popular": TestModel(
                        things_to_return=popular_items,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=ExplicitMetadataRepository({}),
                        user_repository=StorageUserRepository([])
                    ),
                    "test_backfill": TestModel(
                        things_to_return=backfill_items,
                        customer=self._customer,
                        tracer_factory=self._tracer_factory,
                        metadata_repository=ExplicitMetadataRepository({}),
                        user_repository=StorageUserRepository([])
                    )
                }
            ),
            chart_read_repository=StubChartReadRepository()
        )

        invocation_result = invoker.invoke(
            [
                popular_model,
                backfill_model
            ],
            Request(size=50),
            Ruleset("test", 50, [])
        )

        self.assertEqual(4, len(invocation_result.model_results))

        self.assertTrue(all(invocation_result.model_results))

        model_results = [
            invocation_result.model_results[0],
            invocation_result.model_results[1],
            invocation_result.model_results[2],
            invocation_result.model_results[3]
        ]

        # expected_attribution should come from thing.model_info.model_name
        # not from the invocation_result.model.key, as this can only support one model...
        self.assert_id_at_position_with_attribution(
            actual_item=model_results[0],
            actual_result=invocation_result,
            expected_item=popular_items[0],
            expected_attribution=popular_model
        )
        self.assert_id_at_position_with_attribution(
            actual_item=model_results[1],
            actual_result=invocation_result,
            expected_item=backfill_items[0],
            expected_attribution=backfill_model
        )

        self.assert_id_at_position_with_attribution(
            actual_item=model_results[2],
            actual_result=invocation_result,
            expected_item=backfill_items[1],
            expected_attribution=backfill_model
        )

        self.assert_id_at_position_with_attribution(
            actual_item=model_results[3],
            actual_result=invocation_result,
            expected_item=backfill_items[2],
            expected_attribution=backfill_model
        )

    def test_inner_enforce_rules_ids(self):
        # importing here as it's part of the test condition.
        import numpy as np

        expected = ['1', '2']
        page_of_things = [{"id": '1'}, {"id": '2'}, {"id": np.nan}, {"id": ""}]
        list_of_ids = ids_in_list_checker(page_of_things)

        self.assertEqual(expected, list_of_ids)

    @freeze_time("2022-10-08T14:00:00")
    def test__remove_out_of_window_items_no_removing(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 10, 8, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        initial_invocation_result = \
            InvocationResult(
                model=ModelDefinition(
                    key='test_model',
                    version=1,
                    source='PrebuiltModel',
                    fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                    parameters={'deduplicate_path': 'custom.parentBrandId'},
                    block=None,
                    key_strategy=None),
                model_results=[
                    InvocationItem(thing={
                        'id': 'test_id_0',
                        'typeName': 'Movie',
                        # a publications exists that is in window - so keep item.
                        'publication': [
                            {'endDate': '1999-12-12T10:00:00+00:00',
                             'startDate': '1990-01-09T07:10:00+00:00'},
                            {'endDate': '2037-12-31T23:50:00+00:00',
                             'startDate': '2022-01-13T05:10:00+00:00'}],
                        'custom': {'active': {'state': True}}},
                        position_in_model_result=0)
                ]
            )

        invocation_result = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)

        self.assertEqual(initial_invocation_result, invocation_result)

    def test__remove_out_of_window_items_apply_removal(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model',
                version=1,
                source='PrebuiltModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                parameters={'deduplicate_path': 'custom.parentBrandId'},
                block=None,
                key_strategy=None),
            model_results=[
                InvocationItem(thing={
                    'id': 'test_id_0',
                    'typeName': 'Movie',
                    # these publications are out of window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '3000-12-13T10:00:00+00:00',
                         'startDate': '3000-01-13T05:10:00+00:00'}],
                    'custom': {'active': {'state': True}}},
                    position_in_model_result=0)
            ])
        initial_invocation_result = deepcopy(invocation_result)

        invocation_result = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)

        self.assertEqual([], invocation_result.model_results)

    def test__remove_out_of_window_items_multi_things(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model',
                version=1,
                source='PrebuiltModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                parameters={'deduplicate_path': 'custom.parentBrandId'},
                block=None,
                key_strategy=None),
            model_results=[
                InvocationItem(thing={
                    'id': 'test_id_0',
                    'typeName': 'Movie',
                    # these publications are in window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '2037-12-31T23:50:00+00:00',
                         'startDate': '2022-01-13T05:10:00+00:00'}],
                    "custom": {"active": {"state": True}}},
                    position_in_model_result=0),
                InvocationItem(thing={
                    'id': 'test_id_1',
                    'typeName': 'Movie',
                    # these publications are out of window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '3000-12-13T10:00:00+00:00',
                         'startDate': '3000-01-13T05:10:00+00:00'}],
                    "custom": {"active": {"state": True}}},
                    position_in_model_result=1)
            ])
        initial_invocation_result = deepcopy(invocation_result)

        invocation_result = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)
        expected = [
            InvocationItem(
                thing={'id': 'test_id_0', 'typeName': 'Movie', 'publication': [
                    {'endDate': '1999-12-12T10:00:00+00:00',
                     'startDate': '1990-01-09T07:10:00+00:00'},
                    {'endDate': '2037-12-31T23:50:00+00:00',
                     'startDate': '2022-01-13T05:10:00+00:00'}],
                       'custom': {'active': {'state': True}}},
                position_in_model_result=0)
        ]

        self.assertEqual(expected, invocation_result.model_results)

    def test__bypass_remove_out_of_window_for_autoplay(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        # One item is out of window, but the model type will allow these as results.
        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model',
                version=1,
                source='AutoPlayMLTModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                parameters={'deduplicate_path': ''},
                block=None,
                key_strategy=None),
            model_results=[
                InvocationItem(thing={
                    'id': 'test_id_0',
                    'typeName': 'Movie',
                    # these publications are in window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '2022-12-13T10:00:00+00:00',
                         'startDate': '2022-01-13T05:10:00+00:00'}],
                    'custom': {'active': {'state': True}}},
                    position_in_model_result=0),
                InvocationItem(thing={
                    'id': 'test_id_1',
                    'typeName': 'Movie',
                    # these publications are out of window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '3000-12-13T10:00:00+00:00',
                         'startDate': '3000-01-13T05:10:00+00:00'}],
                    'custom': {'active': {'state': True}}},
                    position_in_model_result=1)
            ])
        initial_invocation_result = deepcopy(invocation_result)

        invocation_result = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)
        expected = [
            InvocationItem(
                thing={'id': 'test_id_0', 'typeName': 'Movie',
                       'publication': [
                           {'endDate': '1999-12-12T10:00:00+00:00',
                            'startDate': '1990-01-09T07:10:00+00:00'},
                           {'endDate': '2022-12-13T10:00:00+00:00',
                            'startDate': '2022-01-13T05:10:00+00:00'}],
                       'custom': {'active': {'state': True}}},
                position_in_model_result=0),
            InvocationItem(
                thing={'id': 'test_id_1', 'typeName': 'Movie', 'publication': [
                    {'endDate': '1999-12-12T10:00:00+00:00',
                     'startDate': '1990-01-09T07:10:00+00:00'},
                    {'endDate': '3000-12-13T10:00:00+00:00',
                     'startDate': '3000-01-13T05:10:00+00:00'}],
                       'custom': {'active': {'state': True}}},
                position_in_model_result=1)
        ]

        self.assertEqual(expected, invocation_result.model_results)

    def test_remove_out_of_window_items_backfill_chart(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model',
                version=1,
                source='PrebuiltModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                parameters={'backfillChart': 'most-popular', 'deduplicate_path': ''},
                block=None,
                key_strategy=None),
            model_results=[
                InvocationItem(thing={
                    'id': 'test_id_0',
                    'typeName': 'Movie',
                    # these publications are in window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '2037-12-31T23:50:00+00:00',
                         'startDate': '2022-01-13T05:10:00+00:00'}],
                    "custom": {"active": {"state": True}}
                },
                    position_in_model_result=0),
                InvocationItem(thing={
                    'id': 'test_id_1',
                    'typeName': 'Movie',
                    # these publications are out of window!
                    'publication': [
                        {'endDate': '1999-12-12T10:00:00+00:00',
                         'startDate': '1990-01-09T07:10:00+00:00'},
                        {'endDate': '3000-12-13T10:00:00+00:00',
                         'startDate': '3000-01-13T05:10:00+00:00'}],
                    "custom": {"active": {"state": True}}
                },
                    position_in_model_result=1)
            ])
        initial_invocation_result = deepcopy(invocation_result)

        invocation_result_1 = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)

        expected = [
            InvocationItem(thing={'id': 'test_id_0', 'typeName': 'Movie',
                                  'publication': [
                                      {'endDate': '1999-12-12T10:00:00+00:00',
                                       'startDate': '1990-01-09T07:10:00+00:00'},
                                      {'endDate': '2037-12-31T23:50:00+00:00',
                                       'startDate': '2022-01-13T05:10:00+00:00'}],
                                  "custom": {"active": {"state": True}}
                                  },
                           position_in_model_result=0),
            InvocationItem(thing={
                'id': 'most_popular_thing_id_0',
                'typeName': 'Chart'
            },
                position_in_model_result=1)
        ]

        self.assertEqual(expected, invocation_result_1.model_results)

        # initial_invocation_result = deepcopy(invocation_result)
        initial_invocation_result.model.parameters = None

        expected = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='PrebuiltModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=11)]),
                parameters=None, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'typeName': 'Movie',
                           'publication': [
                               {'endDate': '1999-12-12T10:00:00+00:00',
                                'startDate': '1990-01-09T07:10:00+00:00'},
                               {'endDate': '2037-12-31T23:50:00+00:00',
                                'startDate': '2022-01-13T05:10:00+00:00'}],
                           "custom": {"active": {"state": True}}
                           },
                    position_in_model_result=0)
            ],
            model_info=None,
            is_empty=False
        )

        invocation_result_2 = \
            self._invoker_api._remove_out_of_window_items(initial_invocation_result,
                                                          request)
        self.assertEqual(expected, invocation_result_2)

    def test__deduplicate_by_name_slovaktelekom(self):

        self._invoker_api._model_factory._dependency_library = \
            {'customer': 'slovaktelekom'}
        # Just reusing Waoo data...
        test_data = deepcopy(test_data_model_results_duplicate_name)
        self.assertEqual(20, len(test_data))
        # Make the zeroeth item's name the same as the first index.
        test_data[0].thing['name'] = 'Biler 2 - dansk tale'

        invocation_result = InvocationResult(
            model=StubModelDefinition(),
            model_results=test_data,
            seed_info={}
        )
        deduplicated = self._invoker_api._deduplicate_by_name(invocation_result)
        self.assertEqual(19, len(deduplicated))

        actual_deduplicated_names = [i.thing['name'] for i in deduplicated]
        expected_deduplicated_names = [
            'Biler 2 - dansk tale', 'Biler 2', 'Toy Story 2', 'Toy Story 2 - dansk tale',
            'Team Hot Wheels: the Orgin of Awesome - dansk tale', 'Bolt',
            'Bolt - dansk tale', 'CarGo', 'Skyllet væk', 'Skyllet væk - dansk tale',
            'Robotter', 'Robotter - dansk tale',
            'Scooby Doo Mystery in Motion - dansk tale',
            'Rooster-Doodle-Do - dansk tale', 'Toy Story - dansk tale', 'Toy Story',
            'Walter og Trofast - Det store grøntsagskup', 'Biler 3',
            'Biler 3 - dansk tale'
        ]

        self.assertEqual(expected_deduplicated_names, actual_deduplicated_names)

    def test_remove_adult_content(self):
        self.assertEqual(5, len(test_data_model_results_adult_content))
        adult_content_removed = self._invoker_api._remove_adult_content(
            test_data_model_results_adult_content
        )
        self.assertEqual(3, len(adult_content_removed))
        self.assertFalse(
            any([i.thing['custom']['isAdult'] for i in adult_content_removed])
        )

    def test_invoke_single_model(self):
        model_definition = ModelDefinition(
            key='test_model', version=1, source='test_model',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]), block=None,
            key_strategy=None, parameters={}
        )
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})
        ruleset = Ruleset(id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=88, tuners=[])

        metadata_results = {
            'test_id_0': {
                'thing': {
                    'id': 'test_id_0',
                    'name': 'test_name_0',
                    'typeName': 'Movie',
                    'custom': {"active": {"state": True}}
                }
            },
            'test_id_1': {
                'thing': {
                    'id': 'test_id_1',
                    'name': 'test_name_1',
                    'typeName': 'Movie',
                    'custom': {"active": {"state": True}}
                }
            },
            'test_id_2': {
                'thing': {
                    'id': 'test_id_2',
                    'name': 'test_name_2',
                    'typeName': 'Movie',
                    'custom': {"active": {"state": True}}
                }
            }
        }

        self._invoker_api = self._set_up_invoker_api(metadata_results)
        actual = \
            self._invoker_api._invoke_single_model(model_definition, request, ruleset,
                                                   'test')

        expected = InvocationResult(
            model=ModelDefinition(key='test_model',
                                  version=1,
                                  source='test_model',
                                  fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0,
                                                                                end=88)]),
                                  parameters={},
                                  block=None,
                                  key_strategy=None),
            model_results=[
                InvocationItem(thing={'custom': {'active': {'state': True},
                                                 'modelInfo': {'modelName': 'test_model',
                                                               'positionInModel': 0},
                                                 'user_history': False},
                                      'id': 'test_id_0',
                                      'name': 'test_name_0',
                                      'typeName': 'Movie'},
                               position_in_model_result=0),
                InvocationItem(thing={'custom': {'active': {'state': True},
                                                 'modelInfo': {'modelName': 'test_model',
                                                               'positionInModel': 1},
                                                 'user_history': False},
                                      'id': 'test_id_1',
                                      'name': 'test_name_1',
                                      'typeName': 'Movie'},
                               position_in_model_result=1)],
            model_info=None,
            seed_info=None,
            is_empty=False)

        self.assertEqual(expected, actual)

    def test_invoke_multiple_model_non_serial(self):
        test_model_definition = ModelDefinition(
            key='test_model', version=1, source='test_model',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]), block=None,
            key_strategy=None, parameters={}
        )

        test_model_override_definition = ModelDefinition(
            key='test_model_override', version=1, source='test_model_override',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]), block=None,
            key_strategy=None, parameters={}
        )

        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=[], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})
        ruleset = Ruleset(id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=88, tuners=[])

        metadata_results = {
            'test_id_0': {
                'thing': {
                    'id': 'test_id_0',
                    'name': 'test_name_0',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}},
                }
            },
            'test_id_1': {
                'thing': {
                    'id': 'test_id_1',
                    'name': 'test_name_1',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}},
                }
            },
            'test_id_2': {
                'thing': {
                    'id': 'test_id_2',
                    'name': 'test_name_2',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}},
                }
            },
            'test_id_3': {
                'thing': {
                    'id': 'test_id_3',
                    'name': 'test_name_3',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}},
                }
            }
        }

        self._invoker_api = self._set_up_invoker_api(metadata_results)

        actual = \
            self._invoker_api.invoke(
                [test_model_definition, test_model_override_definition], request,
                ruleset)

        expected = InvocationResult(
            model=ModelDefinition(
                key='test_model',
                version=1,
                source='test_model',
                fulfilment=Fulfilment(ranges=[
                    RangeFulfilment(start=0,
                                    end=88)]),
                parameters={},
                block=None,
                key_strategy=None),
            model_results=[InvocationItem(
                thing={'custom': {'active': {'state': True},
                                  'modelInfo': {
                                      'modelName': 'test_model',
                                      'positionInModel': 0},
                                  'user_history': False},
                       'id': 'test_id_0',
                       'name': 'test_name_0',
                       'typeName': 'Movie'},
                position_in_model_result=0),
                InvocationItem(thing={'custom': {
                    'active': {'state': True},
                    'modelInfo': {
                        'modelName': 'test_model',
                        'positionInModel': 1},
                    'user_history': False},
                    'id': 'test_id_1',
                    'name': 'test_name_1',
                    'typeName': 'Movie'},
                    position_in_model_result=1),
                InvocationItem(thing={'custom': {
                    'active': {'state': True},
                    'modelInfo': {
                        'modelName': 'test_model_override',
                        'positionInModel': 0},
                    'user_history': False},
                    'id': 'test_id_2',
                    'name': 'test_name_2',
                    'typeName': 'Movie'},
                    position_in_model_result=0),
                InvocationItem(thing={'custom': {
                    'active': {'state': True},
                    'modelInfo': {
                        'modelName': 'test_model_override',
                        'positionInModel': 1},
                    'user_history': False},
                    'id': 'test_id_3',
                    'name': 'test_name_3',
                    'typeName': 'Movie'},
                    position_in_model_result=1)],
            model_info={},
            seed_info=None,
            is_empty=False)
        self.assertEqual(expected, actual)

    def test_invoke_multiple_model_serial(self):
        test_model_definition = ModelDefinition(
            key='test_model', version=1, source='test_model',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]), block=None,
            key_strategy=None, parameters={}
        )

        test_model_override_definition = ModelDefinition(
            key='test_model_override', version=1, source='test_model_override',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]), block=None,
            key_strategy=None, parameters={}
        )

        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=[], query_string={},
            request_start=datetime.datetime(2022, 9, 3, 10, 48, 27, 274161,
                                            tzinfo=datetime.timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})
        ruleset = Ruleset(id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=88, tuners=[])

        metadata_results = {
            'test_id_0': {
                'thing': {
                    'id': 'test_id_0',
                    'name': 'test_name_0',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'test_id_1': {
                'thing': {
                    'id': 'test_id_1',
                    'name': 'test_name_1',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'test_id_2': {
                'thing': {
                    'id': 'test_id_2',
                    'name': 'test_name_2',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'test_id_3': {
                'thing': {
                    'id': 'test_id_3',
                    'name': 'test_name_3',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            }
        }

        self._invoker_api = self._set_up_invoker_api(metadata_results)

        actual = \
            self._invoker_api.invoke(
                [test_model_override_definition, test_model_definition], request,
                ruleset, 'test', True)

        expected = InvocationResult(
            model=ModelDefinition(key='test_model_override',
                                  version=1,
                                  source='test_model_override',
                                  fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0,
                                                                                end=88)]),
                                  parameters={},
                                  block=None,
                                  key_strategy=None),
            model_results=[InvocationItem(thing={'custom': {'active': {'state': True},
                                                            'modelInfo': {
                                                                'modelName': 'test_model_override',
                                                                'positionInModel': 0},
                                                            'user_history': False},
                                                 'id': 'test_id_2',
                                                 'name': 'test_name_2',
                                                 'typeName': 'Movie'},
                                          position_in_model_result=0),
                           InvocationItem(thing={'custom': {'active': {'state': True},
                                                            'modelInfo': {
                                                                'modelName': 'test_model_override',
                                                                'positionInModel': 1},
                                                            'user_history': False},
                                                 'id': 'test_id_3',
                                                 'name': 'test_name_3',
                                                 'typeName': 'Movie'},
                                          position_in_model_result=1)],
            model_info={},
            seed_info=None,
            is_empty=False)
        self.assertEqual(expected, actual)

    def test_stub_model_response_override(self):
        model = StubModel(
            customer=self._customer,
            tracer_factory=self._tracer_factory,
            metadata_repository=StubMetadataRepository({}),
            user_repository=StorageUserRepository([])
        )

        parameters = {
            "responseOverride": [
                {
                    "id": "bW92aWU7MTc3NTU=",
                    "typeName": "Movie"
                },
                {
                    "id": "bW92aWU7MTc1NjI=",
                    "typeName": "Movie"
                }
            ]
        }

        model_definition = ModelDefinition(key='StubModel', version=1,
                                           source='StubModel',
                                           fulfilment=Fulfilment(
                                               ranges=[RangeFulfilment(start=0, end=5)]),
                                           parameters=parameters,
                                           block=None, key_strategy=None)

        actual_results = model.invoke(model_definition, Request(size=50),
                                      Ruleset("test", 50, []))

        expected_results = [
            {'id': 'bW92aWU7MTc3NTU=', 'typeName': 'Movie'},
            {'id': 'bW92aWU7MTc1NjI=', 'typeName': 'Movie'}
        ]

        self.assertEqual(expected_results, actual_results)

    @staticmethod
    def run_user_history_test(model_results: List[dict], metadata_results: dict,
                              user_history: List[dict],
                              model_definition: ModelDefinition,
                              promotions: dict) -> InvocationResult:

        test_model = TestModel(
            things_to_return=model_results,
            tracer_factory=NoOpTracerFactory(),
            customer='test',
            metadata_repository=StubMetadataRepository(metadata_results),
            user_repository=StorageUserRepository(user_history),
        )

        result = test_model.get_invocation_result(
            model_definition,
            Request(size=50, userId='test_user', query_string={}, promotions=promotions),
            Ruleset("test", 50, []),
            'test'
        )

        return result

    @freeze_time("2022-12-12T17:00:00")
    def test_general_user_history_method(self):
        model_results = [
            {'id': 'movie_1', 'typeName': 'Movie'},
            {'id': 'movie_2', 'typeName': 'Movie'},
            {'id': 'movie_3', 'typeName': 'Movie'},
            {'id': 'movie_4', 'typeName': 'Movie'},
        ]

        metadata_results = {
            'movie_1': {
                'thing': {
                    'id': 'movie_1',
                    'name': 'Movie 1',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_2': {
                'thing': {
                    'id': 'movie_2',
                    'name': 'Movie 2',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_3': {
                'thing': {
                    'id': 'movie_3',
                    'name': 'Movie 3',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_4': {
                'thing': {
                    'id': 'movie_4',
                    'name': 'Movie 4',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            }
        }

        user_history = [
            {'action': 'play', 'timestamp': {'initiated': '2022-12-11T16:30:00+00:00'},
             'thing': [{'id': 'movie_1'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-10T12:30:00+00:00'},
             'thing': [{'id': 'movie_2'}]}
        ]

        # No User History Method supplied
        model_definition = ModelDefinition(
            key='TestModel', version=1,
            source='TestModel',
            fulfilment=Fulfilment(
                ranges=[RangeFulfilment(start=0, end=5)]),
            parameters={},
            block=None, key_strategy=None)

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, {})

        expected_results = [
            InvocationItem(thing={'id': 'movie_1', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=1),
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie'},
                           position_in_model_result=2),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=3)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'no' method")

        # With 'keep' method
        model_definition.parameters['historyMethod'] = 'keep'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, {})

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'keep' method")

        # With "remove" method
        model_definition.parameters['historyMethod'] = 'remove'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, {})

        expected_results = [
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'remove' method")

        # With "moveToEnd" method
        model_definition.parameters['historyMethod'] = 'moveToEnd'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, {})

        expected_results = [
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1),
            InvocationItem(
                thing={'id': 'movie_1', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=2),
            InvocationItem(
                thing={'id': 'movie_2', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=3)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'removeIfPossible' method (not possible)")

        # With "removeIfPossible" method (possible):
        model_definition.parameters['historyMethod'] = 'removeIfPossible'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, {})

        expected_results = [
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'removeIfPossible' method")

        # With "removeIfPossible" method (not possible):
        full_user_history = [
            {'action': 'play', 'timestamp': {'initiated': '2022-12-11T16:30:00+00:00'},
             'thing': [{'id': 'movie_1'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-10T12:30:00+00:00'},
             'thing': [{'id': 'movie_2'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-09T12:30:00+00:00'},
             'thing': [{'id': 'movie_3'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-08T12:30:00+00:00'},
             'thing': [{'id': 'movie_4'}]}
        ]

        actual = self.run_user_history_test(model_results, metadata_results,
                                            full_user_history, model_definition, {})

        expected_results = [
            InvocationItem(
                thing={'id': 'movie_1', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=0),
            InvocationItem(
                thing={'id': 'movie_2', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=1),
            InvocationItem(
                thing={'id': 'movie_3', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=2),
            InvocationItem(
                thing={'id': 'movie_4', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=3)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Matching results for 'removeIfPossible' method")

    def test_apply_promotions(self):
        model_results = [
            {'id': 'movie_1', 'typeName': 'Movie'},
            {'id': 'movie_2', 'typeName': 'Movie'},
            {'id': 'movie_3', 'typeName': 'Movie'},
            {'id': 'movie_4', 'typeName': 'Movie'},
        ]

        promotions = {
            'movie_3': {
                'promotionId': 'promo1',
                'promotionName': 'promo1'
            }
        }

        model_definition = ModelDefinition(
            key='TestModel', version=1,
            source='TestModel',
            fulfilment=Fulfilment(
                ranges=[RangeFulfilment(start=0, end=5)]),
            parameters={},
            block=None, key_strategy=None)

        actual = self.run_user_history_test(model_results, {}, [], model_definition,
                                            promotions)

        expected_results = [
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie',
                                  'promotion': {'promotionId': 'promo1',
                                                'promotionName': 'promo1'}},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_1', 'typeName': 'Movie'},
                           position_in_model_result=1),
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=2),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=3)
        ]

        self.assertEqual(actual.model_results, expected_results,
                         "Promotion boosting as expected")

    def test_apply_promotions_and_user_history_methods(self):
        # promotions now occur after all models have been invoked,
        # so no longer apply here!
        model_results = [
            {'id': 'movie_1', 'typeName': 'Movie'},
            {'id': 'movie_2', 'typeName': 'Movie'},
            {'id': 'movie_3', 'typeName': 'Movie'},
            {'id': 'movie_4', 'typeName': 'Movie'},
        ]

        metadata_results = {
            'movie_1': {
                'thing': {
                    'id': 'movie_1',
                    'name': 'Movie 1',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_2': {
                'thing': {
                    'id': 'movie_2',
                    'name': 'Movie 2',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_3': {
                'thing': {
                    'id': 'movie_3',
                    'name': 'Movie 3',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            },
            'movie_4': {
                'thing': {
                    'id': 'movie_4',
                    'name': 'Movie 4',
                    'typeName': 'Movie',
                    "custom": {"active": {"state": True}}
                }
            }
        }

        promotions = {
            'movie_3': {
                'promotionId': 'promo1',
                'promotionName': 'promo1'
            }
        }

        user_history = [
            {'action': 'play', 'timestamp': {'initiated': '2022-12-11T16:30:00+00:00'},
             'thing': [{'id': 'movie_1'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-10T12:30:00+00:00'},
             'thing': [{'id': 'movie_3'}]}
        ]

        # With 'keep' method
        model_definition = ModelDefinition(
            key='TestModel', version=1,
            source='TestModel',
            fulfilment=Fulfilment(
                ranges=[RangeFulfilment(start=0, end=5)]),
            parameters={
                'historyMethod': 'keep'
            },
            block=None, key_strategy=None)

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, promotions)

        expected_results = [
            InvocationItem(thing={'id': 'movie_1', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=1),
            InvocationItem(thing={'id': 'movie_3', 'typeName': 'Movie'},
                           position_in_model_result=2),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=3)
        ]

        self.assertEqual(expected_results, actual.model_results,
                         "Matching results for 'keep' method")

        # With "remove" method
        model_definition.parameters['historyMethod'] = 'remove'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, promotions)

        expected_results = [
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1)
        ]

        self.assertEqual(expected_results, actual.model_results,
                         "Matching results for 'remove' method")

        # With "moveToEnd" method
        model_definition.parameters['historyMethod'] = 'moveToEnd'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, promotions)

        expected_results = [
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1),
            InvocationItem(
                thing={'id': 'movie_1', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=2),
            InvocationItem(
                thing={'id': 'movie_3', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=3)
        ]

        self.assertEqual(expected_results, actual.model_results,
                         "Matching results for 'moveToEnd' method")

        # With "removeIfPossible" method (possible):
        model_definition.parameters['historyMethod'] = 'removeIfPossible'

        actual = self.run_user_history_test(model_results, metadata_results,
                                            user_history, model_definition, promotions)

        expected_results = [
            InvocationItem(thing={'id': 'movie_2', 'typeName': 'Movie'},
                           position_in_model_result=0),
            InvocationItem(thing={'id': 'movie_4', 'typeName': 'Movie'},
                           position_in_model_result=1)
        ]

        self.assertEqual(expected_results, actual.model_results,
                         "Matching results for 'removeIfPossible' method")

        # With "removeIfPossible" method (not possible):
        full_user_history = [
            {'action': 'play', 'timestamp': {'initiated': '2022-12-11T16:30:00+00:00'},
             'thing': [{'id': 'movie_1'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-10T12:30:00+00:00'},
             'thing': [{'id': 'movie_2'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-09T12:30:00+00:00'},
             'thing': [{'id': 'movie_3'}]},
            {'action': 'play', 'timestamp': {'initiated': '2022-12-08T12:30:00+00:00'},
             'thing': [{'id': 'movie_4'}]}
        ]

        actual = self.run_user_history_test(model_results, metadata_results,
                                            full_user_history, model_definition,
                                            promotions)

        expected_results = [
            InvocationItem(
                thing={'id': 'movie_1', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=0),
            InvocationItem(
                thing={'id': 'movie_2', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=1),
            InvocationItem(
                thing={'id': 'movie_3', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=2),
            InvocationItem(
                thing={'id': 'movie_4', 'typeName': 'Movie', 'user_history': True},
                position_in_model_result=3)
        ]

        self.assertEqual(expected_results, actual.model_results,
                         "Matching results for 'removeIfPossible' method")

    def test_apply_promotions(self):
        api = self._set_up_invoker_api()

        model_results = [
            InvocationItem(
                thing={'id': 'thing_id_0'},
                position_in_model_result=0
            ),
            InvocationItem(
                thing={'id': 'thing_id_1'},
                position_in_model_result=1
            ),
            InvocationItem(
                thing={'id': 'thing_id_2'},
                position_in_model_result=2
            )
        ]

        items_to_promote = {
            'thing_id_2': {
                'promotionId': 'test_promotionId_0',
                'promotionName': 'test_promotionName_0'
            },
            'thing_id_1': {
                'promotionId': 'test_promotionId_1',
                'promotionName': 'test_promotionName_1'
            }
        }

        result = api._apply_promotion_boosting(model_results, items_to_promote)

        expected = [
            InvocationItem(
                thing={
                    'id': 'thing_id_2',
                    'promotion': {'promotionId': 'test_promotionId_0',
                                  'promotionName': 'test_promotionName_0'}
                },
                position_in_model_result=2
            ),
            InvocationItem(
                thing={
                    'id': 'thing_id_1',
                    'promotion': {
                        'promotionId': 'test_promotionId_1',
                        'promotionName': 'test_promotionName_1'}
                },
                position_in_model_result=1
            ),
            InvocationItem(
                thing={
                    'id': 'thing_id_0'
                },
                position_in_model_result=0
            )
        ]

        self.assertEqual(expected, result)

    def test_apply_promotions_but_with_user_history(self):
        api = self._set_up_invoker_api()

        model_results = [
            InvocationItem(
                thing={'id': 'thing_id_0'},
                position_in_model_result=0
            ),
            InvocationItem(
                thing={'id': 'thing_id_1'},
                position_in_model_result=1
            ),
            InvocationItem(
                thing={'id': 'thing_id_2', 'custom': {'user_history': True}},
                position_in_model_result=2
            )
        ]

        items_to_promote = {
            'thing_id_2': {
                'promotionId': 'test_promotionId_2',
                'promotionName': 'test_promotionName_2'
            }
        }

        actual = api._apply_promotion_boosting(model_results, items_to_promote)

        # thing_id_2 is promoted, but the thing.custom.user_history will prevent boosting
        expected = [
            InvocationItem(thing={'id': 'thing_id_0'}, position_in_model_result=0),
            InvocationItem(thing={'id': 'thing_id_1'}, position_in_model_result=1),
            InvocationItem(thing={'id': 'thing_id_2', 'custom': {'user_history': True}},
                           position_in_model_result=2)
        ]
        self.assertEqual(expected, actual)

    def test_multi_slot_definition(self):
        request = Request(
            rule_id='********-701f-4391-87da-baf604c28a31', size=72, sort=None,
            sortBy=None, seedIds=[], chart_Id=None, subscription_type=None,
            message=None,
            userId='cea54778a8877ad7b9725c93c49bc02b7b94e25ffe5e457bcc56d3052f9fea86',
            anonId=None, accountId=None, profileId=None,
            query_string={
                'ignore': 'testtool',
                'userId': 'cea54778a8877ad7b9725c93c49bc02b7b94e25ffe5e457bcc56d3052f9fea86'
            },
            request_start=datetime.datetime(2023, 8, 9, 15, 33, 17, 128366,
                                            tzinfo=datetime.timezone.utc),
            debug_metadata=None, exclude=[], headers={}, editorials=[],
            allowed_items=None, stats=None,
            promotions={}, q=None, count=None, space=None
        )

        all_invocation_results = [
            InvocationResult(
                model=ModelDefinition(
                    key='mlt_20230306', version=1, source='PrebuiltBYWModel',
                    fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=5)]),
                    parameters={'useSeedId': True, 'deduplicate_path': '',
                                'excludeGenre': 'erotic',
                                'titlePlaceholder': 'Because You Watched {seed_name}'},
                    block=None, key_strategy=None), model_results=[InvocationItem(
                    thing={'id': 'bW92aWU7MTkxMDM=', 'brandId': 'bW92aWU7MTkxMDM=',
                           'name': 'A House on the Bayou', 'typeName': 'Movie',
                           'publication': [{'endDate': '2031-11-19T04:59:59+00:00',
                                            'startDate': '2021-11-19T05:00:01+00:00'}],
                           'genres': ['Thriller'], 'popularity': Decimal('1817'),
                           'age': Decimal('629'),
                           'custom': {'active': {'state': True}, 'user_history': False,
                                      'modelInfo': {'modelName': 'mlt_20220926',
                                                    'positionInModel': 1}}},
                    position_in_model_result=0), InvocationItem(
                    thing={'id': 'bW92aWU7NTA0Nw==', 'brandId': 'bW92aWU7NTA0Nw==',
                           'name': 'Paranormal Activity 4', 'typeName': 'Movie',
                           'publication': [{'endDate': '2023-09-01T03:59:59+00:00',
                                            'startDate': '2023-01-01T05:00:01+00:00'}],
                           'genres': ['Horror'], 'popularity': Decimal('242'),
                           'age': Decimal('221'),
                           'custom': {'active': {'state': True}, 'user_history': False,
                                      'modelInfo': {'modelName': 'mlt_20220926',
                                                    'positionInModel': 0}}},
                    position_in_model_result=1)
                ], model_info={
                    'seed': {'id': 'c2VyaWVzOzEwMzc=', 'name': 'model_info_name_0',
                             'title': 'Because You Watched model_info_name_0'}},
                seed_info=None, is_empty=False),
            InvocationResult
                (model=ModelDefinition(
                key='mlt_20220926', version=1, source='PrebuiltBYWModel',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=5)]),
                parameters={'useSeedId': True, 'deduplicate_path': '',
                            'excludeGenre': 'erotic',
                            'titlePlaceholder': 'Because You Watched {seed_name}'},
                block=None, key_strategy=None),
                model_results=[InvocationItem(
                    thing={'id': 'bW92aWU7NTA0Nw==', 'brandId': 'bW92aWU7NTA0Nw==',
                           'name': 'Paranormal Activity 4', 'typeName': 'Movie',
                           'publication': [{'endDate': '2023-09-01T03:59:59+00:00',
                                            'startDate': '2023-01-01T05:00:01+00:00'}],
                           'genres': ['Horror'], 'popularity': Decimal('242'),
                           'age': Decimal('221'),
                           'custom': {'active': {'state': True}, 'user_history': False,
                                      'modelInfo': {'modelName': 'mlt_20220926',
                                                    'positionInModel': 0}}},
                    position_in_model_result=0)
                ], model_info={
                'seed': {'id': 'c2VyaWVzOzEwMzc=', 'name': 'model_info_name_1',
                         'title': 'Because You Watched model_info_name_1'}},
                seed_info=None,
                is_empty=False)
        ]

        results = self._invoker_api._combine_invocation_results(
            request,
            all_invocation_results
        )

        expected = {
            'seed': {
                'id': 'c2VyaWVzOzEwMzc=',
                'name': 'model_info_name_0',
                'title': 'Because You Watched model_info_name_0'
            }
        }

        self.assertEqual(expected, results.model_info)

