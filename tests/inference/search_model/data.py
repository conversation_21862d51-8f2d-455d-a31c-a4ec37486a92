import datetime

from requests import Response

from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, \
    RangeFulfilment
from thefilter.model.messages.request import Request

test_request = Request(
    rule_id='76dd5146-c45c-464d-92dd-2c9fbfb1ee5d', size=101, sort=None,
    sortBy=None, seedIds=[], chart_Id=None, subscription_type=None,
    message=None, userId='tcwptest', anonId=None, accountId=None,
    profileId=None,
    query_string={'userId': 'tcwptest', 'q': 'oier', 'ignore': 'postman'},
    request_start=datetime.datetime(2023, 6, 30, 0, 39, 40, 652832,
                                    tzinfo=datetime.timezone.utc),
    debug_metadata=None, exclude=[], headers={}, editorials=[
        {'block': True, 'slotId': 'all', 'boostPercentage': 0.0,
         'endDate': '2037-12-31T00:00:00+00:00',
         'startDate': '2022-01-16T13:43:12.977761+00:00',
         'description': 'TCWP TEST TF-1991', 'thingId': 'bW92aWU7MTQ3NjQ='}],
    allowed_items=None, stats=None, promotions={
        'c2VyaWVzOzEwNDE=': {'promotionId': '4fa55ce3-24c0-4dfe-85b5-de80577c1a9e',
                             'promotionName': 'TEST'}}, q='oier', count=None, space=None)

test_model_definition = ModelDefinition(
    key='search',
    version=1,
    source='SearchModel',
    fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=50)]),
    parameters={
        'mlt': {'model_key': 'version_20220620',
                'search_threshold': 5,
                'items_to_consider': 3,
                'depth': 5},
        'search': {
            'settings': {'translations': {
                'filters.rating': {'TV Y': 0,
                                   'TV TV-Y7': 5,
                                   'TV TV-Y7-FV': 5,
                                   'MPAA G': 10,
                                   'TV TV-G': 10, 'G': 10,
                                   'MPAA PG': 20,
                                   'TV TV-PG': 20,
                                   'MPAA NR': 20, 'PG': 20,
                                   'MPAA PG-13': 30,
                                   'TV TV-14': 30,
                                   'PG-13': 30, 'MPAA R': 40,
                                   'TV TV-MA': 40, 'R': 40,
                                   'MPAA NC-17': 50,
                                   'NC-17': 50, 'MPAA UR': 60,
                                   'NR': 60}}},
            'mlt': {'userHistoryDepth': 10,
                    'userHistoryMaxBoost': 1,
                    'fields': ['genre.searchable',
                               'enrichedGenre.searchable'],
                    'includeFromUserHistory': True},
            'perFieldQueries': [{
                'description': '100,000 : Full, 100% field value matches! If you literally type in the exact name of something, who are we to argue?',
                'query': {'dis_max': {
                    'tie_breaker': 0.7,
                    'boost': 100000,
                    'queries': [{
                        'constant_score': {
                            'filter': {
                                'term': {
                                    'name.lowercase_keyword': {
                                        'value': '{q}'}}},
                            'boost': 1.2}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'alternateName.lowercase_keyword': {
                                            'value': '{q}'}}},
                                'boost': 0.8}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'actor.lowercase_keyword': '{q}'}},
                                'boost': 0.7}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'director.lowercase_keyword': '{q}'}},
                                'boost': 0.5}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'people.lowercase_keyword': '{q}'}},
                                'boost': 0.5}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'franchise.lowercase_keyword': '{q}'}},
                                'boost': 1}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'tags.lowercase_keyword': '{q}'}},
                                'boost': 0.2}},
                        {
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'moods.lowercase_keyword': '{q}'}},
                                'boost': 0.1}}]}}},
                {
                    'description': '10,000 : Full, 100% field value match for Crew',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 10000,
                        'queries': [{
                            'constant_score': {
                                'filter': {
                                    'term': {
                                        'crew.lowercase_keyword': {
                                            'value': '{q}'}}},
                                'boost': 1}}]}}},
                {
                    'description': '10,000 : Full term matches! If any of the terms in your query match ',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 10000,
                        'queries': [{
                            'constant_score': {
                                'filter': {
                                    'match': {
                                        'name.searchable': {
                                            'query': '{q}'}}},
                                'boost': 3}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'alternateName.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 0.95}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'genre.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 1.2}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'franchise.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 1}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'tags.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 0.01}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'moods.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 0.001}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'people.searchable': {
                                                'query': '{q}'}}},
                                    'boost': 0.4}}]}}},
                {
                    'description': '1000: Full term matches based on the number of matching terms. Reorders items so that multi-term matches get a little extra somethin',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 1000,
                        'queries': [{
                            'match': {
                                'name.searchable': {
                                    'query': '{q}'}}},
                            {
                                'match': {
                                    'alternateName.searchable': {
                                        'query': '{q}'}}},
                            {
                                'match': {
                                    'franchise.searchable': {
                                        'query': '{q}'}}},
                            {
                                'match': {
                                    'tags.searchable': {
                                        'query': '{q}',
                                        'boost': 0.01}}},
                            {
                                'match': {
                                    'moods.searchable': {
                                        'query': '{q}'}}}]}}},
                {
                    'description': '100 : Partial full matches!',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 500,
                        'queries': [{
                            'dis_max': {
                                'tie_breaker': 0.7,
                                'boost': 1,
                                'queries': [
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'name.lowercase_keyword': {
                                                        'query': '{q}'}}},
                                            'boost': 1.5}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'alternateName.lowercase_keyword': {
                                                        'query': '{q}'}}},
                                            'boost': 1}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'franchise.lowercase_keyword': {
                                                        'query': '{q}'}}},
                                            'boost': 1.05}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'moods.lowercase_keyword': {
                                                        'query': '{q}'}}},
                                            'boost': 0.01}}]}}]}}},
                {
                    'description': '10 : Partial full matches, lessened score but with a possibility to match multiple times.',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 10,
                        'queries': [{
                            'dis_max': {
                                'tie_breaker': 0.7,
                                'boost': 1,
                                'queries': [
                                    {
                                        'match_phrase_prefix': {
                                            'name.lowercase_keyword': {
                                                'query': '{q}',
                                                'boost': 2}}},
                                    {
                                        'match_phrase_prefix': {
                                            'alternateName.lowercase_keyword': {
                                                'query': '{q}'}}},
                                    {
                                        'match_phrase_prefix': {
                                            'franchise.lowercase_keyword': {
                                                'query': '{q}',
                                                'boost': 0.1}}},
                                    {
                                        'match_phrase_prefix': {
                                            'tags.lowercase_keyword': {
                                                'query': '{q}',
                                                'boost': 0.1}}},
                                    {
                                        'match_phrase_prefix': {
                                            'moods.lowercase_keyword': {
                                                'query': '{q}',
                                                'boost': 0.1}}},
                                    {
                                        'match_phrase_prefix': {
                                            'people.lowercase_keyword': {
                                                'query': '{q}',
                                                'boost': 0.1}}}]}}]}}},
                {
                    'description': '25 : Fuzzy full matches!',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 25,
                        'queries': [{
                            'constant_score': {
                                'filter': {
                                    'match': {
                                        'name.lowercase_keyword': {
                                            'query': '{q}',
                                            'fuzziness': 1,
                                            'fuzzy_transpositions': 'true',
                                            'boost': 1}}},
                                'boost': 5}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'alternateName.lowercase_keyword': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 1}}},
                                    'boost': 0.8}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'franchise.lowercase_keyword': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 1}}},
                                    'boost': 1.05}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'tags.lowercase_keyword': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 0.01}}},
                                    'boost': 0.01}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'moods.lowercase_keyword': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 0.01}}},
                                    'boost': 0.01}}]}}},
                {
                    'description': '50 : Partial term matches!',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 100,
                        'queries': [{
                            'dis_max': {
                                'tie_breaker': 0.7,
                                'boost': 1,
                                'queries': [
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'name.searchable': {
                                                        'query': '{q}'}}},
                                            'boost': 1000}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'alternateName.searchable': {
                                                        'query': '{q}'}}},
                                            'boost': 1}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'franchise.searchable': {
                                                        'query': '{q}'}}},
                                            'boost': 1.05}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'tags.searchable': {
                                                        'query': '{q}'}}},
                                            'boost': 0.01}},
                                    {
                                        'constant_score': {
                                            'filter': {
                                                'match_phrase_prefix': {
                                                    'moods.searchable': {
                                                        'query': '{q}'}}},
                                            'boost': 0.01}}]}}]}}},
                {
                    'description': '10 : Partial term matches based on closeness of match',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 10,
                        'queries': [{
                            'dis_max': {
                                'tie_breaker': 0.7,
                                'boost': 1,
                                'queries': [
                                    {
                                        'match_phrase_prefix': {
                                            'name.searchable': {
                                                'query': '{q}',
                                                'boost': 100}}},
                                    {
                                        'match_phrase_prefix': {
                                            'alternateName.searchable': {
                                                'query': '{q}'}}},
                                    {
                                        'match_phrase_prefix': {
                                            'franchise.searchable': {
                                                'query': '{q}'}}},
                                    {
                                        'match_phrase_prefix': {
                                            'tags.searchable': {
                                                'query': '{q}',
                                                'boost': 0.1}}},
                                    {
                                        'match_phrase_prefix': {
                                            'moods.searchable': {
                                                'query': '{q}'}}}]}}]}}},
                {
                    'description': '20 : Fuzzy term matches!',
                    'query': {'dis_max': {
                        'tie_breaker': 0.7,
                        'boost': 25,
                        'queries': [{
                            'constant_score': {
                                'filter': {
                                    'match': {
                                        'name.searchable': {
                                            'query': '{q}',
                                            'fuzziness': 1,
                                            'fuzzy_transpositions': 'true',
                                            'boost': 100}}},
                                'boost': 1000}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'alternateName.searchable': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 1}}},
                                    'boost': 1}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'franchise.searchable': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 1}}},
                                    'boost': 1.05}},
                            {
                                'constant_score': {
                                    'filter': {
                                        'match': {
                                            'moods.searchable': {
                                                'query': '{q}',
                                                'fuzziness': 1,
                                                'fuzzy_transpositions': 'true',
                                                'boost': 1}}},
                                    'boost': 0.1}}]}}}],
            'rescorers': [{
                'description': 'Boost items with a box office score',
                'rescore': {'query': {
                    'rescore_query': {
                        'function_score': {
                            'query': {'bool': {
                                'must': [{
                                    'range': {
                                        'scores.boxOffice': {
                                            'gt': 0}}}]}},
                            'functions': [{
                                'field_value_factor': {
                                    'field': 'scores.boxOffice',
                                    'factor': 10}}]}}},
                    'window_size': 500}},
                {
                    'description': 'Boost items with a newness score',
                    'rescore': {'query': {
                        'rescore_query': {
                            'function_score': {
                                'query': {'bool': {
                                    'must': [{
                                        'range': {
                                            'scores.newness': {
                                                'gt': 0}}}]}},
                                'functions': [{
                                    'field_value_factor': {
                                        'field': 'scores.newness',
                                        'factor': 30000}}]}}},
                        'window_size': 2000}},
                {
                    'description': "100 : Boost items by normalised popularity score, but don't let that override term matches!",
                    'rescore': {
                        'query': {'query_weight': 1,
                                  'rescore_query_weight': 1,
                                  'rescore_query': {
                                      'function_score': {
                                          'query': {
                                              'bool': {
                                                  'must': [
                                                      {
                                                          'range': {
                                                              'scores.popularity': {
                                                                  'gt': 0}}}]}},
                                          'functions': [
                                              {
                                                  'field_value_factor': {
                                                      'field': 'scores.popularity',
                                                      'factor': 300}}]}}},
                        'window_size': 1000}}, {
                    'description': 'Push search results in the vault genre down to the bottom of the result set.',
                    'rescore': {'query': {
                        'rescore_query': {
                            'function_score': {
                                'query': {'bool': {
                                    'must': [{
                                        'term': {
                                            'genre.lowercase_keyword': {
                                                'value': 'vault'}}}]}},
                                'functions': [{
                                    'weight': -500000}]}}},
                        'window_size': 5000}}],
            'filters': [{
                'description': "If active state is false, don't return it. Pretty clear cut.",
                'query': {'bool': {'must_not': [{
                    'term': {
                        'thing.custom.active.state': False}}]}}},
                {
                    'description': "Don't return Episodes",
                    'query': {'bool': {'must_not': [{
                        'term': {
                            'typeName': 'episode'}}]}}},
                {
                    'description': 'Filter category when category is provided on the query string.',
                    'parameters': {'required': [
                        {'key': 'category'}]},
                    'query': {'bool': {'must': [{
                        'term': {
                            'filters.category.lowercase_keyword': '{category}'}}]}}},
                {
                    'description': 'Set lower bound for releaseYear when releaseYearMin is provided on the query string.',
                    'parameters': {'required': [
                        {'key': 'releaseYearMin'}]},
                    'query': {'bool': {'must': [{
                        'range': {
                            'filters.releaseYear': {
                                'gte': '{releaseYearMin}'}}}]}}},
                {
                    'description': 'Set upper bound for releaseYear when releaseYearMax is provided on the query string.',
                    'parameters': {'required': [
                        {'key': 'releaseYearMax'}]},
                    'query': {'bool': {'must': [{
                        'range': {
                            'filters.releaseYear': {
                                'lte': '{releaseYearMax}'}}}]}}},
                {
                    'description': 'Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration',
                    'parameters': {'required': [
                        {'key': 'ratingMax',
                         'translation': 'filters.rating'}]},
                    'query': {'bool': {'must': [{
                        'range': {
                            'filters.rating': {
                                'lte': '{ratingMax}'}}}]}}},
                {
                    'description': 'Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration',
                    'parameters': {'required': [
                        {'key': 'ratingMin',
                         'translation': 'filters.rating'}]},
                    'query': {'bool': {'must': [{
                        'range': {
                            'filters.rating': {
                                'gte': '{ratingMin}'}}}]}}}]}},
    block=None, key_strategy=None)

results_to_return = [
    {
        'userId': 'test_id_0',
        'model': 'version_20220620',
        'recommended':
            '['
            '{"id": "mlt_0_item_0", "typeName": "Movie", "alg": "mlt"},'
            '{"id": "mlt_0_item_1", "typeName": "Movie", "alg": "mlt"},'
            '{"id": "mlt_0_item_2", "typeName": "Movie", "alg": "mlt"},'
            '{"id": "mlt_0_item_3", "typeName": "Movie", "alg": "mlt"},'
            '{"id": "mlt_0_item_4", "typeName": "Movie", "alg": "mlt"}'
            ']'
    },
    {
        'model': 'version_20220620',
        'userId': 'test_id_1',
        'recommended':
            '['
            '{"id": "mlt_1_item_0", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_1_item_1", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_1_item_2", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_1_item_3", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_1_item_4", "typeName": "TVSeries", "alg": "mlt"}'
            ']'
    },
    {
        'model': 'version_20220620',
        'userId': 'test_id_2',
        'recommended':
            '['
            '{"id": "mlt_2_item_0", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_2_item_1", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_2_item_2", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_2_item_3", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_2_item_4", "typeName": "TVSeries", "alg": "mlt"}'
            ']'
    },
    {
        'model': 'version_20220620',
        'userId': 'test_id_3',
        'recommended':
            '['
            '{"id": "mlt_3_item_0", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_3_item_1", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_3_item_2", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_3_item_3", "typeName": "TVSeries", "alg": "mlt"},'
            '{"id": "mlt_3_item_4", "typeName": "TVSeries", "alg": "mlt"}'
            ']'
    }
]

# Not using this - but could be used in the search model invoke test
test_es_response = Response()
test_es_response.status_code = 200
# can't set content!
# test_es_response.content = json.dumps({'took': 4, 'timed_out': False,
#                      '_shards': {'total': 3, 'successful': 3, 'skipped': 0, 'failed': 0},
#                      'hits': {'total': 5, 'max_score': 177314.05, 'hits': [
#                          {'_index': 'search_epix_20230629030429', '_type': '_doc',
#                           '_id': 'bW92aWU7MTg0MzE=', '_score': 177314.05,
#                           '_source': {'id': 'bW92aWU7MTg0MzE=',
#                                       'name': 'Over the Brooklyn Bridge',
#                                       'alternateName': ['Over the Brooklyn Bridge',
#                                                         'OvertheBrooklynBridge'],
#                                       'typeName': 'Movie',
#                                       'genre': ['Comedy', 'Romance'],
#                                       'datePublished': '1984-01-01T00:00:00+00:00',
#                                       'popularity': 0,
#                                       'actor': ['Burt Young', 'Carol Kane',
#                                                 'Elliot Gould', 'Margaux Hemingway',
#                                                 'Shelley Winters', 'Sid Caesar',
#                                                 'Menahem Golan', 'Yoram Globus'],
#                                       'director': ['Menahem Golan'],
#                                       'crew': ['Arnold Somkin', 'Adam Greenberg',
#                                                'Christopher Pearce'], 'filters': {
#                                   'category': ['Comedy', 'Romance', 'comedy', 'romance'],
#                                   'releaseYear': 1984, 'rating': 40},
#                                       'editorial': {'slotId': None,
#                                                     'boostPercentage': None,
#                                                     'editorialBlock': None,
#                                                     'boostStartDate': '1970-01-01T00:00:00+00:00',
#                                                     'boostEndDate': '2038-01-19T00:00:00+00:00'},
#                                       'availableFrom': '2023-01-01T05:00:01+00:00',
#                                       'availableUntil': '2025-01-01T04:59:59+00:00',
#                                       'scores': {'newness': 1.7408182206817178}}},
#                          {'_index': 'search_epix_20230629030429', '_type': '_doc',
#                           '_id': 'bW92aWU7MTcyNzY=', '_score': 104703.43,
#                           '_source': {'id': 'bW92aWU7MTcyNzY=',
#                                       'name': 'The Moose Head Over the Mantel',
#                                       'alternateName': ['The Moose Head Over the Mantel',
#                                                         'TheMooseHeadOvertheMantel'],
#                                       'typeName': 'Movie', 'genre': ['Vault'],
#                                       'datePublished': '2017-01-01T00:00:00+00:00',
#                                       'popularity': 3,
#                                       'actor': ['Jessi Gotta', 'Nat Cassidy',
#                                                 'Jesse Van Derveer', 'Kristen Vaughan',
#                                                 'Walter Brandes', 'Rebecca Gray Davis',
#                                                 'Stephen Heskett', 'Robert Pinnock',
#                                                 'Alyssa Simon', 'John Amir',
#                                                 'Eric John Meyer',
#                                                 'Timothy McCown Reynolds'],
#                                       'director': ['Jane Rose', 'Matt Gray',
#                                                    'Shannon K. Hall', 'Jessi Gotta',
#                                                    'Rebecca Comtois', 'Bryan Enk'],
#                                       'crew': ['Jessi Gotta', 'Dominick Sivilli',
#                                                'Stephanie Cox-Williams', 'Bryan Enk',
#                                                'Jessi Gotta', 'Adam Bertocci'],
#                                       'filters': {'category': ['Vault', 'vault'],
#                                                   'releaseYear': 2017, 'rating': 40},
#                                       'editorial': {'slotId': None,
#                                                     'boostPercentage': None,
#                                                     'editorialBlock': None,
#                                                     'boostStartDate': '1970-01-01T00:00:00+00:00',
#                                                     'boostEndDate': '2038-01-19T00:00:00+00:00'},
#                                       'availableFrom': '2020-09-01T04:00:00+00:00',
#                                       'availableUntil': '2023-08-31T05:00:00+00:00',
#                                       'scores': {'newness': 3.225540928492468}}},
#                          {'_index': 'search_epix_20230629030429', '_type': '_doc',
#                           '_id': 'bW92aWU7MTQ2MjQ=', '_score': 62941.055,
#                           '_source': {'id': 'bW92aWU7MTQ2MjQ=',
#                                       'name': 'Mind Over Murder',
#                                       'alternateName': ['Mind Over Murder',
#                                                         'MindOverMurder'],
#                                       'typeName': 'Movie', 'genre': ['Vault'],
#                                       'datePublished': '1979-01-01T00:00:00+00:00',
#                                       'popularity': 2,
#                                       'actor': ['Deborah Raffin', 'David Ackroyd',
#                                                 'Bruce Davison', 'Andrew Prine',
#                                                 'Christopher Cary', 'Robert Englund',
#                                                 'Penelope Willis', 'Wayne Heffley',
#                                                 'Carl Anderson'],
#                                       'director': ['Ivan Nagy'],
#                                       'crew': ['Robert Carrington', 'Dennis Dalzell',
#                                                'Jay Benson'],
#                                       'filters': {'category': ['Vault', 'vault'],
#                                                   'releaseYear': 1979, 'rating': 30},
#                                       'editorial': {'slotId': None,
#                                                     'boostPercentage': None,
#                                                     'editorialBlock': None,
#                                                     'boostStartDate': '1970-01-01T00:00:00+00:00',
#                                                     'boostEndDate': '2038-01-19T00:00:00+00:00'},
#                                       'availableFrom': '2020-01-01T05:00:00+00:00',
#                                       'availableUntil': '2023-12-31T05:00:00+00:00',
#                                       'scores': {'newness': 1.627089085273056}}},
#                          {'_index': 'search_epix_20230629030429', '_type': '_doc',
#                           '_id': 'bW92aWU7MTU2MTk=', '_score': 51786.477,
#                           '_source': {'id': 'bW92aWU7MTU2MTk=',
#                                       'name': 'The Over-the-Hill Gang Rides Again',
#                                       'alternateName': [
#                                           'The OvertheHill Gang Rides Again',
#                                           'TheOvertheHillGangRidesAgain'],
#                                       'typeName': 'Movie', 'genre': ['Vault'],
#                                       'datePublished': '1970-01-01T00:00:00+00:00',
#                                       'popularity': 2,
#                                       'actor': ['Walter Brennan', 'Fred Astaire',
#                                                 'Edgar Buchanan', 'Andy Devine',
#                                                 'Chill Wills', 'Paul Richards',
#                                                 'Lana Wood', 'Parley Baer',
#                                                 'Walter Burke', 'Lillian Bronson',
#                                                 'Jonathan Hole', 'Burt Mustin'],
#                                       'director': ['George McCowan'],
#                                       'crew': ['Richard Carr', 'Fleet Southcott',
#                                                'Aaron Spelling', 'Danny Thomas',
#                                                'Shelley Hull', 'Andy Brennan'],
#                                       'filters': {'category': ['Vault', 'vault'],
#                                                   'releaseYear': 1970, 'rating': 20},
#                                       'editorial': {'slotId': None,
#                                                     'boostPercentage': None,
#                                                     'editorialBlock': None,
#                                                     'boostStartDate': '1970-01-01T00:00:00+00:00',
#                                                     'boostEndDate': '2038-01-19T00:00:00+00:00'},
#                                       'availableFrom': '2022-06-01T04:00:01+00:00',
#                                       'availableUntil': '2026-06-01T04:59:59+00:00',
#                                       'scores': {'newness': 1.4645590203609116}}},
#                          {'_index': 'search_epix_20230629030429', '_type': '_doc',
#                           '_id': 'bW92aWU7MTU2MDc=', '_score': 44032.406,
#                           '_source': {'id': 'bW92aWU7MTU2MDc=',
#                                       'name': 'Shadows Over Shanghai',
#                                       'alternateName': ['Shadows Over Shanghai',
#                                                         'ShadowsOverShanghai'],
#                                       'typeName': 'Movie', 'genre': ['Vault'],
#                                       'datePublished': '1938-01-01T00:00:00+00:00',
#                                       'popularity': 0,
#                                       'actor': ['James Dunn', 'Ralph Morgan',
#                                                 'Robert Barrat', 'Lynda Grey',
#                                                 'Paul Sutton', 'Edward Woods',
#                                                 'Edwin Mordant', 'Chester Gan',
#                                                 'Victor Wong', 'Edward Keane',
#                                                 'Billy Bevan', 'William Haade'],
#                                       'director': ['Charles Lamont'],
#                                       'crew': ['Richard Sale', 'Joseph Hoffman',
#                                                'Arthur Martinelli', 'Franklyn Warner',
#                                                'Charles Lamont'],
#                                       'filters': {'category': ['Vault', 'vault'],
#                                                   'releaseYear': 1938, 'rating': 20},
#                                       'editorial': {'slotId': None,
#                                                     'boostPercentage': None,
#                                                     'editorialBlock': None,
#                                                     'boostStartDate': '1970-01-01T00:00:00+00:00',
#                                                     'boostEndDate': '2038-01-19T00:00:00+00:00'},
#                                       'availableFrom': '2022-06-01T04:00:01+00:00',
#                                       'availableUntil': '2026-06-01T04:59:59+00:00',
#                                       'scores': {'newness': 1.159879746079694}}}]}}
#                                    )
