from unittest import TestCase, mock

from tests.inference.search_model.data import test_es_response, results_to_return
from thefilter.aws.elastic_search import Searcher
from thefilter.behaviours.recommendation import StubRecommendationRepository
from thefilter.inference.logic.invokable_models import SearchModel
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.repositories import StubUserHistoryRepository, StubMetadataRepository
from thefilter.rules.ruleset import Ruleset

mocked_requests_response = test_es_response


@mock.patch(
    'requests.post',
    return_value=mocked_requests_response
)
class TestSearchModel(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        elasticsearch_metadata_endpoint = 'test_es_endpoint'
        customer = 'test_customer'
        tracer_factory = NoOpTracerFactory()
        searcher = Searcher(
            metadata_cluster_url=elasticsearch_metadata_endpoint,
            customer=customer,
            tracer_factory=tracer_factory
        )

        user_history_repo = {}
        get_items_response = {}

        self._test_things = [
            {'id': 'test_id_0', 'typeName': 'Movie'},
            {'id': 'test_id_1', 'typeName': 'TVSeries'},
            {'id': 'test_id_2', 'typeName': 'Movie'},
            {'id': 'test_id_3', 'typeName': 'TVSeries'}
        ]

        self._api = SearchModel(
            searcher=searcher,
            user_repository=StubUserHistoryRepository(user_history_repo),
            customer=customer,
            tracer_factory=tracer_factory,
            metadata_repository=StubMetadataRepository(get_items_response),
            recommendation_repository=StubRecommendationRepository(results_to_return)
        )

    def test_invoke(self, mock_es_response: dict):
        # incomplete!
        ruleset = Ruleset(id='76dd5146-c45c-464d-92dd-2c9fbfb1ee5d', size=50, tuners=[])
        # result = self._api.invoke(
        #     test_model_definition,
        #     test_request,
        #     ruleset
        # )
        pass

    # def test__invoke_searcher(self):
    # incomplete!
    #     pass

    def test__clean_search_term(self, mock_es_response: dict):

        dirty_term = '¡Me Gusta! 🎉😃🙌'
        cleaned_term = self._api._clean_search_term(dirty_term)
        expected = 'Me Gusta'
        self.assertEqual(expected, cleaned_term)

    def test__remove_emoji(self, mock_es_response: dict):
        emoji_string = "Cool Beans! ☃️😎🌱"
        actual = self._api._remove_emoji(emoji_string)
        expected = 'Cool Beans!  '
        self.assertEqual(expected, actual)

    # def test__get_user_history(self, mock_es_response: dict):
    #     self.fail()

    def test__augment_search_with_mlt__search_threshold(self, mock_es_response: dict):
        # Config with a search_threshold lower than the number of results, no mlt added
        mlt_config = {
            'model_key': 'version_20220620',
            'search_threshold': 3,  # This parameter affects this test condition
            'items_to_consider': 3,
            'depth': 4
        }
        results = self._api._augment_search_with_mlt(self._test_things, mlt_config)
        expected = self._test_things
        self.assertEqual(expected, results)

    def test__augment_search_with_mlt__items_to_consider(self, mock_es_response: dict):
        mlt_config = {
            'model_key': 'version_20220620',
            'search_threshold': 5,
            'items_to_consider': 3,  # This parameter affects this test condition
            'depth': 4
        }
        results = self._api._augment_search_with_mlt(self._test_things, mlt_config)
        expected = [
            {'id': 'test_id_0', 'typeName': 'Movie'},
            {'id': 'test_id_1', 'typeName': 'TVSeries'},
            {'id': 'test_id_2', 'typeName': 'Movie'},
            {'id': 'test_id_3', 'typeName': 'TVSeries'},
            {'id': 'mlt_0_item_0', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_1', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_2', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_3', 'typeName': 'Movie'},
            {'id': 'mlt_1_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_1_item_1', 'typeName': 'TVSeries'},
            {'id': 'mlt_1_item_2', 'typeName': 'TVSeries'},
            {'id': 'mlt_1_item_3', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_1', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_2', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_3', 'typeName': 'TVSeries'}
        ]
        self.assertEqual(expected, results)

    def test__augment_search_with_mlt__depth(self, mock_es_response: dict):
        mlt_config = None
        results = self._api._augment_search_with_mlt(self._test_things, mlt_config)
        expected = self._test_things
        self.assertEqual(expected, results)

        mlt_config = {}
        results = self._api._augment_search_with_mlt(self._test_things, mlt_config)
        expected = self._test_things
        self.assertEqual(expected, results)

        mlt_config = {
            'model_key': 'version_20220620',
            'search_threshold': 5,
            'items_to_consider': 3,
            'depth': 2  # This parameter affects this test condition
        }
        results = self._api._augment_search_with_mlt(self._test_things, mlt_config)
        expected = [
            {'id': 'test_id_0', 'typeName': 'Movie'},
            {'id': 'test_id_1', 'typeName': 'TVSeries'},
            {'id': 'test_id_2', 'typeName': 'Movie'},
            {'id': 'test_id_3', 'typeName': 'TVSeries'},
            {'id': 'mlt_0_item_0', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_1', 'typeName': 'Movie'},
            {'id': 'mlt_1_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_1_item_1', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_1', 'typeName': 'TVSeries'}
        ]
        self.assertEqual(expected, results)

    def test__combine_mlt_results(self, mock_es_response: dict):
        mlt_results = {
            'test_id_0': [
                {'id': 'mlt_0_item_0', 'typeName': 'Movie'},
                {'id': 'mlt_0_item_1', 'typeName': 'Movie'},
                {'id': 'mlt_0_item_2', 'typeName': 'Movie'},
                {'id': 'mlt_2_item_2', 'typeName': 'Movie'},
                {'id': 'mlt_1_item_3', 'typeName': 'Movie'}],
            'test_id_1': [
                {'id': 'mlt_1_item_0', 'typeName': 'TVSeries'},
                {'id': 'mlt_1_item_1', 'typeName': 'TVSeries'},
                {'id': 'mlt_0_item_1', 'typeName': 'TVSeries'},
                {'id': 'test_id_1', 'typeName': 'TVSeries'},
                {'id': 'mlt_1_item_4', 'typeName': 'TVSeries'}],
            'test_id_2': [
                {'id': 'mlt_2_item_0', 'typeName': 'TVSeries'},
                {'id': 'mlt_0_item_3', 'typeName': 'TVSeries'},
                {'id': 'mlt_2_item_2', 'typeName': 'TVSeries'},
                {'id': 'mlt_1_item_3', 'typeName': 'TVSeries'},
                {'id': 'mlt_2_item_4', 'typeName': 'TVSeries'}
            ]
        }
        size = 3
        results = self._api._combine_mlt_results(self._test_things, mlt_results, size)
        expected = [
            {'id': 'test_id_0', 'typeName': 'Movie'},
            {'id': 'test_id_1', 'typeName': 'TVSeries'},
            {'id': 'test_id_2', 'typeName': 'Movie'},
            {'id': 'test_id_3', 'typeName': 'TVSeries'},
            {'id': 'mlt_0_item_0', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_1', 'typeName': 'Movie'},
            {'id': 'mlt_0_item_2', 'typeName': 'Movie'},
            {'id': 'mlt_1_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_1_item_1', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_0', 'typeName': 'TVSeries'},
            {'id': 'mlt_0_item_3', 'typeName': 'TVSeries'},
            {'id': 'mlt_2_item_2', 'typeName': 'TVSeries'}
        ]
        self.assertEqual(expected, results)
