import unittest
from datetime import datetime, timezone
from typing import List, <PERSON><PERSON>

from thefilter.inference.logic.invokable_models import TvHighlightsModel
from thefilter.inference.logic.query_builder import ElasticSearchQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.repositories import MetadataRepository, StorageUserRepository

CUSTOMER = "UKTV"


class TvHighlightsTests(unittest.TestCase):
    def test_enriched_results_format(self):

        class TestMetadataRepository(MetadataRepository):
            def get_item(self, item_id: str) -> dict:
                pass

            def get_series_item(self, series_id: str) -> dict:
                pass

            def get_metadata_types(self,
                                   path: Tuple[str] = (),
                                   highlight_nested: bool = False) -> dict:
                pass

            def get_metadata_hierarchy(self, path: Tuple[str] = ()):
                pass

            def get_items(self, item_ids: List[str]) -> dict:
                item_metadata = {
                    "Ep1": {"thing": {"attribute": "data"}}
                }
                return item_metadata

            def get_metadata_items(self) -> dict:
                pass

        tv_highlights_model = TvHighlightsModel(
            query_builder=ElasticSearchQueryBuilder,
            metadata_repository=TestMetadataRepository(),
            tracer_factory=NoOpTracerFactory(),
            customer=CUSTOMER,
            endpoint="endpoint",
            user_repository=StorageUserRepository([])
        )
        result_of_query = [{
            "_index": "metadata_uktv_20191115",
             "_type": "_doc",
             "_id": "Ep1",
             "_score": 4.842154,
             "_source": {"thing": {"typeName": "Episode", "id": "Ep1"}}
        }]
        enriched_result = tv_highlights_model._enrich_items(result_of_query)
        self.assertEqual(enriched_result, [{"attribute": "data"}])

    def test_execute_query(self):
        class TestResponse:
            def success(self):
                return True

            def to_dict(self):
                return {"hits": {"hits": [{"attribute": "data"}]}}

        class TestSearch:
            def execute(self):
                return TestResponse()

        tv_highlights_model = TvHighlightsModel(
            query_builder=None,
            metadata_repository=None,
            tracer_factory=NoOpTracerFactory(),
            customer=CUSTOMER,
            endpoint="endpoint",
            user_repository=StorageUserRepository([])
        )
        response = tv_highlights_model._execute_query(TestSearch())
        self.assertEqual([{"attribute": "data"}], response)

    def test_listing_lookup_multiple_listings(self):
        enriched_result = [{
            "id": "E1",
            "publication": [
                {
                    "id": 11,
                    "startDate": "2019-11-18T23:00:00+00:00",
                    "endDate": "2019-11-18T23:40:00+00:00"
                },
                {
                    "id": 12,
                    "startDate": "2019-11-19T23:00:00+00:00",
                    "endDate": "2019-11-19T23:40:00+00:00"
                },
                {
                    "id": None,
                    "startDate": "2019-11-19T23:00:00+00:00",
                    "endDate": "2019-11-19T23:40:00+00:00"
                }
            ],
            "partOfSeries": [
                {
                    "id": 1,
                }
            ]
        }]
        expected_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-18T23:00:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-19T23:00:00+00:00"}
        }
        listing_lookup = TvHighlightsModel._build_listing_lookup(enriched_result, False)
        self.assertEqual(expected_lookup, listing_lookup)

    def test_listing_lookup_multiple_episodes(self):
        enriched_result = [
            {
                "id": "E1",
                "publication": [
                    {
                        "id": 11,
                        "startDate": "2019-11-18T23:00:00+00:00",
                        "endDate": "2019-11-18T23:40:00+00:00"
                    },
                    {
                        "id": 12,
                        "startDate": "2019-11-19T23:00:00+00:00",
                        "endDate": "2019-11-19T23:40:00+00:00"
                    }
                ],
                "partOfSeries": [
                    {
                        "id": 1,
                    }
                ]
            },
            {
                "id": "E2",
                "publication": [
                    {
                        "id": 21,
                        "startDate": "2019-11-18T22:00:00+00:00",
                        "endDate": "2019-11-18T22:30:00+00:00"
                    }
                ],
                "partOfSeries": [
                    {
                        "id": 2,
                    }
                ]
            }
        ]
        expected_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-18T23:00:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-19T23:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-11-18T22:00:00+00:00"}
        }
        listing_lookup = TvHighlightsModel._build_listing_lookup(enriched_result, False)
        self.assertEqual(expected_lookup, listing_lookup)

    def test_subcat_listing_lookup_multiple_listings(self):
        enriched_result = [{
            "id": "E1",
            "custom": {
                "subCategory": [
                    {
                        "id": 1
                    },
                    {
                        "id": 2
                    }
                ]
            },
            "publication": [
                {
                    "id": 11,
                    "startDate": "2019-11-18T23:00:00+00:00",
                    "endDate": "2019-11-18T23:40:00+00:00"
                },
                {
                    "id": 12,
                    "startDate": "2019-11-19T23:00:00+00:00",
                    "endDate": "2019-11-19T23:40:00+00:00"
                }
            ],
            "partOfSeries": [
                {
                    "id": 1,
                }
            ]
        }]
        expected_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-18T23:00:00+00:00",
                 "subCategoryIds": ['1', '2']},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-19T23:00:00+00:00",
                 "subCategoryIds": ['1', '2']}
        }
        listing_lookup = TvHighlightsModel._build_listing_lookup(enriched_result, True)
        self.assertEqual(expected_lookup, listing_lookup)

    def test_subcat_listing_lookup_multiple_episodes(self):
        enriched_result = [
            {
                "id": "E1",
                "custom": {
                    "subCategory": [
                        {
                            "id": 1
                        },
                        {
                            "id": 2
                        }
                    ]
                },
                "publication": [
                    {
                        "id": 11,
                        "startDate": "2019-11-18T23:00:00+00:00",
                        "endDate": "2019-11-18T23:40:00+00:00"
                    },
                    {
                        "id": 12,
                        "startDate": "2019-11-19T23:00:00+00:00",
                        "endDate": "2019-11-19T23:40:00+00:00"
                    }
                ],
                "partOfSeries": [
                    {
                        "id": 1,
                    }
                ]
            },
            {
                "id": "E2",
                "custom": {
                    "subCategory": [
                        {
                            "id": 3
                        },
                        {
                            "id": 4
                        }
                    ]
                },
                "publication": [
                    {
                        "id": 21,
                        "startDate": "2019-11-18T22:00:00+00:00",
                        "endDate": "2019-11-18T22:30:00+00:00"
                    }
                ],
                "partOfSeries": [
                    {
                        "id": 2,
                    }
                ]
            }
        ]
        expected_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-18T23:00:00+00:00",
                 "subCategoryIds": ['1', '2']},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-11-19T23:00:00+00:00",
                 "subCategoryIds": ['1', '2']},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-11-18T22:00:00+00:00",
                 "subCategoryIds": ['3', '4']}
        }
        listing_lookup = TvHighlightsModel._build_listing_lookup(enriched_result, True)
        self.assertEqual(expected_lookup, listing_lookup)

    def test_metadata_lookup_multiple_listings(self):
        enriched_result = [{
            "id": "E1",
            "publication": [
                {
                    "id": 11,
                    "startDate": "2019-11-18T23:00:00+00:00",
                    "endDate": "2019-11-18T23:40:00+00:00"
                },
                {
                    "id": 12,
                    "startDate": "2019-11-19T23:00:00+00:00",
                    "endDate": "2019-11-19T23:40:00+00:00"
                }
            ],
            "custom": {
                "brand": [
                    {
                        "id": 1,
                    }
                ]
            }
        }]
        expected_lookup = {11: enriched_result[0], 12: enriched_result[0]}
        metadata_lookup = TvHighlightsModel._build_metadata_lookup(enriched_result)
        self.assertEqual(expected_lookup, metadata_lookup)

    def test_metadata_lookup_multiple_episodes(self):
        enriched_result = [
            {
                "id": "E1",
                "publication": [
                    {
                        "id": 11,
                        "startDate": "2019-11-18T23:00:00+00:00",
                        "endDate": "2019-11-18T23:40:00+00:00"
                    },
                    {
                        "id": 12,
                        "startDate": "2019-11-19T23:00:00+00:00",
                        "endDate": "2019-11-19T23:40:00+00:00"
                    }
                ],
                "custom": {
                    "brand": [
                        {
                            "id": 1,
                        }
                    ]
                }
            },
            {
                "id": "E2",
                "publication": [
                    {
                        "id": 21,
                        "startDate": "2019-11-18T22:00:00+00:00",
                        "endDate": "2019-11-18T22:30:00+00:00"
                    }
                ],
                "custom": {
                    "brand": [
                        {
                            "id": 2,
                        }
                    ]
                }
            }
        ]
        expected_lookup = {
            11: enriched_result[0],
            12: enriched_result[0],
            21: enriched_result[1]
        }
        metadata_lookup = TvHighlightsModel._build_metadata_lookup(enriched_result)
        self.assertEqual(expected_lookup, metadata_lookup)

    def test_listing_scoring_before_primetime(self):
        date_time = datetime(2019, 1, 1, 12, 0).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00",
                 "score": 1000},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00",
                 "score": 2},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00",
                 "score": 3}
        }
        output = TvHighlightsModel._score_listings_before_primetime_end(
            listing_lookup, date_time)
        self.assertEqual(expected_output, output)

    def test_listing_scoring_edge_case_start(self):
        date_time = datetime(2019, 1, 1, 20, 00).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:00:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T15:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T21:30:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:00:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00",
                 "score": 3},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T15:00:00+00:00",
                 "score": 100},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T21:30:00+00:00",
                 "score": 4}
        }
        scored_output = TvHighlightsModel._score_listings_before_primetime_end(
            listing_lookup, date_time)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_during_primetime(self):
        date_time = datetime(2019, 1, 1, 22, 00).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T22:30:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00",
                 "score": 1000},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T22:30:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00",
                 "score": 1000},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00",
                 "score": 3}
        }
        scored_output = TvHighlightsModel._score_listings_before_primetime_end(
            listing_lookup, date_time)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_edge_case_end(self):
        date_time = datetime(2019, 1, 1, 23, 00).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:45:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-02T02:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T13:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T21:30:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:45:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-02T02:00:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T13:00:00+00:00",
                 "score": 3},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T21:30:00+00:00",
                 "score": 2}
        }
        scored_output = TvHighlightsModel._score_listings_after_primetime_end(
            listing_lookup, date_time, delta=1)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_after_primetime(self):
        date_time = datetime(2019, 1, 1, 23, 15).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T01:00:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T11:30:00+00:00",
                 "score": 1000},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00",
                 "score": 1000},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T01:00:00+00:00",
                 "score": 1}
        }
        scored_output = TvHighlightsModel._score_listings_after_primetime_end(
            listing_lookup, date_time, delta=1)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_early_morning(self):
        date_time = datetime(2019, 1, 1, 1, 0).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00",
                 "score": 3},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T21:00:00+00:00",
                 "score": 2},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:30:00+00:00",
                 "score": 100}
        }
        scored_output = TvHighlightsModel._score_listings_after_primetime_end(
            listing_lookup, date_time, delta=0)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_future_primetimes(self):
        date_time = datetime(2019, 1, 1, 12, 00).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-02T20:00:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-03T20:30:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-04T23:00:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-02T20:00:00+00:00",
                 "score": 4},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-03T20:30:00+00:00",
                 "score": 4},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-04T23:00:00+00:00",
                 "score": 100}
        }
        scored_output = TvHighlightsModel._score_listings_before_primetime_end(
            listing_lookup, date_time)
        self.assertEqual(expected_output, scored_output)

    def test_listing_scoring_general(self):
        date_time = datetime(2019, 1, 1, 12, 0).replace(tzinfo=timezone.utc)
        listing_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:30:00+00:00"},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00"},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00"},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T22:30:00+00:00"},
            31: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-02T08:30:00+00:00"},
            32: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-01T09:30:00+00:00"}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:30:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T13:00:00+00:00",
                 "score": 2},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T23:00:00+00:00",
                 "score": 3},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-02T22:30:00+00:00",
                 "score": 4},
            31: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-02T08:30:00+00:00",
                 "score": 100},
            32: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-01T09:30:00+00:00",
                 "score": 1000}
        }
        scored_output = TvHighlightsModel._score_listings_before_primetime_end(
            listing_lookup, date_time)
        self.assertEqual(expected_output, scored_output)

    def test_listing_sorting_by_score(self):
        scored_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 2},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 100},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 3}
        }
        expected_output = {
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 1},
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 2},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 3},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T01:30:00+00:00",
                 "score": 100}
        }
        sorted_output = TvHighlightsModel._sort_scored_listings(scored_lookup)
        self.assertEqual(expected_output, sorted_output)

    def test_listing_sorting_by_date(self):
        scored_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:30+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-03-01T12:30:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-02-01T12:30:00+00:00",
                 "score": 1}
        }
        expected_output = {
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:30+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-02-01T12:30:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 1,
                 "startDate": "2019-03-01T12:30:00+00:00",
                 "score": 1}
        }
        sorted_output = TvHighlightsModel._sort_scored_listings(scored_lookup)
        self.assertEqual(expected_output, sorted_output)

    def test_listing_sorting_general(self):
        scored_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T10:30:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T10:00:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T12:30:00+00:00",
                 "score": 1}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            22: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T12:30:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T10:00:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T10:30:00+00:00",
                 "score": 1}
        }
        sorted_output = TvHighlightsModel._sort_scored_listings(scored_lookup)
        self.assertEqual(expected_output, sorted_output)

    def test_deduplicate_listings(self):
        sorted_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            12: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:30:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T10:00:00+00:00",
                 "score": 2},
            22: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T10:30:00+00:00",
                 "score": 2}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T12:00:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T10:00:00+00:00",
                 "score": 2}
        }
        deduplicated_output = TvHighlightsModel._deduplicate_scored_listings(
            sorted_lookup)
        self.assertEqual(expected_output, deduplicated_output)

    def test_sort_listings_subcategory_call(self):
        deduplicated_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:00:00+00:00",
                 "score": 1,
                 "subCategoryIds": ['1', '2']},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T20:30:00+00:00",
                 "score": 1,
                 "subCategoryIds": ['1', '3']},
            31: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-01T20:15:00+00:00",
                 "score": 1,
                 "subCategoryIds": ['3', '2']},
            41: {"episodeId": "E4",
                 "brandId": 4,
                 "startDate": "2019-01-01T20:45:00+00:00",
                 "score": 2,
                 "subCategoryIds": ['1', '4']}
        }
        expected_output = [11, 31, 21]
        sorted_subcategory_listings = TvHighlightsModel._select_listings_subcategory_call(
            deduplicated_lookup, '2', 3)
        self.assertEqual(expected_output, sorted_subcategory_listings)

    def test_sort_top_items(self):
        deduplicated_lookup = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:00:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T20:30:00+00:00",
                 "score": 1},
            31: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-01T20:15:00+00:00",
                 "score": 1},
            41: {"episodeId": "E4",
                 "brandId": 4,
                 "startDate": "2019-01-01T20:45:00+00:00",
                 "score": 2}
        }
        expected_output = {
            11: {"episodeId": "E1",
                 "brandId": 1,
                 "startDate": "2019-01-01T20:00:00+00:00",
                 "score": 1},
            31: {"episodeId": "E3",
                 "brandId": 3,
                 "startDate": "2019-01-01T20:15:00+00:00",
                 "score": 1},
            21: {"episodeId": "E2",
                 "brandId": 2,
                 "startDate": "2019-01-01T20:30:00+00:00",
                 "score": 1}
        }
        sorted_top_items = TvHighlightsModel._sort_top_items(deduplicated_lookup, 3)
        self.assertEqual(expected_output, sorted_top_items)

    def test_return_items_with_metadata(self):
        metadata = {
            11: {"attributes": "metadata for 11"},
            21: {"attributes": "metadata for 21"},
            12: {"attributes": "metadata for 12"}
        }
        sorted_items = {
            11: ["listing data for 11"],
            21: ["listing data for 21"],
            12: ["listing data for 12"]
        }
        expected_output = [
            {"_source": {"thing": {"attributes": "metadata for 11"}}},
            {"_source": {"thing": {"attributes": "metadata for 21"}}},
            {"_source": {"thing": {"attributes": "metadata for 12"}}},
        ]
        returned_items = TvHighlightsModel._return_episodes_with_metadata(
            metadata, sorted_items)
        self.assertEqual(expected_output, returned_items)



