from typing import List
from unittest import TestCase
from unittest.mock import Mock, patch

from elasticsearch_dsl import Q, Search

from thefilter.inference.logic.query_builder import ElasticSearchQueryBuilder
from thefilter.rules.ruleset import Condition, ConditionalTuner, Rule, Ruleset


class TestElasticSearchQueryBuilder(TestCase):
    def test_modifies_search_object(self):
        unmodified_search_obj = Search().query("terms", something="another")
        search_obj = Search().query("terms", something="another")
        rule = Rule(attribute="name", operator="EQUALS", value="this")
        tuners = [
            ConditionalTuner(conditions=Condition(None, None, None, None),
                             action="INCLUDE",
                             boolean="AND",
                             rules=[rule])
        ]
        ElasticSearchQueryBuilder.build(
            search_obj,
            Ruleset(id="1", size=1, tuners=tuners))
        self.assertNotEqual(unmodified_search_obj, search_obj)

    @patch(
        "thefilter.inference.logic.query_builder.ElasticSearchQueryBuilder._nested_query"
    )
    def test_catches_nested_objects(self, mock_nested_query_func):
        ElasticSearchQueryBuilder._create_rule_query_part(
            "offers.start", lambda f: "f", "value", {})
        mock_nested_query_func.assert_called()

    @patch(
        "thefilter.inference.logic.query_builder.BaseQueryBuilder._content_rating_rule",
        return_value=("attr", "val"))
    def test_catches_content_ratings(self, mock_content_rating_func):
        ElasticSearchQueryBuilder._create_rule_query_part(
            "contentRating", lambda f: lambda x: "terms", "pg", {})
        mock_content_rating_func.assert_called()

    def test_content_rating_rule_transformed(self):
        q_part = ElasticSearchQueryBuilder._create_rule_query_part(attribute="contentRating",
                                                                   operator=lambda value: lambda attribute: Q("term",
                                                                                                              **{
                                                                                                                  attribute: value}),
                                                                   value="pg",
                                                                   mapping={},
                                                                   check_exists=False)
        expected = {"term": {"thing.custom.ratingRank": 2}}
        self.assertDictEqual(expected, q_part.to_dict())

    def test_content_rating_rule_transformed_check_exists(self):
        q_part = ElasticSearchQueryBuilder._create_rule_query_part(attribute="contentRating",
                                                                   operator=lambda value: lambda attribute: Q("term",
                                                                                                              **{
                                                                                                                  attribute: value}),
                                                                   value="pg",
                                                                   mapping={},
                                                                   check_exists=True)
        expected = {'bool': {'should': [{'bool': {'must_not': [{'exists': {'field': 'thing.custom.ratingRank'}}]}},
                                        {'term': {'thing.custom.ratingRank': 2}}]}}
        self.assertDictEqual(expected, q_part.to_dict())

    def test_that_only_apply_rule_if_attribute_exists(self):
        query_part = ElasticSearchQueryBuilder._create_rule_query_part(
            "attribute", lambda value: lambda attribute: Q(
                "term", **{attribute: value}), "value", {})
        expected_query = {
            "bool": {
                "should": [{
                    "bool": {
                        "must_not": [{
                            "exists": {
                                "field": "thing.attribute"
                            }
                        }]
                    }
                }, {
                    "term": {
                        "thing.attribute": "value"
                    }
                }]
            }
        }
        self.assertDictEqual(expected_query, query_part.to_dict())

    def test_add_tuner_to_query(self):
        def and_boolean(rules_list: List[Q]):
            booled_rules = rules_list[0]
            for rule in rules_list[1:]:
                booled_rules = booled_rules & rule
            return booled_rules

        rules = [Q("terms", thing="value")]

        search_obj = Search().query("terms", something="another")
        ElasticSearchQueryBuilder._add_tuner_to_query(search_obj, and_boolean, rules)

        search_obj_with_tuner = {
            "query": {
                "bool": {
                    "filter": [{
                        "terms": {
                            "thing": "value"
                        }
                    }],
                    "must": [{
                        "terms": {
                            "something": "another"
                        }
                    }]
                }
            }
        }
        self.assertDictEqual(search_obj_with_tuner, search_obj.to_dict())

    def test_add_tuner_to_query_uses_boolean(self):
        mock_boolean = Mock()
        ElasticSearchQueryBuilder._add_tuner_to_query(Mock(), mock_boolean,
                                                      Mock())
        mock_boolean.assert_called()

    def test_nested_query_top_level(self):
        query = ElasticSearchQueryBuilder._nested_query(
            "offers.start", lambda value: lambda attribute: Q(
                "term", **{attribute: value}), "value", {})

        expected_nest = {
            "nested": {
                "path": "offers",
                "query": {
                    "term": {
                        "offers.start": "value"
                    }
                }
            }
        }
        self.assertDictEqual(expected_nest, query.to_dict())

    def test_nested_query_second_level(self):
        query = ElasticSearchQueryBuilder._nested_query(
            "something.offers.start", lambda value: lambda attribute: Q(
                "term", **{attribute: value}), "value", {})
        expected_nest = {
            "nested": {
                "path": "something.offers",
                "query": {
                    "term": {
                        "something.offers.start": "value"
                    }
                }
            }
        }
        self.assertDictEqual(expected_nest, query.to_dict())

    def test_returns_correct_include_booleans(self):
        inc_and = ElasticSearchQueryBuilder._include_boolean("AND")
        inc_or = ElasticSearchQueryBuilder._include_boolean("OR")
        self.assertEqual(ElasticSearchQueryBuilder._and, inc_and)
        self.assertEqual(ElasticSearchQueryBuilder._or, inc_or)

    def test_returns_correct_exclude_booleans(self):
        exc_and = ElasticSearchQueryBuilder._exclude_boolean("AND")
        exc_or = ElasticSearchQueryBuilder._exclude_boolean("OR")
        self.assertEqual(ElasticSearchQueryBuilder._or, exc_and)
        self.assertEqual(ElasticSearchQueryBuilder._and, exc_or)

    def test_and(self):
        rules = [
            Q("terms", attribute="value"),
            Q("term", another_attribute="another_value"),
            Q("term", a_further_attribute="a_futher_value")
        ]
        booled_rules = ElasticSearchQueryBuilder._and(rules)
        expected_query = {
            "bool": {
                "must": [{
                    "terms": {
                        "attribute": "value"
                    }
                }, {
                    "term": {
                        "another_attribute": "another_value"
                    }
                }, {
                    "term": {
                        "a_further_attribute": "a_futher_value"
                    }
                }]
            }
        }
        self.assertDictEqual(expected_query, booled_rules.to_dict())

    def test_or(self):
        rules = [
            Q("terms", attribute="value"),
            Q("term", another_attribute="another_value"),
            Q("term", a_further_attribute="a_futher_value")
        ]
        booled_rules = ElasticSearchQueryBuilder._or(rules)
        expected_query = {
            "bool": {
                "should": [{
                    "terms": {
                        "attribute": "value"
                    }
                }, {
                    "term": {
                        "another_attribute": "another_value"
                    }
                }, {
                    "term": {
                        "a_further_attribute": "a_futher_value"
                    }
                }]
            }
        }
        self.assertDictEqual(expected_query, booled_rules.to_dict())

    def test_only_return_active_items(self):
        query = Search().filter("terms", example="test")
        ElasticSearchQueryBuilder._only_return_active_items(query)
        expected_query = {"query": {"bool": {"filter": [{"terms": {"example": "test"}},
                                                        {"bool": {"must_not": [
                                                            {"term": {"thing.custom.active.state": False}}]}}]}}}
        self.assertDictEqual(expected_query, query.to_dict())
