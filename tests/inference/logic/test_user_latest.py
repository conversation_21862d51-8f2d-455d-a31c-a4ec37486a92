import unittest
from unittest.mock import <PERSON>Mock, patch

import freezegun

from thefilter.errors.personalisation_errors import \
    MissingRequestAttributeError
from thefilter.inference.logic.configuration import Fulfilment, ModelDefinition, \
    RangeFulfilment
from thefilter.inference.logic.invokable_models import MostRecentlyViewed
from thefilter.inference.logic.query_builder import ElasticSearchQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubMetadataLiteRepository
from thefilter.rules.ruleset import Ruleset

ENDPOINT = None
INDEX = "dummy_index"


@freezegun.freeze_time('2022-06-04T23:59:00+00:00')
class MostRecentlyViewedTests(unittest.TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def test_no_user_or_anon_id(self):
        model = MostRecentlyViewed(NoOpTracerFactory(), ElasticSearchQueryBuilder(),
                                   StorageMetadataRepository({}),
                                   StubMetadataLiteRepository({}),
                                   StorageUserRepository([]), "uktv", ENDPOINT, 100)
        with self.assertRaises(MissingRequestAttributeError):
            model.invoke(
                ModelDefinition(
                    key="",
                    source="",
                    version=1,
                    fulfilment=Fulfilment(ranges=[RangeFulfilment(0, 20)]),
                    parameters={},
                    block=None,
                    key_strategy={}
                ),
                Request(query_string={}),
                Ruleset(id='', size=50, tuners=[])
            )

    @patch.object(MostRecentlyViewed, "_enforce_rules")
    def test_no_results(self, mock_enforce_rules):
        mock_enforce_rules.return_value = iter([])
        model = MostRecentlyViewed(NoOpTracerFactory(), ElasticSearchQueryBuilder(),
                                   StorageMetadataRepository({}),
                                   StubMetadataLiteRepository({}),
                                   StorageUserRepository([]), "uktv", ENDPOINT, 10)
        result = model.invoke(
            ModelDefinition(
                key="",
                source="",
                version=1,
                fulfilment=Fulfilment(ranges=[RangeFulfilment(0, 20)]),
                parameters={},
                block=None,
                key_strategy={}
            ),
            Request(
                query_string={"userId": "user_id"},
                size=50
            ),
            Ruleset(id='', size=50, tuners=[])
        )

        self.assertEqual([], result)

    @patch.object(MostRecentlyViewed, "_enforce_rules")
    def test_results(self, mock_enforce_rules):
        active_publication = [
            {
                'startDate': '2018-01-04T23:59:00+00:00',
                'endDate': '2022-12-04T23:59:00+00:00'
            }
        ]

        user_repository = StorageUserRepository([
            {'thing': [{"id": "ep1"}],
             'timestamp': {'initiated': '2019-07-21T00:00:02+00:00'}},
            {'thing': [{"id": "ep2"}],
             'timestamp': {'initiated': '2019-07-21T00:11:41+00:00'}},
            {'thing': [{"id": "ep3"}],
             'timestamp': {'initiated': '2019-07-21T00:08:41+00:00'}}
        ])

        mock_enforce_rules.return_value = iter([
            {'id': '116_brand', 'typeName': 'Brand'},
            {'id': '117_brand',
             'typeName': 'Brand'}
        ])

        metadata = {
            "ep1": {
                'thing': { 'brandId': '120', 'custom': {'brand': [{'id': 120}], 'active': {'state': True}}},
                'timestamp': {'initiated': '2019-07-21T00:00:02+00:00'},
                "publication": active_publication},
            "ep2": {
                'thing': { 'brandId': '116', 'custom': {'brand': [{'id': 116}], 'active': {'state': True}}},
                'timestamp': {'initiated': '2019-07-21T00:11:41+00:00'},
                "publication": active_publication},
            "ep3": {
                'thing': { 'brandId': '117', 'custom': {'brand': [{'id': 117}], 'active': {'state': True}}},
                'timestamp': {'initiated': '2019-07-21T00:08:41+00:00'},
                "publication": active_publication},
            "116_brand": {"thing": {"brandId": "116", "id": "116", "typeName": "Brand",
                                    "custom": {"parentBrandId": "a",
                                               'active': {'state': True}},
                                    "publication": active_publication}},
            "117_brand": {"thing": {"brandId": "117", "id": "117", "typeName": "Brand",
                                    "custom": {"parentBrandId": "b",
                                               'active': {'state': True}},
                                    "publication": active_publication}
                          }
        }

        model = MostRecentlyViewed(NoOpTracerFactory(), ElasticSearchQueryBuilder(),
                                   StorageMetadataRepository(metadata),
                                   StubMetadataLiteRepository(metadata),
                                   user_repository,
                                   "uktv", ENDPOINT,
                                   100)

        result = model.invoke(
            ModelDefinition(
                key="",
                source="",
                version=1,
                fulfilment=Fulfilment(ranges=[RangeFulfilment(0, 20)]),
                parameters={},
                block=None,
                key_strategy={}
            ),
            Request(
                query_string={"userId": "user_id", "space": "brand"},
                size=50
            ),
            Ruleset(id='', size=50, tuners=[])
        )

        self.assertEqual([{"id": "116_brand", "typeName": "Brand"},
                          {"id": "117_brand", "typeName": "Brand"}]
                         , result)

    @patch.object(MostRecentlyViewed, "_enforce_rules")
    def test_results_no_brand_ids(self, mock_enforce_rules):
        user_repository = StorageUserRepository([{'thing': [{"id": "ep1"}],
                                                  'timestamp': {
                                                      'initiated': '2019-07-21T00:00:02+00:00'}},
                                                 {'thing': [{"id": "ep2"}],
                                                  'timestamp': {
                                                      'initiated': '2019-07-21T00:11:41+00:00'}},
                                                 {'thing': [{"id": "ep3"}],
                                                  'timestamp': {
                                                      'initiated': '2019-07-21T00:08:41+00:00'}}
                                                 ])
        mock_enforce_rules.return_value = iter([])
        metadata = {
            "ep1": {'timestamp': {'initiated': '2019-07-21T00:00:02+00:00'}},
            "ep2": {'timestamp': {'initiated': '2019-07-21T00:11:41+00:00'}},
            "ep3": {'timestamp': {'initiated': '2019-07-21T00:08:41+00:00'}}
        }

        model = MostRecentlyViewed(NoOpTracerFactory(), ElasticSearchQueryBuilder(),
                                   StorageMetadataRepository(metadata),
                                   StubMetadataLiteRepository(metadata),
                                   user_repository,
                                   "uktv", ENDPOINT,
                                   100)
        result = model.invoke(MagicMock(),
                              Request(query_string={"userId": "user_id"}, size=50),
                              Ruleset(id='', size=50, tuners=[]))

        self.assertEqual([], result)
