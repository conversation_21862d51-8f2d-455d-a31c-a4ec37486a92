import json
import unittest
from os.path import join
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

from thefilter.errors.personalisation_errors import QueryError
from thefilter.inference.logic.invokable_models import UKTVSimpleElasticItemSimilarity
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request


class UKTVElasticSimilarityTests(unittest.TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.es_endpoint = 'not_an_endpoint'
        with open(join('tests', 'data', 'uktv_mlt_base_query_brand.json'), 'r') as f:
            self.uktv_base_query_brand = json.load(f)
        with open(join('tests', 'data', 'uktv_mlt_base_query_episode.json'), 'r') as f:
            self.uktv_base_query_episode = json.load(f)
        with open(join('thefilter', 'inference', 'logic', 'uktv_mlt_base.json'), 'r') as f:
            self.uktv_template = json.load(f)

    def test_missing_size(self):
        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )

        with self.assertRaises(Exception) as e:
            # None for the ruleset as we are not testing with one
            similarity.invoke(None, Request(seedIds=['item_id']), None)

        self.assertTrue('Request parameter size not set for call for item similarity' in str(e.exception))

    def test_invalid_item_id(self):
        """
        Ensure an empty list returned when invalid item id is passed.

        """
        mock_response = Mock()
        mock_response.to_dict.return_value = {}
        mock_search = Mock()
        mock_search.execute.return_value = mock_response

        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )

        result = similarity._execute_query(mock_search)

        self.assertEqual([], result)

    def test_status_400_throws(self):
        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )

        mock_failure = Mock()
        mock_failure.success.return_value = False
        mock_search = Mock()
        mock_search.execute.return_value = mock_failure
        mock_search.to_dict.return_value = {}
        with self.assertRaises(QueryError):
            similarity._execute_query(mock_search)

    def test_format_return(self):
        results = [{'_index': 'some_index',
                    '_type': '_doc',
                    '_id': 'doc_id',
                    '_score': 577.4794,
                    '_source': {
                        'thing': {'typeName': 'Movie', 'id': 'another_item_id'}}}]

        expected = [
            {"id": "doc_id", "typeName": "Movie"}
        ]

        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )

        formatted_results = similarity._format_return(results, Request(size=1, seedIds=[
            'item_id']))

        self.assertEqual(expected, formatted_results)

    def test_update_uktv_template_brand(self):
        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )
        filled_in_template = similarity._update_base_query_template(
            self.uktv_template,
            Request(size=10,
                    seedIds=[
                        '1234'],
                    query_string={
                        'space': 'brand'}),
            Mock(),
            Mock()
        )
        self.maxDiff = None
        self.assertDictEqual(self.uktv_base_query_brand, filled_in_template)

    def test_update_uktv_template_episode(self):
        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )
        filled_in_template = similarity._update_base_query_template(
            self.uktv_template,
            Request(size=10,
                    seedIds=[
                        '1234'],
                    query_string={}),
            Mock(),
            Mock()
        )
        self.maxDiff = None
        self.assertDictEqual(self.uktv_base_query_episode, filled_in_template)

    @patch(
        'thefilter.inference.logic.invokable_models'
        '.UKTVSimpleElasticItemSimilarity._execute_query')
    def test_correct_response(self, _send_query_mock):
        expected = [
            {"id": "doc_id", "typeName": "Movie"}
        ]
        similarity = UKTVSimpleElasticItemSimilarity(
            endpoint=self.es_endpoint,
            query_builder=Mock(),
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=MagicMock(),
            customer="UKTV",
            user_repository=MagicMock()
        )

        _send_query_mock.return_value = [
            {
                '_index': 'some_index',
                '_type': '_doc',
                '_id': 'doc_id',
                '_score': 577.4794,
                '_source': {
                    'thing': {
                        'typeName': 'Movie',
                        'id': 'another_item_id'
                    }
                }
            }
        ]

        result = similarity.invoke(
            None,
            Request(size=1, seedIds=['item_id'], query_string={}), None)

        self.assertEqual(expected, result)
