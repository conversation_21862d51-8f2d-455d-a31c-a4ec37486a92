from datetime import datetime, timezone
from unittest import TestCase
from unittest.mock import patch

import freezegun

from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.recommendation import ModelAwareStubRecommendationRepository, \
    StubUserRecommendationRepo, StubColdUserSupportingRecommendationRepo
from thefilter.inference.logic.configuration import Fulfilment, \
    RangeFulfilment, ModelDefinition
from thefilter.inference.logic.invokable_models import PrebuiltModel, \
    ElasticsearchRuleEnforcingModel
from thefilter.inference.logic.query_builder import NoOpQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubEditorialRepository, StubModelConfigRepository
from thefilter.rules.ruleset import Ruleset

active_publication = [
    {
        'startDate': '2022-01-04T23:59:00+00:00',
        'endDate': '2022-12-04T23:59:00+00:00'
    }
]

custom_active_state = {
    'active': {
        'state': True
    }
}

metadata_repository = StorageMetadataRepository({
    "series_1_episode_1": {
        "thing": {
            "id": "series_1_episode_1",
            "partOfSeries": {
                "id": "series_1"
            },
            "custom": custom_active_state,
            "publication": active_publication
        }
    },
    "series_4_episode_4_brand": {
        "thing": {
            "id": "series_4_episode_4",
            "partOfSeries": {
                "id": "series_4"
            },
            "custom": custom_active_state,
            "publication": active_publication
        }
    },
    "movie_123": {
        "thing": {
            "id": "movie_123",
            "name": "Movie One-Two-Three",
            "typeName": "Movie",
            "custom": custom_active_state,
            "publication": active_publication
        }
    },
    "movie_456": {
        "thing": {
            "id": "movie_456",
            "name": "Movie Four-Five-Six",
            "typeName": "Movie",
            "custom": custom_active_state,
            "publication": active_publication
        }
    }
})

user_history = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
     'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_4_episode_4', 'space': 'brand',
                'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'movie_123', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]}
]

rec_repo_results_model_based = {
    'model_a': [
        {
            "id": "movie_123",
            "typeName": "Movie"
        },
        {
            "id": "movie_456",
            "typeName": "Movie"
        }
    ],
    'model_b': [
        {
            "id": "movie_456",
            "typeName": "Movie"
        },
        {
            "id": "movie_123",
            "typeName": "Movie"
        }
    ],
    'profile_model': [
        {
            "id": "profile_movie_456",
            "typeName": "Movie"
        },
        {
            "id": "profile_movie_123",
            "typeName": "Movie"
        }
    ]
}

rec_repo_results_user_based = {
    'user_a':
        {
            'model_a': [
                {
                    "id": "movie_abc",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_bcd",
                    "typeName": "Movie"
                }
            ],
            'model_b': [
                {
                    "id": "movie_cde",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_efg",
                    "typeName": "Movie"
                }
            ]
        },
    'user_b':
        {
            'model_a': [
                {
                    "id": "movie_fgh",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_ghi",
                    "typeName": "Movie"
                }
            ],
            'model_b': [
                {
                    "id": "movie_hij",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_ijk",
                    "typeName": "Movie"
                }
            ]
        },
    'test_profile':
        {
            'model_a': [
                {
                    "id": "profile_movie_456",
                    "typeName": "Movie"
                },
                {
                    "id": "profile_movie_123",
                    "typeName": "Movie"
                }
            ]
        },
    '__cold':
        {
            'model_a': [
                {
                    "id": "movie_jkl",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_klm",
                    "typeName": "Movie"
                }
            ],
            'model_b': [
                {
                    "id": "movie_lmn",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_mno",
                    "typeName": "Movie"
                }
            ]
        },
    '__cold_free':
        {
            'model_a': [
                {
                    "id": "movie_nop",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_opq",
                    "typeName": "Movie"
                }
            ],
            'model_b': [
                {
                    "id": "movie_pqr",
                    "typeName": "Movie"
                },
                {
                    "id": "movie_qrs",
                    "typeName": "Movie"
                }
            ]
        }
}

model_aware_stub_model = PrebuiltModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=ModelAwareStubRecommendationRepository(
        results_by_model=rec_repo_results_model_based
    ),
    model_config_repository=StubModelConfigRepository(
        records=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StorageUserRepository(user_history),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=100
)

stub_repo_model = PrebuiltModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=StubUserRecommendationRepo(
        results=rec_repo_results_user_based
    ),
    model_config_repository=StubModelConfigRepository(
        records=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StorageUserRepository(user_history),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=100
)

cold_stub_repo_model = PrebuiltModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=StubColdUserSupportingRecommendationRepo(
        results=rec_repo_results_user_based
    ),
    model_config_repository=StubModelConfigRepository(
        records=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StorageUserRepository(user_history),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=100
)


@freezegun.freeze_time('2022-06-04T23:59:00+00:00')
class TestPrebuiltModel(TestCase):

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_model_based['model_a'])
    def test_model_invocation_model_aware_repo(self, inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='test_user',
            query_string={'ignore': 'postman',
                          'userId': 'test_user'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = model_aware_stub_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_model_based.get('model_a'), result)

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_user_based['user_a']['model_a'])
    def test_model_invocation_stub_repo(self, inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'test_user'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = stub_repo_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_user_based.get('user_a').get('model_a'),
                         result)

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_user_based['__cold']['model_a'])
    def test_model_invocation_stub_repo_cold(self, inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='__cold',
            query_string={'ignore': 'postman',
                          'userId': 'test_user'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = stub_repo_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_user_based.get('__cold').get('model_a'),
                         result)

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_user_based['__cold']['model_a'])
    def test_model_invocation_cold_fallback_stub_repo(self, inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_c',
            query_string={'ignore': 'postman',
                          'userId': 'test_user'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = cold_stub_repo_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_user_based.get('__cold').get('model_a'),
                         result)

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_user_based['__cold_free']['model_a'])
    def test_model_invocation_cold_fallback_with_key_strategy(self,
                                                              inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={'name': 'SubscriberKeyStrategy'}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_c',
            query_string={'ignore': 'postman',
                          'userId': 'test_user',
                          'subscriptionType': 'free'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = cold_stub_repo_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_user_based.get('__cold_free').get('model_a'),
                         result)

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules",
                  return_value=rec_repo_results_model_based['profile_model'])
    def test_model_profileId(self, inner_enforce_rules_patched):
        model_definition = ModelDefinition(
            key='model_a',
            version=1,
            source='PrebuiltModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': '',
                'useProfileId': 'true'
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='test_user',
            profileId='test_profile',
            query_string={'ignore': 'postman',
                          'userId': 'test_user',
                          'profileId': 'test_profile'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = stub_repo_model.invoke(model_definition, request, ruleset)
        self.assertEqual(rec_repo_results_model_based.get('profile_model'), result)
