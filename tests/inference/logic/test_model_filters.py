from copy import deepcopy
from datetime import datetime, timezone
from unittest import TestCase
from unittest.mock import MagicMock

from thefilter.inference.logic.configuration import InvocationResult, ModelDefinition, \
    InvocationItem, Fulfilment, RangeFulfilment
from thefilter.inference.logic.model_filters import ModelFilters
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository
from thefilter.repositories import StubMetadataLiteRepository

metadata_items = {
    "test_id_0": {
        "thing": {
            "id": "test_id_0",
            'name': 'test_name_0',
            "typeName": "TVSeries",
            "genres": ["Drama", "Action"]
        }
    },
    "test_id_1": {
        "thing": {
            "id": "test_id_1",
            'name': 'test_name_1',
            "typeName": "Movie",
            'genres': ["Action", "Science Fiction and Fantasy"]
        }
    },
    "0": {
        "thing": {
            'id': '0',
            'brandId': '0',
            'name': '0',
            'thing_custom_availabilityarray': "COUNTRY1,COUNTRY2"
        }
    },
    "1": {
        "thing": {
            'id': '1',
            'brandId': '1',
            'name': '1',
            'thing_custom_availabilityarray': "ES"
        }
    },
    "2": {
        "thing": {
            'id': '2',
            'brandId': '2',
            'name': '2',
            'thing_custom_availabilityarray': ""
        }
    },
    "3": {
        "thing": {
            'id': '3',
            'brandId': '3',
            'name': '3',
            'thing_custom_availabilityarray': "COUNTRY1,COUNTRY2"
        }
    },
    "4": {
        "thing": {
            'id': '4',
            'brandId': '4',
            'name': '4',
            'thing_custom_availabilityarray': "COUNTRY4",
            'thing_custom_subscriptionsarray': "COUNTRY5"
        }
    }
}


class TestModelFilters(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.metadata_lite_repository = None
        metadata_lite_repo = StubMetadataLiteRepository(metadata_items)
        chart_repo = StubChartReadRepository()
        self._api = ModelFilters(metadata_lite_repo, chart_repo)

    def test_get_request_params(self):
        request = Request(
            size=70,
            query_string={
                'chartName': 'latest-publicationdate-standard',
                'genre': 'Cocina,drama', 'typename': 'Movie'
            }
        )
        request_params = self._api._get_request_params(request)
        expected = {'genre': ['Cocina', 'drama'], 'typename': ['Movie']}
        self.assertEqual(expected, request_params)

        request = Request(
            size=70,
            query_string={}
        )
        request_params = self._api._get_request_params(request)
        expected = {}
        self.assertEqual(expected, request_params)

    def test_filter_model_combiner_no_count_in_charts(self):
        original_request_size = 10
        threshold_count = 8
        filter_inner_results = {
            'typename': {
                'original_result_ids': ['item_0', 'item_1', 'item_2'],
                'exclude_count': 90,
                'backfill_charts': {
                    'chart-0': ['item_3', 'item_4', 'item_5']
                }
            },
            'genre': {
                'original_result_ids': ['item_6', 'item_1', 'item_7'],
                'exclude_count': 90,
                'backfill_charts': {
                    'chart-1': ['item_0', 'item_8', 'item_9'],
                    'chart-2': ['item_10', 'item_11', 'item_5']
                }
            }
        }
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(
                    thing={
                        'id': 'item_0', 'brandId': 'item_0', 'name': 'item_0 name',
                        'typeName': 'TVSeries', 'genres': ['dud_genre']
                    },
                    position_in_model_result=0
                ),
                InvocationItem(
                    thing={
                        'id': 'item_1', 'brandId': 'item_1', 'name': 'item_1 name',
                        'typeName': 'Movie', 'genres': ['dud_genre']
                    },
                    position_in_model_result=1
                ),
                InvocationItem(
                    thing={
                        'id': 'item_2', 'brandId': 'item_2', 'name': 'item_2 name',
                        'typeName': 'TVSeries', 'genres': ['drama']
                    },
                    position_in_model_result=2
                )
            ]
        )

        # 1. Interleave
        use_count = False

        combined_results = self._api._model_filter_combiner(
            invocation_result, filter_inner_results, original_request_size,
            threshold_count, use_count
        )
        expected = (
            ['item_1', 'item_3', 'item_0', 'item_10', 'item_4',
             'item_8', 'item_11', 'item_5', 'item_9'],
            False
        )
        self.assertEqual(expected, combined_results)

        # 2. if use_count is set, we still expect the same interleaved results
        # as there are no counts in the charts
        use_count = True

        combined_results = self._api._model_filter_combiner(
            invocation_result, filter_inner_results, original_request_size,
            threshold_count, use_count
        )
        self.assertEqual(expected, combined_results)

    def test_filter_model_combiner_with_count_in_charts(self):
        original_request_size = 10
        threshold_count = 8
        filter_inner_results = {
            'typename': {
                'original_result_ids': ['item_0', 'item_1', 'item_2'],
                'exclude_count': 90,
                'backfill_charts': {
                    'chart-0': [
                        ['item_3', 'Movie', 100],
                        ['item_4', 'Movie', 90],
                        ['item_5', 'Movie', 20]
                    ]
                }
            },
            'genre': {
                'original_result_ids': ['item_6', 'item_1', 'item_7'],
                'exclude_count': 90,
                'backfill_charts': {
                    'chart-1': [
                        ['item_0', 'Movie', 50],
                        ['item_8', 'Movie', 40],
                        ['item_9', 'Movie', 30]
                    ],
                    'chart-2': [
                        ['item_10', 'Movie', 80],
                        ['item_11', 'Movie', 70],
                        ['item_5', 'Movie', 35]
                    ]
                }
            }
        }
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(
                    thing={
                        'id': 'item_0', 'brandId': 'item_0', 'name': 'item_0 name',
                        'typeName': 'TVSeries', 'genres': ['dud_genre']
                    },
                    position_in_model_result=0
                ),
                InvocationItem(
                    thing={
                        'id': 'item_1', 'brandId': 'item_1', 'name': 'item_1 name',
                        'typeName': 'Movie', 'genres': ['dud_genre']
                    },
                    position_in_model_result=1
                ),
                InvocationItem(
                    thing={
                        'id': 'item_2', 'brandId': 'item_2', 'name': 'item_2 name',
                        'typeName': 'TVSeries', 'genres': ['drama']
                    },
                    position_in_model_result=2
                )
            ]
        )

        # 1. Interleave
        use_count = False

        combined_results = self._api._model_filter_combiner(
            invocation_result, filter_inner_results, original_request_size,
            threshold_count, use_count
        )
        expected = (
            ['item_1', 'item_3', 'item_0', 'item_10', 'item_4',
             'item_8', 'item_11', 'item_5', 'item_9'],
            False
        )
        self.assertEqual(expected, combined_results)

        # 2. if use_count is set, we still expect the same interleaved results
        # as there are no counts in the charts
        use_count = True

        combined_results = self._api._model_filter_combiner(
            invocation_result, filter_inner_results, original_request_size,
            threshold_count, use_count
        )
        expected = (
            ['item_1', 'item_3', 'item_4', 'item_10', 'item_11',
             'item_0', 'item_8', 'item_5', 'item_9'],
            False
        )
        self.assertEqual(expected, combined_results)

    def test_filter_genre(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={
                "genre": "Drama"
            },
            request_start=datetime(2022, 9, 3, 10, 48, 27, 274161,
                                   tzinfo=timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'name': 'test_name_0',
                           'typeName': 'TVSeries',
                           'genres': ["Drama", "Action"],
                           },
                    position_in_model_result=0),
                InvocationItem(
                    thing={'id': 'test_id_1', 'name': 'test_name_1', 'typeName': 'Movie',
                           'genres': ["Action", "Science Fiction and Fantasy"],
                           },
                    position_in_model_result=1)],
            model_info=None,
            seed_info=None,
            is_empty=False)

        actual = self._api.apply_filters(
            invocation_result, request, original_request_size=10,
            customer={"name": "test_customer"}, slot={"type": "most_popular"}
        )

        expected = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={
                        'id': 'test_id_0', 'name': 'test_name_0', 'typeName': 'TVSeries',
                        'genres': ["Drama", "Action"],
                    },
                    position_in_model_result=0),
            ],
            model_info=None,
            seed_info=None,
            is_empty=False)

        self.assertEqual(expected, actual, "Genre Filtered results match")

    def test_filter_typename(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            query_string={
                "typename": "TVSeries"
            },
            request_start=datetime(2022, 9, 3, 10, 48, 27, 274161,
                                   tzinfo=timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'name': 'test_name_0',
                           'typeName': 'TVSeries',
                           'genres': ["Drama", "Action"],
                           },
                    position_in_model_result=0),
                InvocationItem(
                    thing={'id': 'test_id_1', 'name': 'test_name_1', 'typeName': 'Movie',
                           'genres': ["Action"],
                           },
                    position_in_model_result=1)],
            model_info=None,
            seed_info=None,
            is_empty=False)

        actual = self._api.apply_filters(invocation_result, request,
                                         original_request_size=10,
                                         customer={"name": "test_customer"},
                                         slot={"type": "most_popular"})

        expected = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'name': 'test_name_0',
                           'typeName': 'TVSeries',
                           'genres': ["Drama", "Action"],
                           },
                    position_in_model_result=0),
            ],
            model_info=None,
            seed_info=None,
            is_empty=False)

        self.assertEqual(expected, actual, "Typename Filtered results match")

    def test_filter_genre_and_typename(self):
        request = Request(
            rule_id='91eff463-ad9c-4e57-8514-74c44cdf7108', size=138,
            seedIds=['test_id_0'], query_string={
                "genre": "Action",
                "typename": "TVSeries",
            },
            request_start=datetime(2022, 9, 3, 10, 48, 27, 274161,
                                   tzinfo=timezone.utc),
            exclude=[], headers={}, editorials=[], promotions={})

        invocation_result = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'name': 'test_name_0',
                           'typeName': 'TVSeries',
                           'genres': ["Drama", "Action"],
                           },
                    position_in_model_result=0),
                InvocationItem(
                    thing={'id': 'test_id_1', 'name': 'test_name_1',
                           'typeName': 'Movie',
                           'genres': ["Action", "Science Fiction and Fantasy"],
                           },
                    position_in_model_result=1)],
            model_info=None,
            seed_info=None,
            is_empty=False)

        actual = self._api.apply_filters(invocation_result, request,
                                         original_request_size=10,
                                         customer={"name": "test_customer"},
                                         slot={"type": "most_popular"})

        expected = InvocationResult(
            model=ModelDefinition(
                key='test_model', version=1, source='test_model',
                fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=88)]),
                parameters={}, block=None, key_strategy=None),
            model_results=[
                InvocationItem(
                    thing={'id': 'test_id_0', 'name': 'test_name_0',
                           'typeName': 'TVSeries',
                           'genres': ["Drama", "Action"],
                           },
                    position_in_model_result=0)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False)

        self.assertEqual(expected, actual, "Genre and type name filtered results match")

    def test_filter_rtve_country_code(self):
        # Test with a country code that is not in the list
        test_request_1 = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'countryCode': 'ES'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )

        invocation_result = InvocationResult(
            is_empty=False,
            model=None,
            model_info=None,
            seed_info=None,
            model_results=[
                InvocationItem(
                    thing={
                        'id': '0',
                        'brandId': '0',
                        'name': '0',
                        'thing_custom_availabilityarray': "COUNTRY1,COUNTRY2"
                    },
                    position_in_model_result=0
                ),
                InvocationItem(
                    thing={
                        'id': '1',
                        'brandId': '1',
                        'name': '1',
                        'thing_custom_availabilityarray': "ES"
                    },
                    position_in_model_result=1
                ),
                InvocationItem(
                    thing={
                        'id': '2',
                        'brandId': '2',
                        'name': '2',
                        'thing_custom_availabilityarray': ""
                    },
                    position_in_model_result=2
                ),
                InvocationItem(
                    thing={
                        'id': '3',
                        'brandId': '3',
                        'name': '3',
                        'thing_custom_availabilityarray': "COUNTRY1,COUNTRY2"
                    },
                    position_in_model_result=3
                ),
                InvocationItem(
                    thing={
                        'id': '4',
                        'brandId': '4',
                        'name': '4',
                        'thing_custom_availabilityarray': "COUNTRY4",
                        'thing_custom_subscriptionsarray': "COUNTRY5"
                    },
                    position_in_model_result=4
                )
            ]
        )

        expected = (
            InvocationResult(
                model=None,
                model_results=[
                    InvocationItem(
                        thing={'id': '1', 'brandId': '1', 'name': '1',
                               'thing_custom_availabilityarray': 'ES'},
                        position_in_model_result=0),
                    InvocationItem(
                        thing={'id': '2', 'brandId': '2', 'name': '2',
                               'thing_custom_availabilityarray': ''},
                        position_in_model_result=1)
                ],
                model_info=None,
                seed_info=None,
                is_empty=False)
        )

        items_after_filtering = self._api.apply_filters(
            deepcopy(invocation_result), test_request_1, original_request_size=10,
            customer={"name": "test_customer"}, slot={"type": "most_popular"}
        )
        self.assertEqual(expected, items_after_filtering)

        test_request_2 = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'countryCode': 'COUNTRY2'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )
        expected = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(
                    thing={'id': '0', 'brandId': '0', 'name': '0',
                           'thing_custom_availabilityarray': 'COUNTRY1,COUNTRY2'},
                    position_in_model_result=0), InvocationItem(
                    thing={'id': '2', 'brandId': '2', 'name': '2',
                           'thing_custom_availabilityarray': ''},
                    position_in_model_result=1), InvocationItem(
                    thing={'id': '3', 'brandId': '3', 'name': '3',
                           'thing_custom_availabilityarray': 'COUNTRY1,COUNTRY2'},
                    position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )

        items_after_filtering = self._api.apply_filters(
            deepcopy(invocation_result),
            test_request_2, original_request_size=10,
            customer={"name": "test_customer"},
            slot={"type": "most_popular"}
        )
        self.assertEqual(expected, items_after_filtering)

        test_request_3 = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )
        expected = (
            InvocationResult(
                model=None,
                model_results=[
                    InvocationItem(
                        thing={'id': '0', 'brandId': '0', 'name': '0',
                               'thing_custom_availabilityarray': 'COUNTRY1,COUNTRY2'},
                        position_in_model_result=0), InvocationItem(
                        thing={'id': '1', 'brandId': '1', 'name': '1',
                               'thing_custom_availabilityarray': 'ES'},
                        position_in_model_result=1), InvocationItem(
                        thing={'id': '2', 'brandId': '2', 'name': '2',
                               'thing_custom_availabilityarray': ''},
                        position_in_model_result=2), InvocationItem(
                        thing={'id': '3', 'brandId': '3', 'name': '3',
                               'thing_custom_availabilityarray': 'COUNTRY1,COUNTRY2'},
                        position_in_model_result=3), InvocationItem(
                        thing={'id': '4', 'brandId': '4', 'name': '4',
                               'thing_custom_availabilityarray': 'COUNTRY4',
                               'thing_custom_subscriptionsarray': "COUNTRY5"},
                        position_in_model_result=4)
                ],
                model_info=None,
                seed_info=None,
                is_empty=False
            )
        )

        items_after_filtering = self._api.apply_filters(
            deepcopy(invocation_result), test_request_3, original_request_size=10,
            customer={"name": "test_customer"},
            slot={"type": "most_popular"}
        )

        self.assertEqual(expected, items_after_filtering)

        test_request_4 = Request(
            rule_id='test_slot_id',
            size=6,
            seedIds=['test_seed_id'],
            userId='test_user',
            query_string={
                'countryCode': 'COUNTRY5'
            },
            request_start=datetime(2022, 8, 5, 10, 30, 00, 00,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'test_user_agent'},
            editorials=[]
        )
        expected = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(
                    thing={'id': '2', 'brandId': '2', 'name': '2',
                           'thing_custom_availabilityarray': ''},
                    position_in_model_result=0),
                InvocationItem(
                    thing={'id': '4', 'brandId': '4', 'name': '4',
                           'thing_custom_availabilityarray': 'COUNTRY4',
                           'thing_custom_subscriptionsarray': 'COUNTRY5'},
                    position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )

        items_after_filtering = self._api.apply_filters(
            deepcopy(invocation_result),
            test_request_4, original_request_size=10,
            customer={"name": "test_customer"},
            slot={"type": "most_popular"}
        )
        self.assertEqual(expected, items_after_filtering)

    def test_handle_rtve_mlt_logic(self):
        invocation_items = (
            InvocationResult(
                model=None,
                model_results=[
                    InvocationItem(
                        thing={'id': '00', 'brandId': '0', 'name': 'episode_0',
                               'typeName': 'TVEpisode'},
                        position_in_model_result=0),
                    InvocationItem(
                        thing={'id': '11', 'brandId': '1', 'name': 'episode_1',
                               'typeName': 'TVEpisode'},
                        position_in_model_result=1),
                    InvocationItem(
                        thing={'id': '1', 'brandId': '1', 'name': 'series_2',
                               'typeName': 'TVSeries'},
                        position_in_model_result=2),
                    InvocationItem(
                        thing={'id': '33', 'brandId': '3', 'name': 'episode_3',
                               'typeName': 'TVEpisode'},
                        position_in_model_result=3
                    ),
                    InvocationItem(
                        thing={'id': '3', 'brandId': '3', 'name': 'series_3',
                               'typeName': 'TVSeries'},
                        position_in_model_result=4
                    ),
                    InvocationItem(
                        thing={'id': '4', 'brandId': '4', 'name': 'series_4',
                               'typeName': 'TVSeries'},
                        position_in_model_result=5
                    ),
                ],
                model_info=None,
                seed_info=None,
                is_empty=False
            )
        )
        dummy_request = Request(size=6, query_string={}, seedIds=[])
        dummy_customer = {"name": "rtve"}
        actual = self._api._handle_rtve_mlt_logic(
            invocation_items,
            dummy_request,
            dummy_customer,
            original_request_size=6
        )

        expected = (
            InvocationResult(
                model=None,
                model_results=[
                    InvocationItem(
                        thing={'id': '00', 'brandId': '0', 'name': 'episode_0',
                               'typeName': 'TVEpisode'}, position_in_model_result=0),
                    InvocationItem(
                        thing={'id': '11', 'brandId': '1', 'name': 'episode_1',
                               'typeName': 'TVEpisode'}, position_in_model_result=1),
                    InvocationItem(
                        thing={'id': '33', 'brandId': '3', 'name': 'episode_3',
                               'typeName': 'TVEpisode'}, position_in_model_result=3),
                    InvocationItem(
                        thing={'id': '4', 'brandId': '4', 'name': 'series_4',
                               'typeName': 'TVSeries'},
                        position_in_model_result=5)
                ],
                model_info=None,
                seed_info=None,
                is_empty=False
            )
        )

        self.assertEqual(expected, actual)

    def test_filter_mlt_by_age_rating_seed_under_16(self):
        seed_id = "seed_under_16"
        self._api._metadata_lite_repository = MagicMock()
        self._api._metadata_lite_repository.get_item.return_value = {
            "thing": {"id": seed_id, "thing_custom_agerangeuid": "IF_REDAD1"}
        }

        invocation_result = InvocationResult(
            model=None,
            model_info={"seed": {"id": seed_id}},
            model_results=[
                InvocationItem(
                    thing={"id": "ok1", "thing_custom_agerangeuid": "IF_REDAD1"},
                    position_in_model_result=0,
                ),
                InvocationItem(
                    thing={"id": "ban1", "thing_custom_agerangeuid": "IF_REDAD4"},
                    position_in_model_result=1,
                ),
            ],
            seed_info=None,
            is_empty=False,
        )

        filtered = self._api._filter_mlt_by_age_rating(invocation_result)

        self.assertEqual(
            ["ok1"],
            [item.thing["id"] for item in filtered.model_results]
        )

    def test_filter_mlt_by_age_rating_seed_over_16(self):
        seed_id = "seed_over_16"
        self._api._metadata_lite_repository = MagicMock()
        self._api._metadata_lite_repository.get_item.return_value = {
            "thing": {"id": seed_id, "thing_custom_agerangeuid": "IF_REDAD4"}
        }

        invocation_result = InvocationResult(
            model=None,
            model_info={"seed": {"id": seed_id}},
            model_results=[
                InvocationItem(
                    thing={"id": "ok1", "thing_custom_agerangeuid": "IF_REDAD1"},
                    position_in_model_result=0,
                ),
                InvocationItem(
                    thing={"id": "ok2", "thing_custom_agerangeuid": "IF_REDAD4"},
                    position_in_model_result=1,
                ),
            ],
            seed_info=None,
            is_empty=False,
        )

        filtered = self._api._filter_mlt_by_age_rating(invocation_result)

        self.assertEqual(
            ["ok1", "ok2"],
            [item.thing["id"] for item in filtered.model_results]
        )

    def test_filter_mlt_by_age_rating_seed_no_rating(self):
        seed_id = "seed_no_rating"
        self._api._metadata_lite_repository = MagicMock()
        self._api._metadata_lite_repository.get_item.return_value = {"thing": {"id": seed_id}}

        invocation_result = InvocationResult(
            model=None,
            model_info={"seed": {"id": seed_id}},
            model_results=[
                InvocationItem(
                    thing={"id": "a", "thing_custom_agerangeuid": "IF_REDAD2"},
                    position_in_model_result=0,
                ),
                InvocationItem(
                    thing={"id": "b", "thing_custom_agerangeuid": "IF_REDAD6"},
                    position_in_model_result=1,
                ),
            ],
            seed_info=None,
            is_empty=False,
        )

        filtered = self._api._filter_mlt_by_age_rating(invocation_result)

        self.assertEqual(
            ["a", "b"],
            [item.thing["id"] for item in filtered.model_results]
        )

class TestModelFiltersRTVE(TestCase):
    def setUp(self):
        self.metadata_lite_repository = MagicMock()
        self.chart_read_repository = MagicMock()
        self.model_filters = ModelFilters(self.metadata_lite_repository, self.chart_read_repository)
        self.customer = {"name": "rtve"}
        self.filter_inner_results = {}

    def test_check_item_metadata_against_request_params_country_code(self):
        request_params = {'countryCode': ['ES']}
        item_metadata = {'thing': {'thing_custom_availabilityarray': 'ES,US'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result)

    def test_check_item_metadata_against_request_params_program_type_id(self):
        request_params = {'programTypeId': ['132534']}
        item_metadata = {'thing': {'thing_custom_programtypeid': '132534'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result)

    def test_check_item_metadata_against_request_params_typename(self):
        request_params = {'typename': ['TVSeries']}
        item_metadata = {'thing': {'typeName': 'TVSeries'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result)

    def test_check_item_metadata_against_request_params_all(self):
        item_metadata = {
            'thing': {
                'thing_custom_availabilityarray': 'ES,US',
                'thing_custom_programtypeid': '132534',
                'typeName': 'TVSeries'
            }
        }
        request_params = {'countryCode': ['ES'], 'programTypeId': ['132534'], 'typename': ['TVSeries']}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result)

        request_params = {'countryCode': ['MOON'], 'programTypeId': ['132534'], 'typename': ['TVSeries']}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result)

    def test_check_item_metadata_against_request_params_no_match(self):
        request_params = {'countryCode': ['ES'], 'programTypeId': ['132534'], 'typename': ['TVSeries']}
        item_metadata = {
            'thing': {
                'thing_custom_availabilityarray': 'US',
                'thing_custom_programtypeid': '123456',
                'typeName': 'Movie'
            }
        }
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result)

    def test_check_item_metadata_against_request_params_age_rating_and_typename(self):
        request_params = {'ageRating': ['IF_REDAD4'], 'typename': ['TVSeries']}
        item_metadata = {'thing': {'thing_custom_agerangeuid': 'IF_REDAD4', 'typeName': 'TVSeries'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result)

        item_metadata = {'thing': {'thing_custom_agerangeuid': 'IF_REDAD4', 'typeName': 'Movie'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result)

        item_metadata = {'thing': {'thing_custom_agerangeuid': 'IF_REDAD1', 'typeName': 'TVSeries'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result)

        request_params = {'ageRating': ['IF_REDAD0'], 'typename': ['TVSeries']}
        item_metadata = {'thing': {'thing_custom_agerangeuid': 'IF_REDAD6', 'typeName': 'TVSeries'}}
        result = self.model_filters._check_item_metadata_against_request_params(item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result)

    ### Exclude Genre ################

    def test_filter_exclude_genre_no_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'genres': ['Comedy']}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'genres': ['Action']}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_exclude_genre(
            invocation_result=invocation_result, filter_parameter='excludeGenre',
            filter_values=['Drama'], customer=self.customer, threshold_count=1,
            request_params={'excludeGenre': ['Drama']}
        )
        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(0, result['exclude_count'])

    def test_filter_exclude_genre_with_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'genres': ['Drama']}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'genres': ['Action']}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_exclude_genre(
            invocation_result=invocation_result, filter_parameter='excludeGenre',
            filter_values=['Drama'], customer=self.customer, threshold_count=1,
            request_params={'excludeGenre': ['Drama']}
        )
        self.assertEqual(['2'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_exclude_genre_multiple_exclusions(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'genres': ['Drama', 'Comedy']}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'genres': ['Action', 'Drama']}, position_in_model_result=1),
                InvocationItem(thing={'id': '3', 'genres': ['Action']}, position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_exclude_genre(
            invocation_result=invocation_result, filter_parameter='excludeGenre',
            filter_values=['Drama'], customer=self.customer, threshold_count=1,
            request_params={'excludeGenre': ['Drama']}
        )
        self.assertEqual(['3'], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    def test_filter_exclude_genre_no_genres(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'genres': []}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'genres': None}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_exclude_genre(
            invocation_result=invocation_result, filter_parameter='excludeGenre',
            filter_values=['Drama'], customer=self.customer, threshold_count=1,
            request_params={'excludeGenre': ['Drama']}
        )
        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(0, result['exclude_count'])

    ### ProgramTypeId #################

    def test_filter_program_type_id_no_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_programtypeid': '123456'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_programtypeid': '654321'}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_program_type_id(
            invocation_result=invocation_result, filter_parameter='programTypeId',
            filter_values=['132534'], customer="test_customer", threshold_count=1,
            request_params={'programTypeId': ['132534']}
        )
        self.assertEqual([], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    def test_filter_program_type_id_with_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_programtypeid': '132534'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_programtypeid': '654321'}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_program_type_id(
            invocation_result=invocation_result, filter_parameter='programTypeId',
            filter_values=['132534'], customer="test_customer", threshold_count=1,
            request_params={'programTypeId': ['132534']}
        )
        self.assertEqual(['1'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_program_type_id_multiple_exclusions(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_programtypeid': '132534'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_programtypeid': '132534'}, position_in_model_result=1),
                InvocationItem(thing={'id': '3', 'thing_custom_programtypeid': '654321'}, position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_program_type_id(
            invocation_result=invocation_result, filter_parameter='programTypeId',
            filter_values=['132534'], customer="rtve", threshold_count=10,
            request_params={'programTypeId': ['132534']}
        )
        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_program_type_id_no_program_type_id(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_programtypeid': None}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_programtypeid': ''}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_program_type_id(
            invocation_result=invocation_result, filter_parameter='programTypeId',
            filter_values=['132534'], customer="test_customer", threshold_count=1,
            request_params={'programTypeId': ['132534']}
        )
        self.assertEqual([], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    ### channelIds ##################

    def test_filter_channel_id_no_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_publication_id': 'channel_2'},
                               position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_publication_id': 'channel_3'},
                               position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_channel_id(
            invocation_result=invocation_result, filter_parameter='channelId',
            filter_values=['channel_1'], customer="test_customer", threshold_count=1,
            request_params={'channelId': ['channel_1']}
        )
        self.assertEqual([], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    def test_filter_channel_id_with_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_publication_id': 'channel_1'},
                               position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_publication_id': 'channel_3'},
                               position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_channel_id(
            invocation_result=invocation_result, filter_parameter='channelId',
            filter_values=['channel_1'], customer="test_customer", threshold_count=1,
            request_params={'channelId': ['channel_1']}
        )
        self.assertEqual(['1'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_channel_id_multiple_exclusions(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_publication_id': 'channel_1'},
                               position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_publication_id': 'channel_1'},
                               position_in_model_result=1),
                InvocationItem(thing={'id': '3', 'thing_publication_id': 'channel_3'},
                               position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_channel_id(
            invocation_result=invocation_result, filter_parameter='channelId',
            filter_values=['channel_1'], customer="test_customer", threshold_count=1,
            request_params={'channelId': ['channel_1']}
        )
        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_channel_id_no_channel_id(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_publication_id': None},
                               position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_publication_id': ''},
                               position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_channel_id(
            invocation_result=invocation_result, filter_parameter='channelId',
            filter_values=['channel_1'], customer="test_customer", threshold_count=1,
            request_params={'channelId': ['channel_1']}
        )
        self.assertEqual([], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    ### ageRating #####################

    def test_filter_age_rating_no_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_agerangeuid': 'IF_REDAD0'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_agerangeuid': 'IF_REDAD2'}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_age_rating(
            invocation_result=invocation_result, filter_parameter='ageRating',
            filter_values=['IF_REDAD4'], customer="test_customer", threshold_count=1,
            request_params={'ageRating': ['IF_REDAD4']}
        )
        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(0, result['exclude_count'])

    def test_filter_age_rating_with_exclusion(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_agerangeuid': 'IF_REDAD1'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_agerangeuid': 'IF_REDAD2'}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_age_rating(
            invocation_result=invocation_result, filter_parameter='ageRating',
            filter_values=['IF_REDAD4'], customer="test_customer", threshold_count=1,
            request_params={'ageRating': ['IF_REDAD4']}
        )
        self.assertEqual(["2"], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_filter_age_rating_multiple_exclusions(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_agerangeuid': 'IF_REDAD1'}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_agerangeuid': 'IF_REDAD1'}, position_in_model_result=1),
                InvocationItem(thing={'id': '3', 'thing_custom_agerangeuid': 'IF_REDAD2'}, position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_age_rating(
            invocation_result=invocation_result, filter_parameter='ageRating',
            filter_values=['IF_REDAD4'], customer="test_customer", threshold_count=1,
            request_params={'ageRating': ['IF_REDAD4']}
        )
        self.assertEqual(['3'], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    def test_filter_age_rating_no_age_rating(self):
        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_custom_agerangeuid': None}, position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_custom_agerangeuid': ''}, position_in_model_result=1)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )
        result = self.model_filters._filter_age_rating(
            invocation_result=invocation_result, filter_parameter='ageRating',
            filter_values=['IF_REDAD4'], customer="test_customer", threshold_count=1,
            request_params={'ageRating': ['IF_REDAD4']}
        )
        self.assertEqual([], result['original_result_ids'])
        self.assertEqual(2, result['exclude_count'])

    ### audience ##################

    def test_get_kids_audience_request_params(self):
        request = Request(
            size=70,
            query_string={
                'chartName': 'latest-publicationdate-standard',
                'audience': 'kids'
            }
        )

        self.model_filters._apply_audience_filters(request, self.customer)

        # Ensure logic has been updated to reflect existing filter options
        expected = {'channelId': ['417']}
        request_params = self.model_filters._get_request_params(request)
        self.assertEqual(expected, request_params)

    def test_get_adult_audience_request_params(self):
        request = Request(
            size=70,
            query_string={
                'chartName': 'latest-publicationdate-standard',
                'audience': 'adult',
                'excludeGenre': 'News'
            }
        )

        self.model_filters._apply_audience_filters(request, self.customer)

        # Ensure logic has been updated to reflect existing filter options
        expected = {'excludeGenre': ['News', 'Infantil']}
        request_params = self.model_filters._get_request_params(request)
        self.assertEqual(expected, request_params)

    def test_get_all_audience_request_params(self):
        request = Request(
            size=70,
            query_string={
                'chartName': 'latest-publicationdate-standard',
                'audience': 'all',
                'excludeGenre': 'News'
            }
        )

        self.model_filters._apply_audience_filters(request, self.customer)

        # Ensure no changes are made
        expected = {'excludeGenre': ['News']}
        request_params = self.model_filters._get_request_params(request)
        self.assertEqual(expected, request_params)

    def test_filter_kids_audience_exclusions(self):
        request = Request(
            size=70,
            query_string={
                'audience': 'kids'
            }
        )

        invocation_result = InvocationResult(
            model=None,
            model_results=[
                InvocationItem(thing={'id': '1', 'thing_publication_id': '417'},
                               position_in_model_result=0),
                InvocationItem(thing={'id': '2', 'thing_publication_id': '417'},
                               position_in_model_result=1),
                InvocationItem(thing={'id': '3', 'thing_publication_id': '418'},
                               position_in_model_result=2)
            ],
            model_info=None,
            seed_info=None,
            is_empty=False
        )

        self.model_filters._apply_audience_filters(request, self.customer)
        request_params = self.model_filters._get_request_params(request)

        # Ensure that we have channelId in our request_params
        self.assertIn('channelId', request_params.keys(), "ChannelId in request_params")
        result = self.model_filters._filter_channel_id(
            invocation_result=invocation_result, filter_parameter='channelId',
            filter_values=request_params['channelId'], customer="test_customer", threshold_count=1,
            request_params=request_params
        )

        self.assertEqual(['1', '2'], result['original_result_ids'])
        self.assertEqual(1, result['exclude_count'])

    def test_check_item_metadata_against_request_params_availability_and_subscriptions(
            self):
        # Case 1: availabilityArray is empty, content should be included
        request_params = {'countryCode': ['ES']}
        item_metadata = {'thing': {'thing_custom_availabilityarray': '',
                                   'thing_custom_subscriptionsarray': 'US'}}
        result = self.model_filters._check_item_metadata_against_request_params(
            item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result,
                        "Content should be included when availabilityArray is empty")

        # Case 2: availabilityArray is not available, content should be included
        item_metadata = {'thing': {'thing_custom_subscriptionsarray': 'US'}}
        result = self.model_filters._check_item_metadata_against_request_params(
            item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result,
                        "Content should be included when availabilityArray is not available")

        # Case 3: availabilityArray is not empty, country is in availabilityArray
        item_metadata = {'thing': {'thing_custom_availabilityarray': 'ES,US',
                                   'thing_custom_subscriptionsarray': 'FR'}}
        result = self.model_filters._check_item_metadata_against_request_params(
            item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result,
                        "Content should be included when country is in availabilityArray")

        # Case 4: availabilityArray is not empty, country is in subscriptionsArray
        item_metadata = {'thing': {'thing_custom_availabilityarray': 'FR',
                                   'thing_custom_subscriptionsarray': 'ES'}}
        result = self.model_filters._check_item_metadata_against_request_params(
            item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertTrue(result,
                        "Content should be included when country is in subscriptionsArray")

        # Case 5: availabilityArray is not empty, country is not in either array
        item_metadata = {'thing': {'thing_custom_availabilityarray': 'FR',
                                   'thing_custom_subscriptionsarray': 'US'}}
        result = self.model_filters._check_item_metadata_against_request_params(
            item_metadata, request_params, self.customer, self.filter_inner_results)
        self.assertFalse(result,
                         "Content should not be included when country is not in either array")