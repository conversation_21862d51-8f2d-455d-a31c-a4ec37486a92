from unittest import TestCase
from unittest.mock import MagicMock, patch

from thefilter.inference.logic.query_builder import BaseQueryBuilder, NoOpQueryBuilder
from thefilter.rules.ruleset import ConditionalTuner, Rule, Ruleset


class TestBaseQueryBuilder(TestCase):
    @patch(
        "thefilter.inference.logic.query_builder.BaseQueryBuilder._only_return_active_items")
    def test_build_calls_only_return_active_items(self, mock_only_return_active_items):
        mock_query = MagicMock()
        mock_ruleset = MagicMock()
        mock_mapping = MagicMock()
        BaseQueryBuilder.build(mock_query, mock_ruleset, mock_mapping)
        mock_only_return_active_items.assert_called_with(mock_query)


class TestNoOpQueryBuilder(TestCase):
    def test_noop_query_builder_does_nothing(self):
        query = {"this": [{"is": "here"}]}
        NoOpQueryBuilder.build(
            query,
            Ruleset(
                id="TEST",
                size=100,
                tuners=[ConditionalTuner(
                    action="INCLUDE",
                    boolean="AND",
                    rules=Rule(
                        attribute="test.a",
                        operator="EQUALS",
                        value="value.b"
                    )
                )]
            )
        )
        self.assertEqual({"this": [{"is": "here"}]}, query)
