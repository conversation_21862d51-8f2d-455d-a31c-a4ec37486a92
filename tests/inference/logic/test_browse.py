import unittest
from typing import List

from thefilter.behaviours.browse import StubBrowseRepository
from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment
from thefilter.inference.logic.invokable_models import BrowseModel
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StubMetadataRepository, StorageUserRepository


class BrowseModelTests(unittest.TestCase):

    BROWSE_PARAMETERS = {
              "settings": {
                "translations": {
                  "filters.rating": {
                    "TV Y": 0,
                    "TV TV-Y7": 5,
                    "TV TV-Y7-FV": 5,
                    "MPAA G": 10,
                    "TV TV-G": 10,
                    "G": 10,
                    "MPAA PG": 20,
                    "TV TV-PG": 20,
                    "MPAA NR": 20,
                    "PG": 20,
                    "MPAA PG-13": 30,
                    "TV TV-14": 30,
                    "PG-13": 30,
                    "MPAA R": 40,
                    "TV TV-MA": 40,
                    "R": 40,
                    "MPAA NC-17": 50,
                    "NC-17": 50,
                    "MPAA UR": 60,
                    "NR": 60
                  }
                }
              },
              "filters": [
                {
                  "description": "If active state is false, don't return it. Pretty clear cut.",
                  "query": {
                    "bool": {
                      "must_not": [
                        {
                          "term": {
                            "thing.custom.active.state": True
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Don't return Episodes",
                  "query": {
                    "bool": {
                      "must_not": [
                        {
                          "term": {
                            "typeName": "episode"
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Filter category when category is provided on the query string.",
                  "parameters": {
                    "required": [
                      {
                        "key": "category"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "term": {
                            "filters.category.lowercase_keyword": "{category}"
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Set lower bound for releaseYear when releaseYearMin is provided on the query string.",
                  "parameters": {
                    "required": [
                      {
                        "key": "releaseYearMin"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "range": {
                            "filters.releaseYear": {
                              "gte": "{releaseYearMin}"
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Set upper bound for releaseYear when releaseYearMax is provided on the query string.",
                  "parameters": {
                    "required": [
                      {
                        "key": "releaseYearMax"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "range": {
                            "filters.releaseYear": {
                              "lte": "{releaseYearMax}"
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Set upper bound for rating when ratingMax is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration",
                  "parameters": {
                    "required": [
                      {
                        "key": "ratingMax",
                        "translation": "filters.rating"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "range": {
                            "filters.rating": {
                              "lte": "{ratingMax}"
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Set lower bound for rating when ratingMin is provided on the query string. Note that mappings from input to actual ratings on metadata were derived from the behaviour of the pre-existing epix seach from before our integration",
                  "parameters": {
                    "required": [
                      {
                        "key": "ratingMin",
                        "translation": "filters.rating"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "range": {
                            "filters.rating": {
                              "gte": "{ratingMin}"
                            }
                          }
                        }
                      ]
                    }
                  }
                },
                {
                  "description": "Filter first letter of title when alpha is provided on the query string.",
                  "parameters": {
                    "required": [
                      {
                        "key": "alpha"
                      }
                    ]
                  },
                  "query": {
                    "bool": {
                      "must": [
                        {
                          "term": {
                            "filters.alpha.keyword": "{alpha}"
                          }
                        }
                      ]
                    }
                  }
                }
              ]
            }

    def _execute_model(
            self,
            things_from_browse: List[dict],
            things_from_metadata: List[dict],
            request_query_string: dict):
        model = BrowseModel(
            customer="test",
            tracer_factory=NoOpTracerFactory(),
            browse_repository=StubBrowseRepository(
                results=things_from_browse
            ),
            metadata_repository=StubMetadataRepository(
                get_items_response={
                    thing["thing"]["id"]: thing
                    for thing in things_from_metadata
                }
            ),
            user_repository=StorageUserRepository([])
        )

        return model.invoke(
            model_definition=ModelDefinition(
                key="browse",
                version=0,
                source="BrowseModel",
                fulfilment=Fulfilment(ranges=[]),
                parameters={
                    "browse": self.BROWSE_PARAMETERS
                },
                block=None,
                key_strategy={}
            ),
            request=Request(
                query_string=request_query_string
            ),
            ruleset=None
        )

    def test_browse_returns_things(self):
        things_from_browse = [
            {
                "id": "thing_0",
                "name": "thing_0",
                "typeName": "Movie"
            },
            {
                "id": "thing_1",
                "name": "thing_1",
                "typeName": "Movie"
            },
            {
                "id": "thing_2",
                "name": "thing_2",
                "typeName": "Movie"
            },
        ]

        things_from_metadata = [
            {
                "thing": {
                    "id": "thing_0",
                    "name": "thing_0",
                    "typeName": "Movie"
                }
            },
            {
                "thing": {
                    "id": "thing_1",
                    "name": "thing_1",
                    "typeName": "Movie"
                }
            },
            {
                "thing": {
                    "id": "thing_2",
                    "name": "thing_2",
                    "typeName": "Movie"
                }
            },
        ]

        request_query_string = {}

        things_from_model = self._execute_model(
            things_from_browse,
            things_from_metadata,
            request_query_string
        )

        self.assertEqual(3, len(things_from_model))

        for i in range(0, 3):
            self.assertDictEqual(
                {
                    "id": things_from_browse[i]["id"],
                    "typeName": things_from_browse[i]["typeName"]
                },
                things_from_model[i]
            )

    def test_browse_returns_only_things_in_metadata(self):
        things_from_browse = [
            {
                "id": "thing_0",
                "name": "thing_0",
                "typeName": "Movie"
            },
            {
                "id": "thing_1",
                "name": "thing_1",
                "typeName": "Movie"
            },
            {
                "id": "thing_2",
                "name": "thing_2",
                "typeName": "Movie"
            },
        ]

        things_from_metadata = [
            {
                "thing": {
                    "id": "thing_0",
                    "name": "thing_0",
                    "typeName": "Movie"
                }
            },
            {
                "thing": {
                    "id": "thing_2",
                    "name": "thing_2",
                    "typeName": "Movie"
                }
            },
        ]

        request_query_string = {}

        things_from_model = self._execute_model(
            things_from_browse,
            things_from_metadata,
            request_query_string
        )

        self.assertEqual(2, len(things_from_model))

        self.assertDictEqual(
            {
                "id": things_from_browse[0]["id"],
                "typeName": things_from_browse[0]["typeName"]
            },
            things_from_model[0]
        )
        self.assertDictEqual(
            {
                "id": things_from_browse[2]["id"],
                "typeName": things_from_browse[2]["typeName"]
            },
            things_from_model[1]
        )


