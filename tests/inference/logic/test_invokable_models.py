from datetime import datetime, timezone
from unittest import TestCase

import freezegun

from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.recommendation import StubUserRecommendationRepo
from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, \
    RangeFulfilment, InvocationResult
from thefilter.inference.logic.invokable_models import TrendingModel, CollectionsModel, \
    PrebuiltModel
from thefilter.inference.logic.query_builder import NoOpQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory, NoOpTracer
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubSubscriptionChartReadRepository
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubEditorialRepository, StubModelConfigRepository
from thefilter.rules.ruleset import Ruleset
from thefilter.services.chart_service import StubChartService


class TestModel(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        metadata_repo = StorageMetadataRepository({
                "asset_1234": {
                    "thing": {
                        "id": "asset_1234",
                        "typeName": "Movie",
                        "name": "Asset 1234"
                    }
                }
            })

        self._model_definition = ModelDefinition(
            key="test",
            version=0,
            source="PrebuiltModel",
            fulfilment=Fulfilment(ranges=[]),
            parameters={},
            block={},
            key_strategy={}
        )

        self._model = PrebuiltModel(
            customer="test",
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=metadata_repo,
            user_repository=StorageUserRepository([]),
            metadata_lite_repository=metadata_repo,
            editorial_repository=StubEditorialRepository(),
            recommendation_repository=StubUserRecommendationRepo({}),
            key_strategy_factory=KeyStrategyFactory()
        )

    def test_update_model_info(self):
        updated_result = self._model._update_model_info(
            model_definition=self._model_definition,
            request=Request(seedIds=["asset_1234"]),
            model_invocation_result=InvocationResult(
                model=self._model_definition,
                model_results=[]
            ),
            slot_title="Replacement title for {seed_name}"
        )

        expected_model_info = {
            "seed": {
                "id": "asset_1234",
                "name": "Asset 1234",
                "title": "Replacement title for Asset 1234"
            }
        }

        self.assertEqual(updated_result.model_info, expected_model_info,
                         "Model Info matches")


class TestTrendingModel(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        charts = {
            "latest": ["1", "2", "3"],
            "latest_free": ["1_free", "2_free", "3_free"]
        }

        self._trending_model = TrendingModel(
            endpoint="test",
            query_builder=NoOpQueryBuilder(),
            metadata_repository=StorageMetadataRepository({
                "1": {
                    "thing": {
                        "id": "1",
                        "typeName": "Movie"
                    }
                }
            }),
            tracer_factory=NoOpTracerFactory(),
            page_size=100,
            customer="test",
            chart_service=StubChartService(),
            chart_read_repository=StubSubscriptionChartReadRepository(charts),
            user_repository=StorageUserRepository([]),
            metadata_lite_repository=StorageMetadataRepository({
                "1": {
                    "thing": {
                        "id": "1",
                        "typeName": "Movie"
                    }
                }
            })
        )

    @freezegun.freeze_time("2022-11-22T00:00:00+00:00")
    def test__get_time_mapping_time_0000(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T00:00:00+00:00")
    def test__get_time_mapping_time_0001(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T00:59:59+00:00")
    def test__get_time_mapping_time_0059(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T01:59:00+00:00")
    def test__get_time_mapping_time_0159(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T03:59:00+00:00")
    def test__get_time_mapping_time_0359(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T04:04:01+00:00")
    def test__get_time_mapping_time_0401(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-1'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T12:01:00+00:00")
    def test__get_time_mapping_time_1201(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-3'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T23:12:01+00:00")
    def test__get_time_mapping_time_2301(self):
        customer = 'utc_based_customer'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-5'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T05:43:00+00:00")
    def test__get_time_mapping_time_epix_0543(self):
        customer = 'epix'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-0'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T14:51:00+00:00")
    def test__get_time_mapping_time_epix_1451(self):
        customer = 'epix'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-2'
        self.assertEqual(expected, trending_chart)

    @freezegun.freeze_time("2022-11-22T00:01:00+00:00")
    def test__get_time_mapping_time_epix_0001(self):
        customer = 'epix'
        trending_chart = self._trending_model._get_time_mapping(customer, 'trending')
        expected = 'trending-4'
        self.assertEqual(expected, trending_chart)

    def test_trending_no_user_history(self):
        charts = {
            "trending": ["1", "2", "3"],
        }

        trending_model_no_user_history = TrendingModel(
            endpoint="test",
            query_builder=NoOpQueryBuilder(),
            metadata_repository=StorageMetadataRepository({
                "1": {
                    "thing": {
                        "id": "1",
                        "typeName": "Movie"
                    }
                }
            }),
            tracer_factory=NoOpTracerFactory(),
            page_size=100,
            customer="test",
            chart_service=StubChartService(),
            chart_read_repository=StubSubscriptionChartReadRepository(charts),
            user_repository=[],
            metadata_lite_repository=StorageMetadataRepository({
                "1": {
                    "thing": {
                        "id": "1",
                        "typeName": "Movie"
                    }
                }
            })
        )

        fulfilment = Fulfilment(ranges=[])

        model_definition = ModelDefinition(
            key="test",
            version=0,
            source="TrendingModel",
            fulfilment=fulfilment,
            parameters={},
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id="",
            size=1,
            sort="",
            sortBy="",
            seedIds=[],
            chart_Id="",
            subscription_type="",
            userId="",
            anonId="",
            accountId="",
            query_string={},
            request_start=datetime.now(),
            debug_metadata="",
            exclude=[],
            headers={},
            editorials=[],
            allowed_items=[],
            stats={},
            promotions={},
            q="",
            count=1
        )

        expected = []
        actual = trending_model_no_user_history.handle_get_latest_from_user_history(
            request,
            NoOpTracer(sub_segment_name=""),
            model_definition
        )

        self.assertEqual(expected, actual)

        prefix = trending_model_no_user_history._get_trending_prefix(model_definition)
        expected = 'trending-daily-standard'
        self.assertEqual(expected, prefix)

        model_definition.parameters['trendingPrefix'] = 'trending-movies'
        prefix = trending_model_no_user_history._get_trending_prefix(model_definition)
        expected = 'trending-movies'
        self.assertEqual(expected, prefix)


class TestCollectionsModel(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        active_publication = [
            {
                "startDate": "2022-01-04T23:59:00+00:00",
                "endDate": "2022-12-04T23:59:00+00:00"
            }
        ]

        custom_active_state = {
            "active": {
                "state": True
            }
        }

        metadata_repository = StorageMetadataRepository({
            "movie_123": {
                "thing": {
                    "id": "movie_123",
                    "name": "Movie One-Two-Three",
                    "typeName": "Movie",
                    "custom": custom_active_state,
                    "publication": active_publication
                }
            },
            "movie_456": {
                "thing": {
                    "id": "movie_456",
                    "name": "Movie Four-Five-Six",
                    "typeName": "Movie",
                    "custom": custom_active_state,
                    "publication": active_publication
                }
            },
            "asset_1234": {
                "thing": {
                    "id": "asset_1234",
                    "name": "Asset 1234",
                    "typeName": "Movie",
                    "custom": custom_active_state,
                    "publication": active_publication
                }
            },
        })

        collections_rec_repo = {
            "0": {
                "col_20240118": [
                    {
                        "id": "123",
                        "title": "Collection 123"
                    },
                    {
                        "id": "456",
                        "title": "Collection 456"
                    },
                    {
                        "id": "789",
                        "title": "Collection 789"
                    },
                    {
                        "id": "901",
                        "title": "Collection 901"
                    },
                ],
            },
            "123": {
                "col_20240118": [
                    {
                        "id": "movie_123",
                        "alg": "collections"
                    },
                    {
                        "id": "movie_456",
                        "alg": "collections"
                    }
                ]
            }
        }

        self._model_definition = ModelDefinition(
            key='col_20240118',
            version=1,
            source='CollectionsModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={},
            block={},
            key_strategy={}
        )

        self._ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        self._collections_model = CollectionsModel(
            tracer_factory=NoOpTracerFactory(),
            query_builder=NoOpQueryBuilder(),
            metadata_repository=metadata_repository,
            editorial_repository=StubEditorialRepository(),
            recommendation_repository=StubUserRecommendationRepo(collections_rec_repo),
            model_config_repository=StubModelConfigRepository([]),
            key_strategy_factory=KeyStrategyFactory(),
            user_repository=StorageUserRepository([]),
            customer="test_customer",
            endpoint="test_endpoint",
            page_size=100
        )

    def test_get_collections_list(self):
        collections_list = self._collections_model.invoke(
            model_definition=self._model_definition,
            request=Request(
                rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
                size=10,
                seedIds=[],
                userId='test_user',
                query_string={'ignore': 'postman',
                              'userId': 'test_user'},
                request_start=datetime(2025, 1, 18, 16, 37, 33, 0,
                                       tzinfo=timezone.utc),
                exclude=[],
                headers={},
                editorials=[]
            ),
            ruleset=self._ruleset
        )

        expected = [
            {
                "id": "123",
                "title": "Collection 123",
                "typeName": "Collection"
            },
            {
                "id": "456",
                "title": "Collection 456",
                "typeName": "Collection"
            },
            {
                "id": "789",
                "title": "Collection 789",
                "typeName": "Collection"
            },
            {
                "id": "901",
                "title": "Collection 901",
                "typeName": "Collection"
            }
        ]

        self.assertEqual(expected, collections_list)

    def test_get_collection(self):
        collection_123 = self._collections_model.invoke(
            model_definition=self._model_definition,
            request=Request(
                rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
                size=10,
                seedIds=['123'],
                userId='test_user',
                query_string={'ignore': 'postman',
                              'userId': 'test_user'},
                request_start=datetime(2025, 1, 18, 16, 37, 33, 0,
                                       tzinfo=timezone.utc),
                exclude=[],
                headers={},
                editorials=[]
            ),
            ruleset=self._ruleset
        )

        expected = [
            {
                "id": "movie_123",
                "alg": "collections"
            },
            {
                "id": "movie_456",
                "alg": "collections"
            }
        ]

        self.assertEqual(expected, collection_123)
