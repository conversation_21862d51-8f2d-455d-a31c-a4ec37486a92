from copy import deepcopy, copy
from datetime import datetime, timezone
from unittest import Test<PERSON>ase

import freezegun
from freezegun import freeze_time

from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.recommendation import StubRecommendationRepository, \
    StubUserRecommendationRepo
from thefilter.inference.logic.configuration import PrioritisedModel, Fulfilment, \
    RangeFulfilment, InvocationResult
from thefilter.inference.logic.invokable_models import PrebuiltBYWModel
from thefilter.inference.logic.query_builder import NoOpQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubEditorialRepository, StubUserHistoryRepository, StubMetadataLiteRepository
from thefilter.rules.ruleset import Ruleset
from thefilter.utils.user_history import get_latest_brands

metadata_items = {
    "series_1_episode_1": {
        "thing": {
            "id": "series_1_episode_1",
            "partOfSeries": {
                "id": "series_1_brand"
            },
            "name": "Die Büttenrede",
            "typeName": "TVEpisode",
            "genre": [
                {
                    "name": "Erotic",
                    "id": "None"
                }
            ],
            "active": {
                "state": True,
                "stateUpdateTimestamp": "2021-11-17T06:30:32+00:00"
            },
            "contentRating": [
                {
                    "name": "I12",
                    "id": "None"
                }
            ],
            "partOfSeason": {
                "id": "c_pvui8a3ppt4",
                "name": "Staffel 02"
            },
            "custom": {
                "linking": {
                    "id": "b_pvuiofmn3zl"
                },
                "externalId": "EP033263070017",
                "active": {
                    "state": True,
                    "stateUpdateTimestamp": "2021-11-17T06:30:32+00:00"
                }
            }
        }
    },
    "series_4_episode_4": {
        "thing": {
            "id": "series_4_episode_4",
            "typeName": "TVEpisode",
            "partOfSeries": {
                "id": "series_4_brand"
            },
            "brandId": "series_4_brand"
        }
    },
    "movie_123": {
        "thing": {
            "id": "movie_123",
            "typeName": "Movie",
            "name": "Test Movie",
            "genre": [
                {
                    "name": "Action",
                    "id": "None"
                }
            ]
        }
    },
    "movie_456": {
        "thing": {
            "id": "movie_456",
            "typeName": "Movie",
            "name": "Movie 456"
        }
    },
    "movie_789": {
        "thing": {
            "id": "movie_789"
        }
    },
    "movie_abc": {
        "thing": {
            "id": "movie_abc"
        }
    },
    "movie_def": {
        "thing": {
            "id": "movie_def"
        }
    },
    "series_1_brand": {
        "thing": {
            "id": "series_1_brand",
            "typeName": "TVSeries",
            "name": "Erotic Series",
            "genre": [
                {
                    "name": "Erotic",
                    "id": "None"
                }
            ],
        }
    },
    "series_4_brand": {
        "thing": {
            "id": "series_4_brand",
            "typeName": "TVSeries",
            "name": "Documentary Series",
            "genre": [
                {
                    "name": "Documentary",
                    "id": "None"
                }
            ],
        }
    },
    "profile_movie_123": {
        "thing": {
            "id": "movie_123",
            "typeName": "Movie",
            "name": "Test Movie",
            "genre": [
                {
                    "name": "Action",
                    "id": "None"
                }
            ]
        }
    },
    "profile_movie_456": {
        "thing": {
            "id": "movie_456"
        }
    },
}

metadata_repository = StorageMetadataRepository(metadata_items)
metadata_lite_repository = StubMetadataLiteRepository(metadata_items)

user_history = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
     'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_4_episode_4', 'space': '',
                'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'movie_123', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]}
]

profile_user_history = [
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'profile_movie_123', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'profile_movie_456', 'space': '', 'typeName': 'Movie'}]}
]

user_history_with_dupes = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
     'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_4_episode_4', 'space': '',
                'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'movie_123', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-20T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-04-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-04-10T08:45:22+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
]

user_history_most_watched_tie_break = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-20T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-04-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]}
]

user_history_by_user = {
    'test_profile_userId': profile_user_history,
    'test_user_userId': user_history
}

# For some reason, passing in a set of results, like `rec_repo_results`
# to the StubModelConfigRepository causes and infinite loop between
# the the _deduplicate_page and _enrich_things functions in invokable_models.py!
rec_repo_results = [
    {
        "id": "series_1_brand",
        "typeName": "TVSeries"
    },
    {
        "id": "movie_123",
        "typeName": "Movie"
    }
]

base_model = PrebuiltBYWModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    metadata_lite_repository=metadata_lite_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=StubRecommendationRepository(
        results_to_return=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StorageUserRepository(user_history),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=0,
    chart_read_repository=StubChartReadRepository()
)

profile_model = PrebuiltBYWModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    metadata_lite_repository=metadata_lite_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=StubRecommendationRepository(
        results_to_return=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StubUserHistoryRepository(user_history_by_user),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=0
)

base_model_definition = PrioritisedModel(
    key='byw_model_test',
    version=1,
    source='PrebuiltBYWModel',
    fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
    parameters={
        'deduplicate_path': '',
        'titlePlaceholder': 'Because You Watched {seed_name}',
        'defaultVariant': 'standard'
    },
    priority=0,
    positions=[0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
    block={},
    key_strategy={}
)

base_request = Request(
    rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
    size=10,
    seedIds=[],
    userId='/2CFOzwaEVcfhdyhrs9jXh4L3Vg=',
    query_string={'ignore': 'postman',
                  'userId': '/2CFOzwaEVcfhdyhrs9jXh4L3Vg='},
    request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                           tzinfo=timezone.utc),
    exclude=[],
    headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
)

base_ruleset = Ruleset(
    id='skip_enforce', size=0, tuners=[]
)


class TestPrebuiltBYWModel(TestCase):
    def test_BYW_latest(self):
        count = 4
        model_get_latest_brands = get_latest_brands("test", metadata_repository,
                                                    user_history, count)
        expected_latest_brands = ['series_1_brand', 'series_4_brand', 'movie_123',
                                  'movie_456']
        self.assertEqual(expected_latest_brands, model_get_latest_brands)

        count = 3
        model_get_latest_brands = get_latest_brands("test", metadata_repository,
                                                    user_history, count)
        expected_latest_brands = ['series_1_brand', 'series_4_brand', 'movie_123']
        self.assertEqual(expected_latest_brands, model_get_latest_brands)

    def test_BYW_dupes(self):
        count = 4
        model_get_latest_brands = get_latest_brands("test", metadata_repository,
                                                    user_history_with_dupes, count)
        expected_latest_brands = ['series_1_brand', 'series_4_brand', 'movie_123',
                                  'movie_456']
        self.assertEqual(expected_latest_brands, model_get_latest_brands)

        count = 3
        model_get_latest_brands = get_latest_brands("test", metadata_repository,
                                                    user_history_with_dupes, count)
        expected_latest_brands = ['series_1_brand', 'series_4_brand', 'movie_123']
        self.assertEqual(expected_latest_brands, model_get_latest_brands)

    @freezegun.freeze_time("2023-03-22T14:00:00+00:00")
    def test_BYW_model_invocation(self):
        result = base_model.invoke(base_model_definition, base_request, base_ruleset)

        expected = InvocationResult(
            model=base_model_definition,
            model_results=[],
            model_info={
                'seed': {'id': 'movie_123', 'name': 'Test Movie',
                         'title': 'Because You Watched Test Movie'}},
            seed_info=None,
            is_empty=False
        )

        self.assertEqual(expected, result)

    def test_BYW_loop_until_result(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'loopUntilResult': True,
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        loop_result_model = copy(base_model)
        loop_result_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        invocation_result = loop_result_model.get_invocation_result(model_definition,
                                                                    base_request,
                                                                    base_ruleset,
                                                                    'byw')
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("movie_123").get("byw_model_test"),
            results_to_check
        )

    def test_BYW_handle_watched_items(self):
        user_history = [
            'thingId_0', 'thingId_1', 'thingId_2', 'thingId_3', 'thingId_4', 'thingId_5'
        ]

        prebuilt_byw_results = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'thingId_3', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'thingId_5', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt'}
        ]

        watched, unwatched = base_model._get_watched_and_unwatched(user_history,
                                                                   prebuilt_byw_results)

        self.assertEqual(['thingId_3', 'thingId_5'], watched)
        self.assertEqual(['yolololo0', 'yolololo1', 'yolololo2'], unwatched)

        base_model._history_method = None
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertListEqual(prebuilt_byw_results, items)

        base_model._history_method = "keep"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertListEqual(prebuilt_byw_results, items)

        base_model._history_method = "remove"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt'}
        ]
        self.assertListEqual(expected, items)

        base_model._history_method = "moveToEnd"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'thingId_3', 'typeName': 'TVSeries', 'alg': 'mlt',
             'user_history': True},
            {'id': 'thingId_5', 'typeName': 'Movie', 'alg': 'mlt', 'user_history': True}
        ]
        self.assertListEqual(expected, items)

        user_history = [
            'yolololo0', 'yolololo1', 'thingId_2', 'thingId_3', 'yolololo2', 'thingId_5'
        ]
        base_model._history_method = "remove"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertEqual(0, len(items))

        base_model._history_method = "removeIfPossible"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt',
             'user_history': True},
            {'id': 'thingId_3', 'typeName': 'TVSeries', 'alg': 'mlt',
             'user_history': True},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt', 'user_history': True},
            {'id': 'thingId_5', 'typeName': 'Movie', 'alg': 'mlt', 'user_history': True},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt', 'user_history': True}
        ]
        self.assertEqual(expected, items)

    def test_BYW_handle_watched_items_no_user_history(self):
        user_history = []

        prebuilt_byw_results = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'thingId_3', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'thingId_5', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt'}
        ]

        watched, unwatched = base_model._get_watched_and_unwatched(user_history,
                                                                   prebuilt_byw_results)

        self.assertEqual([], watched)
        self.assertEqual(
            ['yolololo0', 'thingId_3', 'yolololo1', 'thingId_5', 'yolololo2'], unwatched)

        base_model._history_method = None
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertListEqual(prebuilt_byw_results, items)

        base_model._history_method = "keep"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertListEqual(prebuilt_byw_results, items)

        base_model._history_method = "remove"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [{'alg': 'mlt', 'id': 'yolololo0', 'typeName': 'TVSeries'},
                    {'alg': 'mlt', 'id': 'thingId_3', 'typeName': 'TVSeries'},
                    {'alg': 'mlt', 'id': 'yolololo1', 'typeName': 'Movie'},
                    {'alg': 'mlt', 'id': 'thingId_5', 'typeName': 'Movie'},
                    {'alg': 'mlt', 'id': 'yolololo2', 'typeName': 'Movie'}]
        self.assertListEqual(expected, items)

        base_model._history_method = "moveToEnd"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [{'alg': 'mlt', 'id': 'yolololo0', 'typeName': 'TVSeries'},
                    {'alg': 'mlt', 'id': 'thingId_3', 'typeName': 'TVSeries'},
                    {'alg': 'mlt', 'id': 'yolololo1', 'typeName': 'Movie'},
                    {'alg': 'mlt', 'id': 'thingId_5', 'typeName': 'Movie'},
                    {'alg': 'mlt', 'id': 'yolololo2', 'typeName': 'Movie'}]
        self.assertListEqual(expected, items)

        user_history = []
        base_model._history_method = "remove"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        self.assertEqual(5, len(items))

        base_model._history_method = "removeIfPossible"
        items = base_model._handle_history_method(user_history, prebuilt_byw_results)
        expected = [
            {'id': 'yolololo0', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'thingId_3', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'yolololo1', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'thingId_5', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'yolololo2', 'typeName': 'Movie', 'alg': 'mlt'}
        ]
        self.assertEqual(expected, items)

    def test_BYW_no_user_history(self):
        no_history_model = copy(base_model)
        no_history_model._user_repository = StorageUserRepository([])

        result = no_history_model.invoke(base_model_definition, base_request,
                                         base_ruleset)

        self.assertEqual([], result.model_results)

    def test_exclude_genre(self):
        request = deepcopy(base_request)

        prebuilt_byw_results = [
            {'id': 'series_1_brand', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'series_4_brand', 'typeName': 'TVSeries', 'alg': 'mlt'},
            {'id': 'movie_123', 'typeName': 'Movie', 'alg': 'mlt'},
            {'id': 'movie_456', 'typeName': 'Movie', 'alg': 'mlt'}]

        handle_exclusions_and_filters = \
            base_model.handle_exclusions_and_filters(request, base_model_definition,
                                                     prebuilt_byw_results)

        self.assertListEqual(prebuilt_byw_results, handle_exclusions_and_filters)

        request.query_string['excludeGenre'] = "erotic"
        handle_exclusions_and_filters = \
            base_model.handle_exclusions_and_filters(request, base_model_definition,
                                                     prebuilt_byw_results)
        expected = [{'id': 'series_4_brand', 'typeName': 'TVSeries', 'alg': 'mlt'},
                    {'id': 'movie_123', 'typeName': 'Movie', 'alg': 'mlt'},
                    {'id': 'movie_456', 'typeName': 'Movie', 'alg': 'mlt'}]
        self.assertListEqual(expected, handle_exclusions_and_filters)

        request.query_string['excludeGenre'] = None
        request.query_string['typeFilter'] = "TVSeries"
        handle_exclusions_and_filters = \
            base_model.handle_exclusions_and_filters(request, base_model_definition,
                                                     prebuilt_byw_results)
        expected = [{'id': 'series_1_brand', 'typeName': 'TVSeries', 'alg': 'mlt'},
                    {'id': 'series_4_brand', 'typeName': 'TVSeries', 'alg': 'mlt'}]
        self.assertListEqual(expected, handle_exclusions_and_filters)

        request.query_string['typeFilter'] = "tvseries"
        request.query_string['excludeGenre'] = "erotic"
        handle_exclusions_and_filters = \
            base_model.handle_exclusions_and_filters(request, base_model_definition,
                                                     prebuilt_byw_results)
        expected = [{'id': 'series_4_brand', 'typeName': 'TVSeries', 'alg': 'mlt'}]
        self.assertListEqual(expected, handle_exclusions_and_filters)

        request.query_string['excludeGenre'] = None
        request.query_string['typeFilter'] = "Movie"
        handle_exclusions_and_filters = \
            base_model.handle_exclusions_and_filters(request, base_model_definition,
                                                     prebuilt_byw_results)
        # Note: "movie_789" does not have a typeName, so we should not expect it!
        expected = [{'id': 'movie_123', 'typeName': 'Movie', 'alg': 'mlt'},
                    {'id': 'movie_456', 'typeName': 'Movie', 'alg': 'mlt'}]
        self.assertListEqual(expected, handle_exclusions_and_filters)

    def test_seed_selection_7_brands(self):
        base_model._set_byw_method_and_variant(base_model_definition, base_request)

        user_brand_history = [
            'thingId_0', 'thingId_1', 'thingId_2', 'thingId_3', 'thingId_4', 'thingId_5',
            'thingId_6'
        ]

        # Seeds are selected based on weekday 0 = Monday, 6 = Sunday
        with freeze_time("2022-04-04T14:00:00"):
            monday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[0], monday_seed,
                             "Monday seed matches position 0")

        with freeze_time("2022-04-05T14:00:00"):
            tuesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[1], tuesday_seed,
                             "Tuesday seed matches position 1")

        with freeze_time("2022-04-06T14:00:00"):
            wednesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[2], wednesday_seed,
                             "Wednesday seed matches position 2")

        with freeze_time("2022-04-07T14:00:00"):
            thursday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[3], thursday_seed,
                             "Thursday seed matches position 3")

        with freeze_time("2022-04-08T14:00:00"):
            friday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[4], friday_seed,
                             "Friday seed matches position 4")

        with freeze_time("2022-04-09T14:00:00"):
            saturday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[5], saturday_seed,
                             "Saturday seed matches position 5")

        with freeze_time("2022-04-10T14:00:00"):
            sunday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[6], sunday_seed,
                             "Sunday seed matches position 6")

    def test_seed_selection_7_brands_next_variant(self):
        next_request = deepcopy(base_request)
        next_request.variant = "next"

        base_model._set_byw_method_and_variant(base_model_definition, next_request)

        user_brand_history = [
            'thingId_0', 'thingId_1', 'thingId_2', 'thingId_3', 'thingId_4', 'thingId_5',
            'thingId_6'
        ]

        # Seeds are selected based on weekday 0 = Monday, 6 = Sunday
        with freeze_time("2022-04-04T14:00:00"):
            monday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[1], monday_seed,
                             "Monday seed matches position 1")

        with freeze_time("2022-04-05T14:00:00"):
            tuesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[2], tuesday_seed,
                             "Tuesday seed matches position 2")

        with freeze_time("2022-04-06T14:00:00"):
            wednesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[3], wednesday_seed,
                             "Wednesday seed matches position 3")

        with freeze_time("2022-04-07T14:00:00"):
            thursday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[4], thursday_seed,
                             "Thursday seed matches position 4")

        with freeze_time("2022-04-08T14:00:00"):
            friday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[5], friday_seed,
                             "Friday seed matches position 5")

        with freeze_time("2022-04-09T14:00:00"):
            saturday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[6], saturday_seed,
                             "Saturday seed matches position 6")

        with freeze_time("2022-04-10T14:00:00"):
            sunday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[0], sunday_seed,
                             "Sunday seed matches position 0")

    def test_seed_selection_4_brands(self):
        base_model._set_byw_method_and_variant(base_model_definition, base_request)

        user_brand_history = [
            'thingId_0', 'thingId_1', 'thingId_2', 'thingId_3'
        ]

        # Seeds are selected based on weekday 0 = Monday, 6 = Sunday
        with freeze_time("2022-04-04T14:00:00"):
            monday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[0], monday_seed,
                             "Monday seed matches position 0")

        with freeze_time("2022-04-05T14:00:00"):
            tuesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[1], tuesday_seed,
                             "Tuesday seed matches position 1")

        with freeze_time("2022-04-06T14:00:00"):
            wednesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[2], wednesday_seed,
                             "Wednesday seed matches position 2")

        with freeze_time("2022-04-07T14:00:00"):
            thursday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[3], thursday_seed,
                             "Thursday seed matches position 3")

        with freeze_time("2022-04-08T14:00:00"):
            friday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[0], friday_seed,
                             "Friday seed matches position 0")

        with freeze_time("2022-04-09T14:00:00"):
            saturday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[1], saturday_seed,
                             "Saturday seed matches position 1")

        with freeze_time("2022-04-10T14:00:00"):
            sunday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[2], sunday_seed,
                             "Sunday seed matches position 2")

    def test_seed_selection_10_brands(self):
        base_model._set_byw_method_and_variant(base_model_definition, base_request)

        user_brand_history = [
            'thingId_0', 'thingId_1', 'thingId_2', 'thingId_3', 'thingId_4',
            'thingId_5', 'thingId_6', 'thingId_7', 'thingId_8', 'thingId_9'
        ]

        # Seeds are selected based on weekday 0 = Monday, 6 = Sunday
        with freeze_time("2022-04-04T14:00:00"):
            monday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[0], monday_seed,
                             "Monday seed matches position 0")

        with freeze_time("2022-04-05T14:00:00"):
            tuesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[1], tuesday_seed,
                             "Tuesday seed matches position 1")

        with freeze_time("2022-04-06T14:00:00"):
            wednesday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[2], wednesday_seed,
                             "Wednesday seed matches position 2")

        with freeze_time("2022-04-07T14:00:00"):
            thursday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[3], thursday_seed,
                             "Thursday seed matches position 3")

        with freeze_time("2022-04-08T14:00:00"):
            friday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[4], friday_seed,
                             "Friday seed matches position 4")

        with freeze_time("2022-04-09T14:00:00"):
            saturday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[5], saturday_seed,
                             "Saturday seed matches position 5")

        with freeze_time("2022-04-10T14:00:00"):
            sunday_seed = base_model._seed_selector(user_brand_history)
            self.assertEqual(user_brand_history[6], sunday_seed,
                             "Sunday seed matches position 6")

    def test_seed_selection_no_history(self):
        no_seed = base_model._seed_selector([])

        self.assertEqual(None, no_seed, "No seed selected when no history found")

    def test_no_seed_metadata(self):
        no_seed_metadata = base_model.get_seed_metadata("missing_seed")
        self.assertEqual(None, no_seed_metadata, "No seed metadata found")

    def test_BYW_last_item_watched(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'lastItemWatched',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        loop_result_model = copy(base_model)
        loop_result_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )

        invocation_result = loop_result_model.get_invocation_result(model_definition,
                                                                    base_request,
                                                                    base_ruleset,
                                                                    'byw')
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("series_4_brand").get("byw_model_test"),
            results_to_check)

    def test_BYW_last_item_watched_no_matches(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'lastItemWatched',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        loop_result_model = copy(base_model)
        loop_result_model._recommendation_repository = StubUserRecommendationRepo(
            results={}
        )

        invocation_result = loop_result_model._loop_for_results(
            model_definition,
            base_request,
            [
                'series_2_brand',
                'series_3_brand',
                'series_5_brand'
            ],
            2
        )

        self.assertEqual([], invocation_result[0], "No MLT results available")
        self.assertEqual(None, invocation_result[1], "No Seed found")

    def test_BYW_most_watched(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'mostWatchedBrand',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_1_brand': {
                'byw_model_test': [
                    {
                        "id": "series_4_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_456",
                        "typeName": "Movie"
                    }
                ]
            },
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        most_watched_model = copy(base_model)
        most_watched_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        most_watched_model._user_repository = \
            StorageUserRepository(user_history_with_dupes)

        invocation_result = most_watched_model.get_invocation_result(
            model_definition,
            base_request,
            base_ruleset,
            'byw'
        )
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("series_1_brand").get("byw_model_test"),
            results_to_check)

    def test_BYW_most_watched_tiebreak(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'mostWatchedBrand',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_1_brand': {
                'byw_model_test': [
                    {
                        "id": "series_4_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_456",
                        "typeName": "Movie"
                    }
                ]
            },
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        most_watched_model = copy(base_model)
        most_watched_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        most_watched_model._user_repository = \
            StorageUserRepository(user_history_most_watched_tie_break)

        invocation_result = most_watched_model.get_invocation_result(
            model_definition,
            base_request,
            base_ruleset,
            'byw'
        )
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("series_1_brand").get("byw_model_test"),
            results_to_check)

    def test_BYW_most_watched_none(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'mostWatchedBrand',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {}

        most_watched_model = copy(base_model)
        most_watched_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        most_watched_model._user_repository = \
            StorageUserRepository(user_history_with_dupes)

        invocation_result = most_watched_model.get_invocation_result(
            model_definition,
            base_request,
            base_ruleset,
            'byw'
        )
        results_to_check = []

        self.assertEqual(invocation_result.model_results, results_to_check,
                         "Empty results")
        self.assertEqual(invocation_result.seed_info, None, "No Seed Info")

    @freezegun.freeze_time("2023-03-22T14:00:00+00:00")
    def test_byw_seed_selection_next(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'defaultVariant': 'next',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        result = base_model.invoke(model_definition, base_request, base_ruleset)

        expected = InvocationResult(
            model=model_definition,
            model_results=[],
            model_info={
                'seed': {'id': 'movie_456', 'name': 'Movie 456',
                         'title': 'Because You Watched Movie 456'}},
            seed_info=None,
            is_empty=False
        )

        self.assertEqual(expected, result)

    def test_BYW_last_item_watched_next(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'lastItemWatched',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        loop_result_model = copy(base_model)
        loop_result_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )

        next_request = deepcopy(base_request)
        next_request.variant = 'next'
        invocation_result = loop_result_model.get_invocation_result(model_definition,
                                                                    next_request,
                                                                    base_ruleset,
                                                                    'byw')
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("movie_123").get("byw_model_test"),
            results_to_check)

    def test_BYW_most_watched_next(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'mostWatchedBrand',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_1_brand': {
                'byw_model_test': [
                    {
                        "id": "series_4_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_456",
                        "typeName": "Movie"
                    }
                ]
            },
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        most_watched_model = copy(base_model)
        most_watched_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        most_watched_model._user_repository = \
            StorageUserRepository(user_history_with_dupes)

        next_request = deepcopy(base_request)
        next_request.variant = "next"

        invocation_result = most_watched_model.get_invocation_result(
            model_definition,
            next_request,
            base_ruleset,
            'byw'
        )
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("movie_456").get("byw_model_test"),
            results_to_check)

    def test_BYW_most_watched_exclude_last(self):
        model_definition = deepcopy(base_model_definition)
        model_definition.parameters = {
            'deduplicate_path': '',
            'bywMethod': 'mostWatchedBrandExcludeLast',
            'titlePlaceholder': 'Because You Watched {seed_name}'
        }

        rec_repo_results_seed_based = {
            'series_1_brand': {
                'byw_model_test': [
                    {
                        "id": "series_4_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_456",
                        "typeName": "Movie"
                    }
                ]
            },
            'series_4_brand': {
                'byw_model_test': [
                    {
                        "id": "series_1_brand",
                        "typeName": "TVSeries"
                    },
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_123': {
                'byw_model_test': [
                    {
                        "id": "movie_abc",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_bcd",
                        "typeName": "Movie"
                    }
                ]
            },
            'movie_456': {
                'byw_model_test': [
                    {
                        "id": "movie_fgh",
                        "typeName": "Movie"
                    },
                    {
                        "id": "movie_ghi",
                        "typeName": "Movie"
                    }
                ]
            }
        }

        most_watched_model = copy(base_model)
        most_watched_model._recommendation_repository = StubUserRecommendationRepo(
            results=rec_repo_results_seed_based
        )
        most_watched_model._user_repository = \
            StorageUserRepository(user_history_with_dupes)

        invocation_result = most_watched_model.get_invocation_result(
            model_definition,
            base_request,
            base_ruleset,
            'byw'
        )
        results_to_check = []

        for item in invocation_result.model_results:
            results_to_check.append({
                'id': item.thing.get('id'),
                'typeName': item.thing.get('typeName'),
            })

        self.assertEqual(
            rec_repo_results_seed_based.get("movie_456").get("byw_model_test"),
            results_to_check
        )