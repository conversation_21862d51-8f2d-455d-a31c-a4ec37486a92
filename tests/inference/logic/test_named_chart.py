import unittest
from unittest.mock import patch

from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, RangeFulfilment
from thefilter.inference.logic.invokable_models import NamedU<PERSON><PERSON><PERSON><PERSON>, NamedChart
from thefilter.inference.logic.query_builder import NoOpQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository, StubSubscriptionChartReadRepository
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository
from thefilter.rules.ruleset import Ruleset
from thefilter.services.chart_service import StubChartService


class TestNamedChart(unittest.TestCase):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._chart_model_definition = ModelDefinition(
            "latest",
            1,
            "NamedChart",
            Fulfilment(
                [
                    RangeFulfilment(0, 1)
                ]
            ),
            parameters={},
            block=None,
            key_strategy={}
        )

        charts = {
            "latest": ["1", "2", "3"],
            "latest_free": ["1_free", "2_free", "3_free"]
        }

        self._named_chart_model = NamedChart(
            endpoint="test",
            query_builder=NoOpQueryBuilder(),
            metadata_lite_repository=StorageMetadataRepository({}),
            metadata_repository=StorageMetadataRepository({
                "1": {
                    "thing": {
                        "id": "1",
                        "typeName": "Movie"
                    }
                },
                "2": {
                    "thing": {
                        "id": "2",
                        "typeName": "Movie"
                    }
                },
                "3": {
                    "thing": {
                        "id": "3",
                        "typeName": "Movie"
                    }
                },
                "1_free": {
                    "thing": {
                        "id": "1_free",
                        "typeName": "Movie"
                    }
                },
                "2_free": {
                    "thing": {
                        "id": "2_free",
                        "typeName": "Movie"
                    }
                },
                "3_free": {
                    "thing": {
                        "id": "3_free",
                        "typeName": "Movie"
                    }
                }
            }),
            tracer_factory=NoOpTracerFactory(),
            page_size=100,
            customer="test",
            chart_service=StubChartService(charts),
            chart_read_repository=StubSubscriptionChartReadRepository(charts),
            user_repository=StorageUserRepository([])
        )

    @patch("thefilter.services.chart_service.StubChartService.get_chart_ids", return_value=["1", "2", "3"])
    def test_named_chart_uktv(self, mock_get_chart):
        expected_results = [{"id": "1"}, {"id": "2"}, {"id": "3"}]

        chart_repo = StubChartReadRepository()

        results = NamedUKTVChart(
            None, None, None,
            NoOpTracerFactory(),
            page_size=100,
            customer="uktv",
            chart_service=StubChartService(),
            chart_read_repository=chart_repo,
            metadata_lite_repository=StorageMetadataRepository({}),
            user_repository=StorageUserRepository([])
        )._get_chart(
            Request(query_string={'chartName': 'latest'}),
            self._chart_model_definition
        )

        self.assertEqual(expected_results, results)

    @patch("thefilter.services.chart_service.StubChartService.get_chart_ids", return_value=["1", "2", "3"])
    def test_named_chart_with_epix(self, mock_get_chart):
        expected_results = [{"id": "1"}, {"id": "2"}, {"id": "3"}]

        chart_repo = StubChartReadRepository()

        results = NamedChart(
            None, None, None,
            NoOpTracerFactory(),
            page_size=100,
            customer="epix",
            chart_service=StubChartService(),
            chart_read_repository=chart_repo,
            metadata_lite_repository=StorageMetadataRepository({}),
            user_repository=StorageUserRepository([])
        )._get_chart(
            Request(query_string={'chartName': 'Latest-All'}),
            self._chart_model_definition
        )

        self.assertEqual(expected_results, results)

    def test_named_chart_with_no_subscription_type(self):
        paid_results = self._named_chart_model.invoke(self._chart_model_definition,
                                                      Request(query_string={"chartName": "latest"}),
                                                      Ruleset(id="", size=10, tuners=[]))

        self.assertEqual([{'id': '1'},
                          {'id': '2'},
                          {'id': '3'}], paid_results)
