from datetime import datetime, timezone
from unittest import TestCase

from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.recommendation import StubColdUserSupportingRecommendationRepo
from thefilter.inference.logic.configuration import Fulfilment, RangeFulfilment, ModelDefinition
from thefilter.inference.logic.invokable_models import PrebuiltPositionalModel
from thefilter.inference.logic.query_builder import NoOpQueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubEditorialRepository, StubModelConfigRepository
from thefilter.rules.ruleset import Ruleset

metadata_repository = StorageMetadataRepository({
    "item_1": {
        "thing": {
            "id": "item_1",
            "name": "Item One",
            "typeName": "Movie"
        }
    },
    "item_2": {
        "thing": {
            "id": "item_2",
            "name": "Item Two",
            "typeName": "Movie"
        }
    },
    "item_3": {
        "thing": {
            "id": "item_3",
            "name": "Item Three",
            "typeName": "Movie"
        }
    },
    "item_4": {
        "thing": {
            "id": "item_4",
            "name": "Item Four",
            "typeName": "Movie"
        }
    },
    "item_5": {
        "thing": {
            "id": "item_5",
            "name": "Item Five",
            "typeName": "Movie"
        }
    },
})

user_history = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [
         {'id': 'series_1_episode_1', 'space': '', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
     'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_4_episode_4', 'space': 'brand',
                'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'movie_123', 'space': '', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'movie_456', 'space': '', 'typeName': 'Movie'}]}
]


rec_repo_results = {
    'user_a':
        {
            'rfy_a': [
                {
                    "id": "item_1",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": 0,
                        "rfy_a": None,
                        "rfy_a_hero_06:00:00-16:00:00": 2,
                        "rfy_a_hero_16:00:00-06:00:00": None
                    }
                },
                {
                    "id": "item_2",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": 1,
                        "rfy_a": None,
                        "rfy_a_hero_06:00:00-16:00:00": None,
                        "rfy_a_hero_16:00:00-06:00:00": 0
                    }
                },
                {
                    "id": "item_3",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": None,
                        "rfy_a": 1,
                        "rfy_a_hero_06:00:00-16:00:00": 0,
                        "rfy_a_hero_16:00:00-06:00:00": None
                    }
                },
                {
                    "id": "item_4",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": None,
                        "rfy_a": 0,
                        "rfy_a_hero_06:00:00-16:00:00": 1,
                        "rfy_a_hero_16:00:00-06:00:00": 1
                    }
                },
                {
                    "id": "item_5",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": None,
                        "rfy_a": 2,
                        "rfy_a_hero_06:00:00-16:00:00": 3,
                        "rfy_a_hero_16:00:00-06:00:00": 2
                    }
                }
            ]
        },
    'user_a_free':
        {
            'rfy_a': [
                {
                    "id": "item_1",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero_06:00:00-16:00:00": 1,
                        "rfy_a_hero_16:00:00-06:00:00": 2
                    }
                },
                {
                    "id": "item_2",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero_06:00:00-16:00:00": 3,
                        "rfy_a_hero_16:00:00-06:00:00": 1
                    }
                },
                {
                    "id": "item_3",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero_06:00:00-16:00:00": 2,
                        "rfy_a_hero_16:00:00-06:00:00": 3
                    }
                }

            ]
        },
        'user_null':
        {
            'rfy_a': [
                {
                    "id": "item_1",
                    "typeName": "Movie",
                    "position": {
                        "rfy_a_hero": 1,
                        "rfy_a_hero": 2
                    }
                },
                {
                    "id": None,
                    "position": {
                        "rfy_a_hero": 3,
                        "rfy_a_hero": 1
                    }
                },
                {
                    "id": None,
                    "position": {
                        "rfy_a_hero": 2,
                        "rfy_a_hero": 3
                    }
                }
            ]
        },
        'user_no_pos':
        {
            'rfy_a': [
                {
                    "id": "item_1",
                    "typeName": "Movie",
                },
                {
                    "id": "item_2",
                    "typeName": "Movie",
                },
                {
                    "id": "item_3",
                    "typeName": "Movie",
                },
            ]
        }
}
prebuilt_positional_model = PrebuiltPositionalModel(
    tracer_factory=NoOpTracerFactory(),
    query_builder=NoOpQueryBuilder(),
    metadata_repository=metadata_repository,
    editorial_repository=StubEditorialRepository(),
    recommendation_repository=StubColdUserSupportingRecommendationRepo(
        results=rec_repo_results
    ),
    model_config_repository=StubModelConfigRepository(
        records=[]
    ),
    key_strategy_factory=KeyStrategyFactory(),
    user_repository=StorageUserRepository(user_history),
    customer='test_customer',
    endpoint='test_endpoint',
    page_size=100
)


class TestPrebuiltPositionalModel(TestCase):

    def test_model_invocation_rfy(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'deduplicate_path': ''
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = prebuilt_positional_model.invoke(model_definition, request, ruleset)

        expected_result = [
            {
                "id": "item_4",
                "typeName": "Movie"
            },
            {
                "id": "item_3",
                "typeName": "Movie"
            },
            {
                "id": "item_5",
                "typeName": "Movie"
            },
        ]

        self.assertEqual(expected_result, result)

    def test_model_invocation_hero(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'isHero': True
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = prebuilt_positional_model.invoke(model_definition, request, ruleset)

        expected_result = [
            {
                "id": "item_1",
                "typeName": "Movie"
            },
            {
                "id": "item_2",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_result, result)

    def test_model_invocation_hero_timeslots(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'isHero': True
            },
            block={},
            key_strategy={
                "name": "SubscriberPositionalTimeRangedKeyStrategy"
            }
        )

        # Run test for AM
        request_am = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a'},
            request_start=datetime(2022, 5, 17, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset_am = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result_am = prebuilt_positional_model.invoke(model_definition, request_am, ruleset_am)

        expected_am = [
            {
                "id": "item_3",
                "typeName": "Movie"
            },
            {
                "id": "item_4",
                "typeName": "Movie"
            },
            {
                "id": "item_1",
                "typeName": "Movie"
            },
            {
                "id": "item_5",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_am, result_am)

        # Run test for PM
        request_pm = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a'},
            request_start=datetime(2022, 5, 17, 20, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset_pm = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result_pm = prebuilt_positional_model.invoke(model_definition, request_pm, ruleset_pm)

        expected_pm = [
            {
                "id": "item_2",
                "typeName": "Movie"
            },
            {
                "id": "item_4",
                "typeName": "Movie"
            },
            {
                "id": "item_5",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_pm, result_pm)

    def test_model_invocation_free_hero_timeslots(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'isHero': True
            },
            block={},
            key_strategy={
                "name": "SubscriberPositionalTimeRangedKeyStrategy"
            }
        )

        # Run test for AM
        request_am = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a',
                          'subscriptionType': 'free'},
            request_start=datetime(2022, 5, 17, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset_am = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result_am = prebuilt_positional_model.invoke(model_definition, request_am, ruleset_am)

        expected_am = [
            {
                "id": "item_1",
                "typeName": "Movie"
            },
            {
                "id": "item_3",
                "typeName": "Movie"
            },
            {
                "id": "item_2",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_am, result_am)

        # Run test for PM
        request_pm = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_a',
            query_string={'ignore': 'postman',
                          'userId': 'user_a',
                          'subscriptionType': 'free'},
            request_start=datetime(2022, 5, 17, 20, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset_pm = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result_pm = prebuilt_positional_model.invoke(model_definition, request_pm, ruleset_pm)

        expected_pm = [
            {
                "id": "item_2",
                "typeName": "Movie"
            },
            {
                "id": "item_1",
                "typeName": "Movie"
            },
            {
                "id": "item_3",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_pm, result_pm)


    def test_model_invocation_null_item_ids(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'isHero': True
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_null',
            query_string={'ignore': 'postman',
                          'userId': 'user_null'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = prebuilt_positional_model.invoke(model_definition, request, ruleset)

        expected_result = [
            {
                "id": "item_1",
                "typeName": "Movie"
            }
        ]

        self.assertEqual(expected_result, result)


    def test_model_invcoation_no_pos(self):
        model_definition = ModelDefinition(
            key='rfy_a',
            version=1,
            source='PrebuiltPositionalModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                'isHero': True
            },
            block={},
            key_strategy={}
        )

        request = Request(
            rule_id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0',
            size=10,
            seedIds=[],
            userId='user_no_pos',
            query_string={'ignore': 'postman',
                          'userId': 'user_no_pos'},
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

        result = prebuilt_positional_model.invoke(model_definition, request, ruleset)

        expected_result = []

        self.assertEqual(expected_result, result)
