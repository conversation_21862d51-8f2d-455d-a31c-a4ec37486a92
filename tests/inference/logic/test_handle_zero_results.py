from copy import deepcopy
from unittest import TestCase

import freezegun

from thefilter.behaviours.key_strategy import KeyStrategyFactory
from thefilter.behaviours.recommendation import StubUserRecommendationRepo
from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, \
    InvocationResult, InvocationItem
from thefilter.inference.logic.invokable_models import PrebuiltModel
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository, \
    StubEditorialRepository
from thefilter.rules.ruleset import Ruleset


class TestHandleZeroResults(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        metadata_repo = StorageMetadataRepository({
            "asset_1234": {
                "thing": {
                    "id": "asset_1234",
                    "typeName": "Movie",
                    "name": "Asset 1234",
                    "genres": ["testing"]
                }
            },
            "asset_2345": {
                "thing": {
                    "id": "asset_2345",
                    "typeName": "Movie",
                    "name": "Asset 2345"
                }
            },
            "asset_1234_brand": {
                "thing": {
                    "id": "asset_1234",
                    "typeName": "Movie",
                    "name": "Asset 1234",
                    "genres": ["testing"],
                    "space": "brand"
                }
            }
        })

        self._model_definition = ModelDefinition(
            key="mlt_model_test",
            version=0,
            source="PrebuiltModel",
            fulfilment=Fulfilment(ranges=[]),
            parameters={
                "useSeedId": True
            },
            block={},
            key_strategy={}
        )

        self._invocation_result = InvocationResult(
            model=self._model_definition,
            model_results=[]
        )

        self._model = PrebuiltModel(
            customer="test",
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=metadata_repo,
            user_repository=StorageUserRepository([]),
            metadata_lite_repository=metadata_repo,
            editorial_repository=StubEditorialRepository(),
            recommendation_repository=StubUserRecommendationRepo({
                "asset_1": {
                    "mlt_model_test": [
                        {
                            "id": "asset_2",
                            "typeName": "Movie"
                        },
                        {
                            "id": "asset_3",
                            "typeName": "Movie"
                        }
                    ]
                },
                "asset_2": {
                    "mlt_model_test": [
                        {
                            "id": "asset_3",
                            "typeName": "Movie"
                        },
                        {
                            "id": "asset_4",
                            "typeName": "Movie"
                        }
                    ]
                },
                "asset_4": {
                    "mlt_model_test": [
                        {
                            "id": "asset_5",
                            "typeName": "Movie"
                        },
                        {
                            "id": "asset_6",
                            "typeName": "Movie"
                        }
                    ]
                }
            }),
            key_strategy_factory=KeyStrategyFactory(),
            chart_read_repository=StubChartReadRepository(
                {
                    "most-popular-30d-play": [
                        "asset_1",
                        "asset_2",
                        "asset_3",
                        "asset_4",
                        "asset_5"
                    ],
                    "fallback-rfy": [
                        "asset_3",
                        "asset_4",
                        "asset_7",
                        "asset_8",
                        "asset_9"
                    ]
                }
            )
        )

        self._uktv_model = PrebuiltModel(
            customer="uktv",
            tracer_factory=NoOpTracerFactory(),
            metadata_repository=metadata_repo,
            user_repository=StorageUserRepository([]),
            metadata_lite_repository=metadata_repo,
            editorial_repository=StubEditorialRepository(),
            recommendation_repository=StubUserRecommendationRepo({}),
            key_strategy_factory=KeyStrategyFactory(),
            chart_read_repository=StubChartReadRepository()
        )

    def test_zero_results_paw(self):
        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="paw"
        )

        # No matching MLT for this seed
        self.assertEqual([], model_invocation.model_results,
                         "Zero results match for PAW")

    @freezegun.freeze_time("2024-01-30T00:30:00+00:00")
    def test_zero_results_byw(self):
        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        # No matching MLT for this seed
        self.assertEqual("We Think You Might Like", model_invocation.model_info.get("seed").get("title"),
                         "BYW title matches")

        self.assertEqual([], model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T00:30:00+00:00")
    def test_zero_results_byw_updated_placeholder(self):
        model_def = deepcopy(self._model_definition)
        model_def.parameters.update({
            "zeroResultsTitlePlaceholder": "Placeholder Changed"
        })

        model_invocation = self._model._handle_zero_results(
            model_definition=model_def,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        # No matching MLT for this seed
        self.assertEqual("Placeholder Changed", model_invocation.model_info.get("seed").get("title"),
                         "BYW title matches")

        self.assertEqual([], model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T10:30:00+00:00")
    def test_zero_results_byw_1000(self):
        expected_results = [
            InvocationItem(
                thing={
                    "id": "asset_5",
                    "typeName": "Movie"
                },
                position_in_model_result=0),
            InvocationItem(
                thing={
                    "id": "asset_6",
                    "typeName": "Movie"
                },
                position_in_model_result=1
            )
        ]

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T10:30:00+00:00")
    def test_zero_results_byw_1000_skipped(self):
        expected_results = []

        model_definition = deepcopy(self._model_definition)
        model_definition.parameters.update(
            {"skipZeroResult": True}
        )

        model_invocation = self._model._handle_zero_results(
            model_definition=model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T13:30:00+00:00")
    def test_zero_results_byw_1300(self):
        expected_results = []

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T15:30:00+00:00")
    def test_zero_results_byw_1500(self):
        # There is no MLT for this chart item
        expected_results = []

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Zero results match for BYW")

    @freezegun.freeze_time("2024-01-30T20:30:00+00:00")
    def test_zero_results_byw_2000(self):
        expected_results = []

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "BYW Results match")

    @freezegun.freeze_time("2024-01-30T00:30:00+00:00")
    def test_zero_results_byw_mlt(self):
        expected_results = [
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_0"
                },
                position_in_model_result=0
            ),
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_1"
                },
                position_in_model_result=1)
            ,
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_2"
                },
                position_in_model_result=2
            )
        ]

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(
                seedIds=["asset_1234"],
                query_string={}
            ),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        # No matching MLT for this seed
        self.assertEqual(expected_results, model_invocation.model_results,
                         "Zero results match for BYW MLT")

    @freezegun.freeze_time("2024-01-30T20:30:00+00:00")
    def test_zero_results_byw_2000_next(self):
        expected_results = [
            InvocationItem(
                thing={
                    "id": "asset_5",
                    "typeName": "Movie"
                },
                position_in_model_result=0),
            InvocationItem(
                thing={
                    "id": "asset_6",
                    "typeName": "Movie"
                },
                position_in_model_result=1
            )
        ]

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            request=Request(
                variant="next"
            ),
            ruleset=Ruleset(id="test", tuners=[], size=6),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="byw"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "BYW Results match")

    def test_zero_results_mlt(self):
        expected_results = [
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_0"
                },
                position_in_model_result=0
            ),
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_1"
                },
                position_in_model_result=1)
            ,
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_2"
                },
                position_in_model_result=2
            )
        ]

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            ruleset=Ruleset(id="test", tuners=[], size=6),
            request=Request(
                seedIds=["asset_1234"],
                query_string={}
            ),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="mlt"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Handled zero results match for MLT")

    def test_zero_results_mlt_spaced(self):
        expected_results = [
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_0"
                },
                position_in_model_result=0
            ),
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_1"
                },
                position_in_model_result=1
            ),
            InvocationItem(
                thing={
                    "id": "genre-testing_thing_id_2"
                },
                position_in_model_result=2
            )
        ]

        model_invocation = self._uktv_model._handle_zero_results(
            model_definition=self._model_definition,
            ruleset=Ruleset(id="test", tuners=[], size=6),
            request=Request(
                seedIds=["asset_1234"],
                query_string={
                    "space": "brand"
                }
            ),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="mlt"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Handled zero results match for UKTV MLT")

    def test_zero_results_mlt_missing_item(self):
        expected_results = []

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            ruleset=Ruleset(id="test", tuners=[], size=6),
            request=Request(
                seedIds=["missing"],
                query_string={}
            ),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="mlt"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Handled zero results match for MLT")

    def test_zero_results_mlt_missing_genres(self):
        expected_results = []

        model_invocation = self._model._handle_zero_results(
            model_definition=self._model_definition,
            ruleset=Ruleset(id="test", tuners=[], size=6),
            request=Request(
                seedIds=["asset_2345"],
                query_string={}
            ),
            model_invocation_result=deepcopy(self._invocation_result),
            slot_type="mlt"
        )

        self.assertEqual(expected_results, model_invocation.model_results,
                         "Handled zero results match for MLT")
