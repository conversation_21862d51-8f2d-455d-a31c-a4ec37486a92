import unittest
from unittest.mock import patch

from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, RangeFulfilment
from thefilter.inference.logic.invokable_models import UKTVElasticSearchChart
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubChartReadRepository
from thefilter.repositories import StubMetadataRepository, StorageUserRepository, \
    StorageMetadataRepository
from thefilter.services.chart_service import StubChartService


class MockChartResponse:
    @staticmethod
    def to_dict():
        return {"hits": {"hits": [{"_source": {"chart": ["1", "2", "3"]}}]}}

    @staticmethod
    def success():
        return True


class MockNoChartResponse:
    @staticmethod
    def to_dict():
        return {}

    @staticmethod
    def success():
        return True


class UKTVElasticSearchChartTests(unittest.TestCase):

    _chart_model_definition = ModelDefinition(
            key='StubModel',
            version=1,
            source='StubModel',
            fulfilment=Fulfilment(
            ranges=[RangeFulfilment(start=0, end=5)]),
            parameters={},
            block=None,
            key_strategy=None
        )

    @patch("thefilter.services.chart_service.StubChartService.get_chart_ids", return_value=["1", "2", "3"])
    def test_formats_chart(self, mock_get_chart):
        expected_results = [{"id": "1"}, {"id": "2"}, {"id": "3"}]

        chart_repo = StubChartReadRepository()

        results = UKTVElasticSearchChart(
            space="", endpoint=None, query_builder=None,
            tracer_factory=NoOpTracerFactory(),
            page_size=100,
            customer="uktv",
            chart_service=StubChartService(),
            chart_read_repository=chart_repo,
            metadata_lite_repository=StorageMetadataRepository({}),
            user_repository=StorageUserRepository([]),
            metadata_repository=StubMetadataRepository({})
        )._get_chart(Request(query_string={}), self._chart_model_definition)

        self.assertEqual(expected_results, results)

    @patch(
        "thefilter.model.repositories.StubChartReadRepository.get_chart_ids",
        return_value=[]
    )
    def test_formats_empty_chart(self, mock_get_chart):
        expected_results = []

        chart_repo = StubChartReadRepository()

        results = UKTVElasticSearchChart(
            "", None, None,
            tracer_factory=NoOpTracerFactory(),
            page_size=100,
            customer="uktv",
            chart_service=StubChartService(),
            chart_read_repository=chart_repo,
            metadata_lite_repository=StorageMetadataRepository({}),
            metadata_repository=StubMetadataRepository({}),
            user_repository=StorageUserRepository([])
        )._get_chart(Request(query_string={}), self._chart_model_definition)

        self.assertEqual(expected_results, results)

    @patch("thefilter.inference.logic.invokable_models.UKTVElasticSearchChart._execute_promotions_query")
    def test_get_promotion_ids_no_channel(self, mock_execute_promotions_query):
        UKTVElasticSearchChart(
            "",
            None,
            None,
            None,
            NoOpTracerFactory(),
            page_size=100,
            customer="uktv",
            metadata_lite_repository=StorageMetadataRepository({}),
            chart_service=StubChartService(),
            chart_read_repository=StubChartReadRepository(),
            user_repository=StorageUserRepository([]))._get_promotion_ids(None)

        expected_query = {
            "query": {
                "bool": {
                    "filter": [{
                        "term": {
                            "thing.typeName.lowercase_keyword": "promotion"
                        }
                    }, {
                        "term": {
                            "thing.custom.active.state": "true"
                        }
                    }]
                }
            },
            "from": 0,
            "size": 500,
            "_source": "thing.id"
        }

        query_dict = mock_execute_promotions_query.call_args[0][0].to_dict()
        self.assertEqual(expected_query, query_dict)

    @patch("thefilter.inference.logic.invokable_models.UKTVElasticSearchChart._execute_promotions_query")
    def test_get_promotion_ids_with_channel(self,
                                            mock_execute_promotions_query):
        UKTVElasticSearchChart("", None, None, None,
                               NoOpTracerFactory(),
                               metadata_lite_repository=StorageMetadataRepository({}),
                               page_size=100,
                               customer="uktv",
                               chart_service=StubChartService(),
                               chart_read_repository=StubChartReadRepository(),
                               user_repository=StorageUserRepository([])
                               )._get_promotion_ids("1234")
        expected_query = {
            "query": {
                "bool": {
                    "filter": [{
                        "term": {
                            "thing.typeName.lowercase_keyword": "promotion"
                        }
                    }, {
                        "term": {
                            "thing.custom.active.state": "true"
                        }
                    }, {
                        "term": {
                            "thing.custom.channel.id": "1234"
                        }
                    }]
                }
            },
            "from": 0,
            "size": 500,
            "_source": "thing.id"
        }

        query_dict = mock_execute_promotions_query.call_args[0][0].to_dict()
        self.assertEqual(expected_query, query_dict)
