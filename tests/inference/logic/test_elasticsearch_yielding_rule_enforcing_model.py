import unittest
from typing import Dict, List
from unittest.mock import Mock, patch

from thefilter.inference.logic.configuration import ModelDefinition
from thefilter.inference.logic.invokable_models import \
    ElasticsearchRuleEnforcingModel
from thefilter.inference.logic.query_builder import NoOp<PERSON>ueryBuilder
from thefilter.logs.tracer import NoOpTracerFactory
from thefilter.model.messages.request import Request
from thefilter.repositories import StorageMetadataRepository, StorageUserRepository
from thefilter.rules.ruleset import Ruleset


class TestElasticsearchRuleEnforcingModel(
    ElasticsearchRuleEnforcingModel):
    def invoke(self, model_definition: ModelDefinition, request: Request,
               ruleset: Ruleset) -> List[Dict]:
        pass


class ElasticsearchYieldingRuleEnforcingModelTests(unittest.TestCase):
    @staticmethod
    def get_model(metadata: Dict):
        return TestElasticsearchRuleEnforcingModel(
            tracer_factory=NoOpTracerFactory(),
            query_builder=NoOpQueryBuilder(),
            customer="TESTC",
            endpoint="NONE",
            metadata_repository=StorageMetadataRepository(metadata),
            page_size=100,
            user_repository=StorageUserRepository([])
        )

    @patch.object(ElasticsearchRuleEnforcingModel, "_inner_enforce_rules")
    def test_enforce_rules_raises_stop_iteration_exception(self,
                                                           mock_inner_enforce_rules):
        model = self.get_model({})
        with self.assertRaises(StopIteration):
            while True:
                next(model._enforce_rules(
                    request=Request(),
                    ruleset=Mock(),
                    things=iter([{"id": "1234A"}])
                ))
