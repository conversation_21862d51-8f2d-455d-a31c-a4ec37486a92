import unittest
from typing import Dict, List

from thefilter.inference.logic.invokable_models import EnrichThingDeduplicatingModel
from thefilter.repositories import StorageMetadataRepository


class EnrichThingDeduplicatingModelTests(unittest.TestCase):
    
    def _inner_test(self, metadata: Dict, things: List[Dict],
                    deduplicate_path: str, expected_things: List[Dict]):
        page_size = 1  # set to 1 so that tests will require paging
        metadata_check_model = EnrichThingDeduplicatingModel(
            StorageMetadataRepository(metadata),
            page_size
        )
        results = list(
            metadata_check_model._deduplicate(iter(things), deduplicate_path))
        self.assertEqual(expected_things, results)

    def test_deduplicate_all_dicts(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": {
                        "c": "he"
                    }
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": {
                        "c": "he"
                    }
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "b": {
                        "c": "ho"
                    }
                },
                "id": "3"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}]
        deduplicate_path = "a.b.c"
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "3",
            "typeName": None
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)

    def test_deduplicate_all_dicts_missing_values_not_deduplicated(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": {
                        "c": "he"
                    }
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": {
                        "c": "he"
                    }
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "d": {
                        "c": "ho"
                    }
                },
                "id": "3"
            }},
            "4": {"thing": {
                "a": {
                    "e": {
                        "c": "hi"
                    }
                },
                "id": "4",
                "typeName": "A"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}, {"id": "4"}]
        deduplicate_path = "a.b.c"
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "3",
            "typeName": None
        }, {
            "id": "4",
            "typeName": "A"
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)

    def test_deduplicate_list_of_dicts(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": [{
                        "c": "he"
                    }, {
                        "c": "hi"
                    }]
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": [{
                        "c": "he"
                    }]
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "b": [{
                        "c": "ho"
                    }, {
                        "c": "ha"
                    }]
                },
                "id": "3"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}]
        deduplicate_path = "a.b.c"
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "3",
            "typeName": None
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)

    def test_deduplicate_list_of_values(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": {
                        "c": [
                            "1",
                            "2",
                            "3"
                        ]
                    }
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": {
                        "c": [
                            "4",
                            "5",
                            "6"
                        ]
                    }
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "b": {
                        "c": [
                            "2"
                        ]
                    }
                },
                "id": "3"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}]
        deduplicate_path = "a.b.c"
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "2",
            "typeName": "T"
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)

    def test_not_deduplicate_when_deduplicate_empty_string(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": [{
                        "c": "he"
                    }, {
                        "c": "hi"
                    }]
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": [{
                        "c": "he"
                    }]
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "b": [{
                        "c": "ho"
                    }, {
                        "c": "ha"
                    }]
                },
                "id": "3"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}]
        deduplicate_path = ""
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "2",
            "typeName": "T"
        }, {
            "id": "3",
            "typeName": None
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)

    def test_deduplicate_bools(self):
        metadata = {
            "1": {"thing": {
                "a": {
                    "b": {
                        "c": True
                    }
                },
                "id": "1",
                "typeName": "T"
            }},
            "2": {"thing": {
                "a": {
                    "b": {
                        "c": True
                    }
                },
                "id": "2",
                "typeName": "T"
            }},
            "3": {"thing": {
                "a": {
                    "b": {
                        "c": False
                    }
                },
                "id": "3"
            }}
        }
        things = [{"id": "1"}, {"id": "2"}, {"id": "3"}]
        deduplicate_path = "a.b.c"
        expected_things = [{
            "id": "1",
            "typeName": "T"
        }, {
            "id": "3",
            "typeName": None
        }]
        self._inner_test(metadata, things, deduplicate_path, expected_things)
