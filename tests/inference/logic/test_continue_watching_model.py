from _decimal import Decimal
from unittest import TestCase

import freezegun

from thefilter.inference.logic.invokable_models import ContinueWatchingModel
from thefilter.repositories import StubMetadataLiteRepository


class TestContinueWatchingModel(TestCase):

    @freezegun.freeze_time("2024-05-09T15:08:42+00:00")
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        metadata_lite = {
            "thing_id_0": {
                "thing": {
                    "id": "thing_id_0",
                    "name": "test_name_0",
                    "brandId": "test_brand_id_0",
                    "typeName": "TVEpisode"
                }
            },
            "thing_id_1": {
                "thing": {
                    "id": "thing_id_1",
                    "name": "test_name_1",
                    "brandId": "test_brand_id_1",
                    "typeName": "TVEpisode"
                }
            },
        }
        self._model_api = ContinueWatchingModel(
            tracer_factory="", metadata_repository="", user_repository="",
            continue_watching_repository="", customer="",
            elasticache_events_repository="",
            metadata_lite_repository=StubMetadataLiteRepository(metadata_lite),
        )

    def test_format_elasticache_events(self):
        test_ec_data = {
            "2024-05-09T15:08:42+00:00":
                {
                    "user_id": "test_ec",
                    "action": "play",
                    "thing_id": "thing_id_0",
                    "ignore": "postman",
                    "headers_user_agent": "PostmanRuntime/7.38.0",
                    "timestamp_received": "2024-05-09T15:08:42+00:00",
                    "timestamp_initiated": "2024-05-09T15:08:42+00:00",
                    "datepartition": "2024-05-09",
                    "context_application": "api_events",
                    "context_environment": "feature",
                    "customer": "test_customer",
                    "duration": 100,
                    "progress_pct": 60,
                    "identifier_eventid": "f0dea8bf-889e-4f29-ab01-343a49b87b66"
                },
            "2024-04-29T12:45:02+00:00":
                {
                    "user_id": "test_ec",
                    "action": "play",
                    "thing_id": "thing_id_1",
                    "ignore": "postman",
                    "timestamp_initiated": "2024-04-29T12:45:02+00:00",
                    "duration": 101,
                    "progress_pct": 70,
                    "customer": "test_customer"
                },
            "2024-05-09T15:08:33+00:00":
                {
                    "user_id": "test_ec",
                    "action": "play",
                    "thing_id": "unknown_0",
                    "ignore": "postman",
                    "headers_user_agent": "PostmanRuntime/7.38.0",
                    "timestamp_received": "2024-05-09T15:08:33+00:00",
                    "timestamp_initiated": "2024-05-09T15:08:33+00:00",
                    "datepartition": "2024-05-09",
                    "context_application": "api_events",
                    "context_environment": "feature",
                    "customer": "test_customer",
                    "duration": 101,
                    "progress_pct": 99,
                    "identifier_eventid": "d1162d8f-8856-4af7-a73a-ebac4407b985"
                },
            "2024-05-09T14:10:06+00:00":
                {
                    "user_id": "test_ec",
                    "action": "play",
                    "thing_id": "unknown_1",
                    "ignore": "postman",
                    "headers_user_agent": "PostmanRuntime/7.38.0",
                    "timestamp_received": "2024-05-09T14:10:06+00:00",
                    "timestamp_initiated": "2024-05-09T14:10:06+00:00",
                    "datepartition": "2024-05-09",
                    "context_application": "api_events",
                    "context_environment": "feature",
                    "customer": "test_customer",
                    "duration": 101,
                    "progress_pct": 10,
                    "identifier_eventid": "a5fc1ef5-dbe1-46e4-a357-31075f671a0a"
                }
        }
        formatted_ec_events = self._model_api._format_ec_events(test_ec_data)
        expected = [
            {'brandId': None,
             'duration': 100,
             'id': 'thing_id_0',
             'progressPct': 60.0,
             'timestampInitiated': '2024-05-09T15:08:42+00:00',
             'typeName': None},
            {'brandId': None,
             'duration': 101,
             'id': 'thing_id_1',
             'progressPct': 70.0,
             'timestampInitiated': '2024-04-29T12:45:02+00:00',
             'typeName': None},
            {'brandId': None,
             'duration': 101,
             'id': 'unknown_0',
             'progressPct': 99.0,
             'timestampInitiated': '2024-05-09T15:08:33+00:00',
             'typeName': None},
            {'brandId': None,
             'duration': 101,
             'id': 'unknown_1',
             'progressPct': 10.0,
             'timestampInitiated': '2024-05-09T14:10:06+00:00',
             'typeName': None}
        ]
        self.assertEqual(expected, formatted_ec_events)

    def test_format_ddb_cw_events(self):
        cw_events = [
            {
                'dateCreated': '2024-05-08', 'action': 'play',
                'user_primary_id': 'test_user_id', 'thing_typename': 'TVEpisode',
                'timeToLiveEpoch': Decimal('1730441980'), 'duration': Decimal('100'),
                'progress_pct': Decimal('55'),
                'timestamp_initiated': '2023-03-10T04:27:30+00:00',
                'thing_id': 'thing_id_0'},
            {
                'dateCreated': '2024-05-08', 'action': 'play',
                'user_primary_id': 'test_user_id', 'thing_typename': 'TVEpisode',
                'timeToLiveEpoch': Decimal('1730441980'), 'duration': Decimal('100'),
                'progress_pct': Decimal('56'),
                'timestamp_initiated': '2024-05-10T04:27:30+00:00',
                'thing_id': 'thing_id_1'
            },
            {'dateCreated': '2024-05-13', 'action': 'vodprogress_75',
             'user_primary_id': '011fc5565365e8a6306702ab5951d171690b92cebdb6a75b2eedd7034e9b8a521547796098f41495e2506da394f41d47762a8b065c898d91a171ec84b554ba4d',
             'thing_typename': 'unknown', 'timeToLiveEpoch': Decimal('1720887941'),
             'duration': None, 'progress_pct': Decimal('75'),
             'timestamp_initiated': '2024-05-13T16:19:19+00:00', 'thing_id': 'thing_id_3'
             }
        ]
        formatted_cw_events = self._model_api._format_cw_events(cw_events)
        expected = [
            {'brandId': None,
             'duration': 100,
             'id': 'thing_id_0',
             'progressPct': 55.0,
             'timestampInitiated': '2023-03-10T04:27:30+00:00',
             'typeName': 'TVEpisode'},
            {'brandId': None,
             'duration': 100,
             'id': 'thing_id_1',
             'progressPct': 56.0,
             'timestampInitiated': '2024-05-10T04:27:30+00:00',
             'typeName': 'TVEpisode'},
            {'brandId': None,
             'duration': 0,
             'id': 'thing_id_3',
             'progressPct': 75.0,
             'timestampInitiated': '2024-05-13T16:19:19+00:00',
             'typeName': 'unknown'}
        ]
        self.assertEqual(expected, formatted_cw_events)

    def test_collate_ec_and_cw(self):
        formatted_cache_events = [
            {'duration': 100,
             'id': 'thing_id_0',
             'progressPct': 60,  # keep
             'timestampInitiated': '2024-05-15T15:08:42+00:00',
             'typeName': None},
            {'duration': 101,
             'id': 'thing_id_1',
             'progressPct': 70,  # older than ddb cw -> remove
             'timestampInitiated': '2023-01-01T12:45:02+00:00',
             'typeName': None},
            {'duration': 101,
             'id': 'thing_id_2',
             'progressPct': 99,  # edge case: should not be in cw -> remove
             'timestampInitiated': '2024-05-09T15:08:33+00:00',
             'typeName': None},
            {'duration': 101,
             'id': 'thing_id_3',
             'progressPct': 10,  # edge case: should not be in cw -> remove
             'timestampInitiated': '2024-05-09T14:10:06+00:00',
             'typeName': None},
            {'duration': 101,
             'id': 'thing_id_4',
             'progressPct': 60.8,
             'timestampInitiated': '2020-05-09T14:10:06+00:00',  # edge case: -> remove
             'typeName': None}
        ]

        formatted_cw_events = [
            {'duration': 200,
             'id': 'thing_id_0',
             'progressPct': 82,  # older than ddb cw, discard
             'timestampInitiated': '2024-01-01T15:08:42+00:00',
             'typeName': 'TVEpisode'},
            {'duration': 200,
             'id': 'thing_id_1',
             'progressPct': 56,  # latest, keep
             'timestampInitiated': '2024-04-28T12:45:02+00:00',
             'typeName': 'TVEpisode'}
        ]

        result = self._model_api._collate_ec_and_cw(
            formatted_cache_events, formatted_cw_events
        )
        expected = [
            {'brandId': 'test_brand_id_0',
             'duration': 100,
             'id': 'thing_id_0',
             'progressPct': 60,
             'timestampInitiated': '2024-05-15T15:08:42+00:00',
             'typeName': 'TVEpisode'},
            {'brandId': 'test_brand_id_1',
             'duration': 200,
             'id': 'thing_id_1',
             'progressPct': 56,
             'timestampInitiated': '2024-04-28T12:45:02+00:00',
             'typeName': 'TVEpisode'}
        ]
        self.assertEqual(expected, result)

    def test_collate_ec_and_cw_with_nones(self):
        ec = []
        cw = [{'id': 'thing_id_0', 'typeName': 'unknown', 'duration': 0,
               'progressPct': 75.0, 'timestampInitiated': '2024-05-13T16:21:12+00:00'}]
        results = self._model_api._collate_ec_and_cw(ec, cw)
        expected = [
            {
                'id': 'thing_id_0',
                'typeName': 'TVEpisode',
                'duration': 0,
                'progressPct': 75.0,
                'timestampInitiated': '2024-05-13T16:21:12+00:00',
                'brandId': 'test_brand_id_0'
            }
        ]
        self.assertEqual(expected, results)
