from datetime import timezone, datetime
from unittest import TestCase, mock

import freezegun

from thefilter.inference.logic.configuration import ModelDefinition, Fulfilment, \
    RangeFulfilment
from thefilter.inference.logic.invokable_models import EntityModel
from thefilter.logs.tracer import NoOpTracerFactory, NoOpTracer
from thefilter.model.messages.request import Request
from thefilter.model.repositories import StubEntityAssociationRepository
from thefilter.repositories import StorageUserRepository, StubMetadataRepository, \
    StubMetadataLiteRepository
from thefilter.rules.ruleset import Ruleset

user_history = [
    {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
     'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_1_episode_1', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
     'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'series_4_episode_4', 'typeName': 'TVEpisode'}]},
    {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
     'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                   'received': '2021-08-24T10:01:14+0000'},
     'thing': [{'id': 'test_thingId_2', 'typeName': 'Movie'}]},
    {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
     'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                   'received': '2021-06-29T12:20:43+0000'},
     'thing': [{'id': 'test_thingId_0', 'typeName': 'Movie'}]}
]

metadata_items = {
    "series_1_episode_1": {
        "thing": {
            "id": "series_1_episode_1",
            "brandId": "series_1",
            "typeName": "Episode",
            "partOfSeries": {
                "id": "series_1"
            },
            "age": 10,
            "popularity": 10
        }
    },
    "series_4_episode_4": {
        "thing": {
            "id": "series_4_episode_4",
            "brandId": "series_4",
            "typeName": "Episode",
            "partOfSeries": {
                "id": "series_4"
            },
            "age": 15,
            "popularity": 15
        }
    },
    "test_thingId_0": {
        "thing": {
            "id": "test_thingId_0",
            "brandId": "test_thingId_0",
            "typeName": "Movie",
            "age": 50,
            "popularity": 300
        }
    },
    "test_thingId_1": {
        "thing": {
            "id": "test_thingId_1",
            "brandId": "test_thingId_1",
            "typeName": "Movie",
            "age": 40,
            "popularity": 150
        }
    },
    "test_thingId_2": {
        "thing": {
            "id": "test_thingId_2",
            "brandId": "test_thingId_2",
            "typeName": "Movie",
            "age": 40,
            "popularity": 200
        }
    },
    "test_thingId_3": {
        "thing": {
            "id": "test_thingId_3",
            "brandId": "test_thingId_3",
            "typeName": "TVSeries",
            "age": 30,
            "popularity": 50
        }
    },
    "test_thingId_4": {
        "thing": {
            "id": "test_thingId_4",
            "brandId": "test_thingId_4",
            "typeName": "TVSeries",
            "age": 30,
            "popularity": 50
        }
    },
    "movie_456": {
        "thing": {
            "id": "movie_456",
            "brandId": "movie_456",
            "age": 10,
            "popularity": 100
        }
    }
}

metadata_repository = StubMetadataRepository(metadata_items)
metadata_lite_repository = StubMetadataLiteRepository(metadata_items)

es_result_1 = {
    'hits': {
        'total': 1, 'max_score': 0.0,
        'hits': [
            {
                '_index': 'entities_test',
                '_type': '_doc',
                '_id': 'bW9vZF9EaXN0dXJiaW5n',
                '_score': 0.0,
                '_source': {'entity': {'id': 'bW9vZF9EaXN0dXJiaW5n'}}}
        ]
    }
}

es_result_2 = {
    "hits": {
        "total": 1,
        "max_score": 1,
        "hits": [
            {
                "_index": "entities_test",
                "_type": "_doc",
                "_id": "test_entityId_0",
                "_score": 1,
                "_source": {
                    "entity": {
                        "name": "Disturbing",
                        "typeName": "mood"
                    }
                }
            }
        ]
    }
}

entity_associations = [
    {
        "id": "ea0",
        "entityId": "test_entityId_0",
        "entityName": "Disturbing",
        "entityType": "mood",
        "thingId": "test_thingId_0",
        "thingName": "test_thingName_0",
        "thingType": "Movie"
    },
    {
        "id": "ea1",
        "entityId": "test_entityId_0",
        "entityName": "Disturbing",
        "entityType": "mood",
        "thingId": "test_thingId_1",
        "thingName": "test_thingName_1",
        "thingType": "Movie"
    },
    {
        "id": "ea2",
        "entityId": "test_entityId_0",
        "entityName": "Disturbing",
        "entityType": "mood",
        "thingId": "test_thingId_2",
        "thingName": "test_thingName_2",
        "thingType": "Movie"
    },
    {
        "id": "ea3",
        "entityId": "test_entityId_0",
        "entityName": "Disturbing",
        "entityType": "mood",
        "thingId": "test_thingId_3",
        "thingName": "test_thingName_3",
        "thingType": "Movie"
    }
]


# Time frozen to provide fixed random seed
@freezegun.freeze_time("2023-12-11T15:16:17+00:00")
class TestEntityModel(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.model = EntityModel(
            tracer_factory=NoOpTracerFactory(),
            endpoint="test_endpoint",
            customer="test_customer",
            user_repository=StorageUserRepository(user_history),
            entity_association_repository=StubEntityAssociationRepository(
                entity_associations=entity_associations
            ),
            metadata_repository=metadata_repository,
            metadata_lite_repository=metadata_lite_repository
        )

        self.model_definition = ModelDefinition(
            key='',
            version=1,
            source='EntityModel',
            fulfilment=Fulfilment(ranges=[RangeFulfilment(start=0, end=9)]),
            parameters={
                "sortMethod": "random"
            },
            block={},
            key_strategy={}
        )

        self.request = Request(
            userId=None,
            query_string={'entity_id': None,
                          'entity_type': None,
                          'entity_name': None,
                          },
            rule_id='test_GUID',
            size=10,
            seedIds=[],
            request_start=datetime(2021, 8, 26, 10, 3, 33, 736973,
                                   tzinfo=timezone.utc),
            exclude=[],
            headers={'User-Agent': 'PostmanRuntime/7.28.3'}, editorials=[]
        )

        self.ruleset = Ruleset(
            id='b36ce954-f8e8-4a81-b63a-2cacf61d56b0', size=0, tuners=[]
        )

    @mock.patch("elasticsearch.Elasticsearch.search",
                mock.MagicMock(return_value=es_result_2))
    def test_handle_entity_lookups(self):
        self.request.query_string['entityId'] = 'test_entityId_0'
        self.model.handle_entity_lookups(self.model_definition, self.request)
        self.assertEqual("mood", self.model.entity_type)
        self.assertEqual("Disturbing", self.model.entity_name)

    def test_get_associations(self):
        associations = self.model.get_associated_thing_ids("test_entityId_0")

        expected_associations = ['test_thingId_0', 'test_thingId_1',
                                 'test_thingId_2', 'test_thingId_3']
        self.assertEqual(expected_associations, associations)

    def test_handle_user_history(self):
        self.request.userId = "test_user_id"
        history = self.model.get_user_history(self.model_definition, self.request,
                                              NoOpTracer(""))
        expected_history = [
            {'action': 'play', 'id': '91e827ef-f9f9-4a9a-8043-fff3753321bc',
             'timestamp': {'initiated': '2021-05-09T18:12:19+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [{'id': 'series_1_episode_1', 'typeName': 'TVEpisode'}]},
            {'action': 'play', 'id': '45ee6818-52a2-458f-b1e8-2896ca44cc1b',
             'timestamp': {'initiated': '2021-05-09T17:56:06+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [{'id': 'series_4_episode_4', 'typeName': 'TVEpisode'}]},
            {'action': 'play', 'id': '73775828-1c1f-40ee-b074-d24646b18ba4',
             'timestamp': {'initiated': '2021-04-23T13:18:49+0000',
                           'received': '2021-08-24T10:01:14+0000'},
             'thing': [{'id': 'test_thingId_2', 'typeName': 'Movie'}]},
            {'action': 'play', 'id': '9a8cdccd-f698-48d4-afe1-3bd117a06829',
             'timestamp': {'initiated': '2021-04-21T12:32:25+0000',
                           'received': '2021-06-29T12:20:43+0000'},
             'thing': [{'id': 'test_thingId_0', 'typeName': 'Movie'}]}]

        expected_history = ["series_1", "series_4", "test_thingId_2", "test_thingId_0"]
        self.assertEqual(expected_history, history)

    def test_get_watched_and_unwatched(self):
        self.request.userId = "test_user_id"
        history = self.model.get_user_history(self.model_definition, self.request,
                                              NoOpTracer(""))
        associations = self.model.get_associated_thing_ids("test_entityId_0")

        watched, unwatched = \
            self.model.get_watched_and_unwatched(history, associations)
        self.assertEqual({'test_thingId_0', 'test_thingId_2'}, set(watched))
        self.assertEqual({'test_thingId_1', 'test_thingId_3'}, set(unwatched))

    def test_entity_model_sort_method_none(self):
        expected = [
            {'id': 'test_thingId_1', 'typeName': 'Movie'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'series_4_episode_4', 'typeName': 'Episode'}
        ]

        self.model.sort_method = 'None'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_recency(self):
        expected = [
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'},
            {'id': 'test_thingId_2', 'typeName': 'Movie'}
        ]

        self.model.sort_method = 'recency'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_popularity(self):
        expected = [
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'},
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
        ]

        self.model.sort_method = 'popularity'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_recency_popularity(self):
        expected = [
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'}
        ]

        self.model.sort_method = 'recencyPopularity'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

        # Run again with default case, and expect the same results
        self.model.sort_method = 'default'
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_random(self):
        expected = [
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'},
            {'id': 'movie_456', 'typeName': 'CreativeWork'}
        ]

        self.model.sort_method = 'random'

        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_recency_random(self):
        expected = [
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'}
        ]

        self.model.sort_method = 'recencyrandom'

        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_popularity_random(self):
        expected = [
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'},
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
        ]

        self.model.sort_method = 'popularityrandom'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'movie_456'
        ]
        watched = []
        unwatched = [
            'test_thingId_1',
            'test_thingId_3',
            'movie_456',
            'series_4_episode_4'
        ]
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)

    def test_entity_model_sort_method_recency_popularity_random(self):
        expected = [
            {'id': 'movie_456', 'typeName': 'CreativeWork'},
            {'id': 'test_thingId_3', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_4', 'typeName': 'TVSeries'},
            {'id': 'test_thingId_2', 'typeName': 'Movie'},
            {'id': 'test_thingId_1', 'typeName': 'Movie'}
        ]

        self.model.sort_method = 'recencyPopularityRandom'
        association_thing_ids = [
            'test_thingId_1',
            'test_thingId_2',
            'test_thingId_3',
            'test_thingId_4',
            'movie_456'
        ]
        watched = []
        unwatched = []
        actual = self.model.handle_results_ordering(association_thing_ids, watched,
                                                    unwatched)

        self.assertEqual(expected, actual)
