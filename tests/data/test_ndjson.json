{"string":"test_string_0","number":-42,"boolean":false,"null":null,"object":{"this":true,"is":"immense","object":"Sparta"},"array":["1","2",{"3":4}]}
{"string":"test_string_1","number":0.1111,"boolean":false,"null":null,"object":{"this":true,"is":"immense","object":"Sparta"},"array":["1","2",{"3":4}]}
{"string":"test_string_2","number":-42,"boolean":false,"null":null,"object":{"this":true,"is":"immense","object":"Sparta"},"array":["1","2",{"3":4}]}