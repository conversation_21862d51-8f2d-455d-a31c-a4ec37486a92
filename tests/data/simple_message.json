{"identifier": {"eventId": "1234", "operationId": "1234"}, "customer": {"name": "example_customer"}, "user": {"userId": "test_user123", "accountId": "test_acct123", "anonId": "test_anon123", "primaryId": "test_user123", "clusterId": null}, "timestamp": {"initiated": "2019-03-26T12:27:28+00:00", "received": "2019-03-26T12:27:28+00:00"}, "action": "test", "context": {"application": "test", "environment": "test", "server": "bath", "site": "guild", "code_version": "test_version"}, "sourceOperationId": null, "custom": {}, "messageAttributes": {"example_key": "example_value"}}