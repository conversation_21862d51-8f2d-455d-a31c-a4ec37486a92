{"identifier": {"eventId": "eventId", "operationId": "operationId"}, "customer": {"name": "example_customer"}, "user": {"accountId": "accountId", "userId": "userId", "anonId": null, "primaryId": "userId", "clusterId": "A"}, "timestamp": {"initiated": "2019-05-06T08:46:28.077709+00:00", "received": "2019-05-06T08:46:28.079359+00:00"}, "action": "personalisation", "context": {"application": "api_personalisation", "environment": "environment", "server": "", "site": "", "code_version": "example_version"}, "thing": [{"id": "thing1-id", "brandId": "thing1-brandId", "brandName": "thing1-brandName", "name": "thing1-name", "alternateName": null, "typeName": "thing1-typeName", "genre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "duration": null, "datePublished": null, "seasonNumber": null, "image": {}, "inLanguage": null, "numberOfSeasons": null, "custom": {}}, {"id": "thing2-id", "brandId": "thing2-brandId", "brandName": "thing2-brandName", "name": "thing2-name", "alternateName": null, "typeName": "thing2-typeName", "genre": [], "contentRating": [], "actor": [], "director": [], "producer": [], "crew": [], "duration": null, "datePublished": null, "seasonNumber": null, "image": {}, "inLanguage": null, "numberOfSeasons": null, "custom": {}}], "custom": {}, "sourceOperationId": null, "messageAttributes": {"example_key": "example_value"}, "status_code": 200, "execution_time": 1, "parameters": {"rule_id": "rule-1", "size": 50, "seedIds": ["seed-id"]}, "number_items_returned": 10}