import unittest
import jwt
import os

from thefilter.validation.decode_and_authenticate import JwtTokenValidator, JwtRoleTokenValidator

SECRET_KEY = os.environ.get('SECRET_KEY', 'secret')


class TestDecoder(unittest.TestCase):
    def test_decode_token(self):
        # Test that a good token returns True
        token_validator = JwtTokenValidator(SECRET_KEY)
        good_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "AdminEdit",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = good_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_good_token = jwt.encode(good_token, signing_key, algorithm='HS256')
        self.assertTrue(token_validator.validate(encoded_good_token))

    def test_expired_token(self):
        # Test that an expired token returns False
        token_validator = JwtTokenValidator(SECRET_KEY)
        expired_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "AdminEdit",
            "exp": "1568289636",
            "nbf": '1568128999'
        }
        environment = expired_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_expired_token = jwt.encode(expired_token, signing_key, algorithm='HS256')
        self.assertFalse(token_validator.validate(encoded_expired_token))

    def test_before_nbf_token(self):
        # Test that a token generated before the not before time (nbf) returns False
        token_validator = JwtTokenValidator(SECRET_KEY)
        before_nbf_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "AdminEdit",
            "exp": "1883488999",
            "nbf": '1883488999'
        }
        environment = before_nbf_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_before_nbf_token = jwt.encode(before_nbf_token, signing_key, algorithm='HS256')
        self.assertFalse(token_validator.validate(encoded_before_nbf_token))

    def test_bad_aud_token(self):
        # Test that a token with the wrong audience returns False
        token_validator = JwtTokenValidator(SECRET_KEY)
        bad_aud_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://notthefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "AdminEdit",
            "exp": "1883488999",
            "nbf": '1568293304'
        }
        environment = bad_aud_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_bad_aud_token = jwt.encode(bad_aud_token, signing_key, algorithm='HS256')
        self.assertFalse(token_validator.validate(encoded_bad_aud_token))


class TestRoleValidator(unittest.TestCase):
    def test_good_roles(self):
        # Test that a token with authorised roles returns True
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        good_role_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "GoodRole1",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = good_role_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_good_role_token = jwt.encode(good_role_token, signing_key, algorithm='HS256')
        self.assertTrue(roles_token_validator.validate(encoded_good_role_token))

    def test_multiple_roles(self):
        # Test that a token with multiple roles within the payload returns True
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        multiple_roles_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "bad_role,GoodRole1",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = multiple_roles_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_multiple_roles_token = jwt.encode(multiple_roles_token, signing_key, algorithm='HS256')
        self.assertTrue(roles_token_validator.validate(encoded_multiple_roles_token))

    def test_multiple_roles_cases(self):
        # Test that a token with multiple lowercase roles within the payload returns True
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        multiple_lower_roles_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "bad_role,goodrole1",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = multiple_lower_roles_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_multiple_lower_roles_token = jwt.encode(multiple_lower_roles_token, signing_key,
                                                        algorithm='HS256')
        self.assertTrue(roles_token_validator.validate(encoded_multiple_lower_roles_token))

    def test_lowercase_good_role(self):
        # Test that a token with authorised, lowercase roles returns True
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        lowercase_good_role_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "good_role2",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = lowercase_good_role_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_lower_good_role_token = jwt.encode(
            lowercase_good_role_token, signing_key, algorithm='HS256')
        self.assertTrue(roles_token_validator.validate(encoded_lower_good_role_token))

    def test_bad_roles(self):
        # Test that a token with unauthorised roles returns False
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        bad_role_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "BadRole",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = bad_role_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_bad_role_token = jwt.encode(bad_role_token, signing_key, algorithm='HS256')
        self.assertFalse(roles_token_validator.validate(encoded_bad_role_token))

    def test_no_roles(self):
        # Test that a token with no roles returns False
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        no_role_token = {
            "iss": "dev1-thefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = no_role_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_no_role_token = jwt.encode(no_role_token, signing_key, algorithm='HS256')
        self.assertFalse(roles_token_validator.validate(encoded_no_role_token))

    def test_bad_issuer(self):
        # Test that a token with the wrong issuer returns False
        roles_token_validator = JwtRoleTokenValidator(['GoodRole1', 'GOOD_ROLE2'], SECRET_KEY)
        bad_iss_token = {
            "iss": "dev1-notthefilter.com",
            "aud": "http://thefilter.com",
            "unique_name": "bb17ef18-edfe-4dab-a049-3b2d99872157",
            "role": "goodrole1",
            "exp": "1883488999",
            "nbf": '1568128999'
        }
        environment = bad_iss_token.get('iss').split('-')[0]
        signing_key = f'{SECRET_KEY}{environment}'.encode('utf-16le')
        encoded_bad_iss_token = jwt.encode(bad_iss_token, signing_key, algorithm='HS256')
        self.assertFalse(roles_token_validator.validate(encoded_bad_iss_token))
