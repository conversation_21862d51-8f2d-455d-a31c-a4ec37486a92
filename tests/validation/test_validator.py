import json
import unittest

from thefilter.model.messages.message import Message
from thefilter.validation.message_validator import (
    ExternalMessageValidator,
)

test_data_dir = "tests/data"


class ExternalMessageValidatorTests(unittest.TestCase):
    def __init__(self, *args, **kwargs):
        super(ExternalMessageValidatorTests, self).__init__(*args, **kwargs)
        with open(f"{test_data_dir}/valid_external_beacon.json") as f:
            self.test_message = json.load(f)
        with open(f"{test_data_dir}/invalid_event.json") as f:
            self.invalid_event = json.load(f)

    def test_validation_succeeds(self):
        validator = ExternalMessageValidator()
        beacon = validator.validate(self.test_message)
        assert beacon, "Validation succeeded but did not return a beacon."

    def test_validation_fails(self):
        validator = ExternalMessageValidator()
        with self.assertRaises(Exception):
            validator.validate(self.invalid_event)

    def test_validation_creates_message_object(self):
        validator = ExternalMessageValidator()
        catalogue_update = validator.validate(self.test_message)
        self.assertIsInstance(catalogue_update, Message)


if __name__ == "__main__":
    unittest.main()
