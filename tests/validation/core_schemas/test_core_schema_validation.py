from unittest import TestCase

from tests.validation.core_schemas.data import catalogue_data_single, events_data_single
from tests.validation.core_schemas.expected import expected_bad_catalogue_data, \
    expected_bad_events_data
from thefilter.validation.core_schemas.core_schema_validation import CoreSchemaValidation


class TestCoreSchemaValidation(TestCase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._validation_api = CoreSchemaValidation()

    def test_validate_catalogue_data(self):
        result = self._validation_api.validate_data(
            data=catalogue_data_single,
            schema_type='catalogue'
        )
        expected = 'Validation successful. The catalogue data is valid.'
        self.assertEqual(expected, result)

    def test_validate_bad_catalogue_data(self):
        bad_catalogue_data = {
            "something": ["WAT", "is", "this?"]
        }
        result = self._validation_api.validate_data(
            data=bad_catalogue_data,
            schema_type='catalogue'
        )
        expected = expected_bad_catalogue_data
        self.assertEqual(expected, result)

    def test_validate_events_data(self):
        result = self._validation_api.validate_data(
            data=events_data_single,
            schema_type='events'
        )
        expected = 'Validation successful. The events data is valid.'
        self.assertEqual(expected, result)


    def test_validate_bad_events_data(self):
        bad_events_data = {
            "something": ["WAT", "is", "this?"]
        }
        result = self._validation_api.validate_data(
            data=bad_events_data,
            schema_type='events'
        )
        expected = expected_bad_events_data
        self.assertEqual(expected, result)

