catalogue_data_single = {
    "customer": "test_customer",
    "id": "bW92aWU7MjM=",
    "brandId": "bW92aWU7MjM=",
    "name": "Friday the 13th",
    "alternateName": "friday-the-13th",
    "typeName": "Movie",
    "genre": [
        {
            "name": "Horror"
        }
    ],
    "contentRating": [
        {
            "name": "15"
        }
    ],
    "actor": [
        {
            "name": "<PERSON>"
        },
        {
            "name": "<PERSON><PERSON><PERSON>"
        }
    ],
    "director": [
        {
            "name": "<PERSON>"
        }
    ],
    "producer": [
        {
            "name": "<PERSON>"
        },
        {
            "name": "<PERSON>"
        }
    ],
    "crew": [
        {
            "name": "<PERSON>"
        }
    ],
    "publication": [
        {
            "startDate": "2021-12-01T05:00:01+00:00",
            "endDate": "2022-05-01T03:59:59+00:00"
        }
    ],
    "keywords": [
        "noice"
    ],
    "description": "Such a good film it hurts.",
    "duration": 500,
    "datePublished": "1980-01-01T00:00:00+00:00",
    "inLanguage": "en"
}

events_data_single = {
        "session_id": "53088a15-f685-44ee-9b50-c42e82a87745",
        "timestamp_initiated": "2023-06-27T11:51:45+00:00",
        "device_id": "a51e106f-8956-477e-9a10-5cbfd622aeb1",
        "device_type": "laptop",
        "device_platform": "webOS",
        "device_timezone": "+01:00",
        "service_id": "5e0ad1b0-515e-11e9-a7ed-371ac744bd33",
        "user_id": "778014764",
        "action": "ad_loaded",
        "event_trigger": "ad_loaded",
        "thing_id": "AYdNTCP34GRKUc8H2550",
        "player_advertisment_name": "External - Preroll",
        "player_advertisment_position": "1"
    }
