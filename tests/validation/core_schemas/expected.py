expected_bad_catalogue_data = r"""Validation failed. The JSON data is invalid.
 'customer' is a required property

Failed validating 'required' in schema:
    {'$schema': 'http://json-schema.org/draft-07/schema#',
     'additionalProperties': False,
     'definitions': {'dateTimeType': {'pattern': '^(-?(?:[1-9][0-9]*)?[0-9]{4})-(1[0-2]|0[1-9])-(3[01]|0[1-9]|[12][0-9])T(2[0-3]|[01][0-9]):([0-5][0-9]):([0-5][0-9])(\\.[0-9]+)?(\\.(([0-9]{3})|([0-9]{7})))?(Z|[+-](?:2[0-3]|[01][0-9]):[0-5][0-9])?$',
                                      'type': 'string'},
                     'personType': {'properties': {'character': {'type': 'string'},
                                                   'id': {'type': 'string'},
                                                   'name': {'type': 'string'}},
                                    'required': ['name'],
                                    'type': 'object'},
                     'propertyType': {'properties': {'id': {'type': 'string'},
                                                     'name': {'type': 'string'}},
                                      'required': ['name'],
                                      'type': 'object'},
                     'publicationType': {'properties': {'endDate': {'$ref': '#/definitions/dateTimeType'},
                                                        'id': {'type': 'string'},
                                                        'name': {'type': 'string'},
                                                        'startDate': {'$ref': '#/definitions/dateTimeType'}},
                                         'type': 'object'}},
     'description': 'JSON Schema for the Catalogue API',
     'patternProperties': {'^custom_': {'type': 'string'}},
     'properties': {'actor': {'items': {'$ref': '#/definitions/personType'},
                              'type': 'array'},
                    'alternateName': {'type': 'string'},
                    'brandId': {'type': 'string'},
                    'contentRating': {'items': {'$ref': '#/definitions/propertyType'},
                                      'type': 'array'},
                    'crew': {'items': {'$ref': '#/definitions/personType'},
                             'type': 'array'},
                    'customer': {'type': 'string'},
                    'datePublished': {'$ref': '#/definitions/dateTimeType'},
                    'description': {'type': 'string'},
                    'director': {'items': {'$ref': '#/definitions/personType'},
                                 'type': 'array'},
                    'duration': {'type': 'integer'},
                    'episodeNumber': {'type': 'integer'},
                    'genre': {'items': {'$ref': '#/definitions/propertyType'},
                              'type': 'array'},
                    'id': {'type': 'string'},
                    'image': {'properties': {'url': {'type': 'string'}},
                              'type': 'object'},
                    'inLanguage': {'type': 'string'},
                    'isAdult': {'type': 'boolean'},
                    'isKids': {'type': 'boolean'},
                    'isLiveBroadcast': {'type': 'boolean'},
                    'keywords': {'items': {'type': 'string'},
                                 'type': 'array'},
                    'name': {'type': 'string'},
                    'numberOfSeasons': {'type': 'integer'},
                    'partOfSeason': {'items': {'$ref': '#/definitions/propertyType'},
                                     'type': 'array'},
                    'partOfSeries': {'items': {'$ref': '#/definitions/propertyType'},
                                     'type': 'array'},
                    'producer': {'items': {'$ref': '#/definitions/personType'},
                                 'type': 'array'},
                    'productionCompany': {'type': 'string'},
                    'publication': {'items': {'$ref': '#/definitions/publicationType'},
                                    'type': 'array'},
                    'recommendationId': {'type': 'string'},
                    'seasonNumber': {'type': 'integer'},
                    'space': {'type': 'string'},
                    'subGenre': {'items': {'$ref': '#/definitions/propertyType'},
                                 'type': 'array'},
                    'typeName': {'type': 'string'}},
     'required': ['customer',
                  'id',
                  'brandId',
                  'name',
                  'typeName',
                  'description',
                  'genre',
                  'datePublished',
                  'publication'],
     'title': 'Catalogue API Schema (External)',
     'type': 'object'}

On instance:
    {'something': ['WAT', 'is', 'this?']}"""

expected_bad_events_data = r"""Validation failed. The JSON data is invalid.
 'user_id' is a required property

Failed validating 'required' in schema[0]:
    {'required': ['user_id']}

On instance:
    {'something': ['WAT', 'is', 'this?']}"""