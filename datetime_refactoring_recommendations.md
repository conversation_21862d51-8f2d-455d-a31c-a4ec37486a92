# Datetime Refactoring Recommendations

## Overview
This document outlines recommended changes to replace custom datetime logic with existing utility functions from `DateTimeUtil` in the codebase.

## Recommendations for `dynamic_customer_chart_builder.py`

### 1. Remove redundant import in `_get_dynamic_chart_dataset_query` method
**Location**: Line 145-146
```python
from thefilter.utils.datetime import DateTimeUtil
latest_timestamp = DateTimeUtil.now_tf_str()
```

**Recommendation**: Remove the redundant import since `DateTimeUtil` is already imported at the top of the file.
```python
latest_timestamp = DateTimeUtil.now_tf_str()
```

### 2. Replace custom timestamp formatting in `_publish_charts` method
**Location**: Line 279
```python
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
```

**Recommendation**: Create a new utility method in `DateTimeUtil` for this specific format or modify the existing `get_current_date_formatted` method to include seconds and an underscore separator.

Option 1: Create a new utility method in `DateTimeUtil`:
```python
@classmethod
def get_current_datetime_with_seconds(cls) -> str:
    """
    current date and time in YYYYMMDD_HHMMSS format, like 20250102_000000
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")
```

Then use it in `_publish_charts`:
```python
timestamp = DateTimeUtil.get_current_datetime_with_seconds()
```

### 3. Replace custom date parsing in `_parse_date` method
**Location**: Lines 350-366
```python
@staticmethod
def _parse_date(date_str):
    if not isinstance(date_str, str):
        return date_str

    try:
        return datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        try:
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                return datetime.strptime(date_part, "%Y-%m-%d")
            else:
                raise ValueError(f"Unsupported date format: '{date_str}'. Expected format is 'YYYY-MM-DD'.")
        except ValueError:
            # If all parsing attempts fail, re-raise the error
            raise
```

**Recommendation**: Replace with a combination of existing utility methods or create a new utility method in `DateTimeUtil` that handles these specific cases.

Option 1: Create a new utility method in `DateTimeUtil`:
```python
@staticmethod
def parse_date_or_datetime(date_str):
    """
    Parses a date string in either 'YYYY-MM-DD' format or ISO format with 'T'.
    If the input is not a string, returns it as is.
    """
    if not isinstance(date_str, str):
        return date_str

    try:
        return DateTimeUtil.short_date_to_date_time(date_str)
    except ValueError:
        try:
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                return DateTimeUtil.short_date_to_date_time(date_part)
            else:
                raise ValueError(f"Unsupported date format: '{date_str}'. Expected format is 'YYYY-MM-DD'.")
        except ValueError:
            # If all parsing attempts fail, re-raise the error
            raise
```

Then replace the custom method with this utility method.

### 4. Replace `datetime.now()` in `_convert_athena_results_to_dynamic_chart_entries` method
**Location**: Line 414
```python
now = datetime.now()
```

**Recommendation**: Replace with `DateTimeProvider().utc_now()` to ensure consistent timezone handling.
```python
now = DateTimeProvider().utc_now()
```

## Implementation Strategy

1. Create the necessary utility methods in `DateTimeUtil` if they don't exist.
2. Replace the custom datetime logic with calls to the utility methods.
3. Test the changes to ensure functionality is preserved.
4. Update documentation to encourage the use of `DateTimeUtil` for all datetime operations.

## Benefits

1. **Consistency**: Using the same utility functions throughout the codebase ensures consistent datetime handling.
2. **Maintainability**: Centralizing datetime logic makes it easier to update and maintain.
3. **Timezone Handling**: Proper use of utility functions ensures consistent timezone handling.
4. **Error Handling**: Utility functions can include robust error handling that doesn't need to be duplicated.